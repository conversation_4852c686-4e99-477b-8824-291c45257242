# Content Preferences Enhancement Implementation Plan

## Overview
This plan outlines the integration of enhanced content preferences (voice distinctiveness, controversy approach, humor style, and brag level) into the LinkedInsight content generation system using a combined approach of preference embeddings and system prompt modulation.

## Architecture Approach
- Generate embeddings from user preferences for pattern matching
- Use combined brief + preference embeddings for enhanced pattern selection
- Dynamically modulate system prompts based on preferences
- Maintain backward compatibility with existing preference fields

---

[ ] **Phase 1: Backend Schema Updates**

    [ ] Step 1.1: Update Content Preferences Schema
        [ ] Substep 1.1.1: Add new fields to ContentPreferences model in src/api/schemas.py
        [ ] Substep 1.1.2: Update schema field descriptions and validation rules
        [ ] Substep 1.1.3: Add backward compatibility for existing preferences
    
    [ ] Test for Step 1.1: Unit test - Validate new schema accepts all preference combinations

    [ ] Step 1.2: Update Database Models and API Routes
        [ ] Substep 1.2.1: Ensure API routes handle new fields in content_preferences.py
        [ ] Substep 1.2.2: Update preference fetching in mongo_utils_async.py
        [ ] Substep 1.2.3: Add migration handling for existing user preferences
    
    [ ] Test for Step 1.2: Manual test - Save and retrieve preferences via API

[ ] **Phase 2: Preference Embedding Generation**

    [ ] Step 2.1: Create Preference Text Formatting
        [ ] Substep 2.1.1: Add preference_to_text function in strategy_utils.py
        [ ] Substep 2.1.2: Create descriptive mappings for each preference value
        [ ] Substep 2.1.3: Build combined preference description generator
    
    [ ] Test for Step 2.1: Unit test - Verify preference text generation

    [ ] Step 2.2: Implement Preference Embedding Storage
        [ ] Substep 2.2.1: Add preference_embedding field to MongoDB schema
        [ ] Substep 2.2.2: Create embedding generation on preference save
        [ ] Substep 2.2.3: Add caching for preference embeddings
    
    [ ] Test for Step 2.2: Integration test - Verify embedding generation and storage

[ ] **Phase 3: Enhanced Pattern Selection Integration**

    [ ] Step 3.1: Modify Pattern Search to Include Preferences
        [ ] Substep 3.1.1: Update brief_to_patterns.py to accept preference embedding
        [ ] Substep 3.1.2: Implement weighted embedding combination logic
        [ ] Substep 3.1.3: Add preference weight configuration (default 0.3)
    
    [ ] Test for Step 3.1: Unit test - Verify combined embedding calculation

    [ ] Step 3.2: Update Vector Pattern Selector
        [ ] Substep 3.2.1: Modify vector_pattern_selector.py to use combined embeddings
        [ ] Substep 3.2.2: Add preference-aware scoring adjustments
        [ ] Substep 3.2.3: Implement fallback for missing preference embeddings
    
    [ ] Test for Step 3.2: Integration test - Verify pattern selection considers preferences

[ ] **Phase 4: System Prompt Modulation**

    [ ] Step 4.1: Create Preference-Based Prompt Builders
        [ ] Substep 4.1.1: Add voice_distinctiveness prompt modulator
        [ ] Substep 4.1.2: Add controversy_approach prompt modulator
        [ ] Substep 4.1.3: Add humor_style prompt modulator
        [ ] Substep 4.1.4: Add brag_level prompt modulator
    
    [ ] Test for Step 4.1: Unit test - Verify each modulator generates correct prompts

    [ ] Step 4.2: Integrate Prompt Modulation into Agent System
        [ ] Substep 4.2.1: Update content_agent_system_prompt.txt with modulation markers
        [ ] Substep 4.2.2: Modify process_step.py to apply preference modulation
        [ ] Substep 4.2.3: Add preference context to agent scratchpad
    
    [ ] Test for Step 4.2: Manual test - Generate content and verify preference application

[ ] **Phase 5: Agent Integration and Flow**

    [ ] Step 5.1: Update Agent Service for Preferences
        [ ] Substep 5.1.1: Modify agent_service.py to fetch user preferences
        [ ] Substep 5.1.2: Pass preferences to pattern selection pipeline
        [ ] Substep 5.1.3: Include preferences in session state
    
    [ ] Test for Step 5.1: Integration test - Verify preferences flow through session

    [ ] Step 5.2: Enhance Content Generation Tools
        [ ] Substep 5.2.1: Update scratchpad logger to include preferences
        [ ] Substep 5.2.2: Add preference context to hook/body/ending prompts
        [ ] Substep 5.2.3: Create preference-aware example injection
    
    [ ] Test for Step 5.2: Manual test - Generate full content and verify all preferences applied

[ ] **Phase 6: Testing and Refinement**

    [ ] Step 6.1: End-to-End Testing
        [ ] Substep 6.1.1: Test extreme preference combinations
        [ ] Substep 6.1.2: Verify backward compatibility
        [ ] Substep 6.1.3: Test preference embedding regeneration
    
    [ ] Test for Step 6.1: Manual test suite - Test 5 different preference profiles

    [ ] Step 6.2: Performance Optimization
        [ ] Substep 6.2.1: Add preference embedding pre-computation
        [ ] Substep 6.2.2: Optimize prompt caching with preferences
        [ ] Substep 6.2.3: Add monitoring for preference impact
    
    [ ] Test for Step 6.2: Performance test - Verify no significant latency increase

[ ] **Phase 7: Documentation and Deployment**

    [ ] Step 7.1: Update Documentation
        [ ] Substep 7.1.1: Document new preference fields in API docs
        [ ] Substep 7.1.2: Create preference combination examples
        [ ] Substep 7.1.3: Update frontend integration guide
    
    [ ] Test for Step 7.1: Documentation review

    [ ] Step 7.2: Deployment Preparation
        [ ] Substep 7.2.1: Create migration script for existing users
        [ ] Substep 7.2.2: Add feature flags for gradual rollout
        [ ] Substep 7.2.3: Prepare rollback plan
    
    [ ] Test for Step 7.2: Deployment dry run

---

## Implementation Notes

### Preference Embedding Format
```python
preference_text = f"""
Writing voice: {voice_distinctiveness} - brings {get_voice_description(voice_distinctiveness)} to every post
Controversy style: {controversy_approach} - {get_controversy_description(controversy_approach)} when discussing industry topics  
Humor approach: {humor_style} - uses {get_humor_description(humor_style)} to engage readers
Success framing: {brag_level} - attributes achievements with {get_brag_description(brag_level)}
"""
```

### Combined Embedding Calculation
```python
combined_embedding = (brief_embedding * 0.7) + (preference_embedding * 0.3)
```

### System Prompt Modulation Example
```
Base prompt + 
Voice section: "Write with {voice_level} personal distinctiveness..." +
Controversy section: "When addressing industry topics, {controversy_guidance}..." +
Humor section: "Use {humor_style} to create engagement..." +
Confidence section: "Frame successes with {brag_approach}..."
```

## Success Criteria
- All new preferences fields are saved and retrieved correctly
- Pattern selection shows measurable influence from preferences
- Generated content reflects the selected personality traits
- No degradation in generation quality or speed
- Backward compatibility maintained for existing users