# Content Strategy Integration Implementation Plan

## Overview
This plan outlines the integration of user content strategy throughout the LinkedInsight system, including funnel stage classification (Top/Middle/Bottom) for ideas, strategic context awareness in all agents, and pattern selection influence.

## Architecture Decisions
- **Funnel Stages**: Top of Funnel (50-60%), Middle Funnel (30-40%), Bottom Funnel (10-20%)
- **Strategy Storage**: MongoDB with Anthropic prompt caching consideration
- **UI Display**: Funnel stage in idea cards and agent explanations
- **Pattern Selection**: Strategy embeddings influence vector search

---

[x] **Phase 1: Backend Infrastructure for Strategy Flow**

    [x] Step 1.1: Create Strategy Fetching Utilities
        [x] Substep 1.1.1: Add async function to fetch strategy in `src/utils/mongo_utils_async.py`
        [x] Substep 1.1.2: Create strategy formatting helper for prompt injection
        [x] Substep 1.1.3: Add strategy caching key structure for Anthropic

    [x] Step 1.2: Extend Session State with Strategy
        [x] Substep 1.2.1: Modify session service to fetch user strategy on session creation
        [x] Substep 1.2.2: Add strategy to session context object
        [x] Substep 1.2.3: Update session schemas to include optional strategy reference

    [x] Test for Step 1.2: Unit test strategy fetching and session integration

[x] **Phase 2: Ideas Extraction with Funnel Classification**

    [x] Step 2.1: Update Ideas Extraction Schema
        [x] Substep 2.1.1: Modify idea schema to use funnel stage values for category
        [x] Substep 2.1.2: Add strategic_notes field to idea model
        [x] Substep 2.1.3: Update citations_ideas_extraction_models.py with funnel guidance

    [x] Step 2.2: Enhance Ideas Extraction Prompt
        [x] Substep 2.2.1: Modify ideas_extraction_prompt.py to include funnel classification
        [x] Substep 2.2.2: Add strategic context injection to extraction system prompt
        [x] Substep 2.2.3: Create funnel stage classification criteria

    [x] Step 2.3: Update Ideas Extractor Implementation
        [x] Substep 2.3.1: Pass strategy context to ideas extractor
        [x] Substep 2.3.2: Update tool schema to include funnel_stage in category field
        [x] Substep 2.3.3: Add fallback handling for missing strategy

    [x] Test for Step 2.3: Manual test - Process transcript and verify funnel classification

[x] **Phase 3: Interview Agent Strategy Awareness**

    [x] Step 3.1: Enhance Interview Agent Context
        [x] Substep 3.1.1: Fetch user strategy at interview session start
        [x] Substep 3.1.2: Add strategy context to interview agent system prompt
        [x] Substep 3.1.3: Create strategic probing questions based on funnel potential

    [x] Step 3.2: Update Interview Service
        [x] Substep 3.2.1: Modify interview_service.py to include strategy in agent initialization
        [x] Substep 3.2.2: Add strategy-aware follow-up question generation
        [x] Substep 3.2.3: Include subtle ICP relevance hints in agent behavior

    [x] Test for Step 3.2: Manual test - Start interview and verify strategic awareness

[x] **Phase 4: Content Agent Strategy Integration**

    [x] Step 4.1: Extend Scratchpad with Strategy
        [x] Substep 4.1.1: Add content_strategy field to scratchpad structure
        [x] Substep 4.1.2: Modify format_user_preferences to include all strategy fields
        [x] Substep 4.1.3: Create funnel-based application guidance

    [x] Step 4.2: Update Agent Prompts for Strategy
        [x] Substep 4.2.1: Enhance hook_prompt.txt with funnel awareness
        [x] Substep 4.2.2: Update body_prompt.txt with ICP language guidance
        [x] Substep 4.2.3: Modify ending_prompt.txt for goal-aligned CTAs

    [x] Step 4.3: Implement Funnel-Based Generation
        [x] Substep 4.3.1: Add funnel stage to agent explanation output
        [x] Substep 4.3.2: Create intensity mapping (top=0.2, mid=0.5, bottom=0.8)
        [x] Substep 4.3.3: Update SimplifiedAgentRunner to pass strategy context

    [x] Test for Step 4.3: Generate content and verify strategy application

[x] **Phase 5: Pattern Selection with Strategy Influence**

    [x] Step 5.1: Enhance Vector Search Integration
        [x] Substep 5.1.1: Modify brief_to_patterns.py to include strategy embedding
        [x] Substep 5.1.2: Add strategy+brief combined embedding for search
        [x] Substep 5.1.3: Implement funnel-aware pattern scoring boost

    [x] Step 5.2: Update Pattern Selection Logic
        [x] Substep 5.2.1: Add funnel stage consideration to pattern ranking
        [x] Substep 5.2.2: Create mapping of funnel stages to pattern types
        [x] Substep 5.2.3: Implement subtle weighting (not overriding style match)

    [x] Test for Step 5.2: Verify pattern selection considers strategy

[x] **Phase 6: Frontend Funnel Stage Display**

    [x] Step 6.1: Update IdeaCard Component
        [x] Substep 6.1.1: Change "Category" label to show funnel values
        [x] Substep 6.1.2: Add tooltip explaining funnel stages
        [x] Substep 6.1.3: Update edit mode to use funnel dropdown

    [x] Step 6.2: Enhance Agent Explanation Display
        [x] Substep 6.2.1: Parse funnel stage from agent explanations
        [x] Substep 6.2.2: Add funnel stage badge to explanation text
        [x] Substep 6.2.3: Include strategic reasoning in explanation

    [x] Test for Step 6.2: Manual UI test - Verify funnel display in ideas and agents

[ ] **Phase 7: End-to-End Testing and Refinement**

    [ ] Step 7.1: Integration Testing
        [ ] Substep 7.1.1: Test full flow with strategy present
        [ ] Substep 7.1.2: Test graceful handling without strategy
        [ ] Substep 7.1.3: Verify prompt caching efficiency

    [ ] Step 7.2: Prompt Engineering Refinement
        [ ] Substep 7.2.1: Fine-tune strategy application intensity
        [ ] Substep 7.2.2: Adjust funnel classification criteria
        [ ] Substep 7.2.3: Optimize for subtle, non-salesy application

    [ ] Test for Step 7.2: Complete user journey test with dogfooding

---

## Implementation Notes

### Funnel Stage Definitions
- **Top of Funnel**: Broad appeal, thought leadership, personal stories
- **Middle Funnel**: Industry expertise, problem exploration, methodology sharing  
- **Bottom Funnel**: Case studies, transformations, solution showcases

### Strategic Context Object Structure
```python
strategic_context = {
    "business_profile": {
        "industry": str,
        "offering": str,
        "target_audience": str,
        "differentiators": str,
        "content_goal": str
    },
    "funnel_guidance": {
        "top": "Build authentic connection",
        "middle": "Demonstrate domain expertise",
        "bottom": "Showcase transformations"
    }
}
```

### Prompt Engineering Principles
1. "Strategic awareness, not strategic slavery"
2. "Natural resonance over forced relevance"
3. "Let creator style lead, strategy guides"

---

## Version Control Strategy
- Commit after each completed Step with descriptive message
- Include this plan file (with [x] updates) in commits
- Use semantic commit messages: "feat(strategy): Add funnel classification to ideas"