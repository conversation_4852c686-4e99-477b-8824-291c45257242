# Citation-Based Content Generation Refactor Plan

## Overview
This plan refactors the LinkedInsight application to eliminate the brief generation step entirely, instead extracting citations during idea generation and passing them directly to the content agent.

Reason: We 'inherited' this code from another app that generated briefs for ghostwriters.
This app, however, is meant for the kind of people who would hire a ghostwriter; therefore,
they'll want the content to be generated DIRECTLY rather than futzing with a brief.

---

## [ ] Phase 1: Enhance Idea Extraction with Citations

### [x] Step 1.1: Update Idea Schema and Models
#### [x] Substep 1.1.1: Update backend idea models to include citations array
#### [x] Substep 1.1.2: Update frontend TypeScript idea interface to match
#### [x] Substep 1.1.3: Update API response schemas for idea endpoints
#### [x] Test for Step 1.1: Verify models compile without errors

### [x] Step 1.2: Modify Idea Extraction Prompts
#### [x] Substep 1.2.1: Create new system prompt for citation-aware idea extraction
#### [x] Substep 1.2.2: Update user prompt to request citations per idea
#### [x] Substep 1.2.3: Add citation count requirements (minimum 5-10 per idea)
#### [ ] Test for Step 1.2: Manual prompt testing with sample transcript

### [x] Step 1.3: Update Idea Extraction Implementation
#### [x] Substep 1.3.1: Modify ideas extractor to use citations-enabled API call
#### [x] Substep 1.3.2: Update response parsing to handle citations per idea
#### [x] Substep 1.3.3: Enrich summary field generation for more context
#### [x] Test for Step 1.3: Unit test idea extraction with mock transcript

### [x] Step 1.4: Update Database Storage
#### [x] Substep 1.4.1: Modify MongoDB idea storage to include citations
#### [x] Substep 1.4.2: Update idea retrieval queries to include citations
#### [x] Substep 1.4.3: Add indexes for citation search if needed
#### [x] Test for Step 1.4: Integration test for idea storage/retrieval

---

## [ ] Phase 2: Remove Brief Generation Infrastructure

### [x] Step 2.1: Remove Backend Brief Components
#### [x] Substep 2.1.1: Delete brief generation router endpoints
#### [x] Substep 2.1.2: Remove brief generator service and classes
#### [x] Substep 2.1.3: Delete brief-related prompts and templates
#### [x] Test for Step 2.1: Verify API starts without brief endpoints

### [x] Step 2.2: Remove Frontend Brief Components
#### [x] Substep 2.2.1: Delete BriefViewer component
#### [x] Substep 2.2.2: Remove StreamingBrief component
#### [x] Substep 2.2.3: Delete BriefService and related utilities
#### [x] Test for Step 2.2: Verify frontend builds without errors

### [x] Step 2.3: Clean Navigation Flow
#### [x] Substep 2.3.1: Remove brief routes from router configuration
#### [x] Substep 2.3.2: Update idea card to skip brief and go to content
#### [x] Substep 2.3.3: Remove brief-related navigation utilities
#### [x] Test for Step 2.3: Manual test - clicking idea goes directly to content

---

## [x] Phase 3: Update Content Generation

### [x] Step 3.1: Modify Content Agent Interface
#### [x] Substep 3.1.1: Update content generation request to accept idea with citations
#### [x] Substep 3.1.2: Remove brief parameter from content agent
#### [x] Substep 3.1.3: Update API endpoint to handle new structure
#### [x] Test for Step 3.1: API endpoint test with new payload structure

### [x] Step 3.2: Update Pattern Selection for Embeddings
#### [x] Substep 3.2.1: Modify vector pattern selector to use idea summary instead of brief
#### [x] Substep 3.2.2: Test embedding idea summary vs summary + citations
#### [x] Substep 3.2.3: Update pattern selection context to pass idea data
#### [x] Test for Step 3.2: Compare pattern selection quality with different embedding strategies

### [x] Step 3.3: Update Content Generation Prompts
#### [x] Substep 3.3.1: Reviewed prompts - they work agnostically with any input type
#### [x] Substep 3.3.2: No changes needed - "brief" in prompts is just a generic label for user input
#### [x] Substep 3.3.3: System already passes formatted text that includes citations
#### [ ] Test for Step 3.3: Need to test content generation with idea + citations format

### [x] Step 3.4: Update Frontend Content Flow
#### [x] Substep 3.4.1: ContentAgentPage already receives idea with citations and formats them
#### [x] Substep 3.4.2: Content generation passes all citations as part of formatted text
#### [x] Substep 3.4.3: Brief references remain but as generic input field (supports both flows)
#### [ ] Test for Step 3.4: Manual test - generate content from idea with citations

---

## [ ] Phase 4: Enhance User Experience
#### [ ] Substep 4.2.1: Update loading states for direct content generation
#### [ ] Substep 4.2.2: Adjust progress indicators to skip brief step
#### [ ] Substep 4.2.3: Update success/error messaging
#### [ ] Test for Step 4.2: Manual test - full generation flow

---

## [ ] Phase 5: Cleanup and Optimization

### [x] Step 5.1: Remove Unused Dependencies
#### [x] Substep 5.1.1: Remove brief-related imports throughout codebase
#### [x] Substep 5.1.2: Update package dependencies if any are brief-specific
#### [x] Substep 5.1.3: Clean up unused types and interfaces
#### [x] Test for Step 5.1: Build and lint checks pass

### [ ] Step 5.2: Update Documentation
#### [ ] Substep 5.2.1: Update API documentation to reflect new flow
#### [ ] Substep 5.2.2: Modify user-facing documentation/guides
#### [ ] Substep 5.2.3: Update code comments to reflect new architecture
#### [ ] Test for Step 5.2: Documentation review

### [ ] Step 5.3: Performance Optimization
#### [ ] Substep 5.3.1: Optimize citation extraction for large transcripts
#### [ ] Substep 5.3.2: Add caching for idea + citations if beneficial
#### [ ] Substep 5.3.3: Monitor and adjust token usage
#### [ ] Test for Step 5.3: Performance benchmarks

---

## Completion Checklist
- [x] All ideas now include comprehensive citations
- [x] Brief generation completely removed from codebase
- [x] Content generation works directly with idea + citations
- [ ] User flow simplified: Transcript → Ideas → Content (testing needed)
- [ ] All tests passing and system stable