# Vector Pipeline Implementation Plan

## Overview
This plan details the systematic refactoring of the LinkedInsight content generation pipeline to replace creator-matching with vector search while maintaining the 3-step generation process.

---

[x] **Phase 1: Decouple Creator Dependencies**
    
    [x] Step 1.1: Analyze Current Creator Dependencies
        [x] Substep 1.1.1: Document all creator_analyses references in agent_service.py
        [x] Substep 1.1.2: Map scratchpad fields that depend on creator data
        [x] Substep 1.1.3: Identify pattern selection's dependency on creator analyses
    
    [x] Step 1.2: Modify Agent Service
        [x] Substep 1.2.1: Comment out load_creator_analyses() method
        [x] Substep 1.2.2: Remove creator_analyses from generate_content() parameters
        [x] Substep 1.2.3: Update scratchpad initialization to exclude creator_style_guides
        [x] Substep 1.2.4: Update pattern selection calls to pass None for creator_analyses
    [x] Test for Step 1.2: Unit test agent_service.generate_content() without creator data
    
    [x] Step 1.3: Update Pattern Filter for Direct Vector Integration
        [x] Substep 1.3.1: Import vector_pattern_selector in pattern_filter.py
        [x] Substep 1.3.2: Replace pattern_selector calls with vector_pattern_selector
        [x] Substep 1.3.3: Ensure scratchpad updates work without creator data
    [x] Test for Step 1.3: Integration test of complete 3-step generation with vector patterns

---

[ ] **Phase 2: Vector Search Integration**
    
    [x] Step 2.1: Remove Theme Dependencies
        [x] Substep 2.1.1: Remove theme extraction from brief processing
        [x] Substep 2.1.2: Remove theme parameter from pattern selection calls
        [x] Substep 2.1.3: Remove theme_compatibility field from pattern queries
        [x] Substep 2.1.4: Update scratchpad to remove theme references
    [x] Test for Step 2.1: Verify pipeline works without theme parameter
    
    [x] Step 2.2: Remove Parallel Pattern Selection (Simplified approach)
        [x] Substep 2.2.1: Remove parallel_pattern_selector.py
        [x] Substep 2.2.2: Update pattern_filter.py for on-demand selection
        [x] Substep 2.2.3: Remove precomputed patterns from scratchpad
    
    [x] Step 2.3: Implement Context Building (Already implemented)
        [x] Substep 2.3.1: Create build_hook_context(brief) method
        [x] Substep 2.3.2: Create build_body_context(brief, hook) method
        [x] Substep 2.3.3: Create build_ending_context(brief, hook, body) method
    [x] Test for Step 2.3: Unit tests for each context builder method
    
    [x] Step 2.4: Update Pattern Filter for Vector Search (Already implemented)
        [x] Substep 2.4.1: Update filter_patterns_for_step() to use vector search
        [x] Substep 2.4.2: Remove creator_analyses parameter from method signatures
    [x] Test for Step 2.4: Unit test pattern filtering with vector search
    
    [x] Step 2.5: Update Parallel Pattern Selector (No longer needed - removed entirely)
        [x] Substep 2.5.1: Removed parallel_pattern_selector.py
        [x] Substep 2.5.2: Pattern selection happens on-demand
        [x] Substep 2.5.3: Simplified pipeline architecture
    [x] Test for Step 2.5: Integration test confirms removal works
    
    [x] Step 2.6: End-to-End Vector Integration
        [x] Substep 2.6.1: Test complete generation flow with vector search
        [x] Substep 2.6.2: Verify scratchpad context flows correctly
    [x] Test for Step 2.6: Full integration test generating content with vector patterns

---

[x] **Phase 3: Cleanup and Optimization**
    
    [x] Step 3.1: Remove Deprecated Code
        [x] Substep 3.1.1: Delete pattern_selector.py file
        [x] Substep 3.1.2: Remove load_creator_analyses() method completely
        [x] Substep 3.1.3: Clean up unused imports and variables
    
    [x] Step 3.2: Update API Interface
        [x] Substep 3.2.1: Mark creator_name as deprecated in API schemas
        [x] Substep 3.2.2: Update API documentation with deprecation notices
        [x] Substep 3.2.3: Update content generation schemas for draft-based structure
    [x] Test for Step 3.2: API maintains backward compatibility with deprecated parameters
    
    [x] Step 3.3: Add Monitoring and Logging
        [x] Substep 3.3.1: Add timing metrics for vector search calls
        [x] Substep 3.3.2: Log pattern selection results for debugging
        [x] Substep 3.3.3: Add error handling for vector search failures
    [x] Test for Step 3.3: Performance logs show detailed timing breakdown
    
    [x] Step 3.4: Performance Optimization
        [x] Substep 3.4.1: Analyze and optimize vector search parameters
        [x] Substep 3.4.2: Add similarity threshold filtering for quality
        [x] Substep 3.4.3: Tune numCandidates and other MongoDB vector search settings
    [x] Test for Step 3.4: Vector search performance improved with quality filtering

---

[ ] **Phase 4: Multi-Draft Generation with Pattern Diversity**
    
    [ ] Step 4.1: Refactor Session Schema for Draft-Based Generation
        [ ] Substep 4.1.1: Replace `creators` field with `draft_count` in session schemas
        [ ] Substep 4.1.2: Change `generated_posts` from `{creator: post}` to `{draft_id: post}`
        [ ] Substep 4.1.3: Update frontend interfaces to use draft-based structure
        [ ] Substep 4.1.4: Remove creator references from navigation and session creation
    [ ] Test for Step 4.1: Session creation and retrieval works with draft structure
    
    [ ] Step 4.2: Implement Pattern Diversity Selection
        [ ] Substep 4.2.1: Create vector search method that returns top N patterns per step
        [ ] Substep 4.2.2: Implement pattern distribution algorithm (split into sets)
        [ ] Substep 4.2.3: Add pattern set characteristics (analytical, narrative, etc.)
        [ ] Substep 4.2.4: Ensure pattern sets have minimal overlap for diversity
    [ ] Test for Step 4.2: Vector search returns diverse pattern sets for multiple drafts
    
    [ ] Step 4.3: Parallel Draft Generation
        [ ] Substep 4.3.1: Modify agent service to accept pattern sets instead of creator names
        [ ] Substep 4.3.2: Implement parallel generation with different pattern sets
        [ ] Substep 4.3.3: Update scratchpad to track which patterns are used per draft
        [ ] Substep 4.3.4: Ensure draft generation is truly independent
    [ ] Test for Step 4.3: Two distinct drafts generated with different pattern characteristics
    
    [ ] Step 4.4: Frontend Multi-Draft Interface
        [ ] Substep 4.4.1: Update ContentAgentPage to display "Draft A" and "Draft B"
        [ ] Substep 4.4.2: Add pattern characteristics display (e.g., "Analytical", "Storytelling")
        [ ] Substep 4.4.3: Remove all creator selection and matching UI
        [ ] Substep 4.4.4: Update navigation flow to skip creator matching entirely
    [ ] Test for Step 4.4: Frontend displays two drafts with pattern characteristics
    
    [ ] Step 4.5: Draft Quality and Diversity Validation
        [ ] Substep 4.5.1: Implement metrics to measure draft diversity
        [ ] Substep 4.5.2: Add pattern usage tracking and reporting
        [ ] Substep 4.5.3: Create validation tests for draft quality
        [ ] Substep 4.5.4: Ensure drafts use semantically different approaches
    [ ] Test for Step 4.5: Generated drafts show measurable diversity and quality

---

## Verification Criteria

### Phase 1 Success Criteria:
- Pipeline generates content without any creator data
- All 3 steps (hook, body, ending) execute successfully
- Scratchpad maintains context between steps

### Phase 2 Success Criteria:
- Vector search returns relevant patterns for each step
- Context builds correctly (brief → hook → body → ending)
- Generation quality is maintained or improved

### Phase 3 Success Criteria:
- No deprecated code remains
- API is cleaner and simpler
- Performance metrics show improvement
- Error handling is robust

## Next Steps
1. Review and approve this plan
2. Begin with Phase 1, Step 1.1, Substep 1.1.1
3. Progress systematically through each substep with verification