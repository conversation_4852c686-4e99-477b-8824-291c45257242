# Instrumentation and Monitoring Guide

This document provides information about the instrumentation and monitoring setup in the LinkedInsight project.

## Logfire Integration

The project uses [Logfire](https://logfire.dev/) for instrumentation and monitoring of API calls, particularly for the Anthropic API.

### Configuration

Logfire is configured in `src/utils/anthropic_client.py`:

```python
# Configure Logfire for Anthropic instrumentation
logfire.configure()
logfire.instrument_anthropic()
logger.info("Logfire instrumentation configured for Anthropic API")
```

### Known Issues and Patches

#### ThinkingBlock Compatibility Issue (May 2025)

**Issue**: When using the `thinking` parameter with the Anthropic API, Logfire's instrumentation code attempts to access a `name` attribute on `ThinkingBlock` objects, which don't have this attribute. This causes an error:

```
AttributeError: 'ThinkingBlock' object has no attribute 'name'
```

**Patch**: A patch was applied to the Logfire Anthropic integration to handle ThinkingBlock objects correctly. The patch modifies the `on_response` function to only include blocks that have a `name` attribute in the `tool_calls` list:

```python
# Only include blocks that have a 'name' attribute (skip ThinkingBlock)
message['tool_calls'] = [
    {
        'function': {
            'arguments': block.model_dump_json(include={'input'}),
            'name': block.name,  # type: ignore
        }
    }
    for block in response.content
    if hasattr(block, 'name')
]
```

**Patch Script**: The patch is applied using the script `scripts/patch_logfire_anthropic.py`. This script:
1. Creates a backup of the original file
2. Applies the patch to handle ThinkingBlock objects
3. Logs the changes made

**Restoring the Original File**: If needed, you can restore the original file from the backup created by the patch script:

```bash
cp /path/to/anthropic.py.bak /path/to/anthropic.py
```

## Monitoring Dashboard

Logfire provides a dashboard for monitoring API calls and performance metrics. You can access the dashboard at:

[https://logfire-us.pydantic.dev/benawise/linkedin-strategy-agent](https://logfire-us.pydantic.dev/benawise/linkedin-strategy-agent)

## Best Practices

1. **Keep Instrumentation Enabled**: Instrumentation provides valuable insights into API usage and performance. Keep it enabled unless there's a specific reason to disable it.

2. **Monitor API Usage**: Regularly check the Logfire dashboard to monitor API usage and identify any issues.

3. **Update Patches**: If you update the Logfire library, check if the patches are still needed and update them if necessary.
