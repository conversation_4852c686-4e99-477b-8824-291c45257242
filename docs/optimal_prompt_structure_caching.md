# Optimal Prompt Structure for 3-Step Linguistic Cohesion with Caching

## 🎯 Architecture Overview

This document defines the optimal prompt structure to achieve linguistic cohesion across hook, body, and ending generation while maximizing Anthropic prompt caching efficiency.

## 📊 Cache Strategy Summary

- **Cache Hit Rate**: 66% (2 out of 3 API calls use cached content)
- **Minimum Cached Content**: 1024+ tokens (meets Anthropic requirements)
- **Cache Lifetime**: 5 minutes (covers entire 3-step workflow)
- **Cost Efficiency**: Cache hits cost 10% vs 25% premium for writes

## 🏗️ Prompt Structure Design

### CACHED PREFIX (Identical across all steps)

```
SYSTEM INSTRUCTIONS:
You are an expert LinkedIn content creator specializing in creating engaging, 
authentic posts that resonate with professional audiences. You use advanced 
pattern matching to ensure stylistic consistency and engagement optimization.

USER BRIEF:
{user_input}

SESSION LINGUISTIC PATTERN:
{session_linguistic_pattern}

LINGUISTIC APPLICATION GUIDELINES:
- Apply this linguistic pattern consistently throughout your content
- Maintain the creator's voice, tone, and stylistic nuances
- Use the vocabulary, sentence structures, and rhetorical devices provided
- Ensure seamless integration without forcing patterns

[CACHE_CONTROL_BREAKPOINT]
```

### VARYING SUFFIX (Step-specific content)

#### Hook Step Suffix:
```
CURRENT TASK: Hook Generation
You are crafting a compelling 1-3 line opening hook.

CURRENT SCRATCHPAD:
{scratchpad}

AVAILABLE HOOK PATTERNS:
{hook_structural_patterns}

[Hook-specific instructions...]
```

#### Body Step Suffix:
```
CURRENT TASK: Body Generation  
You are crafting the main body content using the generated hook.

CURRENT SCRATCHPAD:
{scratchpad}

AVAILABLE BODY FRAMEWORKS:
{body_structural_patterns}

[Body-specific instructions...]
```

#### Ending Step Suffix:
```
CURRENT TASK: Ending Generation
You are crafting the conclusion and CTA using the hook and body.

CURRENT SCRATCHPAD:
{scratchpad}

AVAILABLE ENDING PATTERNS:
{ending_structural_patterns}

[Ending-specific instructions...]
```

## 🔄 Implementation Flow

### Session Initialization:
1. **Select Session Linguistic Pattern**: Vector search on user brief → 1 best linguistic bundle
2. **Store in Session**: Cache linguistic pattern for reuse across all steps
3. **Build Cached Prefix**: Combine system prompt + brief + linguistic pattern

### Step Execution:
1. **Step 1 (Hook)**: Cached prefix + hook suffix → **CACHE WRITE** (25% premium)
2. **Step 2 (Body)**: Cached prefix + body suffix → **CACHE HIT** (10% cost)
3. **Step 3 (Ending)**: Cached prefix + ending suffix → **CACHE HIT** (10% cost)

## 🎯 Key Benefits

### Linguistic Cohesion:
- **Unified Voice**: Same linguistic pattern applied to all content
- **Consistent Style**: Vocabulary, tone, and structure remain coherent
- **Creator Authenticity**: Maintains authentic voice across entire post

### Performance Optimization:
- **Cost Reduction**: 66% of calls use cheap cache hits
- **Speed Improvement**: Cached content processes faster
- **Resource Efficiency**: Reduces token processing overhead

### Architectural Advantages:
- **Scalable**: Works with any linguistic pattern selection
- **Maintainable**: Clear separation of cached vs dynamic content
- **Flexible**: Easy to modify step-specific logic without affecting cache

## 📋 Implementation Requirements

### Code Changes Needed:
1. **Session-level linguistic pattern selection** in `VectorPatternSelector`
2. **Prompt template restructuring** for cache optimization
3. **Cache control integration** in API calls
4. **Agent service updates** to store session linguistic patterns (NOT in scratchpad to preserve cache hits)

### Validation Points:
- [ ] Cached prefix exceeds 1024 token minimum
- [ ] Identical prefix generates cache hits for steps 2 & 3
- [ ] Linguistic pattern maintains quality across all steps
- [ ] Total prompt stays within model context limits

## 🚨 Critical Success Factors

1. **Prefix Consistency**: Any variation in cached prefix breaks cache hits
2. **Token Threshold**: Must exceed 1024 tokens for caching to activate
3. **Timing Window**: All 3 steps must complete within 5-minute cache lifetime
4. **Quality Preservation**: Linguistic pattern must enhance, not overwhelm content

---

*This structure provides the foundation for implementing unified linguistic cohesion while optimizing for Anthropic's prompt caching capabilities.*