# Guide: Implementing Anthropic Prompt Caching

This guide explains how to use <PERSON><PERSON><PERSON>'s prompt caching, similar to the implementation in the `transcript-processor-cursor` project. Caching is particularly useful when you make multiple related API calls using the same large text document (like a transcript or a lengthy brief) as context. It can significantly reduce token usage and latency for subsequent calls after the initial one.

## Core Concept: Ephemeral Caching

Anthropic's caching works by allowing you to mark specific parts of your input, typically large documents or system prompts, as cacheable. When you make the *first* API call with a cacheable element, <PERSON> processes and stores it in a short-term cache.

On *subsequent* API calls within a short time window (currently documented by <PERSON><PERSON><PERSON> as ~1-2 hours, but subject to change), if you send the *exact same cacheable content* again, <PERSON> can reuse the processed version from the cache instead of reprocessing it. This reuse is reflected in the token usage statistics returned in the API response (`cache_read_input_tokens`).

The key is the `cache_control` parameter.

## Method 1: Using the Standard `anthropic` Python Library

This is the fundamental way to use caching, as demonstrated in `backend/tests/test_prompt_caching.py`. You structure your API call parameters to include the cacheable content within the `system` prompt list.

**Steps:**

1.  **Identify Cacheable Content:** Determine the large text block (e.g., transcript, document) that will be reused across multiple calls.
2.  **First API Call (Populate Cache):**
    *   Place the cacheable content as a separate dictionary within the `system` list.
    *   Add the `cache_control: {"type": "ephemeral"}` field to this dictionary.
    *   Include your standard system instructions (if any) as other dictionaries in the `system` list.
    *   Place the user's specific query for this call in the `messages` list.
3.  **Subsequent API Calls (Use Cache):**
    *   Send the *exact same* `system` list as in the first call, including the cacheable content block with `cache_control`.
    *   Place the *new* user query in the `messages` list.

**Example:**

```python
import os
from anthropic import Anthropic
from dotenv import load_dotenv

load_dotenv() # Make sure ANTHROPIC_API_KEY is set in your .env or environment

client = Anthropic() # API key read from environment automatically

# Your large, reusable document
large_document = "This is a very long document..." * 1000 # Replace with your actual content

# --- First Call (Cache Population) ---
print("\n--- First Call ---")
first_user_prompt = "Summarize this document."

response1 = client.messages.create(
    model="claude-sonnet-4-20250514", # Or your preferred model
    max_tokens=100,
    system=[
        {
            "type": "text",
            "text": "You are a helpful assistant." # Standard system prompt
        },
        {
            "type": "text",
            "text": large_document, # The large document to cache
            "cache_control": {"type": "ephemeral"} # Enable caching for this block
        }
    ],
    messages=[
        {"role": "user", "content": first_user_prompt}
    ]
)

print("Response 1 Content:", response1.content[0].text[:100] + "...")
print("Response 1 Usage:", response1.usage)

# --- Second Call (Cache Reuse) ---
print("\n--- Second Call ---")
second_user_prompt = "What are the key themes in the document?"

response2 = client.messages.create(
    model="claude-sonnet-4-20250514", # Use the same model
    max_tokens=100,
    # *** Send the exact same system prompt structure as the first call ***
    system=[
        {
            "type": "text",
            "text": "You are a helpful assistant."
        },
        {
            "type": "text",
            "text": large_document,
            "cache_control": {"type": "ephemeral"}
        }
    ],
    messages=[
        # *** Only the user message changes ***
        {"role": "user", "content": second_user_prompt}
    ]
)

print("Response 2 Content:", response2.content[0].text[:100] + "...")
print("Response 2 Usage:", response2.usage) # Note the cache_read_input_tokens

```

**Key Observation:** In the `response2.usage` stats, you should see a non-zero value for `cache_read_input_tokens`, indicating the cache was successfully used, and likely a lower `input_tokens` count compared to the first call (or a hypothetical second call without caching).

## Method 2: Using the Project's `CitationsAnthropic` Wrapper (If Applicable)

The `transcript-processor-cursor` project uses a wrapper class `CitationsAnthropic` (in `backend/infrastructure/llm/citations_claude.py`) that abstracts some of this structure, especially when dealing with transcripts or briefs used for citation generation.

If you were to adapt *that specific wrapper* to your project, using caching would look slightly different. The wrapper's methods (`create_transcript_tool_call`, `create_content_tool_call`) take the large text (`transcript` or `brief`) as a direct argument. Internally, these methods construct the necessary API call structure, placing the large text into a `document` type message block within the `messages` list (instead of the `system` list) and automatically adding the `cache_control`.

**Conceptual Example (Based on the wrapper's logic):**

```python
# Assuming you have adapted or recreated a similar CitationsAnthropic class
# from your_adapted_module import CitationsAnthropic

# client = CitationsAnthropic(...) # Initialize your wrapper

large_document = "This is a very long document..." * 1000
system_prompt_text = "You are a helpful assistant using citations."
tool_to_use = "your_analysis_tool" # Name of the tool Claude should use

# --- First Call (via wrapper) ---
print("\n--- First Call (Wrapper) ---")
first_user_prompt = "Analyze this document using the tool."

# The wrapper likely handles placing the transcript and cache_control internally
response1 = await client.create_transcript_tool_call( # Or similar method
    transcript=large_document,
    model="claude-sonnet-4-20250514",
    system_prompt=system_prompt_text,
    user_prompt=first_user_prompt,
    tool_name=tool_to_use,
    max_tokens=100
)

print("Response 1:", response1) # Wrapper might return just the tool output
# Need to inspect how the wrapper exposes usage stats, if at all.

# --- Second Call (via wrapper) ---
print("\n--- Second Call (Wrapper) ---")
second_user_prompt = "Extract key entities from the document using the tool."

response2 = await client.create_transcript_tool_call( # Call the same method again
    transcript=large_document, # Pass the same large document
    model="claude-sonnet-4-20250514",
    system_prompt=system_prompt_text, # Pass the same system prompt
    user_prompt=second_user_prompt, # Pass the NEW user prompt
    tool_name=tool_to_use,
    max_tokens=100
)

print("Response 2:", response2)
# Again, check how the wrapper provides usage stats.
```

**Note:** If you are implementing caching in a *new* project, using **Method 1 (Standard Library)** is generally recommended unless you have a specific reason to build a complex wrapper like `CitationsAnthropic`. The standard library approach is more direct and less prone to abstraction complexities.

## Important Considerations

*   **Exact Match:** The cacheable content (and the `system` prompt structure if using Method 1) must be *exactly identical* between the cache-populating call and the cache-reading calls. Any change will likely invalidate the cache.
*   **Model:** You must use the same model for all calls in a cached sequence.
*   **Ephemeral:** The cache is short-lived. Don't rely on it persisting for long periods. It's best suited for sequences of related calls made close together in time.
*   **Usage Stats:** Always check the `usage` object in the response (`response.usage`) to confirm if the cache was created (`cache_creation_input_tokens`) or read (`cache_read_input_tokens`). 