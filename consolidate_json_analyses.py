import os
import glob
import shutil
from pathlib import Path

# --- Configuration ---
SOURCE_JSON_BASE_DIR = "/Users/<USER>/LinkedInsight/outputs/LLM_friendly_JSONs/"
DESTINATION_ANALYSES_BASE_DIR = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/"

# Using singular as per our findings for filenames
JSON_ANALYSIS_FILENAME_GLOBS = [
    "*_themes_analysis_json_*.json",
    "*_hook_analysis_json_*.json", 
    "*_linguistic_analysis_json_*.json",
    "*_ending_analysis_json_*.json"
]

def main():
    print(f"Starting consolidation of JSON analysis files...")
    print(f"Source base directory: {SOURCE_JSON_BASE_DIR}")
    print(f"Destination base directory: {DESTINATION_ANALYSES_BASE_DIR}")

    if not os.path.isdir(SOURCE_JSON_BASE_DIR):
        print(f"Error: Source directory '{SOURCE_JSON_BASE_DIR}' does not exist. Exiting.")
        return

    # Ensure the root destination directory exists (it should, for body files)
    os.makedirs(DESTINATION_ANALYSES_BASE_DIR, exist_ok=True)

    copied_files_count = 0
    creators_processed_count = 0

    source_creator_dirs = [d for d in os.listdir(SOURCE_JSON_BASE_DIR) if os.path.isdir(os.path.join(SOURCE_JSON_BASE_DIR, d))]

    if not source_creator_dirs:
        print(f"No creator subdirectories found in {SOURCE_JSON_BASE_DIR}.")
    
    for creator_name in source_creator_dirs:
        creators_processed_count += 1
        source_creator_path = os.path.join(SOURCE_JSON_BASE_DIR, creator_name)
        destination_creator_path = os.path.join(DESTINATION_ANALYSES_BASE_DIR, creator_name)

        # Ensure the specific creator's destination directory exists
        os.makedirs(destination_creator_path, exist_ok=True)
        
        print(f"\nProcessing creator: {creator_name}")
        print(f"  Source: {source_creator_path}")
        print(f"  Destination: {destination_creator_path}")

        for file_glob_pattern in JSON_ANALYSIS_FILENAME_GLOBS:
            # Construct the full glob pattern for the source creator directory
            # The glob pattern already includes creator name placeholder if it was meant to be generic, 
            # but here we explicitly target files matching analysis types within the creator's folder.
            # Example: /path/to/source/creatorName/*_themes_analysis_json_*.json
            
            # We should glob for files within the source_creator_path matching the pattern
            # The patterns in JSON_ANALYSIS_FILENAME_GLOBS are generic (e.g. '*_themes_...').
            # We need to find files that match this pattern *within* the source_creator_path.
            
            search_pattern = os.path.join(source_creator_path, file_glob_pattern)
            
            found_files_for_type = False
            for source_filepath in glob.glob(search_pattern):
                found_files_for_type = True
                filename = os.path.basename(source_filepath)
                destination_filepath = os.path.join(destination_creator_path, filename)
                
                try:
                    # shutil.copy2 preserves metadata and overwrites if destination exists
                    shutil.copy2(source_filepath, destination_filepath)
                    print(f"    Copied: {filename} to {destination_filepath}")
                    copied_files_count += 1
                except Exception as e:
                    print(f"    Error copying {filename}: {e}")
            
            # if not found_files_for_type:
            #     # This means for a given analysis type glob, no files were found. This is normal.
            #     # print(f"    No files found matching pattern: {file_glob_pattern} for {creator_name}")

    print(f"\nConsolidation finished.")
    print(f"Processed {creators_processed_count} creator directories from source.")
    print(f"Copied {copied_files_count} JSON files in total.")

if __name__ == "__main__":
    main() 