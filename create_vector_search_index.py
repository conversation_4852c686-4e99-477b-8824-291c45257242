#!/usr/bin/env python3
"""
Create MongoDB Atlas Vector Search Index programmatically.
"""

import os
import time
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_vector_search_index():
    """Create vector search index on pattern_embeddings collection."""
    
    # Connect to MongoDB
    client = MongoClient(os.getenv("MONGODB_URI"))
    db = client["linkedinsight"]
    collection = db["pattern_embeddings"]
    
    print("Creating vector search index on pattern_embeddings collection...")
    
    # Define the vector search index
    index_definition = {
        "name": "pattern_embeddings_vector_index",
        "definition": {
            "mappings": {
                "dynamic": True,
                "fields": {
                    "embedding": {
                        "type": "knnVector",
                        "dimensions": 1024,  # Voyage AI voyage-3.5 dimensions
                        "similarity": "cosine"
                    }
                }
            }
        }
    }
    
    try:
        # Check if index already exists
        existing_indexes = list(collection.list_search_indexes())
        index_names = [idx.get("name") for idx in existing_indexes]
        
        if "pattern_embeddings_vector_index" in index_names:
            print("Index 'pattern_embeddings_vector_index' already exists!")
            for idx in existing_indexes:
                if idx.get("name") == "pattern_embeddings_vector_index":
                    print(f"Status: {idx.get('status', 'Unknown')}")
            return
        
        # Create the index
        result = collection.create_search_index(index_definition)
        print(f"Index creation initiated: {result}")
        
        # Monitor index creation
        print("\nWaiting for index to be ready...")
        attempts = 0
        max_attempts = 60  # 5 minutes max
        
        while attempts < max_attempts:
            indexes = list(collection.list_search_indexes())
            
            for idx in indexes:
                if idx.get("name") == "pattern_embeddings_vector_index":
                    status = idx.get("status", "Unknown")
                    print(f"Status: {status}")
                    
                    if status == "READY":
                        print("\n✅ Vector search index is ready!")
                        print(f"Index name: {idx.get('name')}")
                        print(f"Index type: Vector Search")
                        return
                    elif status == "FAILED":
                        print(f"\n❌ Index creation failed: {idx.get('error', 'Unknown error')}")
                        return
            
            time.sleep(5)
            attempts += 1
            
            if attempts % 6 == 0:  # Every 30 seconds
                print(f"Still waiting... ({attempts * 5} seconds elapsed)")
        
        print("\n⚠️ Timeout waiting for index creation. Check Atlas UI for status.")
        
    except Exception as e:
        print(f"\n❌ Error creating index: {e}")
        print("\nNote: Index creation requires MongoDB Atlas with:")
        print("- MongoDB version 6.0.11, 7.0.2, or later")
        print("- Project Data Access Admin or higher permissions")
        print("- Atlas Search enabled on your cluster")
    
    finally:
        client.close()

def check_collection_status():
    """Check if collection has data before creating index."""
    client = MongoClient(os.getenv("MONGODB_URI"))
    db = client["linkedinsight"]
    collection = db["pattern_embeddings"]
    
    count = collection.count_documents({})
    with_embeddings = collection.count_documents({"embedding": {"$exists": True}})
    
    print(f"\nCollection status:")
    print(f"  Total documents: {count}")
    print(f"  With embeddings: {with_embeddings}")
    
    client.close()
    
    return count > 0 and with_embeddings > 0

if __name__ == "__main__":
    print("MongoDB Atlas Vector Search Index Creator")
    print("=" * 50)
    
    # Check if collection has data
    if check_collection_status():
        create_vector_search_index()
    else:
        print("\n⚠️ Collection is empty or has no embeddings yet.")
        print("Run the embedding script first before creating the index.")