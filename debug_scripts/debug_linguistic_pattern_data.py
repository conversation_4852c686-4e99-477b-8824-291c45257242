#!/usr/bin/env python3
"""
Debug script to examine the actual data structure of linguistic patterns.
"""

import asyncio
import logging
import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.mongo_utils import get_mongo_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def debug_linguistic_patterns():
    """Debug the actual structure of linguistic patterns in MongoDB."""
    
    try:
        db = get_mongo_db()
        collection = db["pattern_embeddings"]
        
        # Query for linguistic bundles
        logger.info("Querying for linguistic bundles...")
        
        linguistic_docs = list(collection.find(
            {"pattern_type": "linguistic_bundle"},
            {"_id": 0, "embedding": 0}  # Exclude embedding for readability
        ).limit(2))
        
        logger.info(f"Found {len(linguistic_docs)} linguistic bundle documents")
        
        for i, doc in enumerate(linguistic_docs):
            logger.info(f"\n=== Linguistic Pattern {i+1} ===")
            logger.info(f"Top-level keys: {list(doc.keys())}")
            
            # Print the full structure (excluding embedding)
            print(json.dumps(doc, indent=2, default=str))
            
            # Check specific fields that should be used for citation text
            if 'bundle_data' in doc:
                bundle_data = doc['bundle_data']
                logger.info(f"Bundle data keys: {list(bundle_data.keys()) if isinstance(bundle_data, dict) else 'bundle_data is not a dict'}")
                
                if isinstance(bundle_data, dict):
                    if 'bundle_name' in bundle_data:
                        logger.info(f"Bundle name: {bundle_data['bundle_name']}")
                    if 'content_purpose' in bundle_data:
                        logger.info(f"Content purpose: {bundle_data['content_purpose'][:100]}...")
                    if 'sentence_patterns' in bundle_data:
                        patterns = bundle_data['sentence_patterns']
                        logger.info(f"Has {len(patterns)} sentence patterns")
            
            logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"Failed to debug linguistic patterns: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(debug_linguistic_patterns())