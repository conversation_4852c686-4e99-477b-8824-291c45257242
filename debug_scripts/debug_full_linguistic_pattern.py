#!/usr/bin/env python3
"""
Debug script to see exactly what data the vector search returns for linguistic patterns.
"""

import asyncio
import logging
import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.vector_pattern_selector import VectorPatternSelector

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def debug_vector_search():
    """Debug what the vector search actually returns."""
    
    try:
        selector = VectorPatternSelector()
        
        # Create a test query embedding
        embedding_result = selector.voyage_client.embed(["test query for linguistic patterns"], model="voyage-3.5")
        query_embedding = embedding_result.embeddings[0]
        
        # Do direct vector search for linguistic bundles
        raw_results = selector._vector_search(
            query_embedding=query_embedding,
            analysis_type="linguistic_bundle",
            limit=2
        )
        
        logger.info(f"Raw vector search returned {len(raw_results)} results")
        
        for i, result in enumerate(raw_results):
            logger.info(f"\n=== Raw Result {i+1} ===")
            print(json.dumps(result, indent=2, default=str))
            
            # Test the citation text building
            citation_text = selector._build_citation_text(result)
            logger.info(f"\n=== Citation Text {i+1} ===")
            print(citation_text)
            logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"Failed to debug vector search: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(debug_vector_search())