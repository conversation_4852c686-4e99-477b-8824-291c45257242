#!/usr/bin/env python3
"""
Test the new bundle discovery analysis on retentionadam.
Follows the same robust error handling pattern as run_json_analysis.py.
"""

import asyncio
import json
import os
import sys
import sqlite3
import datetime
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from src.infrastructure.llm.citations_claude import CitationsAnthropic
from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig
from src.utils.json_utils import extract_json_from_text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database path
DB_PATH = "/Users/<USER>/LinkedInsight/data/db/linkedin_content.db"

def load_bundle_discovery_prompt() -> str:
    """Load the bundle discovery prompt."""
    prompt_path = "/Users/<USER>/LinkedInsight/prompts/LLM_JSON_analyses_prompts/LLM_JSON_bundle_discovery_prompt.md"
    with open(prompt_path, 'r', encoding='utf-8') as f:
        return f.read()

def load_system_prompt() -> str:
    """Load the system prompt."""
    system_prompt_path = "/Users/<USER>/LinkedInsight/prompts/LLM_JSON_analyses_prompts/LLM_JSON_system_prompt.txt"
    with open(system_prompt_path, 'r', encoding='utf-8') as f:
        return f.read()

def get_creator_posts(creator_name: str, limit: int = 50) -> List[Dict[str, Any]]:
    """Get posts for a creator from the database."""
    posts_for_api = []
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get creator ID
        cursor.execute("SELECT creator_id FROM creators WHERE creator_name = ?", (creator_name,))
        result = cursor.fetchone()
        if not result:
            logger.error(f"Creator '{creator_name}' not found.")
            return []
        creator_id = result[0]
        
        # Fetch posts ordered by engagement
        cursor.execute("""
            SELECT post_id, content, likes, comments, shares, date_posted
            FROM posts
            WHERE creator_id = ?
            ORDER BY (likes + comments + shares) DESC
            LIMIT ?
        """, (creator_id, limit))
        
        posts = cursor.fetchall()
        
        if not posts:
            logger.warning(f"No posts found for creator '{creator_name}'.")
            return []
        
        for post_id, content, likes, comments, shares, date_posted in posts:
            # Format the document with full content and engagement metrics
            document_content = f"POST ID: {post_id}\\n\\nFULL POST:\\n{content}\\n\\nENGAGEMENT METRICS:\\nLikes: {likes}\\nComments: {comments}\\nShares: {shares}"
            
            posts_for_api.append({
                "type": "document",
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": document_content
                }
            })
        
        logger.info(f"Loaded {len(posts)} posts for creator '{creator_name}'")
        return posts_for_api
        
    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        return []
    finally:
        if conn:
            conn.close()


async def run_bundle_discovery_analysis(creator_name: str = "retentionadam", num_posts: int = 50):
    """Run bundle discovery analysis on a creator."""
    
    logger.info(f"🔍 Starting bundle discovery analysis for {creator_name}")
    logger.info("=" * 60)
    
    # 1. Load prompts
    system_prompt = load_system_prompt()
    user_prompt = load_bundle_discovery_prompt()
    
    # 2. Get creator posts
    posts = get_creator_posts(creator_name, num_posts)
    if not posts:
        logger.error("No posts found, aborting analysis")
        return False, None, None
    
    # 3. Initialize Citations Claude client  
    from src.config import Config
    config = AnthropicConfig(
        api_key=Config.ANTHROPIC_API_KEY,
        beta_header=Config.CLAUDE_BETA_HEADER
    )
    llm_client = AnthropicClient(config)
    client = CitationsAnthropic(llm_client)
    
    # 4. Prepare the transcript from all posts
    transcript_parts = []
    for i, post in enumerate(posts, 1):
        post_data = post["source"]["data"]
        transcript_parts.append(f"=== POST {i} ===\\n{post_data}\\n")
    
    transcript = "\\n".join(transcript_parts)
    
    # 5. Prepare the user prompt with creator context
    full_prompt = f"Creator: {creator_name}\\n\\n{user_prompt}"
    
    # 6. Call the API with Citations and Interleaved Thinking
    logger.info(f"Calling Claude API with Citations + Interleaved Thinking for bundle discovery...")
    try:
        tool_response = await client.create_transcript_tool_call(
            transcript=transcript,
            model="claude-sonnet-4-20250514",
            system_prompt=system_prompt,
            user_prompt=full_prompt,
            tool_name="extract_linguistic_bundles",
            max_tokens=20000,
            thinking={
                "type": "enabled",
                "budget_tokens": 20000
            }
        )
        
        if not tool_response:
            logger.error("No tool response received from Citations Claude")
            return False, None, None
        
        # Extract the raw response text for saving
        response_text = str(tool_response)
        logger.info(f"Received Citations response ({len(response_text)} characters)")
        
    except Exception as e:
        logger.error(f"Citations API call failed: {e}")
        return False, None, None
    
    # 6. Create output directory
    output_base_dir = "LLM_friendly_Analyses"
    creator_output_dir = os.path.join(output_base_dir, creator_name)
    os.makedirs(creator_output_dir, exist_ok=True)
    
    # 7. Generate timestamp for filename
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    
    # 8. Save raw response FIRST (critical for debugging)
    raw_response_file = os.path.join(
        creator_output_dir,
        f"{creator_name}_bundle_discovery_analysis_{timestamp}_raw_response.txt"
    )
    
    with open(raw_response_file, 'w', encoding='utf-8') as f:
        f.write(response_text)
    
    logger.info(f"✅ Saved raw response to: {raw_response_file}")
    
    # 9. Extract JSON from Citations tool response
    if isinstance(tool_response, dict):
        # Check if the response has the nested JSON string issue
        if "bundles" in tool_response and isinstance(tool_response["bundles"], str):
            logger.info("Found nested JSON string in bundles key, parsing...")
            try:
                json_data = extract_json_from_text(tool_response["bundles"])
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse nested JSON: {e}")
                json_data = {}
        else:
            json_data = tool_response
    else:
        # Fallback to parsing response_text if tool_response isn't a dict
        json_data = extract_json_from_text(response_text)
    
    if not json_data:
        logger.error("❌ Failed to extract valid JSON from Citations response")
        logger.info(f"🔍 Examine raw response at: {raw_response_file}")
        return False, raw_response_file, None
    
    # 10. Save parsed JSON
    json_output_file = os.path.join(
        creator_output_dir,
        f"{creator_name}_bundle_discovery_analysis_{timestamp}.json"
    )
    
    with open(json_output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ Saved parsed JSON to: {json_output_file}")
    
    # 11. Quick analysis of results
    bundle_count = json_data.get("discovered_bundle_count", 0)
    total_posts = json_data.get("total_posts_analyzed", 0)
    
    logger.info(f"\\n📊 BUNDLE DISCOVERY RESULTS:")
    logger.info(f"  📝 Posts analyzed: {total_posts}")
    logger.info(f"  📦 Bundles discovered: {bundle_count}")
    
    if "bundles" in json_data:
        for i, bundle in enumerate(json_data["bundles"], 1):
            bundle_name = bundle.get("bundle_name", f"Bundle {i}")
            post_count = bundle.get("post_count_in_bundle", 0)
            logger.info(f"  📦 {bundle_name}: {post_count} posts")
    
    logger.info(f"\\n🎉 Bundle discovery analysis completed successfully!")
    
    return True, raw_response_file, json_output_file

if __name__ == "__main__":
    success, raw_file, json_file = asyncio.run(run_bundle_discovery_analysis())
    
    if success:
        print(f"\\n✅ SUCCESS: Bundle discovery completed")
        print(f"📁 JSON output: {json_file}")
        print(f"📁 Raw output: {raw_file}")
    else:
        print(f"\\n❌ FAILED: Check raw output for debugging")
        if raw_file:
            print(f"📁 Raw output: {raw_file}")