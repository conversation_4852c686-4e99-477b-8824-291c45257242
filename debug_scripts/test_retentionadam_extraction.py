#!/usr/bin/env python3
"""
Test linguistic bundle extraction for retentionadam only.
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from src.infrastructure.llm.citations_claude import CitationsAnthropic
from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import the bundle extraction prompt from the main script
from scripts.batch_extract_linguistic_bundles import BUNDLE_EXTRACTION_PROMPT, load_linguistic_analysis, extract_bundles_with_citations

async def test_retentionadam_extraction():
    """Test bundle extraction for retentionadam only."""
    
    logger.info("🧪 Testing retentionadam linguistic bundle extraction")
    logger.info("=" * 60)
    
    # Find retentionadam's linguistic analysis file
    retentionadam_file = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/retentionadam/retentionadam_linguistic_analysis_json_20250516164442_raw_response.txt"
    
    if not os.path.exists(retentionadam_file):
        logger.error(f"File not found: {retentionadam_file}")
        return
    
    logger.info(f"Found retentionadam analysis: {retentionadam_file}")
    
    # Load the analysis
    analysis_text = load_linguistic_analysis(retentionadam_file)
    
    if not analysis_text:
        logger.error("No analysis text found")
        return
    
    logger.info(f"Loaded analysis ({len(analysis_text)} characters)")
    
    # Extract bundles using Claude with Citations
    tool_response = await extract_bundles_with_citations(analysis_text, "retentionadam")
    
    if not tool_response:
        logger.error("No tool response received")
        return
    
    # Extract bundles from tool response
    bundles_raw = tool_response.get('bundles', [])
    
    if not bundles_raw:
        logger.error("No bundles found in tool response")
        return
    
    # Parse bundles if they are a JSON string
    if isinstance(bundles_raw, str):
        try:
            bundles = json.loads(bundles_raw)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing bundles JSON: {e}")
            return
    else:
        bundles = bundles_raw
    
    logger.info(f"🎉 Successfully extracted {len(bundles)} bundles for retentionadam")
    
    # Save test results
    test_output = {
        "creator_name": "retentionadam",
        "analysis_type": "linguistic_analysis", 
        "extraction_method": "claude_sonnet_4_citations",
        "bundle_count": len(bundles),
        "bundles": bundles,
        "full_tool_response": tool_response,
        "extraction_timestamp": datetime.utcnow().isoformat(),
        "source_file": retentionadam_file
    }
    
    # Save to test output file
    output_file = "/Users/<USER>/LinkedInsight/test_output/retentionadam_test_extraction.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_output, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📁 Test results saved to: {output_file}")
    
    # Analyze the results
    logger.info("\n📊 BUNDLE ANALYSIS:")
    logger.info("=" * 40)
    
    total_sentence_patterns = 0
    total_linguistic_dos = 0
    total_linguistic_donts = 0
    
    for i, bundle in enumerate(bundles, 1):
        bundle_name = bundle.get("bundle_name", f"Bundle {i}")
        logger.info(f"\n📦 {bundle_name}:")
        
        # Count sentence patterns
        sentence_patterns = bundle.get("sentence_patterns", [])
        total_sentence_patterns += len(sentence_patterns)
        logger.info(f"  📝 Sentence patterns: {len(sentence_patterns)}")
        for pattern in sentence_patterns:
            pattern_name = pattern.get("name", "Unnamed")
            logger.info(f"    - {pattern_name}")
        
        # Count linguistic dos
        linguistic_dos = bundle.get("linguistic_dos", [])
        total_linguistic_dos += len(linguistic_dos)
        logger.info(f"  ✅ Linguistic dos: {len(linguistic_dos)}")
        
        # Count linguistic donts
        linguistic_donts = bundle.get("linguistic_donts", [])
        total_linguistic_donts += len(linguistic_donts)
        logger.info(f"  ❌ Linguistic donts: {len(linguistic_donts)}")
        
        # Check for other key sections
        rhetorical_devices = bundle.get("rhetorical_devices", [])
        logger.info(f"  🎭 Rhetorical devices: {len(rhetorical_devices)}")
        
        contextual_rules = bundle.get("contextual_application_rules", [])
        logger.info(f"  📋 Contextual rules: {len(contextual_rules)}")
    
    logger.info(f"\n🎯 TOTALS ACROSS ALL BUNDLES:")
    logger.info(f"  📝 Total sentence patterns: {total_sentence_patterns}")
    logger.info(f"  ✅ Total linguistic dos: {total_linguistic_dos}")
    logger.info(f"  ❌ Total linguistic donts: {total_linguistic_donts}")
    
    # Expected totals from original analysis
    logger.info(f"\n🎯 EXPECTED FROM ORIGINAL ANALYSIS:")
    logger.info(f"  📝 Expected sentence patterns: 4 (Dialogue attribution, Declarative insight, Question-as-transition, If-then conditional)")
    logger.info(f"  ✅ Expected linguistic dos: 5 (with priorities)")
    logger.info(f"  ❌ Expected linguistic donts: 5")
    
    # Success assessment
    success_sentence_patterns = total_sentence_patterns >= 4
    success_linguistic_dos = total_linguistic_dos >= 5
    success_linguistic_donts = total_linguistic_donts >= 5
    
    overall_success = success_sentence_patterns and success_linguistic_dos and success_linguistic_donts
    
    logger.info(f"\n🏆 SUCCESS ASSESSMENT:")
    logger.info(f"  📝 Sentence patterns: {'✅ PASS' if success_sentence_patterns else '❌ FAIL'} ({total_sentence_patterns}/4)")
    logger.info(f"  ✅ Linguistic dos: {'✅ PASS' if success_linguistic_dos else '❌ FAIL'} ({total_linguistic_dos}/5)")
    logger.info(f"  ❌ Linguistic donts: {'✅ PASS' if success_linguistic_donts else '❌ FAIL'} ({total_linguistic_donts}/5)")
    logger.info(f"  🎯 Overall: {'✅ SUCCESS' if overall_success else '❌ NEEDS IMPROVEMENT'}")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(test_retentionadam_extraction())
    if success:
        print("\n🎉 Test completed successfully! Ready to run full extraction.")
    else:
        print("\n⚠️ Test revealed issues. Review the output above.")