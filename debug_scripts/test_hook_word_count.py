#!/usr/bin/env python3
"""
Test hook word count calculation in process_step.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.agent_utils.process_step import process_step

async def test_hook_word_count():
    """Test that hook word count is calculated correctly."""
    
    # Sample hook text from the span
    hook_text = "The AI shift agencies are missing isn't what you think. 🎯\n\nWhile everyone's obsessing over visual automation, the real efficiency gains are hiding in plain sight."
    
    # Calculate expected word count manually
    expected_count = len(hook_text.split())
    print(f"Expected hook word count: {expected_count}")
    
    # Create test scratchpad with hook
    test_scratchpad = {
        "generation_pipeline": {
            "hook_creation": {
                "generated_hook_text": hook_text
            }
        },
        "current_draft_post": hook_text,
        "user_content_preferences": {"post_length": "medium"}
    }
    
    # Test the word count calculation directly
    print("\nTesting word count extraction:")
    hook_creation = test_scratchpad["generation_pipeline"].get("hook_creation", {})
    if hook_text_from_scratchpad := hook_creation.get("generated_hook_text"):
        calculated_count = len(hook_text_from_scratchpad.split())
        print(f"Calculated word count: {calculated_count}")
        
        if calculated_count == expected_count:
            print("✅ Word count calculation is correct")
        else:
            print(f"❌ Word count mismatch: expected {expected_count}, got {calculated_count}")
    else:
        print("❌ Could not extract hook text from scratchpad")

if __name__ == "__main__":
    asyncio.run(test_hook_word_count())