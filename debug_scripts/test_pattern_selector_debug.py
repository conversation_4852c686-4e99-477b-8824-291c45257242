#!/usr/bin/env python3
"""
Debug test for pattern selector to verify linguistic patterns are being returned.
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.vector_pattern_selector import select_relevant_patterns_vector

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_pattern_selector():
    """Test the pattern selector for body generation to verify linguistic patterns are returned."""
    
    # Simulate the context that would be passed to the content agent
    test_context = {
        'user_input': 'I want to write about overcoming imposter syndrome in tech careers',
        'target_length': 'medium',
        'generated_hook': 'That moment when you sit in a meeting full of senior developers and wonder if everyone can tell you googled "how to center a div" five minutes ago.'
    }
    
    logger.info("Testing body pattern selection...")
    logger.info(f"Context: {test_context}")
    
    try:
        # Test body pattern selection (should include both frameworks and linguistic patterns)
        result = await select_relevant_patterns_vector(
            step_type="body",
            context=test_context,
            max_selections={"body": 2}  # Limit for faster testing
        )
        
        logger.info(f"Pattern selection result keys: {list(result.keys())}")
        
        # Check if linguistic patterns are included
        if 'selected_linguistic' in result:
            linguistic_patterns = result['selected_linguistic']
            logger.info(f"Found {len(linguistic_patterns)} linguistic patterns")
            
            if linguistic_patterns:
                logger.info("✅ Linguistic patterns found!")
                for i, pattern in enumerate(linguistic_patterns):
                    logger.info(f"  Pattern {i+1}: {pattern.get('pattern_name', 'Unknown')}")
                    logger.info(f"    Creator: {pattern.get('creator_id', 'Unknown')}")
                    logger.info(f"    Score: {pattern.get('score', 0):.4f}")
                    
                    # Check if citation text contains linguistic pattern formatting
                    citations = pattern.get('citations', [])
                    if citations:
                        citation_text = citations[0].get('text', '')
                        if 'Key Techniques:' in citation_text or 'Flow Pattern:' in citation_text:
                            logger.info(f"    ✅ Citation contains linguistic pattern content")
                        else:
                            logger.warning(f"    ⚠️  Citation does not contain expected linguistic pattern formatting")
                        
                        # Show a preview of the citation
                        preview = citation_text[:200] + '...' if len(citation_text) > 200 else citation_text
                        logger.info(f"    Citation preview: {preview}")
            else:
                logger.error("❌ No linguistic patterns returned!")
        else:
            logger.error("❌ 'selected_linguistic' key not found in result!")
            
        # Also check frameworks
        if 'selected_structures' in result:
            framework_patterns = result['selected_structures']
            logger.info(f"Found {len(framework_patterns)} framework patterns")
            
            if framework_patterns:
                logger.info("✅ Framework patterns found!")
                for i, pattern in enumerate(framework_patterns):
                    logger.info(f"  Framework {i+1}: {pattern.get('pattern_name', 'Unknown')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Pattern selection failed: {e}", exc_info=True)
        return None


if __name__ == "__main__":
    asyncio.run(test_pattern_selector())