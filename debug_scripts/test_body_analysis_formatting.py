#!/usr/bin/env python3
"""
Test the full body analysis formatting to verify linguistic patterns are included correctly.
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.agent_utils.pattern_filter import filter_analysis_with_patterns

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_body_analysis_formatting():
    """Test the complete body analysis formatting to verify linguistic patterns are included."""
    
    # Simulate the context for body generation
    test_scratchpad = {
        "generation_pipeline": {
            "hook_creation": {
                "generated_hook_text": "That moment when you sit in a meeting full of senior developers and wonder if everyone can tell you googled \"how to center a div\" five minutes ago."
            }
        },
        "current_draft_post": ""
    }
    
    # Empty analysis data (vector search replaces this)
    empty_analysis = {}
    
    test_input = 'I want to write about overcoming imposter syndrome in tech careers'
    
    logger.info("Testing body analysis formatting with vector pattern selection...")
    
    try:
        # Test the pattern filter function that creates the analysis_data for body step
        filtered_analysis = await filter_analysis_with_patterns(
            step="body",
            analysis_data=empty_analysis,
            scratchpad=test_scratchpad,
            input_text=test_input,
            post_length="medium",
            use_pattern_selector=True
        )
        
        logger.info(f"Filtered analysis type: {type(filtered_analysis)}")
        
        if isinstance(filtered_analysis, str):
            # This is the formatted analysis text that goes into the prompt
            logger.info("✅ Body analysis is formatted as string (expected)")
            
            # Check for the key sections
            if "BODY FRAMEWORKS (Use for STRUCTURE)" in filtered_analysis:
                logger.info("✅ Found 'BODY FRAMEWORKS (Use for STRUCTURE)' section")
            else:
                logger.warning("❌ Missing 'BODY FRAMEWORKS (Use for STRUCTURE)' section")
            
            if "LINGUISTIC PATTERNS (Use for PROSE STYLE)" in filtered_analysis:
                logger.info("✅ Found 'LINGUISTIC PATTERNS (Use for PROSE STYLE)' section")
            else:
                logger.warning("❌ Missing 'LINGUISTIC PATTERNS (Use for PROSE STYLE)' section")
            
            if "LINGUISTIC PATTERN:" in filtered_analysis:
                logger.info("✅ Found linguistic pattern content")
            else:
                logger.warning("❌ Missing linguistic pattern content")
            
            # Show a preview
            logger.info("\n=== FORMATTED ANALYSIS PREVIEW ===")
            preview = filtered_analysis[:1000] + "..." if len(filtered_analysis) > 1000 else filtered_analysis
            print(preview)
            logger.info("=== END PREVIEW ===")
            
        else:
            logger.error(f"❌ Unexpected analysis format: {type(filtered_analysis)}")
            
    except Exception as e:
        logger.error(f"❌ Body analysis formatting test failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(test_body_analysis_formatting())