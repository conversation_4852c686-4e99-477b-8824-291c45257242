#!/usr/bin/env python3
"""
Test linguistic bundle vector search functionality.
Validates that linguistic patterns can be found via semantic similarity search.
"""

import json
import os
import sys
from typing import Dict, List, Any
import voyageai
from pymongo import MongoClient
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB connection
MONGO_URI = os.getenv("MONGODB_URI")
VOYAGE_AI_API_KEY = os.getenv("VOYAGE_AI_API_KEY")

voyage_client = voyageai.Client(api_key=VOYAGE_AI_API_KEY)


def test_linguistic_vector_search():
    """Test linguistic bundle vector search with various queries."""
    
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client['linkedinsight']
    collection = db.pattern_embeddings
    
    try:
        # Test queries covering different linguistic aspects
        test_queries = [
            {
                "name": "Storytelling & Dialogue",
                "query": "storytelling with conversations and dialogue to build credibility",
                "expected_bundles": ["Narrative", "Storytelling", "Dialogue"]
            },
            {
                "name": "Reader Engagement",
                "query": "asking questions to engage readers and create interaction",
                "expected_bundles": ["Engagement", "Interactive", "Reader"]
            },
            {
                "name": "Authority & Expertise",
                "query": "establishing thought leadership and professional authority",
                "expected_bundles": ["Authority", "Impact", "Leadership"]
            },
            {
                "name": "Personal Reflection",
                "query": "sharing personal experiences and vulnerable moments",
                "expected_bundles": ["Personal", "Reflection", "Vulnerable"]
            },
            {
                "name": "Brand Analysis",
                "query": "analyzing brand strategies and marketing campaigns with metrics",
                "expected_bundles": ["Brand", "Analysis", "Case Study"]
            }
        ]
        
        logger.info("🔍 Testing Linguistic Vector Search")
        logger.info("=" * 60)
        
        # First, get basic stats
        total_embeddings = collection.count_documents({})
        linguistic_count = collection.count_documents({"pattern_type": "linguistic_bundle"})
        
        logger.info(f"📊 Collection Stats:")
        logger.info(f"   Total embeddings: {total_embeddings}")
        logger.info(f"   Linguistic bundles: {linguistic_count}")
        
        # Sample a few linguistic bundles to verify structure
        sample_bundles = list(collection.find(
            {"pattern_type": "linguistic_bundle"}, 
            {"bundle_name": 1, "creator_name": 1, "content_purpose": 1}
        ).limit(5))
        
        logger.info(f"\n📝 Sample Linguistic Bundles:")
        for bundle in sample_bundles:
            logger.info(f"   {bundle['creator_name']}: {bundle['bundle_name']}")
        
        # Test each query
        for i, test in enumerate(test_queries, 1):
            logger.info(f"\n🧪 Test {i}: {test['name']}")
            logger.info(f"Query: \"{test['query']}\"")
            
            # Create embedding for the query
            query_result = voyage_client.embed([test['query']], model="voyage-3.5", input_type="query")
            query_embedding = query_result.embeddings[0]
            
            # Perform vector search specifically for linguistic bundles
            pipeline = [
                {
                    "$vectorSearch": {
                        "index": "pattern_embeddings_vector_index",
                        "path": "embedding",
                        "queryVector": query_embedding,
                        "numCandidates": 100,
                        "limit": 5,
                        "filter": {"pattern_type": "linguistic_bundle"}
                    }
                },
                {
                    "$project": {
                        "creator_name": 1,
                        "bundle_name": 1,
                        "content_purpose": 1,
                        "when_to_use": 1,
                        "score": {"$meta": "vectorSearchScore"}
                    }
                }
            ]
            
            results = list(collection.aggregate(pipeline))
            
            if results:
                logger.info(f"✅ Found {len(results)} relevant bundles:")
                for j, result in enumerate(results, 1):
                    score = result.get('score', 0)
                    logger.info(f"   {j}. {result['creator_name']}: {result['bundle_name']}")
                    logger.info(f"      Score: {score:.4f}")
                    logger.info(f"      Purpose: {result['content_purpose'][:100]}...")
                    
                    # Check if this matches expected bundle types
                    bundle_name = result['bundle_name'].lower()
                    expected = test['expected_bundles']
                    matches = [exp.lower() for exp in expected if exp.lower() in bundle_name]
                    if matches:
                        logger.info(f"      ✅ Matches expected: {matches}")
                    else:
                        logger.info(f"      ⚠️  Unexpected but may be relevant")
                    logger.info("")
            else:
                logger.error(f"❌ No results found for query: {test['query']}")
        
        # Test creator-specific filtering
        logger.info(f"\n🎯 Testing Creator-Specific Search")
        creator_query = "professional storytelling with personal experiences"
        query_result = voyage_client.embed([creator_query], model="voyage-3.5", input_type="query")
        query_embedding = query_result.embeddings[0]
        
        # Search for specific creator
        target_creator = "retentionadam"
        pipeline = [
            {
                "$vectorSearch": {
                    "index": "pattern_embeddings_vector_index",
                    "path": "embedding",
                    "queryVector": query_embedding,
                    "numCandidates": 50,
                    "limit": 3,
                    "filter": {
                        "pattern_type": "linguistic_bundle",
                        "creator_name": target_creator
                    }
                }
            },
            {
                "$project": {
                    "creator_name": 1,
                    "bundle_name": 1,
                    "content_purpose": 1,
                    "score": {"$meta": "vectorSearchScore"}
                }
            }
        ]
        
        creator_results = list(collection.aggregate(pipeline))
        logger.info(f"Query: \"{creator_query}\"")
        logger.info(f"Creator: {target_creator}")
        
        if creator_results:
            logger.info(f"✅ Found {len(creator_results)} bundles for {target_creator}:")
            for result in creator_results:
                score = result.get('score', 0)
                logger.info(f"   • {result['bundle_name']} (Score: {score:.4f})")
                logger.info(f"     {result['content_purpose'][:80]}...")
        else:
            logger.error(f"❌ No results found for {target_creator}")
        
        # Test bundle type diversity
        logger.info(f"\n📈 Testing Bundle Type Diversity")
        all_bundles = list(collection.find(
            {"pattern_type": "linguistic_bundle"}, 
            {"bundle_name": 1, "creator_name": 1}
        ))
        
        # Analyze bundle name patterns
        bundle_patterns = {}
        for bundle in all_bundles:
            name = bundle['bundle_name']
            # Extract key terms from bundle names
            key_terms = []
            name_lower = name.lower()
            
            if 'story' in name_lower or 'narrative' in name_lower:
                key_terms.append('Storytelling')
            if 'engage' in name_lower or 'interact' in name_lower:
                key_terms.append('Engagement')
            if 'authority' in name_lower or 'impact' in name_lower:
                key_terms.append('Authority')
            if 'personal' in name_lower or 'reflection' in name_lower:
                key_terms.append('Personal')
            if 'brand' in name_lower or 'analysis' in name_lower:
                key_terms.append('Analysis')
            
            for term in key_terms or ['Other']:
                bundle_patterns[term] = bundle_patterns.get(term, 0) + 1
        
        logger.info("Bundle type distribution:")
        for pattern, count in sorted(bundle_patterns.items()):
            logger.info(f"   {pattern}: {count} bundles")
        
        logger.info(f"\n✅ Linguistic Vector Search Test Completed!")
        logger.info(f"📊 Summary: {linguistic_count} linguistic bundles across {len(set(b['creator_name'] for b in all_bundles))} creators")
        
    except Exception as e:
        logger.error(f"❌ Error during vector search test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()


if __name__ == "__main__":
    test_linguistic_vector_search()