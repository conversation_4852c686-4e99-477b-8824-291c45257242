[{"type": "user", "content": {"type": "array", "content": [{"type": "text", "content": "You are creating the BODY of a LinkedIn post, using vector-selected patterns to ensure compelling structure and style.\n\nThe original user brief is cached in the system for reference.\nCURRENT SCRATCHPAD (includes THEME and HOOK): {\n  \"generation_pipeline\": {\n    \"hook_creation\": {\n      \"selected_hook_pattern\": \"The Insight Reframer\",\n      \"generated_hook_text\": \"The AI shift agencies are missing isn't what you think. \\ud83c\\udfaf\\n\\nWhile everyone's obsessing over visual automation, the real efficiency gains are hiding in plain sight.\",\n      \"decision_summary_for_next_step\": \"Using \\\"The Insight Reframer\\\" pattern to challenge the industry assumption that AI design automation is the key to efficiency, while promising to reveal where the real AI value lies today. The body should expand on this reframing by contrasting current agency behavior with smarter approaches.\\n\\nCHARACTER COUNT VERIFICATION:\\nHook text is 156 characters, which falls within the required 100-200 character range.\",\n      \"pattern_selection\": {\n        \"patterns_used\": 3,\n        \"pattern_names\": [\n          \"The Insight Reframer\",\n          \"Specific Data/Metrics-Driven Opening\",\n          \"Question Hook\"\n        ]\n      }\n    },\n    \"body_linguistic_construction\": {\n      \"selected_body_structure\": null,\n      \"selected_linguistic_patterns\": null,\n      \"generated_body_text\": null,\n      \"decision_summary_for_next_step\": null,\n      \"pattern_selection\": {\n        \"patterns_used\": 2,\n        \"pattern_names\": [\n          \"Claim-Evidence Structure\",\n          \"Numbered Insight Lists\"\n        ]\n      }\n    },\n    \"ending_creation\": {\n      \"selected_ending_pattern\": null,\n      \"generated_ending_text\": null\n    }\n  },\n  \"current_draft_post\": \"The AI shift agencies are missing isn't what you think. \\ud83c\\udfaf\\n\\nWhile everyone's obsessing over visual automation, the real efficiency gains are hiding in plain sight.\\n\\n\",\n  \"final_explanation_to_user_components\": {},\n  \"user_content_preferences\": {\n    \"clerk_user_id\": \"user_2w9GQ52Vjd2gCULrDwayk5mg2v2\",\n    \"emoji_usage\": \"frequent\",\n    \"opinion_style\": \"strongly_opinionated\",\n    \"post_length\": \"medium\",\n    \"tone_style\": \"casual\",\n    \"transcript_name\": \"Ben Wise\"\n  }\n}\n\nPREVIOUS STEP SUMMARY (from hook creation): Using \"The Insight Reframer\" pattern to challenge the industry assumption that AI design automation is the key to efficiency, while promising to reveal where the real AI value lies today. The body should expand on this reframing by contrasting current agency behavior with smarter approaches.\n\nCHARACTER COUNT VERIFICATION:\nHook text is 156 characters, which falls within the required 100-200 character range.\n\nAVAILABLE PATTERNS (JSON format):\n\"\\n\\n==================================================## BODY FRAMEWORKS (Use for STRUCTURE)\\n\\n## Claim-Evidence Structure\\n\\nDescription: This structure presents a bold claim or thesis statement, followed by logical reasoning and structured evidence supporting the central claim.\\n\\nImplementation Steps:\\n1. Make a strong, clear claim in the opening section\\n2. Provide initial reasoning for why the claim matters\\n3. Present 3-7 structured points of evidence using numbers or bullets\\n4. For each evidence point, include a headline and brief explanation\\n5. Conclude with implications or call-to-action\\n\\nExample:\\nIf you can't break a goal down into clear, sequenced tasks, you can't:\\n- Tell where AI can help\\n- Assign work to the right tools or people\\n- Or build a system that compounds instead of collapses\\n\\nMost people try to \\\"delegate to AI\\\" before they've even defined the work.\\n\\nAnd here's the non-obvious part:\\nWhen you decompose a task well, you don't just make AI useful, you create a blueprint that makes your entire org more intelligent.\\n\\nYour workflows get clearer. Your automation paths become visible. You uncover handoffs and decisions that were implicit before \\u2014 now they can be improved, delegated, measured.\\n\\nTake a real example\\n\\nLet's say your goal is: \\\"Create an email campaign for churned customers.\\\"\\n\\nBreak it down like this:\\n- Define what \\\"churned\\\" means and who qualifies (Data task)\\n- Analyze why those customers left (Behavioral analysis)\\n- Decide what message or offer might bring them back (Strategy)\\n- Write subject lines and body copy (Creative)\\n- Design and QA the email (Design & QA)\\n- Set up the send and monitor results (Execution & Analytics)\\n\\n---\\n\\n## Numbered Insight Lists\\n\\nDescription: This structure organizes content as numbered points, each presenting a discrete insight with supporting explanation. This creates easily scannable content with clear progression.\\n\\nImplementation Steps:\\n1. Begin each point with a bold claim or insight\\n2. Follow with 1-2 explanatory sentences that expand on the claim\\n3. Use parallel structure across numbered points\\n4. Maintain consistent point length (100-200 characters per point)\\n\\nExample:\\n1. a subtle but huge reframe: \\\"hire an AI before you hire a human.\\\"\\n2. AI is now a baseline expectation at shopify. hiring filters will probably favor ai-fluent candidates at shopify and other companies.\\n3. AI agents are now treated like teammates, not tools.\\n4. prompting is now a core skill. top performers will be top prompters.\\n5. AI usage is now measured. kinda wild. probably a business idea there to build the lattice for AI usage.\\n\\n## LINGUISTIC PATTERNS (Use for PROSE STYLE)\\n\\nLINGUISTIC PATTERN: Contrarian Industry Voice\\n\\n{\\n      \\\"bundle_name\\\": \\\"Contrarian Industry Voice\\\",\\n      \\\"bundle_description\\\": \\\"Posts that challenge conventional sales wisdom with provocative takes and counterintuitive insights about industry practices\\\",\\n      \\\"usage_context\\\": \\\"When challenging standard practices, offering contrarian perspectives, or provoking thought about industry norms\\\",\\n      \\\"post_count_in_bundle\\\": 6,\\n      \\\"representative_posts\\\": [\\n        {\\n          \\\"post_id\\\": \\\"911\\\",\\n          \\\"excerpt\\\": \\\"'AI isn't here to replace sales people' It's sort of BS. But not in a bad way. And yes... this may enrage some.\\\",\\n          \\\"why_representative\\\": \\\"Classic contrarian opening that directly challenges popular belief with provocative stance and acknowledgment of controversy\\\",\\n          \\\"linguistic_markers\\\": [\\\"provocative contradiction\\\", \\\"controversy acknowledgment\\\", \\\"logical argument structure\\\", \\\"prediction framework\\\"]\\n        },\\n        {\\n          \\\"post_id\\\": \\\"909\\\",\\n          \\\"excerpt\\\": \\\"Talked to a BD Leader who left a legacy engagement solution... 'It turned our reps into zombies.' Sellers ~5 years ago began to dictate their prospecting strategy around sales engagement platforms\\\",\\n          \\\"why_representative\\\": \\\"Industry critique with vivid metaphor, historical analysis, and systematic problem identification\\\",\\n          \\\"linguistic_markers\\\": [\\\"vivid metaphorical language\\\", \\\"historical perspective\\\", \\\"systematic problem analysis\\\", \\\"opportunity identification\\\"]\\n        },\\n        {\\n          \\\"post_id\\\": \\\"901\\\",\\n          \\\"excerpt\\\": \\\"Weird sales concept: Once most people start doing it, it will stop working. You have to be so compelling, you're impossible to ignore versus the 99 other sellers\\\",\\n          \\\"why_representative\\\": \\\"Counterintuitive concept introduction with philosophical framing and practical application\\\",\\n          \\\"linguistic_markers\\\": [\\\"philosophical concept introduction\\\", \\\"contrarian logic\\\", \\\"competitive advantage framing\\\", \\\"systematic differentiation\\\"]\\n        }\\n      ],\\n      \\\"audience_relationship\\\": \\\"Thought leader challenging industry peers\\\",\\n      \\\"emotional_intent\\\": \\\"Provoke critical thinking and inspire strategic differentiation\\\",\\n      \\\"content_characteristics\\\": [\\n        \\\"Direct challenges to conventional wisdom\\\",\\n        \\\"Historical analysis of industry evolution\\\",\\n        \\\"Predictions about future trends\\\",\\n        \\\"Philosophical observations about sales dynamics\\\",\\n        \\\"Systematic problem identification with solutions\\\"\\n      ],\\n      \\\"contextual_triggers\\\": [\\n        {\\n          \\\"trigger\\\": \\\"Observing widespread adoption of ineffective practices\\\",\\n          \\\"response\\\": \\\"Challenge the conventional wisdom with logical analysis and alternative perspective\\\",\\n          \\\"examples\\\": [\\\"Sales engagement platform critique\\\", \\\"AI replacement discussion\\\"]\\n        },\\n        {\\n          \\\"trigger\\\": \\\"Identifying market differentiation opportunities\\\",\\n          \\\"response\\\": \\\"Frame contrarian approach as competitive advantage with philosophical backing\\\",\\n          \\\"examples\\\": [\\\"Weird sales concept about differentiation\\\", \\\"Standing out strategies\\\"]\\n        }\\n      ],\\n      \\\"1_overall_voice_tone\\\": {\\n        \\\"primary_descriptors\\\": [\\\"provocative\\\", \\\"analytical\\\", \\\"contrarian\\\"],\\n        \\\"secondary_descriptors\\\": [\\\"thoughtful\\\", \\\"strategic\\\", \\\"confident\\\"],\\n        \\\"audience_relationship\\\": \\\"thought leader to industry peers\\\",\\n        \\\"emotional_tone_patterns\\\": {\\n          \\\"primary_emotions_evoked\\\": [\\\"intellectual curiosity\\\", \\\"strategic insight\\\", \\\"competitive awareness\\\"],\\n          \\\"emotional_arc\\\": \\\"conventional challenge \\u2192 logical analysis \\u2192 strategic opportunity identification\\\",\\n          \\\"tone_variation_by_content_type\\\": [\\n            {\\n              \\\"content_type\\\": \\\"industry_critique\\\",\\n              \\\"typical_tone\\\": \\\"analytical and provocative\\\"\\n            },\\n            {\\n              \\\"content_type\\\": \\\"prediction_framework\\\",\\n              \\\"typical_tone\\\": \\\"confident and systematic\\\"\\n            }\\n          ]\\n        }\\n      },\\n      \\\"2_sentence_rhythm_and_flow\\\": {\\n        \\\"typical_sentence_length_words_description\\\": \\\"Varied length for dramatic effect and analytical depth\\\",\\n        \\\"sentence_length_distribution\\\": [\\n          {\\\"length_words\\\": \\\"3-8 words\\\", \\\"frequency_percent\\\": 30},\\n          {\\\"length_words\\\": \\\"9-18 words\\\", \\\"frequency_percent\\\": 40},\\n          {\\\"length_words\\\": \\\"19-35 words\\\", \\\"frequency_percent\\\": 30}\\n        ],\\n        \\\"dominant_sentence_patterns\\\": [\\n          {\\n            \\\"name\\\": \\\"provocative_contradiction\\\",\\n            \\\"example_template\\\": \\\"\\\\\\\"[Popular belief]\\\\\\\" [Contradiction statement]. [Qualification or context].\\\",\\n            \\\"creator_example\\\": \\\"\\\\\\\"AI isn't here to replace sales people\\\\\\\" It's sort of BS. But not in a bad way.\\\",\\n            \\\"frequency_percent\\\": 40,\\n            \\\"typical_usage_context\\\": \\\"Opening contrarian arguments\\\"\\n          },\\n          {\\n            \\\"name\\\": \\\"philosophical_concept_introduction\\\",\\n            \\\"example_template\\\": \\\"[Unusual descriptor] [industry concept]: [Counterintuitive principle].\\\",\\n            \\\"creator_example\\\": \\\"Weird sales concept: Once most people start doing it, it will stop working.\\\",\\n            \\\"frequency_percent\\\": 35,\\n            \\\"typical_usage_context\\\": \\\"Introducing contrarian frameworks\\\"\\n          },\\n          {\\n            \\\"name\\\": \\\"systematic_prediction\\\",\\n            \\\"example_template\\\": \\\"Here's what's going to happen: [Detailed scenario analysis]\\\",\\n            \\\"creator_example\\\": \\\"Here's what's going to happen: Oversimplified Situation: It's 2020.\\\",\\n            \\\"frequency_percent\\\": 25,\\n            \\\"typical_usage_context\\\": \\\"Building logical argument structures\\\"\\n          }\\n        ],\\n        \\\"sentence_variety_preference\\\": \\\"Strategic variation from short provocative statements to longer analytical explanations\\\"\\n      },\\n      \\\"3_paragraph_dynamics\\\": {\\n        \\\"sentences_per_paragraph_rule\\\": \\\"1-3 sentences with strategic breaks for emphasis\\\",\\n        \\\"paragraph_length_distribution\\\": [\\n          {\\\"sentences_count\\\": 1, \\\"frequency_percent\\\": 40},\\n          {\\\"sentences_count\\\": 2, \\\"frequency_percent\\\": 35},\\n          {\\\"sentences_count\\\": 3, \\\"frequency_percent\\\": 25}\\n        ],\\n        \\\"whitespace_preference\\\": \\\"Strategic whitespace for dramatic emphasis and argument separation\\\",\\n        \\\"paragraph_sequencing_patterns\\\": [\\n          {\\n            \\\"pattern_name\\\": \\\"challenge_to_analysis\\\",\\n            \\\"description\\\": \\\"Provocative statement followed by systematic logical analysis\\\",\\n            \\\"example\\\": \\\"Provocative claim \\u2192 Supporting evidence \\u2192 Logical conclusion\\\"\\n          },\\n          {\\n            \\\"pattern_name\\\": \\\"historical_to_prediction\\\",\\n            \\\"description\\\": \\\"Historical context leading to future predictions\\\",\\n            \\\"example\\\": \\\"Past situation \\u2192 Current state \\u2192 Future projection\\\"\\n          }\\n        ]\\n      },\\n      \\\"4_vocabulary_and_phrasing\\\": {\\n        \\\"formality_and_specificity_level\\\": \\\"Intellectual professional with philosophical elements\\\",\\n        \\\"jargon_level\\\": \\\"Moderate industry jargon mixed with analytical language\\\",\\n        \\\"unique_creator_lexicon\\\": [\\n          {\\n            \\\"term\\\": \\\"zombies\\\",\\n            \\\"meaning\\\": \\\"mindless automation without strategic thinking\\\",\\n            \\\"typical_usage\\\": \\\"critiquing over-reliance on tools\\\"\\n          },\\n          {\\n            \\\"term\\\": \\\"zig and zag\\\",\\n            \\\"meaning\\\": \\\"going different directions from the crowd\\\",\\n            \\\"typical_usage\\\": \\\"differentiation strategy discussions\\\"\\n          },\\n          {\\n            \\\"term\\\": \\\"weird sales concept\\\",\\n            \\\"meaning\\\": \\\"counterintuitive principle\\\",\\n            \\\"typical_usage\\\": \\\"introducing contrarian frameworks\\\"\\n          }\\n        ],\\n        \\\"vocabulary_preferences\\\": {\\n          \\\"preferred_industry_terms\\\": [\\\"systematic\\\", \\\"strategic\\\", \\\"differentiation\\\", \\\"conventional wisdom\\\"],\\n          \\\"avoided_terms\\\": [\\\"best practices\\\", \\\"industry standard\\\", \\\"proven methods\\\"],\\n          \\\"specificity_with_numbers\\\": true\\n        },\\n        \\\"metaphor_and_analogy_style\\\": \\\"Vivid metaphors from technology, warfare, and systems thinking\\\"\\n      },\\n      \\\"5_rhetorical_signature\\\": {\\n        \\\"key_rhetorical_devices_used\\\": [\\n          {\\n            \\\"device\\\": \\\"direct_contradiction\\\",\\n            \\\"frequency\\\": \\\"very_high\\\",\\n            \\\"typical_context\\\": \\\"challenging popular beliefs or practices\\\",\\n            \\\"example\\\": \\\"\\\\\\\"AI isn't here to replace sales people\\\\\\\" It's sort of BS.\\\"\\n          },\\n          {\\n            \\\"device\\\": \\\"systematic_scenario_building\\\",\\n            \\\"frequency\\\": \\\"high\\\",\\n            \\\"typical_context\\\": \\\"proving contrarian points through logical progression\\\",\\n            \\\"example\\\": \\\"Oversimplified Situation: It's 2020. Pretend your company has 100 opportunities...\\\"\\n          },\\n          {\\n            \\\"device\\\": \\\"vivid_metaphorical_language\\\",\\n            \\\"frequency\\\": \\\"moderate\\\",\\n            \\\"typical_context\\\": \\\"making abstract concepts memorable and impactful\\\",\\n            \\\"example\\\": \\\"It turned our reps into zombies\\\"\\n          }\\n        ],\\n        \\\"overall_persuasion_style_summary\\\": \\\"Intellectual provocation through logical analysis and strategic differentiation\\\",\\n        \\\"argument_construction_pattern\\\": \\\"Provocative challenge \\u2192 logical analysis \\u2192 systematic proof \\u2192 strategic application\\\"\\n      },\\n      \\\"6_structural_emphasis_techniques\\\": {\\n        \\\"text_formatting_for_emphasis\\\": [\\\"quotation marks for challenged beliefs\\\", \\\"strategic capitalization\\\", \\\"section breaks for argument phases\\\"],\\n        \\\"use_of_standalone_impact_phrases\\\": {\\n          \\\"frequency\\\": \\\"high\\\",\\n          \\\"typical_length_words\\\": \\\"4-8 words\\\",\\n          \\\"typical_placement\\\": \\\"dramatic reveals and conclusions\\\",\\n          \\\"example\\\": \\\"While others continue to zig, we will zag.\\\"\\n        },\\n        \\\"emphasis_patterns\\\": [\\n          {\\n            \\\"technique\\\": \\\"controversy_acknowledgment\\\",\\n            \\\"example\\\": \\\"And yes... this may enrage some.\\\"\\n          },\\n          {\\n            \\\"technique\\\": \\\"dramatic_conclusion\\\",\\n            \\\"example\\\": \\\"While others continue to zig, we will zag.\\\"\\n          }\\n        ]\\n      },\\n      \\\"7_linguistic_dos\\\": [\\n        {\\n          \\\"rule\\\": \\\"Lead with direct challenges to popular beliefs for immediate engagement\\\",\\n          \\\"priority\\\": 9,\\n          \\\"example\\\": \\\"\\\\\\\"AI isn't here to replace sales people\\\\\\\" It's sort of BS.\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Use systematic logical progression to prove contrarian points\\\",\\n          \\\"priority\\\": 8,\\n          \\\"example\\\": \\\"Here's what's going to happen: [detailed scenario analysis]\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Include vivid metaphors to make abstract concepts memorable\\\",\\n          \\\"priority\\\": 7,\\n          \\\"example\\\": \\\"It turned our reps into zombies\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Acknowledge controversy while maintaining confident stance\\\",\\n          \\\"priority\\\": 8,\\n          \\\"example\\\": \\\"And yes... this may enrage some.\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Frame contrarian approaches as competitive advantages\\\",\\n          \\\"priority\\\": 8,\\n          \\\"example\\\": \\\"This all presents an opportunity. The bar is lower than it's ever been\\\"\\n        }\\n      ],\\n      \\\"8_linguistic_donts\\\": [\\n        {\\n          \\\"rule\\\": \\\"Don't make contrarian claims without logical supporting evidence\\\",\\n          \\\"priority\\\": 9,\\n          \\\"rationale\\\": \\\"Provocation without logic reduces credibility and appears reactionary\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't use conventional sales language when challenging conventions\\\",\\n          \\\"priority\\\": 8,\\n          \\\"rationale\\\": \\\"Language choice must align with contrarian positioning\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't avoid controversy when making provocative points\\\",\\n          \\\"priority\\\": 7,\\n          \\\"rationale\\\": \\\"Hedging undermines the strength of contrarian arguments\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't provide contrarian views without actionable alternatives\\\",\\n          \\\"priority\\\": 8,\\n          \\\"rationale\\\": \\\"Criticism without solutions reduces practical value\\\"\\n        }\\n      ],\\n      \\\"9_mid_body_reader_engagement_linguistics\\\": {\\n        \\\"rhetorical_questions_for_reflection_style\\\": {\\n          \\\"frequency\\\": \\\"moderate\\\",\\n          \\\"typical_placement\\\": \\\"challenging reader assumptions\\\",\\n          \\\"example\\\": \\\"Agree? Disagree?\\\"\\n        },\\n        \\\"direct_address_to_reader_style\\\": {\\n          \\\"frequency\\\": \\\"moderate\\\",\\n          \\\"typical_phrasing\\\": \\\"challenging conventional thinking\\\",\\n          \\\"example\\\": \\\"You have to be so compelling, you're impossible to ignore\\\"\\n        },\\n        \\\"anticipation_building_phrasing_style\\\": \\\"Logical progression and systematic scenario building\\\",\\n        \\\"validation_or_empathy_cue_usage\\\": \\\"Minimal - focus on intellectual challenge rather than emotional validation\\\"\\n      },\\n      \\\"10_transition_techniques\\\": {\\n        \\\"between_paragraphs\\\": {\\n          \\\"dominant_style\\\": \\\"logical progression and argumentative development\\\",\\n          \\\"common_transition_phrases\\\": [\\\"Here's what's going to happen\\\", \\\"But from 2020 to 2030\\\", \\\"Instead\\\"]\\n        },\\n        \\\"between_sections\\\": {\\n          \\\"dominant_style\\\": \\\"argument phase transitions\\\",\\n          \\\"examples\\\": [\\\"TLDR:\\\", \\\"This all presents an opportunity\\\", \\\"Fast forward today\\\"]\\n        },\\n        \\\"hook_to_body_transition\\\": {\\n          \\\"typical_pattern\\\": \\\"provocative statement \\u2192 detailed logical analysis\\\",\\n          \\\"examples\\\": [\\\"It's sort of BS. But not in a bad way. Here's what's going to happen:\\\"]\\n        },\\n        \\\"body_to_ending_transition\\\": {\\n          \\\"typical_pattern\\\": \\\"analysis completion \\u2192 strategic application or challenge\\\",\\n          \\\"examples\\\": [\\\"While others continue to zig, we will zag.\\\", \\\"Agree? Disagree?\\\"]\\n        },\\n        \\\"pacing_variation\\\": \\\"Alternates between provocative punctuation and detailed analytical development\\\"\\n      },\\n      \\\"11_contextual_application_rules\\\": [\\n        {\\n          \\\"when\\\": \\\"Challenging widely accepted industry practices\\\",\\n          \\\"apply\\\": \\\"Lead with direct contradiction, provide systematic logical analysis, offer strategic alternatives\\\",\\n          \\\"avoid\\\": \\\"Hedging language or apologetic tone\\\",\\n          \\\"example\\\": \\\"\\\\\\\"AI isn't here to replace sales people\\\\\\\" It's sort of BS. [followed by logical analysis]\\\"\\n        },\\n        {\\n          \\\"when\\\": \\\"Identifying competitive differentiation opportunities\\\",\\n          \\\"apply\\\": \\\"Frame contrarian approach as competitive advantage, use vivid metaphors, provide philosophical context\\\",\\n          \\\"avoid\\\": \\\"Conventional competitive language\\\",\\n          \\\"example\\\": \\\"Weird sales concept: Once most people start doing it, it will stop working\\\"\\n        },\\n        {\\n          \\\"when\\\": \\\"Making industry predictions or observations\\\",\\n          \\\"apply\\\": \\\"Use systematic scenario building, include historical context, acknowledge controversy\\\",\\n          \\\"avoid\\\": \\\"Vague generalizations or unsubstantiated claims\\\",\\n          \\\"example\\\": \\\"Here's what's going to happen: [detailed scenario analysis with timeframes]\\\"\\n        }\\n      ],\\n      \\\"12_overall_linguistic_intent_summary\\\": \\\"Establish thought leadership through intellectual provocation and systematic challenge of conventional wisdom while providing strategic alternatives that position contrarian thinking as competitive advantage\\\"\\n    }\\n\\n---\\n\\nLINGUISTIC PATTERN: Tactical Framework Teacher\\n\\n{\\n      \\\"bundle_name\\\": \\\"Tactical Framework Teacher\\\",\\n      \\\"bundle_description\\\": \\\"Posts delivering specific sales frameworks, step-by-step processes, and actionable tactical advice with clear implementation guidance\\\",\\n      \\\"usage_context\\\": \\\"When educating audience on specific sales techniques, sharing frameworks, or providing tactical how-to content\\\",\\n      \\\"post_count_in_bundle\\\": 21,\\n      \\\"representative_posts\\\": [\\n        {\\n          \\\"post_id\\\": \\\"902\\\",\\n          \\\"excerpt\\\": \\\"My 10 favorite email CTAs. 1. Use anything like this today? 2. Ever come across us before? 3. Want to see how this could work for you?\\\",\\n          \\\"why_representative\\\": \\\"Classic tactical format with numbered list, specific examples, and direct implementation focus\\\",\\n          \\\"linguistic_markers\\\": [\\\"numbered list structure\\\", \\\"specific actionable examples\\\", \\\"direct imperative language\\\", \\\"implementation focus\\\"]\\n        },\\n        {\\n          \\\"post_id\\\": \\\"933\\\",\\n          \\\"excerpt\\\": \\\"Here's my always / never rules for negotiating: Always: -Confirm you are vendor of choice -Be consultative + work together\\\",\\n          \\\"why_representative\\\": \\\"Binary framework structure with clear dos/don'ts and specific tactical guidance\\\",\\n          \\\"linguistic_markers\\\": [\\\"binary framework structure\\\", \\\"clear rules format\\\", \\\"actionable imperatives\\\", \\\"systematic organization\\\"]\\n        },\\n        {\\n          \\\"post_id\\\": \\\"926\\\",\\n          \\\"excerpt\\\": \\\"Like a great NFL team, I always script my first few plays. Here's how I start all discovery calls now. 1. Ultra-prepared and scripted POV\\\",\\n          \\\"why_representative\\\": \\\"Sports metaphor opening, systematic approach, numbered steps with detailed examples\\\",\\n          \\\"linguistic_markers\\\": [\\\"sports metaphor\\\", \\\"systematic process\\\", \\\"numbered steps\\\", \\\"specific examples with context\\\"]\\n        }\\n      ],\\n      \\\"audience_relationship\\\": \\\"Expert teacher to eager students\\\",\\n      \\\"emotional_intent\\\": \\\"Empower through specific, actionable knowledge transfer\\\",\\n      \\\"content_characteristics\\\": [\\n        \\\"Step-by-step frameworks and processes\\\",\\n        \\\"Numbered lists with specific examples\\\",\\n        \\\"Always/never rules and best practices\\\",\\n        \\\"Templates and scripts for implementation\\\",\\n        \\\"Systematic approaches to sales challenges\\\"\\n      ],\\n      \\\"contextual_triggers\\\": [\\n        {\\n          \\\"trigger\\\": \\\"Audience asking how to solve specific sales challenge\\\",\\n          \\\"response\\\": \\\"Provide numbered framework with specific examples and implementation guidance\\\",\\n          \\\"examples\\\": [\\\"Email CTA list\\\", \\\"Discovery question framework\\\", \\\"Negotiation rules\\\"]\\n        },\\n        {\\n          \\\"trigger\\\": \\\"Sharing proven methodology or system\\\",\\n          \\\"response\\\": \\\"Use systematic structure with clear steps and real examples\\\",\\n          \\\"examples\\\": [\\\"2x4 prospecting method\\\", \\\"C-Suite demo framework\\\"]\\n        }\\n      ],\\n      \\\"1_overall_voice_tone\\\": {\\n        \\\"primary_descriptors\\\": [\\\"authoritative\\\", \\\"systematic\\\", \\\"practical\\\"],\\n        \\\"secondary_descriptors\\\": [\\\"confident\\\", \\\"experienced\\\", \\\"implementable\\\"],\\n        \\\"audience_relationship\\\": \\\"expert teacher to students\\\",\\n        \\\"emotional_tone_patterns\\\": {\\n          \\\"primary_emotions_evoked\\\": [\\\"confidence\\\", \\\"competence\\\", \\\"clarity\\\"],\\n          \\\"emotional_arc\\\": \\\"problem identification \\u2192 systematic solution \\u2192 implementation confidence\\\",\\n          \\\"tone_variation_by_content_type\\\": [\\n            {\\n              \\\"content_type\\\": \\\"framework_teaching\\\",\\n              \\\"typical_tone\\\": \\\"authoritative but accessible\\\"\\n            },\\n            {\\n              \\\"content_type\\\": \\\"tactical_advice\\\",\\n              \\\"typical_tone\\\": \\\"direct and actionable\\\"\\n            }\\n          ]\\n        }\\n      },\\n      \\\"2_sentence_rhythm_and_flow\\\": {\\n        \\\"typical_sentence_length_words_description\\\": \\\"Short, punchy instructional sentences with longer explanatory context\\\",\\n        \\\"sentence_length_distribution\\\": [\\n          {\\\"length_words\\\": \\\"3-7 words\\\", \\\"frequency_percent\\\": 35},\\n          {\\\"length_words\\\": \\\"8-12 words\\\", \\\"frequency_percent\\\": 40},\\n          {\\\"length_words\\\": \\\"13-20 words\\\", \\\"frequency_percent\\\": 25}\\n        ],\\n        \\\"dominant_sentence_patterns\\\": [\\n          {\\n            \\\"name\\\": \\\"imperative_instruction\\\",\\n            \\\"example_template\\\": \\\"[Action verb] [specific object/target] [method/approach].\\\",\\n            \\\"creator_example\\\": \\\"Confirm you are vendor of choice\\\",\\n            \\\"frequency_percent\\\": 45,\\n            \\\"typical_usage_context\\\": \\\"Giving direct tactical advice\\\"\\n          },\\n          {\\n            \\\"name\\\": \\\"numbered_framework_intro\\\",\\n            \\\"example_template\\\": \\\"My [number] [category] [specific thing].\\\",\\n            \\\"creator_example\\\": \\\"My 10 favorite email CTAs\\\",\\n            \\\"frequency_percent\\\": 25,\\n            \\\"typical_usage_context\\\": \\\"Introducing systematic frameworks\\\"\\n          },\\n          {\\n            \\\"name\\\": \\\"systematic_process_description\\\",\\n            \\\"example_template\\\": \\\"Here's how [I/you] [specific process].\\\",\\n            \\\"creator_example\\\": \\\"Here's how I start all discovery calls now\\\",\\n            \\\"frequency_percent\\\": 30,\\n            \\\"typical_usage_context\\\": \\\"Teaching step-by-step methods\\\"\\n          }\\n        ],\\n        \\\"sentence_variety_preference\\\": \\\"Consistent short commands mixed with explanatory context\\\"\\n      },\\n      \\\"3_paragraph_dynamics\\\": {\\n        \\\"sentences_per_paragraph_rule\\\": \\\"1-2 sentences per paragraph for digestible instruction chunks\\\",\\n        \\\"paragraph_length_distribution\\\": [\\n          {\\\"sentences_count\\\": 1, \\\"frequency_percent\\\": 60},\\n          {\\\"sentences_count\\\": 2, \\\"frequency_percent\\\": 35},\\n          {\\\"sentences_count\\\": 3, \\\"frequency_percent\\\": 5}\\n        ],\\n        \\\"whitespace_preference\\\": \\\"Heavy use of formatting and whitespace to separate distinct steps\\\",\\n        \\\"paragraph_sequencing_patterns\\\": [\\n          {\\n            \\\"pattern_name\\\": \\\"numbered_step_progression\\\",\\n            \\\"description\\\": \\\"Each step gets its own paragraph with clear numbering\\\",\\n            \\\"example\\\": \\\"1. Step one description\\\\n\\\\n2. Step two description\\\"\\n          },\\n          {\\n            \\\"pattern_name\\\": \\\"example_illustration\\\",\\n            \\\"description\\\": \\\"Framework followed by specific implementation example\\\",\\n            \\\"example\\\": \\\"Framework statement \\u2192 Example: [specific case]\\\"\\n          }\\n        ]\\n      },\\n      \\\"4_vocabulary_and_phrasing\\\": {\\n        \\\"formality_and_specificity_level\\\": \\\"Professional tactical with specific sales terminology\\\",\\n        \\\"jargon_level\\\": \\\"Moderate sales jargon with clear explanations\\\",\\n        \\\"unique_creator_lexicon\\\": [\\n          {\\n            \\\"term\\\": \\\"multithread\\\",\\n            \\\"meaning\\\": \\\"connect with multiple stakeholders\\\",\\n            \\\"typical_usage\\\": \\\"deal strategy and relationship building\\\"\\n          },\\n          {\\n            \\\"term\\\": \\\"vendor of choice\\\",\\n            \\\"meaning\\\": \\\"preferred solution provider\\\",\\n            \\\"typical_usage\\\": \\\"negotiation and closing contexts\\\"\\n          },\\n          {\\n            \\\"term\\\": \\\"1-yard line\\\",\\n            \\\"meaning\\\": \\\"very close to closing a deal\\\",\\n            \\\"typical_usage\\\": \\\"deal stage descriptions\\\"\\n          }\\n        ],\\n        \\\"vocabulary_preferences\\\": {\\n          \\\"preferred_industry_terms\\\": [\\\"discovery\\\", \\\"prospecting\\\", \\\"MEDDICC\\\", \\\"stakeholders\\\", \\\"pipeline\\\"],\\n          \\\"avoided_terms\\\": [\\\"revolutionize\\\", \\\"game-changing\\\", \\\"paradigm\\\"],\\n          \\\"specificity_with_numbers\\\": true\\n        },\\n        \\\"metaphor_and_analogy_style\\\": \\\"Sports metaphors and military/tactical language for systematic approaches\\\"\\n      },\\n      \\\"5_rhetorical_signature\\\": {\\n        \\\"key_rhetorical_devices_used\\\": [\\n          {\\n            \\\"device\\\": \\\"systematic_enumeration\\\",\\n            \\\"frequency\\\": \\\"very_high\\\",\\n            \\\"typical_context\\\": \\\"organizing tactical advice into digestible steps\\\",\\n            \\\"example\\\": \\\"My 10 favorite email CTAs. 1. Use anything like this today? 2. Ever come across us before?\\\"\\n          },\\n          {\\n            \\\"device\\\": \\\"binary_framework\\\",\\n            \\\"frequency\\\": \\\"high\\\",\\n            \\\"typical_context\\\": \\\"creating clear dos and don'ts\\\",\\n            \\\"example\\\": \\\"Always: -Confirm you are vendor of choice Never: -Negotiate before the 1-yard line\\\"\\n          },\\n          {\\n            \\\"device\\\": \\\"sports_military_metaphors\\\",\\n            \\\"frequency\\\": \\\"moderate\\\",\\n            \\\"typical_context\\\": \\\"making tactical advice memorable\\\",\\n            \\\"example\\\": \\\"Like a great NFL team, I always script my first few plays\\\"\\n          }\\n        ],\\n        \\\"overall_persuasion_style_summary\\\": \\\"Authority through systematic organization and proven tactical specificity\\\",\\n        \\\"argument_construction_pattern\\\": \\\"Problem identification \\u2192 systematic framework \\u2192 specific examples \\u2192 implementation guidance\\\"\\n      },\\n      \\\"6_structural_emphasis_techniques\\\": {\\n        \\\"text_formatting_for_emphasis\\\": [\\\"numbered lists\\\", \\\"bold headers\\\", \\\"bullet points\\\", \\\"dashes for sub-points\\\"],\\n        \\\"use_of_standalone_impact_phrases\\\": {\\n          \\\"frequency\\\": \\\"high\\\",\\n          \\\"typical_length_words\\\": \\\"2-5 words\\\",\\n          \\\"typical_placement\\\": \\\"introducing frameworks or summarizing key points\\\",\\n          \\\"example\\\": \\\"No fluff today. Just plug and play to use.\\\"\\n        },\\n        \\\"emphasis_patterns\\\": [\\n          {\\n            \\\"technique\\\": \\\"bold_framework_headers\\\",\\n            \\\"example\\\": \\\"Always: / Never:\\\"\\n          },\\n          {\\n            \\\"technique\\\": \\\"numbered_systematic_progression\\\",\\n            \\\"example\\\": \\\"1. Ultra-prepared and scripted POV\\\\n2. Summarize what they said more concisely\\\"\\n          }\\n        ]\\n      },\\n      \\\"7_linguistic_dos\\\": [\\n        {\\n          \\\"rule\\\": \\\"Use numbered lists and systematic organization for complex processes\\\",\\n          \\\"priority\\\": 10,\\n          \\\"example\\\": \\\"My 10 favorite email CTAs. 1. Use anything like this today? 2. Ever come across us before?\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Provide specific, implementable examples for each framework element\\\",\\n          \\\"priority\\\": 9,\\n          \\\"example\\\": \\\"Example: 'Thanks for sharing - to make sure I understand, the team is moving upmarket'\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Use imperative voice for direct tactical instruction\\\",\\n          \\\"priority\\\": 8,\\n          \\\"example\\\": \\\"Confirm you are vendor of choice\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Include sports or tactical metaphors to make frameworks memorable\\\",\\n          \\\"priority\\\": 7,\\n          \\\"example\\\": \\\"Like a great NFL team, I always script my first few plays\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"End with clear implementation guidance or next steps\\\",\\n          \\\"priority\\\": 8,\\n          \\\"example\\\": \\\"No fluff today. Just plug and play to use.\\\"\\n        }\\n      ],\\n      \\\"8_linguistic_donts\\\": [\\n        {\\n          \\\"rule\\\": \\\"Don't provide frameworks without specific implementation examples\\\",\\n          \\\"priority\\\": 9,\\n          \\\"rationale\\\": \\\"Tactical advice must be immediately actionable to maintain credibility\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't use theoretical language when giving practical advice\\\",\\n          \\\"priority\\\": 8,\\n          \\\"rationale\\\": \\\"Maintains focus on implementable tactics rather than abstract concepts\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't mix personal storytelling with systematic instruction\\\",\\n          \\\"priority\\\": 7,\\n          \\\"rationale\\\": \\\"Keeps focus on the framework rather than personal narrative\\\"\\n        },\\n        {\\n          \\\"rule\\\": \\\"Don't use overly long paragraphs for step-by-step processes\\\",\\n          \\\"priority\\\": 8,\\n          \\\"rationale\\\": \\\"Each step needs clear separation for easy implementation\\\"\\n        }\\n      ],\\n      \\\"9_mid_body_reader_engagement_linguistics\\\": {\\n        \\\"rhetorical_questions_for_reflection_style\\\": {\\n          \\\"frequency\\\": \\\"low\\\",\\n          \\\"typical_placement\\\": \\\"rare, only for framework validation\\\",\\n          \\\"example\\\": \\\"Anything else you'd recommend?\\\"\\n        },\\n        \\\"direct_address_to_reader_style\\\": {\\n          \\\"frequency\\\": \\\"high\\\",\\n          \\\"typical_phrasing\\\": \\\"direct imperative instructions\\\",\\n          \\\"example\\\": \\\"Use imperative voice for direct tactical instruction\\\"\\n        },\\n        \\\"anticipation_building_phrasing_style\\\": \\\"Systematic step progression creates natural anticipation\\\",\\n        \\\"validation_or_empathy_cue_usage\\\": \\\"Minimal - focus on expertise transfer rather than emotional connection\\\"\\n      },\\n      \\\"10_transition_techniques\\\": {\\n        \\\"between_paragraphs\\\": {\\n          \\\"dominant_style\\\": \\\"numerical progression and logical sequencing\\\",\\n          \\\"common_transition_phrases\\\": [\\\"Step 1:\\\", \\\"Next:\\\", \\\"2.\\\", \\\"Here's where\\\"]\\n        },\\n        \\\"between_sections\\\": {\\n          \\\"dominant_style\\\": \\\"clear section headers and formatting breaks\\\",\\n          \\\"examples\\\": [\\\"Always:\\\", \\\"Never:\\\", \\\"Why it works:\\\"]\\n        },\\n        \\\"hook_to_body_transition\\\": {\\n          \\\"typical_pattern\\\": \\\"framework introduction \\u2192 systematic breakdown\\\",\\n          \\\"examples\\\": [\\\"My 10 favorite email CTAs. 1.\\\", \\\"Here's my always / never rules\\\"]\\n        },\\n        \\\"body_to_ending_transition\\\": {\\n          \\\"typical_pattern\\\": \\\"framework completion \\u2192 implementation encouragement\\\",\\n          \\\"examples\\\": [\\\"No fluff today. Just plug and play to use.\\\", \\\"Let's get it!\\\"]\\n        },\\n        \\\"pacing_variation\\\": \\\"Consistent systematic pace with occasional motivational punctuation\\\"\\n      },\\n      \\\"11_contextual_application_rules\\\": [\\n        {\\n          \\\"when\\\": \\\"Teaching specific sales frameworks or processes\\\",\\n          \\\"apply\\\": \\\"Use numbered lists, provide specific examples, include implementation guidance\\\",\\n          \\\"avoid\\\": \\\"Theoretical language or personal storytelling\\\",\\n          \\\"example\\\": \\\"My 10 favorite email CTAs [followed by numbered specific examples]\\\"\\n        },\\n        {\\n          \\\"when\\\": \\\"Sharing tactical advice or best practices\\\",\\n          \\\"apply\\\": \\\"Use imperative voice, organize systematically, provide clear dos/don'ts\\\",\\n          \\\"avoid\\\": \\\"Vague generalizations or emotional appeals\\\",\\n          \\\"example\\\": \\\"Always: -Confirm you are vendor of choice Never: -Negotiate before the 1-yard line\\\"\\n        },\\n        {\\n          \\\"when\\\": \\\"Breaking down complex sales processes\\\",\\n          \\\"apply\\\": \\\"Use step-by-step progression, include specific dialogue examples, reference proven methodologies\\\",\\n          \\\"avoid\\\": \\\"Overwhelming detail in single paragraphs\\\",\\n          \\\"example\\\": \\\"Here's how I start all discovery calls now. 1. Ultra-prepared and scripted POV [with specific example]\\\"\\n        }\\n      ],\\n      \\\"12_overall_linguistic_intent_summary\\\": \\\"Establish authority through systematic knowledge transfer, enabling immediate tactical implementation while maintaining clear, organized presentation that builds confidence through specific, proven methodologies\\\"\\n    }\"\n\nPATTERN USAGE INSTRUCTIONS:\n- **Body Frameworks**: Use for STRUCTURE - organize your content using these frameworks\n- **Linguistic Patterns**: Use for PROSE STYLE - apply these writing techniques to improve engagement\n\n\nUSER CONTENT SPECIFICATIONS (adapt body to meet these requirements):\n- Target Length: 150-200 words\n\nWORD COUNT INFORMATION:\nHook word count: 0 words\n\nWORD COUNT GUIDANCE:\n**Important**: The target length in USER CONTENT SPECIFICATIONS is for the ENTIRE post (hook + body + ending). \n- Hook contains: 0 words\n- Calculate: body_words_needed = (total_target - 0 - estimated_ending_words)\n- Endings are typically 20-40 words\n- Generate your body content to match body_words_needed (DO NOT exceed this target by more than 10%)\n\nCRITICAL: LinkedIn users prefer concise content. After calculating body_words_needed, aim for the LOWER end of that range. Quality over quantity - every word must earn its place.\n\nYour tasks:\n1.  Review the PREVIOUS STEP SUMMARY from hook creation to understand how the hook was constructed and how it should influence your body content.\n2.  Select the most effective BODY FRAMEWORK from the provided patterns for STRUCTURE that fits the user's input, the chosen theme, and follows naturally from the hook approach.\n3.  Select complementary LINGUISTIC PATTERNS for PROSE STYLE that will make your writing more engaging and match the content tone.\n4.  Develop the main content for the post, expanding on the hook and aligning with the selected theme.\n5.  As you write, apply both the structural framework AND the linguistic style patterns. Use frameworks for organization, linguistic patterns for compelling prose.\n\n **HANDLING SPECIFIC EXAMPLES, DATA, OR CASE STUDIES:**\n    -   Your primary role is to structure and stylize the information present in the cached user brief.\n    -   If the chosen BODY FRAMEWORK requires a specific example, statistic, case study detail, or quantifiable result (e.g., \"One agency I work with cut their time by X%...\") AND this specific detail is NOT explicitly provided in the USER INPUT:\n        -   **DO NOT invent or fabricate these details.**\n        -   Instead, **insert a clear, descriptive placeholder** in the text where the user should provide their own specific information.\n        -   **Placeholder Format:** Use bracketed, capitalized text that clearly indicates what information is needed. Examples:\n            -   `[INSERT SPECIFIC % EFFICIENCY GAIN AND TIMEFRAME FOR AGENCY EXAMPLE HERE]`\n            -   `[USER TO ADD: CONCRETE EXAMPLE OF AN AGENCY THAT SUCCEEDED WITH PARTIAL AI IMPLEMENTATION, INCLUDING 1-2 KEY METRICS]`\n            -   `[PROVIDE YOUR REAL EXAMPLE: Agency Name/Type, Initial Hours, Hours After AI, Key AI Tools Used]`\n            -   `\"One agency I work with [DESCRIBE THE AGENCY BRIEFLY, e.g., 'a mid-sized marketing agency'] recently [DESCRIBE THE ACHIEVEMENT, e.g., 'slashed their campaign development time'] from approximately [OLD METRIC, e.g., '30 hours'] to around [NEW METRIC, e.g., '10 hours'] by focusing on [BRIEF STRATEGY USER SHOULD VALIDATE/REPLACE].\"` (This last one is more guided).\n    -   If the cached user brief *does* contain relevant specific data or examples, integrate them naturally into the structured body.\n\n6.  Create an impactful body that naturally leads to an engaging ending.\n7.  The generated body should be well-integrated with the provided hook and set up a strong conclusion.\n\nFormat your response as:\nREASONING: [Your detailed thought process for selecting the body framework AND linguistic patterns. Explain which specific structural framework you chose and why, plus which linguistic patterns you are applying for prose style. Reference how the previous step summary from hook creation influenced your approach.]\nSELECTION: [Name of the selected body framework pattern AND key linguistic patterns applied.]\nDECISION SUMMARY: [Provide a concise 1-2 sentence summary of your key decisions about body structure and linguistic style that will guide the next step (ending creation). This should capture the essence of how you've structured the body and the key linguistic patterns used, which should influence how the ending is crafted. This summary will be directly provided to the ending creation step.]\nOUTPUT: [The actual body text, using both the structural framework and linguistic style patterns.]\n"}]}}, {"type": "assistant", "content": {"type": "usage", "content": {"cache_creation_input_tokens": 2252, "cache_read_input_tokens": 0, "input_tokens": 10859, "output_tokens": 1162, "service_tier": "standard"}}}]