"""<PERSON><PERSON><PERSON> to run the LinkedInsight API server."""

import argparse
import uvicorn
import os
import sys
from pathlib import Path

# Add project root to system path
root_dir = Path(__file__).parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Database initialization removed - using MongoDB now

# Import the FastAPI app for Gunicorn
from src.api.main import app as fastapi_app

# Create a WSGI app that Gun<PERSON> can use
app = fastapi_app

def main():
    parser = argparse.ArgumentParser(description="Run the LinkedInsight API server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind the server to (default: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=int(os.getenv("PORT", 8000)), help="Port to bind the server to (default: 8000)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")

    args = parser.parse_args()

    # Set environment variables if needed
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("Warning: ANTHROPIC_API_KEY environment variable is not set.")
        print("API endpoints that require Anthropic API will not work.")

    print(f"Starting LinkedInsight API server on http://{args.host}:{args.port}")
    print("Press Ctrl+C to stop the server")

    # Run the API server
    uvicorn.run(
        "src.api.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload
    )

if __name__ == "__main__":
    main()