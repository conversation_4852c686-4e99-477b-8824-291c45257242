{"rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "buildCommand": "cd linkedinsight-viewer && npm install && npm run build", "outputDirectory": "linkedinsight-viewer/dist"}