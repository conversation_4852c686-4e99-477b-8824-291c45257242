import os
import glob
import re
import json
from datetime import datetime, timezone
from pymongo import MongoClient, UpdateOne
from pymongo.errors import ConnectionFailure, OperationFailure
import sys # For path manipulation
from pathlib import Path # For path manipulation

# Add project root to system path to allow importing src.config
# This assumes migrate_llm_analyses_to_mongo.py is in the project root /Users/<USER>/LinkedInsight/
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path: # Ensure it's not added multiple times if script structure changes
    sys.path.insert(0, str(project_root))

try:
    from src.config import Config
except ModuleNotFoundError as e:
    print(f"Error: Could not import Config from src.config. Original error: {e}")
    print("Attempted to add project root to sys.path. This script expects to be in the project root directory.")
    print(f"Current sys.path: {sys.path}")
    print(f"Calculated project_root: {project_root}")
    print("Please ensure src/config.py exists and the Python path is correctly configured.")
    sys.exit(1)
except ImportError as e:
    print(f"Error: ImportError when trying to import Config from src.config. Original error: {e}")
    print("This might indicate an issue within src.config or its dependencies, or a path problem.")
    sys.exit(1)

# --- Configuration ---
BASE_ANALYSIS_DIR = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/" # Single base directory
MONGO_COLLECTION_NAME = "LLM_friendly_analyses"

JSON_ANALYSIS_TYPES = ["themes", "hook", "linguistic", "ending"] # Corrected to singular
MARKDOWN_ANALYSIS_TYPES = ["body"] # Currently just "body"

JSON_FILENAME_PATTERN = re.compile(r"(.+?)_(" + "|".join(JSON_ANALYSIS_TYPES) + r")_analysis_json_(\d{14})\.json")
MD_FILENAME_PATTERN = re.compile(r"(.+?)_body_llm_friendly_analysis_(\d{14})\.md")

def get_latest_file(directory_path, creator_name, analysis_type):
    latest_file = None
    latest_timestamp = ""
    if analysis_type in JSON_ANALYSIS_TYPES:
        pattern_to_glob = f"{creator_name}_{analysis_type}_analysis_json_*.json"
        regex_pattern = JSON_FILENAME_PATTERN 
        timestamp_group_index = 3 
    elif analysis_type == "body":
        pattern_to_glob = f"{creator_name}_body_llm_friendly_analysis_*.md"
        regex_pattern = MD_FILENAME_PATTERN
        timestamp_group_index = 2
    else:
        # This case should ideally not be hit if all_analysis_types is well-defined
        print(f"      Warning: Unknown analysis type '{analysis_type}' for {creator_name} in get_latest_file. Skipping search.")
        return None

    full_glob_path = os.path.join(directory_path, pattern_to_glob)
    files_found_by_glob = glob.glob(full_glob_path)
    if not files_found_by_glob:
      return None # No files found matching the glob pattern

    processed_one_file_flag = False
    for filepath in files_found_by_glob:
        processed_one_file_flag = True
        filename = os.path.basename(filepath)
        match = regex_pattern.match(filename)
        if match:
            file_creator = match.group(1)
            file_timestamp = match.group(timestamp_group_index)
            if file_creator == creator_name: 
                if file_timestamp > latest_timestamp:
                    latest_timestamp = file_timestamp
                    latest_file = filepath
        else:
            print(f"      Warning: Filename '{filename}' in {directory_path} did not match expected regex pattern for {analysis_type}.")
    
    if processed_one_file_flag and not latest_file:
        print(f"      Warning: Glob found files for pattern '{pattern_to_glob}' but regex matching or creator check failed for all of them in {directory_path}.")

    return latest_file

def main():
    print("Starting LLM-friendly analysis migration to MongoDB script (Corrected Single Source)...")
    mongodb_uri = None
    actual_db_name = None
    try:
        mongodb_uri = Config.get_mongodb_uri()
        uri_parts = mongodb_uri.split('/')
        db_name_from_uri = ""
        if len(uri_parts) >= 4:
            db_name_candidate = uri_parts[3]
            if '?' in db_name_candidate:
                db_name_from_uri = db_name_candidate.split('?')[0]
            else:
                db_name_from_uri = db_name_candidate
        actual_db_name = db_name_from_uri if db_name_from_uri else "linkedinsight"
        client = MongoClient(mongodb_uri)
        client.admin.command('ping') 
        db = client[actual_db_name]
        collection = db[MONGO_COLLECTION_NAME]
        print(f"Successfully connected to MongoDB: {mongodb_uri}, DB: {actual_db_name}, Collection: {MONGO_COLLECTION_NAME}")
    except ValueError as ve: 
        print(f"Configuration error: {ve}. Please ensure MONGODB_URI is set in your .env file.")
        return
    except ConnectionFailure:
        print(f"Failed to connect to MongoDB at {mongodb_uri}. Please check your connection string and ensure MongoDB is running.")
        return
    except Exception as e:
        print(f"An unexpected error occurred during MongoDB connection: {e}")
        return

    operations = [] 

    if not os.path.exists(BASE_ANALYSIS_DIR):
        print(f"Error: Base analysis directory not found: {BASE_ANALYSIS_DIR}")
        return

    creator_dirs = [d for d in os.listdir(BASE_ANALYSIS_DIR) if os.path.isdir(os.path.join(BASE_ANALYSIS_DIR, d))]
    if not creator_dirs: 
        print(f"No creator directories found in {BASE_ANALYSIS_DIR}.")
    
    for creator_name in creator_dirs:
        creator_path = os.path.join(BASE_ANALYSIS_DIR, creator_name)
        print(f"\nProcessing creator: {creator_name} in {creator_path}")

        all_analysis_types = JSON_ANALYSIS_TYPES + MARKDOWN_ANALYSIS_TYPES

        for analysis_type in all_analysis_types:
            print(f"  Looking for {analysis_type} analysis in {creator_path}...")
            latest_file_path = get_latest_file(creator_path, creator_name, analysis_type)

            if latest_file_path:
                print(f"    Found latest file: {os.path.basename(latest_file_path)}")
                file_content_for_db = None
                source_timestamp_str = ""
                analysis_datetime = datetime.now(timezone.utc)

                filename_for_ts_extraction = os.path.basename(latest_file_path)
                if analysis_type in JSON_ANALYSIS_TYPES:
                    match_ts = JSON_FILENAME_PATTERN.match(filename_for_ts_extraction)
                    if match_ts: source_timestamp_str = match_ts.group(3)
                elif analysis_type == "body":
                    match_ts = MD_FILENAME_PATTERN.match(filename_for_ts_extraction)
                    if match_ts: source_timestamp_str = match_ts.group(2)
                
                if source_timestamp_str:
                    try:
                        dt_naive = datetime.strptime(source_timestamp_str, "%Y%m%d%H%M%S")
                        analysis_datetime = dt_naive.replace(tzinfo=timezone.utc)
                    except ValueError:
                        print(f"      Warning: Could not parse timestamp '{source_timestamp_str}' from filename '{filename_for_ts_extraction}'. Using current UTC.")
                else:
                    print(f"      Warning: Could not extract timestamp from filename '{filename_for_ts_extraction}'. Using current UTC.")

                try:
                    with open(latest_file_path, 'r', encoding='utf-8') as f:
                        if analysis_type in JSON_ANALYSIS_TYPES: file_content_for_db = json.load(f)
                        elif analysis_type in MARKDOWN_ANALYSIS_TYPES: file_content_for_db = f.read()
                except json.JSONDecodeError as e:
                    print(f"      Error decoding JSON from file {latest_file_path}: {e}")
                    continue 
                except Exception as e:
                    print(f"      Error reading file {latest_file_path}: {e}")
                    continue

                if file_content_for_db is not None:
                    mongo_document_set_part = {
                        "creator_name": creator_name, "analysis_type": analysis_type,
                        "llm_analysis_data": file_content_for_db, "source_file_path": latest_file_path,
                        "analysis_timestamp": analysis_datetime, "version": 1, 
                        "migrated_at": datetime.now(timezone.utc)
                    }
                    operations.append(
                        UpdateOne({"creator_name": creator_name, "analysis_type": analysis_type},
                                  {"$set": mongo_document_set_part, "$setOnInsert": {"initial_migration_at": datetime.now(timezone.utc)}},
                                  upsert=True))
                    print(f"      Prepared upsert for {analysis_type} for {creator_name}")
            else:
                print(f"    No {analysis_type} file found for {creator_name} in {creator_path}.")

    if operations:
        print(f"\nAttempting to write {len(operations)} documents to MongoDB...")
        try:
            result = collection.bulk_write(operations)
            print(f"  Bulk write successful: Upserted IDs: {result.upserted_ids}, Matched: {result.matched_count}, Modified: {result.modified_count}")
        except OperationFailure as e:
            print(f"  MongoDB bulk write error: {e.details}")
        except Exception as e:
            print(f"  An unexpected error occurred during MongoDB bulk write: {e}")
    else:
        print("\nNo operations to perform for MongoDB.")

    try:
        client.close()
        print("\nMongoDB connection closed.")
    except Exception as e:
        print(f"Error closing MongoDB connection: {e}")
        
    print("Migration script finished.")

if __name__ == "__main__":
    main() 