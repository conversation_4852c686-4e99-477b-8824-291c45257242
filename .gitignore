# System files
.DS_Store

# Environment files
.env
.venv/
__pycache__/
*.pyc
.specstory/

# Node.js files
/node_modules/
/package.json
/package-lock.json

# Generated output directories
/outputs/
/@outputs/
/test_output/
/LLM_friendly_Analyses/
# Exclude specific directories from being ignored
!/outputs/LLM_friendly_Analyses/
# /data/  # Removed to include data directory
# !/data/db/  # Removed exclusion
# !/data/db/linkedin_content.db  # Removed exclusion

# Database files
*.db
*.sqlite
*.sqlite3

# MongoDB dumps
dump/
# /docs/  # Removed to include documentation
/frontend/

# Rules and config files
/.roo
/.cursor
/.roomodes
/.taskmasterconfig
/.windsurfrules

# Generated output files
*_analysis*.md
*_analysis*.html
*_analysis*.json
*_themes*.md
*_themes*.json
*_report*.html
*_raw_response.txt
*bundle_discovery_analysis*.txt
*bundle_discovery_analysis*.json
bundle_discovery_batch_summary_*.json
*_metadata.yaml
temp_*.md
temp_*.txt
temp_*.json

# Temporary test result files
*_test.json
*_sample.json
*_bundles.json

# But allow these specific files
!*_overview_*.md
!*_themes_*.md
!*_hooks_*.md
!*_body_*.md
!*_endings_*.md
!*_linguistic_*.md
!*_analysis_v2_*.md

# Keep important files
!README.md
!hook_analysis_spec.md
!linguistic_analysis_prompt.md
!LLM_JSON_hook_analysis_prompt.md
!LLM_JSON_linguistic_analysis_prompt.md
!LLM_JSON_ending_analysis_prompt.md
!LLM_JSON_themes_analysis_prompt.md
!LLM_JSON_system_prompt.txt
!report_template.html
!template.html

# Keep source code files
!*.py
!*.txt
!*.css

# Added by Claude Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

# bson
*.bson 

#backup
dump/