#!/usr/bin/env python3
"""
Script to generate Creator Style Card summaries for LinkedInsight.

This script extracts information from MongoDB analyses and generates
JSON data for Creator Style Cards to be used in the frontend.
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional
import argparse
from datetime import datetime

# Import Pydantic model for function calling
from src.models.card_summary_schema import CreatorStyleCard

# Add the src directory to the Python path
src_dir = os.path.dirname(os.path.abspath(__file__))
if src_dir not in sys.path:
    sys.path.append(src_dir)

# Import project modules
from src.utils.mongo_retrieval import (
    fetch_latest_markdown_content,
    fetch_latest_json_content,
    get_all_creators
)
from src.utils.anthropic_client import AnthropicClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define the prompts directory
PROMPTS_DIR = "prompts"

# Output directory will be set dynamically based on the creator and timestamp

def load_prompt_template(filename: str) -> str:
    """Load a prompt template from a file."""
    filepath = os.path.join(PROMPTS_DIR, filename)
    try:
        with open(filepath, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading prompt template {filepath}: {e}")
        return ""

async def extract_analysis_summaries(creator_name: str) -> Dict[str, Any]:
    """
    Extract concise summaries from various analysis files for a creator.

    Args:
        creator_name: The name of the creator

    Returns:
        A dictionary containing summaries of themes, linguistic patterns, and structural patterns
    """
    logger.info(f"Extracting analysis summaries for {creator_name}")

    # Initialize summaries
    summaries = {
        "theme_summary": "",
        "linguistic_summary": "",
        "structural_summary": ""
    }

    # 1. Extract theme summary from overview_analysis.json and themes_analysis.md
    overview_json = await fetch_latest_json_content(creator_name, "overview")
    themes_md = fetch_latest_markdown_content(creator_name, "themes")

    if overview_json:
        # Extract top themes from overview_analysis.json
        themes = overview_json.get("themes", [])
        if themes:
            # Sort themes by prominence (if available)
            sorted_themes = sorted(
                themes,
                key=lambda x: x.get("prominence", 0),
                reverse=True
            )
            # Take top 5 themes
            top_themes = sorted_themes[:5]
            theme_names = [theme.get("name", "") for theme in top_themes]
            summaries["theme_summary"] = "Top themes: " + ", ".join(theme_names)

    if themes_md:
        # Add more detailed theme information if available
        summaries["theme_summary"] += "\n\n" + themes_md

    # 2. Extract linguistic summary from linguistic_analysis.md
    linguistic_md = fetch_latest_markdown_content(creator_name, "linguistic")
    if linguistic_md:
        summaries["linguistic_summary"] = linguistic_md

    # 3. Extract structural patterns from hook, body, and ending analyses
    hook_md = fetch_latest_markdown_content(creator_name, "hooks")
    body_md = fetch_latest_markdown_content(creator_name, "body")
    endings_md = fetch_latest_markdown_content(creator_name, "endings")

    structural_summary = ""
    if hook_md:
        structural_summary += "HOOK PATTERNS:\n" + hook_md + "\n\n"
    if body_md:
        structural_summary += "BODY PATTERNS:\n" + body_md + "\n\n"
    if endings_md:
        structural_summary += "ENDING PATTERNS:\n" + endings_md

    summaries["structural_summary"] = structural_summary

    return summaries

async def generate_style_card(creator_name: str, summaries: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate a Creator Style Card using the Anthropic API with function calling.

    Args:
        creator_name: The name of the creator
        summaries: Dictionary containing analysis summaries

    Returns:
        A dictionary containing the Creator Style Card data
    """
    logger.info(f"Generating style card for {creator_name}")

    # Load the system prompt
    system_prompt = load_prompt_template("generate_creator_card_summary_system_prompt.txt")

    # Load and format the user prompt
    user_prompt_template = load_prompt_template("generate_creator_card_summary_user_prompt.txt")
    user_prompt = user_prompt_template.format(
        creator_name=creator_name,
        theme_summary=summaries['theme_summary'],
        linguistic_summary=summaries['linguistic_summary'],
        structural_summary=summaries['structural_summary']
    )

    # Create tool definition from Pydantic model
    schema = CreatorStyleCard.model_json_schema()
    logger.info(f"Schema being sent to Claude: {schema}")

    tool_definition = {
        "name": "generate_creator_style_card",
        "description": "Generate a style card for a LinkedIn creator based on analysis of their content",
        "input_schema": schema
    }

    # Call the Anthropic API with function calling
    client = AnthropicClient()
    try:
        # Force Claude to use the tool by setting tool_choice
        tool_choice = {
            "type": "tool",
            "name": "generate_creator_style_card"
        }

        response = await client.call_with_functions(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            tools=[tool_definition],
            tool_choice=tool_choice,  # Add tool_choice to force tool use
            max_tokens=4000,
            temperature=1.0
            # Removed thinking parameter
        )

        # Process the response
        # Add debugging to see the structure of the response
        logger.info(f"Response content type: {type(response.content)}")
        if response.content:
            logger.info(f"First content block type: {type(response.content[0])}")
            logger.info(f"First content block attributes: {dir(response.content[0])}")

        if response.content and response.content[0].type == "tool_use":
            # Get the function call parameters
            tool_use_block = response.content[0]
            logger.info(f"Tool use block attributes: {dir(tool_use_block)}")

            # Try to access the name directly from the tool_use_block
            try:
                tool_name = tool_use_block.name
                logger.info(f"Tool name: {tool_name}")

                # Try to access the input directly from the tool_use_block
                tool_input = tool_use_block.input
                logger.info(f"Tool input: {tool_input}")

                if tool_name == "generate_creator_style_card":
                    # Parse the function call parameters
                    style_card = tool_input

                    # Convert string fields to arrays if they're not already
                    for field in ['top_themes', 'voice_keywords', 'signature_styles']:
                        if field in style_card and isinstance(style_card[field], str):
                            # Split by comma and strip whitespace
                            style_card[field] = [item.strip() for item in style_card[field].split(',')]

                    return style_card
            except AttributeError as e:
                logger.error(f"Error accessing tool attributes: {e}")
                # Try alternative attribute names or structures here

        # If we didn't get a valid function call, log an error and return an empty card
        logger.error(f"No valid function call in response for {creator_name}")
        return create_empty_style_card(creator_name)
    except Exception as e:
        logger.error(f"Error calling Anthropic API for {creator_name}: {e}")
        return create_empty_style_card(creator_name)

def create_empty_style_card(creator_name: str) -> Dict[str, Any]:
    """Create an empty style card for a creator."""
    return {
        "creator_name": creator_name,
        "top_themes": [],
        "voice_keywords": [],
        "signature_styles": [],
        "ideal_for_topics": ""
    }

async def process_creator(creator_name: str) -> Optional[Dict[str, Any]]:
    """
    Process a single creator and generate their style card.

    Returns:
        The style card dictionary if successful, None if generation failed
    """
    try:
        # Extract summaries from analyses
        summaries = await extract_analysis_summaries(creator_name)

        # Generate style card
        style_card = await generate_style_card(creator_name, summaries)

        # Check if we got a valid style card (not an empty one)
        if style_card and style_card.get("top_themes") and style_card.get("voice_keywords"):
            logger.info(f"Successfully generated style card for {creator_name}")
            return style_card
        else:
            logger.warning(f"Generated style card for {creator_name} is incomplete or empty")
            return None
    except Exception as e:
        logger.error(f"Error processing creator {creator_name}: {e}")
        return None

async def process_all_creators() -> List[Dict[str, Any]]:
    """
    Process all creators and generate style cards for each.

    Returns:
        A list of successfully generated style cards
    """
    # Get all creators
    creators = get_all_creators()
    creator_names = [creator.get("creator_name") for creator in creators]

    # Process each creator
    style_cards = []
    successful_count = 0
    failed_count = 0

    for creator_name in creator_names:
        style_card = await process_creator(creator_name)
        if style_card:
            style_cards.append(style_card)
            successful_count += 1
        else:
            failed_count += 1

    logger.info(f"Processed {len(creator_names)} creators: {successful_count} successful, {failed_count} failed")
    return style_cards

async def save_style_cards(style_cards: List[Dict[str, Any]], creator_name: str = None) -> Optional[str]:
    """
    Save style cards to JSON file and to the frontend mocks directory.

    Args:
        style_cards: List of style card dictionaries
        creator_name: Name of the creator (for single creator mode)

    Returns:
        Path to the saved file, or None if no cards were saved
    """
    # Check if there are any cards to save
    if not style_cards:
        logger.warning("No style cards to save")
        return None

    # Create a timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    # Set up the output directory and filename for the test output
    output_dir = "test_output"

    if creator_name:
        # Single creator mode
        filename = f"{creator_name}_style_card_{timestamp}.json"
    else:
        # Multiple creators mode
        filename = f"all_creators_style_cards_{timestamp}.json"

    output_file = os.path.join(output_dir, filename)

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create output JSON
    output_json = {"cards": style_cards}

    # Write to test output file
    with open(output_file, 'w') as f:
        json.dump(output_json, f, indent=2)

    logger.info(f"Saved {len(style_cards)} style cards to {output_file}")

    # Also save to the frontend mocks directory
    frontend_mocks_dir = os.path.join("linkedinsight-viewer", "src", "mocks")
    frontend_file = os.path.join(frontend_mocks_dir, "creatorStyleCards.json")

    try:
        # Check if the frontend mocks directory exists
        if not os.path.exists(frontend_mocks_dir):
            logger.warning(f"Frontend mocks directory not found at: {frontend_mocks_dir}")
            return output_file

        # If we're updating a single creator, we need to merge with existing data
        if creator_name and os.path.exists(frontend_file):
            try:
                # Read existing style cards
                with open(frontend_file, 'r') as f:
                    existing_data = json.load(f)

                # Get existing cards
                existing_cards = existing_data.get("cards", [])

                # Remove the creator's card if it exists
                existing_cards = [card for card in existing_cards if card.get("creator_name") != creator_name]

                # Add the new card
                existing_cards.extend(style_cards)

                # Update the output JSON
                output_json = {"cards": existing_cards}

                logger.info(f"Merged new style card for {creator_name} with existing data")
            except Exception as e:
                logger.error(f"Error reading existing style cards: {e}")
                # Continue with just the new cards

        # Write to frontend mocks file
        with open(frontend_file, 'w') as f:
            json.dump(output_json, f, indent=2)

        logger.info(f"Saved {len(style_cards)} style cards to frontend mocks: {frontend_file}")

        return output_file
    except Exception as e:
        logger.error(f"Error saving to frontend mocks: {e}")
        return output_file

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Generate Creator Style Card Summaries')
    parser.add_argument('--creator', type=str, help='Process a specific creator')
    parser.add_argument('--all', action='store_true', help='Process all creators')
    args = parser.parse_args()

    if args.creator:
        # Process a single creator
        creator_name = args.creator
        style_card = await process_creator(creator_name)

        if style_card:
            output_file = await save_style_cards([style_card], creator_name)
            print(f"\nStyle card saved to: {output_file}")
        else:
            print(f"\nFailed to generate style card for {creator_name}. No file saved.")

    elif args.all:
        # Process all creators
        style_cards = await process_all_creators()

        if style_cards:
            output_file = await save_style_cards(style_cards)
            print(f"\nStyle cards saved to: {output_file}")
        else:
            print("\nNo style cards were successfully generated. No file saved.")

    else:
        parser.print_help()

if __name__ == "__main__":
    asyncio.run(main())
