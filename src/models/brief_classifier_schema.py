"""
Pydantic schemas for Brief Classifier component.

This module defines the Pydantic models used for structured output
when matching briefs to creators using the Anthropic API.
"""

from pydantic import BaseModel, Field
from typing import List

class CreatorRecommendation(BaseModel):
    """
    Pydantic model for creator recommendations based on a brief.

    This model defines the structure of the data returned by the LLM when
    matching a brief to the most suitable creators.
    """
    recommended_creators: List[str] = Field(
        description="Names of the recommended creators (e.g., ['retentionadam', 'joshlowman'])",
        min_items=0,
        max_items=2
    )

    reasoning: str = Field(
        description="A brief explanation of why these creators were selected, focusing on the match between the brief and the creators' style cards"
    )
