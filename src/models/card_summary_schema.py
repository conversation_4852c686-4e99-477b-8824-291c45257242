"""
Pydantic schemas for Creator Style Card generation.

This module defines the Pydantic models used for structured output
when generating Creator Style Cards with the Anthropic API.
"""

from pydantic import BaseModel, Field
from typing import List

class CreatorStyleCard(BaseModel):
    """
    Pydantic model for a Creator Style Card.

    This model defines the structure of the data returned by the LLM when
    generating a style card for a LinkedIn creator.
    """
    creator_name: str = Field(
        description="The name of the creator (e.g., 'retentionadam')"
    )

    top_themes: List[str] = Field(
        description="3-5 concise theme names that represent the creator's most common content topics",
        min_items=1,
        max_items=5
    )

    voice_keywords: List[str] = Field(
        description="3-4 descriptive keywords for the creator's voice and tone",
        min_items=1,
        max_items=4
    )

    signature_styles: List[str] = Field(
        description="2-4 extremely concise (2-3 words max) descriptors of structural/stylistic techniques used by the creator",
        min_items=1,
        max_items=4
    )

    ideal_for_topics: str = Field(
        description="A short sentence describing what kind of content this creator is best for emulating"
    )
