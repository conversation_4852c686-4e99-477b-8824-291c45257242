"""MongoDB storage utilities for LinkedInsight.

This module provides functions for saving analysis results to MongoDB.
"""
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import json
from bson.objectid import ObjectId

# Import project modules
from src.utils.mongo_utils_async import (
    get_async_mongo_db,
    ANALYSES_COLLECTION,
)

# Configure logging
logger = logging.getLogger(__name__)

async def save_analysis_content(creator_name: str, analysis_type: str, content: Any, is_json: bool = False, metadata: Dict[str, Any] = None) -> str:
    """
    Save analysis content (markdown or JSON) to MongoDB.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (overview, themes, etc.)
        content: The content to save (markdown text or JSON data)
        is_json: Whether the content is JSON data
        metadata: Additional metadata about the analysis

    Returns:
        The ID of the saved document as a string
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Convert analysis_type to MongoDB pattern
        db_analysis_type = f"{analysis_type}_{'json' if is_json else 'md'}"

        # Prepare document
        document = {
            "creator_name": creator_name,
            "analysis_type": db_analysis_type,
            "generated_at": datetime.now(),
            "content": content,
            "metadata": metadata or {}
        }

        # Insert document
        result = await db[ANALYSES_COLLECTION].insert_one(document)
        doc_id = str(result.inserted_id)

        logger.info(f"Saved {analysis_type} {'JSON' if is_json else 'Markdown'} for {creator_name} to MongoDB (ID: {doc_id})")
        return doc_id

    except Exception as e:
        logger.error(f"Error saving {analysis_type} content to MongoDB: {e}")
        return ""

async def get_document_by_id(doc_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a document by its ID from MongoDB.

    Args:
        doc_id: The MongoDB document ID

    Returns:
        The document as a dictionary, or None if not found
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query by ID
        document = await db[ANALYSES_COLLECTION].find_one({"_id": ObjectId(doc_id)})

        if not document:
            logger.warning(f"No document found with ID: {doc_id}")
            return None

        return document

    except Exception as e:
        logger.error(f"Error retrieving document by ID: {e}")
        return None