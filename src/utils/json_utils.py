"""
JSON utility functions for LinkedInsight.

This module provides utility functions for working with JSON data,
particularly for extracting JSON from text that may contain JSON in
various formats (raw JSON, JSON in markdown code blocks, etc.).

These utilities are used primarily by the analysis engine to process
API responses that contain JSON data.
"""
import json
import re
import logging
import os
from typing import Dict, Any, Optional

# Configure logging
logger = logging.getLogger(__name__)

def extract_json_from_text(text: str, debug_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Extract JSON data from text, handling various formats.

    This function tries multiple methods to extract valid JSON:
    1. Direct JSON parsing
    2. Extracting JSON from markdown code blocks

    Args:
        text: The text to extract JSO<PERSON> from
        debug_file: Optional path to save the raw text for debugging

    Returns:
        Extracted JSON data as a dictionary

    Raises:
        json.JSONDecodeError: If JSON extraction fails
    """
    # Save raw text for debugging if a file path is provided
    if debug_file:
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(debug_file), exist_ok=True)
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(text)
            logger.info(f"Raw text saved to: {debug_file}")
        except IOError as e:
            logger.error(f"Failed to save raw text to {debug_file}: {e}")

    # First try direct JSON parsing
    try:
        json_data = json.loads(text)
        logger.info("Successfully parsed JSON directly")
        return json_data
    except json.JSONDecodeError:
        # If direct parsing fails, try to extract JSON from a markdown code block
        json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
        if json_match:
            # Extract just the JSON part from the markdown code block
            json_content = json_match.group(1)
            logger.info("Extracted JSON from markdown code block")
            try:
                return json.loads(json_content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse extracted code block as JSON: {e}")
                # Don't raise here, continue trying other methods
        
        # Try to find JSON array or object anywhere in the text
        # Look for [...] or {...} patterns
        # Use DOTALL flag to make . match newlines
        json_array_match = re.search(r'\[[\s\S]*?\]', text, re.DOTALL)
        json_object_match = re.search(r'\{[\s\S]*?\}', text, re.DOTALL)
        
        if json_array_match:
            try:
                json_data = json.loads(json_array_match.group(0))
                logger.info("Successfully extracted JSON array from text")
                return json_data
            except json.JSONDecodeError:
                pass
        
        if json_object_match:
            try:
                json_data = json.loads(json_object_match.group(0))
                logger.info("Successfully extracted JSON object from text")
                return json_data
            except json.JSONDecodeError:
                pass

        # If we get here, no valid JSON was found
        logger.error("No valid JSON found in text")
        if debug_file:
            logger.error(f"Raw text saved to: {debug_file}")
        raise json.JSONDecodeError("No valid JSON found in text", text, 0)
