"""
Utility module for processing individual steps in the content generation pipeline.

This module contains functions for processing individual steps in the content generation
pipeline, including formatting prompts, calling the LLM, and parsing responses.
"""

import json
import logging
import re
from typing import Dict, Any, Optional

from src.utils.anthropic_client import AnthropicClient
from src.utils.api_cache_utils import call_with_agent_caching
from src.utils.scratchpad_logger import record_step
from src.config import STEP_MODEL_MAPPING
from src.utils.pipeline_debug_logger import debug_step
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

def format_user_preferences(preferences: Optional[Dict[str, Any]], post_length: Optional[str] = None, content_strategy: Optional[Dict[str, Any]] = None) -> str:
    """Format user preferences and content strategy for prompt injection.
    
    Args:
        preferences: User content preferences
        post_length: Desired post length
        content_strategy: User's content strategy (industry, target audience, etc.)
    
    Returns:
        Formatted string combining preferences and strategy
    """
    formatted = []
    
    # Handle per-post length selection (default to medium if not provided)
    effective_length = post_length or "medium"
    length_mapping = {
        "short": "150-200 words",
        "medium": "201-300 words", 
        "long": "301-500 words"
    }
    target_length = length_mapping.get(effective_length, effective_length)
    formatted.append(f"- Target Length: {target_length}")
    
    # Add content strategy if provided
    if content_strategy:
        try:
            from src.utils.strategy_utils import format_strategy_for_prompt
            strategy_section = format_strategy_for_prompt(content_strategy)
            if strategy_section:  # Only add if formatting succeeded
                formatted.append("\n" + strategy_section)
        except Exception as e:
            logger.warning(f"Failed to format content strategy: {e}")
            # Continue without strategy - graceful degradation
    
    # Add all preferences with clear labeling and AI guidance
    if preferences:
        formatted.append("\n<voice_preferences>")
        
        # Import definitions for AI guidance lookup
        try:
            from src.utils.content_preferences_definitions import CONTENT_PREFERENCES
        except ImportError:
            logger.warning("Could not import content preferences definitions, using values only")
            CONTENT_PREFERENCES = {}
        
        # Helper function to format preference with guidance
        def format_preference(tag_name: str, field_name: str, value: str) -> str:
            """Format a preference tag with AI guidance if available."""
            if not value:
                return ""
            
            # Look up AI guidance
            guidance = ""
            extra_attrs = ""
            if field_name in CONTENT_PREFERENCES and value in CONTENT_PREFERENCES[field_name]:
                pref_data = CONTENT_PREFERENCES[field_name][value]
                guidance = pref_data.get("ai_guidance", "")
                
                # Add special attributes for specific fields
                if field_name == "humor_style" and "humor_instances" in pref_data:
                    extra_attrs = f' instances="{pref_data["humor_instances"]}"'
                elif field_name == "emoji_usage" and "count" in pref_data:
                    extra_attrs = f' count="{pref_data["count"]}"'
            
            # Format with guidance if available, otherwise just the value
            if guidance:
                return f'  <{tag_name} value="{value}"{extra_attrs}>{guidance}</{tag_name}>'
            else:
                return f'  <{tag_name}>{value}</{tag_name}>'
        
        # Format each preference with its guidance
        if "tone_style" in preferences and preferences["tone_style"]:
            # Note: tone_style doesn't exist in our schema, skip it
            pass
        
        if "humor_style" in preferences:
            formatted.append(format_preference("humor", "humor_style", preferences["humor_style"]))
            
        if "opinion_style" in preferences:
            formatted.append(format_preference("opinion", "opinion_style", preferences["opinion_style"]))
            
        if "emoji_usage" in preferences:
            formatted.append(format_preference("emoji", "emoji_usage", preferences["emoji_usage"]))
            
        if "voice_distinctiveness" in preferences:
            formatted.append(format_preference("voice_distinctiveness", "voice_distinctiveness", preferences["voice_distinctiveness"]))
            
        if "brag_level" in preferences:
            formatted.append(format_preference("brag_level", "brag_level", preferences["brag_level"]))
            
        if "controversy_approach" in preferences:
            formatted.append(format_preference("controversy", "controversy_approach", preferences["controversy_approach"]))
            
        if "readability_style" in preferences:
            formatted.append(format_preference("readability", "readability_style", preferences["readability_style"]))
            
        formatted.append("</voice_preferences>")
    
    if not formatted:
        return "No specific user preferences provided - use creator's default patterns."
    
    return "\n".join(formatted)

async def process_step(
    step: str,
    input_text: str,
    analysis_data: Dict[str, Any],
    scratchpad: Dict[str, Any],
    prompt_template: str,
    system_prompt: str,
    session_id: Optional[str] = None,
    post_length: Optional[str] = None,
    draft_id: Optional[str] = None,
    session_state: Optional[Any] = None
) -> Dict[str, Any]:
    """
    Process a single step in the content generation pipeline using vector search for pattern selection.
    
    This function:
    1. Creates a filtered copy of the scratchpad that excludes full analyses
    2. Formats the appropriate prompt for this step (with specific handling for body step)
    3. Calls the LLM with the appropriate prompt and model
    4. Parses and returns the results

    The filtered scratchpad prevents duplicate sending of ALL analyses in every step,
    as each step already receives only the specific analysis data it needs.

    Args:
        step: The step to process (hook, body, ending)
        input_text: User's brief or draft post
        analysis_data: Analysis data for this step (legacy, usually empty with vector search)
        scratchpad: Current state of the generation process
        prompt_template: The prompt template to use
        system_prompt: The system prompt to use
        session_id: Unique identifier for this generation session (for logging)
        post_length: Desired post length (short, medium, long) - only used for body step

    Returns:
        Dictionary with reasoning, selection, output, and decision_summary
    """
    # Debug context for this step
    debug_context = {
        "step": step,
        "input_length": len(input_text),
        "post_length": post_length,
        "session_id": session_id
    }
    
    # Initialize variables used later
    hook_word_count = 0
    
    # Calculate hook word count for body step
    if step == "body" and "generation_pipeline" in scratchpad:
        hook_creation = scratchpad["generation_pipeline"].get("hook_creation", {})
        if hook_text := hook_creation.get("generated_hook_text"):
            hook_word_count = len(hook_text.split())
            logger.info(f"Calculated hook word count: {hook_word_count} words")
    
    # Wrap the entire step in debug logging
    with debug_step(step, debug_context):
        # Log scratchpad state before processing this step
        logger.info(f"Processing {step} step with vector search")
        
        # Log the analysis data size for debugging and monitoring
        analysis_size = len(json.dumps(analysis_data))
        logger.info(f"Analysis data size for {step} step: {analysis_size} bytes")
        
        # Apply pattern filtering to reduce context size
        if step in ["hook", "body", "ending"]:
            from src.utils.agent_utils.pattern_filter import filter_analysis_with_patterns
            
            try:
                filtered_analysis = await filter_analysis_with_patterns(
                    step=step,
                    analysis_data=analysis_data,
                    scratchpad=scratchpad,
                    input_text=input_text,
                    post_length=post_length,
                    use_pattern_selector=True,
                    draft_id=draft_id,
                    session_state=session_state
                )
                
                # Log the reduction
                filtered_size = len(json.dumps(filtered_analysis))
                reduction_pct = (1 - filtered_size / analysis_size) * 100 if analysis_size > 0 else 0
                logger.info(f"Pattern filtering reduced {step} analysis: {analysis_size} → {filtered_size} bytes ({reduction_pct:.0f}% reduction)")
                
                # Use filtered analysis
                analysis_data = filtered_analysis
                logger.info(f"Updated analysis_data for {step}: {json.dumps(analysis_data)[:500]}...")
                
            except Exception as e:
                # Log error but continue with full analysis
                logger.error(f"Pattern filtering failed for {step}, using full analysis: {e}")
                # Continue with unfiltered analysis_data
        
        # Create a copy of the scratchpad for logging (to capture 'before' state)
        scratchpad_before = json.loads(json.dumps(scratchpad))

        # Step names are mapped to pipeline steps as follows:
        # hook -> hook_creation
        # body -> body_linguistic_construction
        # ending -> ending_creation

        # Get the previous step's decision summary if applicable
        previous_step_summary = None
        if step == "hook":
            # No previous step for hook in simplified pipeline
            previous_step_summary = None
        elif step == "body":
            if scratchpad["generation_pipeline"]["hook_creation"]["decision_summary_for_next_step"]:
                previous_step_summary = scratchpad["generation_pipeline"]["hook_creation"]["decision_summary_for_next_step"]
        elif step == "ending":
            if scratchpad["generation_pipeline"]["body_linguistic_construction"]["decision_summary_for_next_step"]:
                previous_step_summary = scratchpad["generation_pipeline"]["body_linguistic_construction"]["decision_summary_for_next_step"]

        # Format the prompt with the relevant data
        # Create a filtered scratchpad that excludes the creator_style_guides (analyses)
        # This prevents sending ALL analyses in every step
        filtered_scratchpad = json.loads(json.dumps(scratchpad))  # Deep copy
        if "creator_style_guides" in filtered_scratchpad:
            # Replace with a note to maintain valid JSON structure while reducing token usage
            # The actual analysis data is passed separately via the analysis_data parameter
            filtered_scratchpad["creator_style_guides"] = {"note": "Analyses are provided separately as needed"}
        
        logger.info(f"Using filtered scratchpad for {step} step (removed full analyses)")
        
        if step == "body":
            # For body step with vector search pattern selection
            prompt_data = {
                "input_text": input_text,
                "analysis_data": json.dumps(analysis_data, indent=2),
                "scratchpad": json.dumps(filtered_scratchpad, indent=2),  # Use filtered scratchpad
                # REMOVED: "user_input_brief": scratchpad["user_input_brief"],  # Brief referenced from cache
                # REMOVED: "current_draft_post": scratchpad["current_draft_post"],  # Already in scratchpad, avoid duplication
                "previous_step_summary": previous_step_summary,
                "user_content_preferences": format_user_preferences(scratchpad.get("user_content_preferences"), post_length, scratchpad.get("content_strategy")),
                "hook_word_count": hook_word_count
            }

            # Add specific fields from the generation pipeline for direct access

            if "hook_creation" in scratchpad["generation_pipeline"]:
                prompt_data["selected_hook_pattern"] = scratchpad["generation_pipeline"]["hook_creation"]["selected_hook_pattern"]
                prompt_data["generated_hook_text"] = scratchpad["generation_pipeline"]["hook_creation"]["generated_hook_text"]

            # Add current date to prompt data
            prompt_data["current_date"] = datetime.now().strftime('%Y-%m-%d')
            
            prompt = prompt_template.format(**prompt_data)
        else:
            # For other steps, use the standard format
            prompt_data = {
                "input_text": input_text,
                "analysis_data": json.dumps(analysis_data, indent=2),
                "scratchpad": json.dumps(filtered_scratchpad, indent=2),  # Use filtered scratchpad
                # REMOVED: "user_input_brief": scratchpad["user_input_brief"],  # Brief referenced from cache
                "current_draft_post": scratchpad.get(f"current_draft_post_{draft_id or 'default'}", ""),
                "previous_step_summary": previous_step_summary,
                "user_content_preferences": format_user_preferences(scratchpad.get("user_content_preferences"), None, scratchpad.get("content_strategy"))
            }
            
            # Debug: Log what's being put into the prompt
            logger.info(f"Prompt data for {step} - analysis_data length: {len(json.dumps(analysis_data))}")
            logger.info(f"First 200 chars of analysis_data: {json.dumps(analysis_data)[:200]}...")

            # Add specific fields from the generation pipeline for direct access
            if step == "ending":
                if "hook_creation" in scratchpad["generation_pipeline"]:
                    prompt_data["selected_hook_pattern"] = scratchpad["generation_pipeline"]["hook_creation"]["selected_hook_pattern"]

                if "body_linguistic_construction" in scratchpad["generation_pipeline"]:
                    prompt_data["selected_body_structure"] = scratchpad["generation_pipeline"]["body_linguistic_construction"]["selected_body_structure"]

            # Add current date to prompt data
            prompt_data["current_date"] = datetime.now().strftime('%Y-%m-%d')
            
            prompt = prompt_template.format(**prompt_data)

        # Select the appropriate model for this step
        model = STEP_MODEL_MAPPING.get(step, STEP_MODEL_MAPPING["default"])
        
        # Determine if thinking should be enabled based on the model and step
        # - Claude-3-5-Haiku doesn't support thinking
        # - Only enable thinking for body step (experimental)
        thinking_enabled = (not model.startswith("claude-3-5-haiku")) and (step == "body")
        
        # Set appropriate max_tokens based on model
        max_tokens = 8000 if model.startswith("claude-3-5-haiku") else 12000
        
        # Log prompt size for debugging and monitoring
        prompt_size = len(prompt)
        filtered_scratchpad_size = len(json.dumps(filtered_scratchpad))
        logger.info(f"Prompt size for {step} step: {prompt_size} characters")
        logger.info(f"Filtered scratchpad size: {filtered_scratchpad_size} bytes")
        
        # Pre-compute word count for body step instead of using tools
        tools = None
        
        if step == "body":
            # Calculate hook word count directly
            pipeline = scratchpad.get("generation_pipeline", {})
            if "hook_creation" in pipeline:
                hook_text = pipeline["hook_creation"].get("generated_hook_text", "")
                if hook_text.strip():
                    hook_word_count = len(hook_text.split())
                    logger.info(f"Pre-computed hook word count for body step: {hook_word_count} words")
                else:
                    logger.info("Hook text is empty, using 0 word count")
            else:
                logger.info("No hook creation step found, using 0 word count")
            logger.info("Removed tools from body step - using pre-computed word count instead")
        
        # Get session linguistic pattern for optimized caching
        session_linguistic_pattern = None
        if session_state and hasattr(session_state, 'get_session_linguistic_pattern'):
            session_linguistic_pattern = session_state.get_session_linguistic_pattern(draft_id)
            if session_linguistic_pattern:
                logger.info(f"Retrieved session linguistic pattern for {step} (draft {draft_id}): {session_linguistic_pattern.name}")
            else:
                logger.warning(f"No linguistic pattern found for {step} (draft {draft_id})")
        else:
            logger.warning(f"No session state available for {step} - linguistic patterns will be missing")
        
        # Import the optimized caching function
        from src.utils.api_cache_utils import call_with_optimized_caching
        
        # Extract content strategy from session state
        content_strategy = None
        if session_state and hasattr(session_state, 'content_strategy'):
            content_strategy = session_state.content_strategy
            if content_strategy:
                logger.info(f"Using content strategy for {step}: industry={content_strategy.get('industry', 'unknown')}")
        
        # Call the Anthropic API with optimized caching structure
        client = AnthropicClient()
        result = await call_with_optimized_caching(
            client=client,
            unified_system_prompt=system_prompt,  # Same for all steps now
            user_input=input_text,  # User brief in cached prefix
            session_linguistic_pattern=session_linguistic_pattern,  # Linguistic pattern in cached prefix
            step_specific_prompt=prompt,  # Step-specific content (varying)
            max_tokens=max_tokens,
            temperature=1.0,
            thinking_enabled=thinking_enabled,
            thinking_budget_tokens=8000 if thinking_enabled else 0,
            step_name=step,
            model=model,
            tools=tools,
            content_strategy=content_strategy  # Pass content strategy for strategic context
        )

        # Parse the response
        content = result["text"]
        try:
            parsed = parse_step_response(content)
        except Exception as e:
            logger.error(f"Error parsing LLM response for {step}: {e}")
            logger.error(f"Response content: {content[:1000]}...")
            raise

        # Log cache metrics and response info
        logger.info(f"Step {step} cache metrics: {json.dumps(result['cache_metrics'], indent=2)}")
        logger.debug(f"Response text length for {step}: {len(content)} chars")
        logger.debug(f"Response preview: {content[:500]}...")

        # Add thinking tokens and step name to the result
        if "thinking" in result:
            parsed["thinking"] = result["thinking"]
            parsed["step_name"] = step

        # Record this step for complete generation history
        record_step(
            step_name=step,
            input_text=input_text,
            scratchpad_before=scratchpad_before,
            result=parsed,
            conversation_id=session_id
        )
        logger.info(f"Recorded {step} step for generation history")

        return parsed

def parse_step_response(response_text: str) -> Dict[str, Any]:
    """
    Parse the response from the LLM for a step.

    Args:
        response_text: Response text from the LLM

    Returns:
        Dictionary with reasoning, selection, output, and decision_summary
    """
    result = {
        "reasoning": "",
        "selection": "",
        "output": "",
        "decision_summary": ""
    }

    # Log the response for debugging
    logger.debug(f"Parsing response text (first 500 chars): {response_text[:500]}")

    # Extract reasoning
    reasoning_match = re.search(r"REASONING:(.*?)(?:SELECTION:|DECISION SUMMARY:|OUTPUT:|$)", response_text, re.DOTALL | re.IGNORECASE)
    if reasoning_match:
        result["reasoning"] = reasoning_match.group(1).strip()

    # Extract selection
    selection_match = re.search(r"SELECTION:(.*?)(?:DECISION SUMMARY:|OUTPUT:|$)", response_text, re.DOTALL | re.IGNORECASE)
    if selection_match:
        result["selection"] = selection_match.group(1).strip()
        
    # Extract decision summary
    decision_summary_match = re.search(r"DECISION SUMMARY:(.*?)(?:OUTPUT:|$)", response_text, re.DOTALL | re.IGNORECASE)
    if decision_summary_match:
        result["decision_summary"] = decision_summary_match.group(1).strip()

    # Extract output - try multiple patterns
    output_match = re.search(r"OUTPUT:(.*?)$", response_text, re.DOTALL | re.IGNORECASE)
    if output_match:
        result["output"] = output_match.group(1).strip()
    else:
        # Fallback: If no OUTPUT marker found, check for common body text patterns
        # This handles cases where Claude generates content after tool use without proper formatting
        
        # Pattern 1: Look for body text after other sections
        fallback_match = re.search(r"(?:DECISION SUMMARY:|SELECTION:|REASONING:).*?\n\n(.+)$", response_text, re.DOTALL | re.IGNORECASE)
        if fallback_match and len(fallback_match.group(1).strip()) > 50:  # Ensure it's substantial content
            logger.warning("No OUTPUT marker found, using fallback pattern to extract body text")
            result["output"] = fallback_match.group(1).strip()
        else:
            # Pattern 2: If response doesn't follow format at all, try to extract substantive content
            # Look for paragraphs that seem like body content (multiple sentences, substantial length)
            paragraphs = response_text.split('\n\n')
            for para in reversed(paragraphs):  # Start from end where body content typically is
                para = para.strip()
                if len(para) > 100 and '.' in para and not any(marker in para.upper() for marker in ['REASONING:', 'SELECTION:', 'DECISION SUMMARY:', 'OUTPUT:']):
                    logger.warning("Using heuristic extraction for body text due to missing format markers")
                    result["output"] = para
                    break
    
    # Log warning if output is empty
    if not result["output"]:
        logger.error(f"Failed to extract output from response. Response structure may not match expected format.")
        logger.error(f"Full response text: {response_text}")
    
    return result
