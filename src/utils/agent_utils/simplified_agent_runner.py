"""
Simplified parallel agent runner using vector search patterns.
Replaces the complex multi-step pipeline with direct pattern matching.
"""

import asyncio
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from src.utils.brief_to_patterns import match_brief_to_patterns
from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig
from src.config import Config
from src.utils.enhanced_logging import get_logger
from dotenv import load_dotenv

load_dotenv()

logger = get_logger(__name__)


class SimplifiedAgentRunner:
    """Orchestrates parallel content generation using vector-matched patterns."""
    
    def __init__(self, anthropic_client: Optional[AnthropicClient] = None):
        if anthropic_client:
            self.anthropic_client = anthropic_client
        else:
            # Create client from config
            config = Config()
            anthropic_config = AnthropicConfig(
                api_key=config.ANTHROPIC_API_KEY,
                beta_header=os.getenv("ANTHROPIC_BETA_HEADER", "")
            )
            self.anthropic_client = AnthropicClient(config=anthropic_config)
    
    async def generate_content(
        self,
        brief_text: Optional[str] = None,
        idea_summary: Optional[str] = None,
        desired_length: str = "medium",
        num_agents: int = 3,
        temperature: float = 0.7,
        content_strategy: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate content variations using parallel agents with vector-matched patterns.
        
        Args:
            brief_text: Full brief text (for pasted briefs)
            idea_summary: Pre-existing summary (from transcript processor)
            desired_length: Target content length
            num_agents: Number of parallel agents to run
            temperature: LLM temperature for generation
            content_strategy: Optional content strategy (industry, target audience, etc.)
            
        Returns:
            List of generated content variations with metadata
        """
        start_time = datetime.now()
        
        # Step 1: Get patterns via vector search (single operation)
        logger.info("Performing vector search for patterns...")
        patterns = await match_brief_to_patterns(
            brief_text=brief_text,
            idea_summary=idea_summary,
            desired_length=desired_length,
            content_strategy=content_strategy
        )
        
        logger.info(f"Found patterns: {list(patterns.keys())}")
        for pattern_type, matches in patterns.items():
            logger.info(f"  {pattern_type}: {len(matches)} matches")
        
        # Step 2: Distribute patterns to agents
        agent_pattern_sets = self._distribute_patterns_to_agents(patterns, num_agents)
        
        # Step 3: Generate content in parallel
        logger.info(f"Launching {num_agents} parallel agents...")
        generation_tasks = []
        
        for agent_num, pattern_set in enumerate(agent_pattern_sets, 1):
            task = self._generate_single_variation(
                agent_num=agent_num,
                patterns=pattern_set,
                brief_text=brief_text,
                idea_summary=idea_summary,
                temperature=temperature,
                content_strategy=content_strategy
            )
            generation_tasks.append(task)
        
        # Run all agents in parallel
        results = await asyncio.gather(*generation_tasks, return_exceptions=True)
        
        # Process results
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Agent {i+1} failed: {result}")
            else:
                successful_results.append(result)
        
        elapsed = (datetime.now() - start_time).total_seconds()
        logger.info(f"Generated {len(successful_results)} variations in {elapsed:.2f}s")
        
        return successful_results
    
    def _distribute_patterns_to_agents(
        self, 
        patterns: Dict[str, List[Dict]], 
        num_agents: int
    ) -> List[Dict[str, Dict]]:
        """
        Distribute patterns to agents based on score ranking.
        Agent 1 gets best patterns, Agent 2 gets second-best, etc.
        """
        agent_patterns = []
        
        for agent_idx in range(num_agents):
            agent_set = {}
            
            for pattern_type, pattern_list in patterns.items():
                # Give this agent the pattern at their index (if it exists)
                if agent_idx < len(pattern_list):
                    agent_set[pattern_type] = pattern_list[agent_idx]
            
            # Only add agents that have at least one pattern
            if agent_set:
                agent_patterns.append(agent_set)
        
        return agent_patterns
    
    async def _generate_single_variation(
        self,
        agent_num: int,
        patterns: Dict[str, Dict],
        brief_text: Optional[str],
        idea_summary: Optional[str],
        temperature: float,
        content_strategy: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate a single content variation using assigned patterns."""
        
        logger.info(f"Agent {agent_num} starting with patterns: {list(patterns.keys())}")
        
        # Build the prompt
        prompt = self._build_agent_prompt(
            patterns=patterns,
            brief_text=brief_text,
            idea_summary=idea_summary,
            content_strategy=content_strategy
        )
        
        # Load system prompt
        system_prompt_path = "prompts/agent_prompts/content_agent_system_prompt.txt"
        if os.path.exists(system_prompt_path):
            with open(system_prompt_path, 'r') as f:
                system_prompt = f.read()
        else:
            system_prompt = "You are a skilled content creator who writes engaging LinkedIn posts."
        
        # Generate content
        try:
            client = self.anthropic_client.get_standard_client()
            response = await client.messages.create(
                messages=[{"role": "user", "content": prompt}],
                system=system_prompt,
                model="claude-3-5-sonnet-20241022",
                temperature=temperature,
                max_tokens=2000
            )
            
            content = response.content[0].text if response.content else ""
            
            return {
                "agent_num": agent_num,
                "content": content,
                "patterns_used": {
                    pt: {
                        "name": p.get("pattern_name", "Unknown"),
                        "score": p.get("score", 0),
                        "creator": p.get("creator_name", "Unknown")
                    }
                    for pt, p in patterns.items()
                },
                "metadata": {
                    "temperature": temperature,
                    "generation_time": datetime.now().isoformat(),
                    "model": "claude-3-5-sonnet-20241022"
                }
            }
            
        except Exception as e:
            logger.error(f"Agent {agent_num} generation failed: {e}")
            raise
    
    def _build_agent_prompt(
        self,
        patterns: Dict[str, Dict],
        brief_text: Optional[str],
        idea_summary: Optional[str],
        content_strategy: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for content generation with specific patterns."""
        
        prompt_parts = []
        
        # Add context
        if idea_summary:
            prompt_parts.append(f"Create a LinkedIn post about: {idea_summary}")
        elif brief_text:
            prompt_parts.append(f"Create a LinkedIn post based on this brief:\n{brief_text}")
        
        # Add content strategy if provided
        if content_strategy:
            from src.utils.strategy_utils import format_strategy_for_prompt
            strategy_section = format_strategy_for_prompt(content_strategy)
            prompt_parts.append(f"\n{strategy_section}")
            prompt_parts.append("\nApply this strategy subtly - let the content naturally resonate with the target audience without being overtly salesy.")
        
        # Add pattern instructions
        prompt_parts.append("\nUse these specific patterns for your post:")
        
        # Format each pattern
        for pattern_type, pattern in patterns.items():
            prompt_parts.append(f"\n{pattern_type.upper()}:")
            prompt_parts.append(f"Pattern: {pattern.get('pattern_name', 'Unknown')}")
            
            # Add appropriate text field based on pattern type
            if pattern_type == "body":
                if pattern.get('description'):
                    prompt_parts.append(f"Description: {pattern['description']}")
                if pattern.get('when_to_use'):
                    prompt_parts.append(f"When to use: {pattern['when_to_use']}")
                if pattern.get('implementation_steps'):
                    prompt_parts.append(f"Implementation: {pattern['implementation_steps']}")
            else:
                if pattern.get('searchable_text'):
                    prompt_parts.append(f"Details: {pattern['searchable_text']}")
        
        prompt_parts.append("\nGenerate a complete, cohesive LinkedIn post using these patterns.")
        
        return "\n".join(prompt_parts)


# Convenience function for testing
async def test_simplified_runner():
    """Test the simplified runner with a sample brief."""
    
    runner = SimplifiedAgentRunner()
    
    test_brief = """
    How to build resilience in the face of adversity - practical strategies for entrepreneurs.
    
    Drawing from personal experience with business failures and comebacks, this post will explore 
    concrete techniques for maintaining mental strength and turning setbacks into opportunities.
    """
    
    results = await runner.generate_content(
        brief_text=test_brief,
        num_agents=3
    )
    
    for i, result in enumerate(results):
        print(f"\n=== AGENT {result['agent_num']} OUTPUT ===")
        print(result['content'])
        print(f"\nPatterns used: {json.dumps(result['patterns_used'], indent=2)}")


if __name__ == "__main__":
    asyncio.run(test_simplified_runner())