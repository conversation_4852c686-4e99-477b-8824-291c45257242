"""
Pattern filtering utility for the agent pipeline.

This module provides functions to filter analysis data using the pattern selector,
reducing context size while maintaining relevance.
"""

import json
import logging
from typing import Dict, Any, Optional, Literal, cast

logger = logging.getLogger(__name__)


async def filter_analysis_with_patterns(
    step: str,
    analysis_data: Any,  # Can be Dict[str, Any] or str
    scratchpad: Dict[str, Any],
    input_text: str,
    post_length: Optional[str] = None,
    use_pattern_selector: bool = True,
    draft_id: Optional[str] = None,
    session_state: Optional[Any] = None
) -> Any:  # Returns Dict[str, Any] or str depending on input
    """
    Filter analysis data using the pattern selector to reduce context size.
    Now includes session linguistic patterns for consistency across all steps.
    
    Args:
        step: The current step (hook, body, ending)
        analysis_data: The full analysis data for this step
        scratchpad: Current generation state containing previous selections
        input_text: User's original input
        post_length: Target post length (short/medium/long)
        use_pattern_selector: Whether to use pattern selection (default: True)
        draft_id: Draft ID for pattern splitting between drafts
        session_state: Session state containing session linguistic pattern
        
    Returns:
        Filtered analysis data containing only selected patterns + session linguistic pattern
    """
    # Skip pattern selection if disabled or not applicable to this step
    if not use_pattern_selector or step not in ["hook", "body", "ending"]:
        return analysis_data
    
    # Always use on-demand pattern selection with vector search
    logger.info(f"Selecting patterns on-demand for {step} using vector search")
    
    try:
        # Import here to avoid circular dependencies
        from src.utils.vector_pattern_selector import select_relevant_patterns_vector
        
        # Get session linguistic pattern for this draft
        session_linguistic_pattern = None
        if session_state and hasattr(session_state, 'get_session_linguistic_pattern'):
            session_linguistic_pattern = session_state.get_session_linguistic_pattern(draft_id)
            if session_linguistic_pattern:
                # Pattern is now a UnifiedPattern instance
                logger.info(f"Using session linguistic pattern for {step} ({draft_id}): {session_linguistic_pattern.name}")
            else:
                logger.warning(f"No session linguistic pattern available for {step} step ({draft_id})")
        else:
            logger.warning(f"No session state provided for {step} step - linguistic consistency may be impacted")
        
        # Build context for pattern selection
        pattern_context = {
            "user_input": input_text,
            "target_length": post_length or "medium"
        }
        
        # Add generated content to context if available
        if step in ["body", "ending"]:
            hook_creation = scratchpad.get("generation_pipeline", {}).get("hook_creation", {})
            if hook_creation.get("generated_hook_text"):
                pattern_context["generated_hook"] = hook_creation["generated_hook_text"]
        
        if step == "ending":
            # Include body content from current draft
            current_draft_key = f"current_draft_post_{draft_id or 'default'}"
            if scratchpad.get(current_draft_key):
                pattern_context["generated_body"] = scratchpad[current_draft_key]
        
        # Select relevant patterns using vector search
        logger.info(f"Selecting relevant patterns for {step} step using vector search")
        # Ensure step is a valid literal type
        if step not in ["hook", "body", "ending"]:
            raise ValueError(f"Invalid step type: {step}")
        
        # Cast to proper literal type after validation
        step_literal = cast(Literal["hook", "body", "ending"], step)
        
        pattern_selection = await select_relevant_patterns_vector(
            step_type=step_literal,
            context=pattern_context,
            session_linguistic_pattern=session_linguistic_pattern
        )
        
        # Apply draft-specific pattern splitting for variety between drafts
        if draft_id and draft_id in ['draft_a', 'draft_b']:
            pattern_selection = _split_patterns_for_draft(pattern_selection, step, draft_id)
        
        # Create filtered analysis based on selection
        # Handle string analysis_data (for body markdown) separately
        if isinstance(analysis_data, str):
            filtered_analysis = analysis_data
        else:
            filtered_analysis = dict(analysis_data)  # Shallow copy
        
        if step == "hook" and "selected_archetypes" in pattern_selection:
            # Build pattern content for hook archetypes only
            # NOTE: Session linguistic pattern is now in cached prefix, not here
            combined_content = []
            
            # Add session linguistic pattern FIRST for consistency
            if session_linguistic_pattern:
                combined_content.append(f"## SESSION LINGUISTIC PATTERN: {session_linguistic_pattern.name}\n{session_linguistic_pattern.to_prompt_text()}")
                logger.info(f"Including linguistic pattern in hook analysis: {session_linguistic_pattern.name}")
            
            # Add hook archetype patterns
            selected_patterns = pattern_selection["selected_archetypes"]
            for pattern in selected_patterns:
                # Pattern is now a UnifiedPattern instance
                combined_content.append(f"## HOOK ARCHETYPE: {pattern.name}\n{pattern.to_prompt_text()}")
            
            logger.info(f"Hook patterns prepared with {len(combined_content)} total patterns")
            
            # Create formatted content for hook patterns only
            filtered_analysis = "\n\n" + "="*50 + "\n\n".join(combined_content)
            
            # Log the reduction
            original_size = len(json.dumps(analysis_data))
            filtered_size = len(json.dumps(filtered_analysis))
            reduction_pct = (1 - filtered_size / original_size) * 100 if original_size > 0 else 0
            logger.info(
                f"Pattern selector reduced hook analysis: "
                f"{original_size} → {filtered_size} bytes ({reduction_pct:.0f}% reduction)"
            )
            
        elif step == "ending" and "selected_endings" in pattern_selection:
            # Build pattern content for ending patterns only
            # NOTE: Session linguistic pattern is now in cached prefix, not here
            combined_content = []
            
            # Add session linguistic pattern FIRST for consistency
            if session_linguistic_pattern:
                combined_content.append(f"## SESSION LINGUISTIC PATTERN: {session_linguistic_pattern.name}\n{session_linguistic_pattern.to_prompt_text()}")
                logger.info(f"Including linguistic pattern in ending analysis: {session_linguistic_pattern.name}")
            
            # Add ending patterns
            selected_patterns = pattern_selection["selected_endings"]
            for pattern in selected_patterns:
                # Pattern is now a UnifiedPattern instance
                combined_content.append(f"## ENDING PATTERN: {pattern.name}\n{pattern.to_prompt_text()}")
            
            logger.info(f"Ending patterns prepared with {len(combined_content)} total patterns")
            
            # Create formatted content for ending patterns only
            filtered_analysis = "\n\n" + "="*50 + "\n\n".join(combined_content)
            
            # Log the reduction
            original_size = len(json.dumps(analysis_data))
            filtered_size = len(json.dumps(filtered_analysis))
            reduction_pct = (1 - filtered_size / original_size) * 100 if original_size > 0 else 0
            logger.info(
                f"Pattern selector reduced ending analysis: "
                f"{original_size} → {filtered_size} bytes ({reduction_pct:.0f}% reduction)"
            )
            
        elif step == "body" and "selected_structures" in pattern_selection:
            # For body, use body frameworks only
            # NOTE: Session linguistic pattern is now in cached prefix, not here
            sections = []
            
            # Add session linguistic pattern FIRST for consistency
            if session_linguistic_pattern:
                sections.append(f"## SESSION LINGUISTIC PATTERN: {session_linguistic_pattern.name}\n{session_linguistic_pattern.to_prompt_text()}")
                logger.info(f"Including linguistic pattern in body analysis: {session_linguistic_pattern.name}")
            
            # Collect body framework patterns (for structure)
            body_patterns = pattern_selection.get("selected_structures", [])
            body_texts = []
            for pattern in body_patterns:
                # Pattern is now a UnifiedPattern instance
                body_texts.append(pattern.to_prompt_text())
            
            if body_texts:
                sections.append("## BODY FRAMEWORKS (Use for STRUCTURE)\n\n" + "\n\n---\n\n".join(body_texts))
            
            logger.info(f"Body patterns prepared with {len(sections)} sections (linguistic + frameworks)")
            
            # Create combined analysis with clear separation
            filtered_body_text = "\n\n" + "="*50 + "\n\n".join(sections) if sections else ""
            
            if isinstance(analysis_data, str):
                # Replace the entire markdown with selected citations
                filtered_analysis = filtered_body_text
            else:
                # For dict analysis_data, return the formatted text directly for body step
                # This ensures the prompt gets the properly formatted pattern data
                filtered_analysis = filtered_body_text
                
            # Log the reduction
            original_text = analysis_data if isinstance(analysis_data, str) else analysis_data.get("body_analysis_data", "")
            original_size = len(original_text)
            filtered_size = len(filtered_body_text)
            reduction_pct = (1 - filtered_size / original_size) * 100 if original_size > 0 else 0
            logger.info(
                f"Pattern selector enhanced body analysis: "
                f"{original_size} → {filtered_size} bytes with {len(body_patterns)} body + session linguistic pattern"
            )
        
        # Update scratchpad with pattern selection info
        selected_key = None
        if step == "hook" and "selected_archetypes" in pattern_selection:
            selected_key = "selected_archetypes"
        elif step == "body" and "selected_structures" in pattern_selection:
            selected_key = "selected_structures"
        elif step == "ending" and "selected_endings" in pattern_selection:
            selected_key = "selected_endings"
            
        if selected_key:
            pattern_info = {
                "patterns_used": len(pattern_selection[selected_key]),
                "pattern_names": [p.name for p in pattern_selection[selected_key]]
            }
            # Map step names to scratchpad keys
            step_mapping = {
                "hook": "hook_creation",
                "body": "body_linguistic_construction", 
                "ending": "ending_creation"
            }
            scratchpad_key = step_mapping.get(step, f"{step}_creation")
            
            # Safely update scratchpad (create structure if needed)
            if "generation_pipeline" not in scratchpad:
                scratchpad["generation_pipeline"] = {}
            if scratchpad_key not in scratchpad["generation_pipeline"]:
                scratchpad["generation_pipeline"][scratchpad_key] = {}
                
            scratchpad["generation_pipeline"][scratchpad_key]["pattern_selection"] = pattern_info
            logger.info(f"Updated scratchpad with {step} pattern selection: {pattern_info}")
        
        return filtered_analysis
        
    except Exception as e:
        logger.error(f"Pattern filtering failed for {step}: {e}", exc_info=True)
        # Return original data if pattern selection fails
        return analysis_data


async def filter_patterns_for_step(
    step: str,
    creator_analyses: Dict[str, Any],
    scratchpad: Dict[str, Any],
    input_text: str,
    post_length: Optional[str] = None
) -> Any:
    """
    Legacy interface for backward compatibility.
    
    Delegates to filter_analysis_with_patterns.
    """
    # Get the analysis data for this step
    if step == "hook":
        analysis_data = creator_analyses.get("hook", {})
    elif step == "body":
        analysis_data = creator_analyses.get("body", {})
    elif step == "ending":
        analysis_data = creator_analyses.get("ending", {})
    else:
        return {}
    
    return await filter_analysis_with_patterns(
        step=step,
        analysis_data=analysis_data,
        scratchpad=scratchpad,
        input_text=input_text,
        post_length=post_length
    )


def _split_patterns_for_draft(pattern_selection: Dict[str, Any], step: str, draft_id: str) -> Dict[str, Any]:
    """
    Split patterns between drafts for variety.
    
    draft_a gets first half of patterns (higher ranked)
    draft_b gets second half of patterns (slightly lower ranked)
    
    Args:
        pattern_selection: Full pattern selection from vector search
        step: Current step (hook, body, ending) 
        draft_id: Either 'draft_a' or 'draft_b'
        
    Returns:
        Pattern selection with only the patterns for this draft
    """
    logger.info(f"Splitting patterns for {draft_id} in {step} step")
    
    # Define split sizes for each step - simplified to 1 pattern per draft
    split_config = {
        "hook": {"total": 2, "each": 1},
        "body": {"total": 2, "each": 1}, 
        "ending": {"total": 2, "each": 1}
    }
    
    if step not in split_config:
        logger.warning(f"No split config for step {step}, returning all patterns")
        return pattern_selection
        
    config = split_config[step]
    
    # Get the appropriate pattern list for this step
    pattern_key = None
    if step == "hook" and "selected_archetypes" in pattern_selection:
        pattern_key = "selected_archetypes"
    elif step == "body" and "selected_structures" in pattern_selection:
        pattern_key = "selected_structures"
    elif step == "ending" and "selected_endings" in pattern_selection:
        pattern_key = "selected_endings"
    
    if not pattern_key:
        logger.warning(f"No patterns found for {step} step, returning original selection")
        return pattern_selection
        
    patterns = pattern_selection[pattern_key]
    
    # Handle case where we have fewer patterns than expected
    if len(patterns) < config["total"]:
        logger.info(f"Only {len(patterns)} patterns available for {step}, both drafts will get same patterns")
        return pattern_selection
    
    # Split patterns between drafts
    if draft_id == 'draft_a':
        # First half (positions 0 to each-1)
        split_patterns = patterns[:config["each"]]
        logger.info(f"draft_a gets patterns 0-{config['each']-1} for {step}")
    else:  # draft_b
        # Second half (positions each to total-1)
        split_patterns = patterns[config["each"]:config["total"]]
        logger.info(f"draft_b gets patterns {config['each']}-{config['total']-1} for {step}")
    
    # Create new pattern selection with split patterns
    split_selection = dict(pattern_selection)
    split_selection[pattern_key] = split_patterns
    
    logger.info(f"Split {len(patterns)} patterns into {len(split_patterns)} for {draft_id}")
    return split_selection