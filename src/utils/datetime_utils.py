"""Centralized datetime and timestamp utilities for consistent time handling across the application."""

import time
import uuid
from datetime import datetime
from typing import Union


def get_current_timestamp() -> float:
    """
    Get current Unix timestamp as float.
    
    Returns:
        Current timestamp in seconds since epoch
    """
    return time.time()


def get_current_timestamp_int() -> int:
    """
    Get current Unix timestamp as integer.
    
    Returns:
        Current timestamp in seconds since epoch (integer)
    """
    return int(time.time())


def get_current_datetime() -> datetime:
    """
    Get current datetime object.
    
    Returns:
        Current datetime
    """
    return datetime.now()


def get_current_datetime_iso() -> str:
    """
    Get current datetime as ISO formatted string.
    
    Returns:
        Current datetime in ISO format (YYYY-MM-DDTHH:MM:SS.ffffff)
    """
    return datetime.now().isoformat()


def get_timestamp_string(format_str: str = "%Y%m%d%H%M%S") -> str:
    """
    Get current timestamp as formatted string.
    
    Args:
        format_str: strftime format string (default: YYYYMMDDHHMMSS)
        
    Returns:
        Formatted timestamp string
    """
    return datetime.now().strftime(format_str)


def generate_job_id(prefix: str = "") -> str:
    """
    Generate unique job ID with timestamp and UUID.
    
    Args:
        prefix: Optional prefix for the job ID
        
    Returns:
        Unique job ID in format: [prefix_]timestamp-uuid
    """
    timestamp = get_current_timestamp_int()
    uuid_part = str(uuid.uuid4())
    
    if prefix:
        return f"{prefix}_{timestamp}-{uuid_part}"
    return f"{timestamp}-{uuid_part}"


def generate_session_id(prefix: str = "session") -> str:
    """
    Generate unique session ID with formatted timestamp.
    
    Args:
        prefix: Prefix for the session ID (default: "session")
        
    Returns:
        Session ID in format: prefix_YYYY-MM-DD_HH-MM-SS
    """
    timestamp_str = get_timestamp_string("%Y-%m-%d_%H-%M-%S")
    return f"{prefix}_{timestamp_str}"


def create_mongo_timestamp_fields() -> dict:
    """
    Create standard MongoDB timestamp fields.
    
    Returns:
        Dictionary with created_at and updated_at fields
    """
    now = get_current_datetime()
    return {
        "created_at": now,
        "updated_at": now
    }


def update_mongo_timestamp_field() -> dict:
    """
    Create MongoDB update timestamp field.
    
    Returns:
        Dictionary with updated_at field for $set operations
    """
    return {"updated_at": get_current_datetime()}