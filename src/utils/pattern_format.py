"""
Unified pattern format definition and utilities.

This module defines the standard pattern format used throughout the LinkedInsight pipeline,
eliminating the need for multiple format conversions.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum


class PatternType(Enum):
    """Types of patterns in the system."""
    HOOK = "hook"
    BODY = "body" 
    ENDING = "ending"
    LINGUISTIC = "linguistic"


@dataclass
class UnifiedPattern:
    """
    Unified pattern format used throughout the pipeline.
    
    This single format replaces the multiple format conversions
    between MongoDB, citations, agent format, and prompt format.
    """
    # Core fields
    id: str  # MongoDB ObjectId or unique identifier
    type: PatternType  # Type of pattern
    name: str  # Human-readable pattern name
    content: str  # The actual pattern content (formatted for prompts)
    
    # Metadata
    score: Optional[float] = None  # Similarity score from vector search
    
    # Optional fields for specific pattern types
    description: Optional[str] = None  # Pattern description
    example: Optional[str] = None  # Example usage
    steps: Optional[List[str]] = None  # Steps for structured patterns
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, handling enum serialization."""
        data = asdict(self)
        data['type'] = self.type.value
        return data
    
    def to_prompt_text(self) -> str:
        """
        Convert pattern to text format suitable for prompts.
        This is the ONLY place patterns are formatted for prompts.
        """
        if self.type == PatternType.LINGUISTIC:
            # Linguistic patterns are already formatted
            return f"LINGUISTIC PATTERN: {self.name}\n{self.content}"
        
        elif self.type in [PatternType.HOOK, PatternType.BODY, PatternType.ENDING]:
            # Structured patterns with optional components
            parts = [f"Pattern: {self.name}"]
            
            if self.description:
                parts.append(f"Description: {self.description}")
            
            if self.steps:
                parts.append("Steps:")
                for i, step in enumerate(self.steps, 1):
                    parts.append(f"{i}. {step}")
            
            if self.example:
                parts.append(f"Example: {self.example}")
            
            if self.content and self.content not in [self.description, self.example]:
                parts.append(f"Content: {self.content}")
            
            return "\n".join(parts)
        
        else:
            # Default format
            return f"{self.name}:\n{self.content}"


def convert_mongodb_to_unified(doc: Dict[str, Any], pattern_type: PatternType, score: Optional[float] = None) -> UnifiedPattern:
    """
    Convert MongoDB document to unified pattern format.
    
    Args:
        doc: MongoDB document
        pattern_type: Type of pattern
        score: Optional similarity score from vector search
        
    Returns:
        UnifiedPattern instance
    """
    # Extract common fields
    pattern_id = str(doc.get('_id', ''))
    
    if pattern_type == PatternType.LINGUISTIC:
        # Handle linguistic bundles
        bundle_name = doc.get('bundle_name', 'Unknown Bundle')
        bundle_text = doc.get('bundle_text', '{}')
        
        # Parse JSON bundle text if needed
        import json
        try:
            bundle_data = json.loads(bundle_text)
            # Format linguistic bundle content
            content_parts = []
            
            if 'sentence_structures' in bundle_data:
                content_parts.append("Sentence Structures:")
                for structure in bundle_data['sentence_structures']:
                    content_parts.append(f"• {structure}")
            
            if 'vocabulary_patterns' in bundle_data:
                content_parts.append("\nVocabulary Patterns:")  
                for pattern in bundle_data['vocabulary_patterns']:
                    content_parts.append(f"• {pattern}")
            
            if 'rhetorical_devices' in bundle_data:
                content_parts.append("\nRhetorical Devices:")
                for device in bundle_data['rhetorical_devices']:
                    content_parts.append(f"• {device}")
            
            content = "\n".join(content_parts) if content_parts else bundle_text
        except json.JSONDecodeError:
            content = bundle_text
        
        return UnifiedPattern(
            id=pattern_id,
            type=pattern_type,
            name=bundle_name,
            content=content,
            score=score
        )
    
    elif pattern_type == PatternType.HOOK:
        # Handle hook patterns - use fields from vector search
        return UnifiedPattern(
            id=pattern_id,
            type=pattern_type,
            name=doc.get('name') or doc.get('pattern_name') or doc.get('hook_archetype', 'Unknown Hook'),
            content=doc.get('searchable_text') or doc.get('pattern_text', ''),
            description=doc.get('description') or doc.get('pattern_description', ''),
            example=doc.get('example') or doc.get('example_text', ''),
            steps=doc.get('implementation_steps', []),
            score=score
        )
    
    elif pattern_type == PatternType.BODY:
        # Handle body patterns - use fields from vector search
        return UnifiedPattern(
            id=pattern_id,
            type=pattern_type,
            name=doc.get('name') or doc.get('pattern_name') or doc.get('framework_name', 'Unknown Framework'),
            content=doc.get('searchable_text') or doc.get('framework_structure', ''),
            description=doc.get('description') or doc.get('framework_description', ''),
            example=doc.get('example') or doc.get('example_implementation', ''),
            steps=doc.get('implementation_steps') or doc.get('structure_elements', []),
            score=score
        )
    
    elif pattern_type == PatternType.ENDING:
        # Handle ending patterns - use fields from vector search
        return UnifiedPattern(
            id=pattern_id,
            type=pattern_type,
            name=doc.get('name') or doc.get('pattern_name') or doc.get('ending_type', 'Unknown Ending'),
            content=doc.get('searchable_text') or doc.get('pattern_text', ''),
            description=doc.get('description') or doc.get('pattern_description', ''),
            example=doc.get('example') or doc.get('example_text', ''),
            steps=doc.get('implementation_steps', []),
            score=score
        )
    
    else:
        # Generic conversion
        return UnifiedPattern(
            id=pattern_id,
            type=pattern_type,
            name=doc.get('name', 'Unknown Pattern'),
            content=doc.get('content', ''),
            score=score
        )


def batch_convert_to_unified(docs: List[Dict[str, Any]], pattern_type: PatternType) -> List[UnifiedPattern]:
    """Convert multiple MongoDB documents to unified patterns."""
    return [convert_mongodb_to_unified(doc, pattern_type, doc.get('score')) for doc in docs]