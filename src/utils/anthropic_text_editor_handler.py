"""
Reusable utility to handle operations for an Anthropic-compatible Text Editor Tool.
This utility will process commands like view, str_replace, insert, create, and undo_edit
as defined by Anthropic's official text editor tool documentation.
Ref: https://docs.anthropic.com/en/docs/agents-and-tools/tool-use/text-editor-tool
"""
import os
import shutil # For file operations like copy2 and robust move (replace)
import re
from typing import Optional, Tuple, List, Dict, Any

MEMORY_PATH_PREFIX = "memory://"
BACKUP_SUFFIX = ".anthropic_tool_backup"

class AnthropicTextEditorHandler:
    """
    Handles text editing operations based on Anthropic's official text editor tool spec.
    Can operate on both file paths and in-memory content (using 'memory://' prefix).
    """

    def __init__(self, allow_real_filesystem_access: bool = False, base_path: Optional[str] = None):
        """
        Initializes the handler.

        Args:
            allow_real_filesystem_access: If True, allows operations on actual files.
                                          If False, only 'memory://' paths are allowed.
            base_path: If allow_real_filesystem_access is True, this restricts
                       file operations to a subdirectory. If None, operations are
                       relative to the current working directory (if allow_real_filesystem_access is True).
        """
        self.allow_real_filesystem_access = allow_real_filesystem_access
        
        if self.allow_real_filesystem_access and base_path:
            self.base_path = os.path.abspath(base_path)
            if not os.path.isdir(self.base_path):
                # Or, try to create it: os.makedirs(self.base_path, exist_ok=True)
                raise ValueError(f"Base path '{self.base_path}' does not exist or is not a directory.")
        elif self.allow_real_filesystem_access:
            self.base_path = os.path.abspath(".") # Default to current working directory
        else:
            self.base_path = None # No filesystem access

        self.in_memory_files: Dict[str, str] = {}
        self.in_memory_undo_history: Dict[str, List[str]] = {} # path -> list of previous states

    def _is_memory_path(self, path: str) -> bool:
        return path.startswith(MEMORY_PATH_PREFIX)

    def _get_memory_content(self, path: str) -> Optional[str]:
        return self.in_memory_files.get(path)

    def _update_memory_content(self, path: str, new_content: str, create_undo_snapshot: bool = True):
        if create_undo_snapshot:
            if path not in self.in_memory_undo_history:
                self.in_memory_undo_history[path] = []
            
            current_content = self.in_memory_files.get(path)
            if current_content is not None:
                if len(self.in_memory_undo_history[path]) >= 10: # Limit undo history
                    self.in_memory_undo_history[path].pop(0)
                self.in_memory_undo_history[path].append(current_content)
        
        self.in_memory_files[path] = new_content

    def _resolve_path(self, path: str, check_exists_for_read: bool = False, is_for_creation: bool = False) -> Tuple[Optional[str], Optional[str]]:
        """
        Validates path and checks permissions. Returns (absolute_path, error_message).
        For creation, parent directory existence is checked by the create function itself.
        """
        if self._is_memory_path(path):
            if check_exists_for_read and path not in self.in_memory_files:
                return None, f"Error: In-memory content for '{path}' not found."
            return path, None

        if not self.allow_real_filesystem_access:
            return None, "Error: Filesystem access is disabled for this tool handler."

        if not self.base_path: # Should not happen if allow_real_filesystem_access is True due to __init__
             return None, "Error: Filesystem access is allowed but base_path is not configured."

        # Prevent path components like "memory://" in filesystem paths
        if MEMORY_PATH_PREFIX in path:
            return None, f"Error: Invalid characters '{MEMORY_PATH_PREFIX}' in filesystem path."

        # Normalize the input path and join it with the base_path.
        # os.path.join handles leading "/" in `path` by treating `path` as absolute.
        # To ensure it's relative to base_path, strip leading slashes if path is meant to be relative.
        # However, os.path.abspath(os.path.join(base, path)) handles many cases well.
        
        # Treat path as relative to base_path
        # If path is absolute, os.path.join might ignore base_path.
        # So, ensure path is treated as relative first.
        normalized_input_path = os.path.normpath(path)
        if os.path.isabs(normalized_input_path):
            # If user gives absolute path, it must be within base_path already
            abs_path = normalized_input_path
        else:
            # Relative path is joined with base_path
            abs_path = os.path.abspath(os.path.join(self.base_path, normalized_input_path))

        # Security Check: Ensure the resolved path is within the base_path
        if not abs_path.startswith(self.base_path):
            return None, f"Error: Path '{path}' resolves outside the allowed base directory ('{self.base_path}')."

        if is_for_creation:
            # For creation, the file itself doesn't need to exist, but the resolved path is returned.
            # Parent directory creation is handled in `handle_create`.
            pass
        elif check_exists_for_read:
            if not os.path.exists(abs_path):
                return None, f"Error: File or directory not found at '{abs_path}'."
        
        return abs_path, None

    def _backup_file(self, file_path: str) -> Tuple[Optional[str], Optional[str]]:
        """Creates a backup of the file if it exists. Returns (backup_path, error_message)."""
        if not os.path.exists(file_path):
            return None, None # No file to back up

        backup_target_path = file_path + BACKUP_SUFFIX
        try:
            # Remove old backup if it exists to prevent error with os.replace later if undo is used
            if os.path.exists(backup_target_path):
                os.remove(backup_target_path)
            shutil.copy2(file_path, backup_target_path) # Preserves metadata
            return backup_target_path, None
        except Exception as e:
            return None, f"Error creating backup for '{file_path}': {str(e)}"

    def handle_view(self, path: str, view_range: Optional[List[int]] = None) -> Tuple[str, bool]:
        validated_path, error = self._resolve_path(path, check_exists_for_read=True)
        if error:
            return error, True
        path = validated_path # Use the validated, absolute path for FS

        if self._is_memory_path(path):
            content = self._get_memory_content(path) # Existence already checked by _resolve_path
            # (Identical logic as original for view_range on memory content)
            if view_range:
                lines = content.splitlines(keepends=True)
                start_line_0_idx = view_range[0] - 1
                end_line_exclusive = view_range[1] if view_range[1] != -1 else len(lines)
                if start_line_0_idx < 0: start_line_0_idx = 0
                if view_range[1] != -1 and end_line_exclusive > len(lines):
                    end_line_exclusive = len(lines)
                if start_line_0_idx >= len(lines) or start_line_0_idx >= end_line_exclusive:
                    return "", False 
                return "".join(lines[start_line_0_idx:end_line_exclusive]), False
            return content, False
        else: # Real file system logic
            # Existence already checked by _resolve_path
            if os.path.isdir(path):
                try:
                    return f"Directory listing for '{path}':\n" + "\n".join(os.listdir(path)), False
                except Exception as e:
                    return f"Error listing directory '{path}': {str(e)}", True
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    if view_range:
                        lines = f.readlines()
                        start_line_0_idx = view_range[0] - 1
                        end_line_exclusive = view_range[1] if view_range[1] != -1 else len(lines)
                        if start_line_0_idx < 0: start_line_0_idx = 0
                        if view_range[1] != -1 and end_line_exclusive > len(lines):
                            end_line_exclusive = len(lines)
                        if start_line_0_idx >= len(lines) or start_line_0_idx >= end_line_exclusive:
                            return "", False
                        return "".join(lines[start_line_0_idx:end_line_exclusive]), False
                    else:
                        return f.read(), False
            except Exception as e:
                return f"Error reading file '{path}': {str(e)}", True

    def handle_str_replace(self, path: str, old_str: str, new_str: str) -> Tuple[str, bool]:
        # For str_replace, the file/content must exist.
        validated_path, error = self._resolve_path(path, check_exists_for_read=True)
        if error:
            return error, True
        path = validated_path

        if not old_str:
            return "Error: 'Text to replace' (old_str) cannot be empty.", True

        backup_file_path_created: Optional[str] = None

        if self._is_memory_path(path):
            content = self._get_memory_content(path) # Existence checked
            
            # Replace entire content or check for unique match
            if content == old_str or content.count(old_str) == 1:
                if content == old_str:
                    updated_content = new_str
                else:
                    updated_content = content.replace(old_str, new_str, 1)
                self._update_memory_content(path, updated_content)
                return f"Successfully replaced text in '{path}'.", False
            
            # Error cases
            count = content.count(old_str)
            if count == 0:
                return f"Error: No match found for replacement of '{old_str}'.", True
            return f"Error: Found {count} matches for replacement text '{old_str}'. Please provide more context to make a unique match.", True
        else: # Real file system logic
             # Existence and type (not dir) checked by _resolve_path + isfile check
            if not os.path.isfile(path):
                 return f"Error: Path '{path}' is a directory, not a file. Cannot replace string.", True
            
            backup_file_path_created, backup_error = self._backup_file(path)
            if backup_error:
                return backup_error, True
            
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                count = content.count(old_str)
                if count == 0:
                    if backup_file_path_created: os.remove(backup_file_path_created) # No change, remove backup
                    return f"Error: No match found for replacement of '{old_str}'.", True
                if count > 1:
                    if backup_file_path_created: os.remove(backup_file_path_created) # No change, remove backup
                    return f"Error: Found {count} matches for replacement text '{old_str}'. Please provide more context for a unique match.", True

                updated_content = content.replace(old_str, new_str, 1)
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                return f"Successfully replaced text in '{path}'.", False
            except Exception as e:
                # Try to restore from backup if one was made for this operation
                if backup_file_path_created and os.path.exists(backup_file_path_created):
                    try:
                        os.replace(backup_file_path_created, path) # Restore
                    except Exception as restore_e:
                        return f"Error during str_replace for '{path}': {str(e)}. Additionally, failed to restore backup: {str(restore_e)}", True
                return f"Error during str_replace for '{path}': {str(e)}", True

    def handle_insert(self, path: str, insert_line: int, new_str: str) -> Tuple[str, bool]:
        # For insert, the file/content must exist.
        validated_path, error = self._resolve_path(path, check_exists_for_read=True)
        if error:
            return error, True
        path = validated_path
        
        if insert_line < 0:
            return f"Error: Invalid insert_line {insert_line}. Must be 0 or greater.", True

        backup_file_path_created: Optional[str] = None
        
        if self._is_memory_path(path):
            content = self._get_memory_content(path) # Existence checked
            lines = content.splitlines(keepends=True)
            
            final_lines = []
            if insert_line == 0:
                final_lines.append(new_str)
                if lines and not new_str.endswith(os.linesep) and lines[0] and not lines[0].startswith(os.linesep):
                    final_lines.append(os.linesep) # Ensure separation if new_str isn't a full line
                final_lines.extend(lines)
            elif insert_line >= len(lines): # Append
                final_lines.extend(lines)
                if lines and not lines[-1].endswith(os.linesep) and not new_str.startswith(os.linesep):
                     final_lines.append(os.linesep)
                final_lines.append(new_str)
            else: # Insert after line `insert_line` (0-indexed)
                final_lines.extend(lines[:insert_line + 1])
                # Ensure new_str is on a new line if the previous line ended and new_str doesn't start with a line break
                if lines[insert_line].endswith(os.linesep) and not new_str.startswith(os.linesep):
                    pass # new_str will start on a new line naturally
                elif not lines[insert_line].endswith(os.linesep) and not new_str.startswith(os.linesep):
                     final_lines.append(os.linesep) # Add a line break before new_str
                final_lines.append(new_str)
                final_lines.extend(lines[insert_line + 1:])
            
            updated_content = "".join(final_lines)
            self._update_memory_content(path, updated_content) # Handles memory undo
            return f"Successfully inserted text in '{path}'.", False
        else: # Real file system logic
            if not os.path.isfile(path):
                 return f"Error: Path '{path}' is a directory, not a file. Cannot insert text.", True

            backup_file_path_created, backup_error = self._backup_file(path)
            if backup_error:
                return backup_error, True

            try:
                with open(path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                final_content_lines = []
                if insert_line == 0:
                    final_content_lines.append(new_str)
                    if lines and not new_str.endswith(os.linesep) and lines[0] and not lines[0].startswith(os.linesep):
                         final_content_lines.append(os.linesep)
                    final_content_lines.extend(lines)
                elif insert_line >= len(lines):
                    final_content_lines.extend(lines)
                    if lines and lines[-1] and not lines[-1].endswith(os.linesep) and not new_str.startswith(os.linesep):
                        final_content_lines.append(os.linesep)
                    final_content_lines.append(new_str)
                else:
                    final_content_lines.extend(lines[:insert_line + 1])
                    if lines[insert_line].endswith(os.linesep) and not new_str.startswith(os.linesep):
                        pass
                    elif not lines[insert_line].endswith(os.linesep) and not new_str.startswith(os.linesep):
                        final_content_lines.append(os.linesep)
                    final_content_lines.append(new_str)
                    final_content_lines.extend(lines[insert_line + 1:])
                
                with open(path, 'w', encoding='utf-8') as f:
                    f.write("".join(final_content_lines))
                return f"Successfully inserted text in '{path}'.", False
            except Exception as e:
                if backup_file_path_created and os.path.exists(backup_file_path_created):
                    try:
                        os.replace(backup_file_path_created, path) # Restore
                    except Exception as restore_e:
                        return f"Error during insert for '{path}': {str(e)}. Additionally, failed to restore backup: {str(restore_e)}", True
                return f"Error during insert for '{path}': {str(e)}", True

    def handle_create(self, path: str, file_text: str) -> Tuple[str, bool]:
        # For create, the file doesn't need to exist beforehand.
        validated_path, error = self._resolve_path(path, check_exists_for_read=False, is_for_creation=True)
        if error:
            return error, True
        path = validated_path

        backup_file_path_created: Optional[str] = None
        operation_type = "created"

        if self._is_memory_path(path):
            # For memory, 'create' can overwrite. Undo snapshot is taken if overwriting.
            is_overwriting = path in self.in_memory_files
            self._update_memory_content(path, file_text, create_undo_snapshot=is_overwriting)
            status_verb = "updated" if is_overwriting else "created"
            return f"Successfully {status_verb} in-memory content at '{path}'.", False
        else: # Real file system logic
            if os.path.isdir(path):
                return f"Error: A directory already exists at '{path}'. Cannot create file.", True

            # If the file already exists, it's an overwrite. Back it up.
            if os.path.exists(path):
                operation_type = "overwritten" # Or "updated"
                backup_file_path_created, backup_error = self._backup_file(path)
                if backup_error:
                    return backup_error, True
            
            parent_dir = os.path.dirname(path)
            if parent_dir: # Ensure parent directory exists
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                except Exception as e:
                    # If backup was made, attempt to remove it as the main operation failed before writing
                    if backup_file_path_created and os.path.exists(backup_file_path_created): os.remove(backup_file_path_created)
                    return f"Error creating parent directory for '{path}': {str(e)}", True
            try:
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(file_text)
                return f"Successfully {operation_type} file at '{path}'.", False
            except Exception as e:
                if backup_file_path_created and os.path.exists(backup_file_path_created):
                    try:
                        os.replace(backup_file_path_created, path) # Restore original if overwrite failed
                    except Exception as restore_e:
                         return f"Error {operation_type} file at '{path}': {str(e)}. Additionally, failed to restore backup: {str(restore_e)}", True
                return f"Error {operation_type} file at '{path}': {str(e)}", True

    def handle_undo_edit(self, path: str) -> Tuple[str, bool]:
        # For undo, the actual file/memory item might not exist if last op was e.g. delete,
        # but the undo history or backup might. So, check_exists_for_read=False.
        validated_path, error = self._resolve_path(path, check_exists_for_read=False)
        if error:
            return error, True
        path = validated_path

        if self._is_memory_path(path):
            if not self.in_memory_undo_history.get(path): # No history or empty history list
                return f"Error: No undo history available for in-memory content '{path}'.", True
            
            previous_content = self.in_memory_undo_history[path].pop()
            # Current state in self.in_memory_files[path] is the one we are undoing from.
            # We don't add it to a "redo" stack in this simple undo model.
            self.in_memory_files[path] = previous_content # Restore
            return f"Successfully undone last edit for in-memory '{path}'.", False
        else: # Real file system logic
            backup_to_restore_path = path + BACKUP_SUFFIX
            if os.path.exists(backup_to_restore_path):
                try:
                    # os.replace is atomic move: backup_to_restore_path becomes 'path'
                    # This means the backup file is "consumed" by the undo operation.
                    os.replace(backup_to_restore_path, path) 
                    return f"Successfully undone last edit for '{path}' by restoring backup.", False
                except Exception as e:
                    return f"Error undoing edit for '{path}' from backup '{backup_to_restore_path}': {str(e)}", True
            else:
                return f"Error: No backup file found at '{backup_to_restore_path}' to undo edit for '{path}'.", True

    def _get_params(self, tool_input: Dict[str, Any], expected_params: List[str]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        params = {}
        for p_name in expected_params:
            if p_name not in tool_input:
                # view_range is optional for 'view'. Other commands have their params as required by spec.
                if p_name not in ['view_range']: 
                    return None, f"Error: Missing required parameter '{p_name}'."
            params[p_name] = tool_input.get(p_name) # .get() handles missing optional params like view_range gracefully
        return params, None

    def dispatch_command(self, command_name: str, tool_input: Dict[str, Any]) -> Tuple[str, bool]:
        path_param = tool_input.get('path')
        if not path_param: # Path is fundamental to all these operations
            return "Error: 'path' parameter is required for all commands.", True

        if command_name == "view":
            params, error = self._get_params(tool_input, ['path', 'view_range'])
            if error: return error, True
            return self.handle_view(path=params['path'], view_range=params.get('view_range'))
        
        elif command_name == "str_replace":
            params, error = self._get_params(tool_input, ['path', 'old_str', 'new_str'])
            if error: return error, True
            # Ensure required params that aren't 'path' were actually provided
            if params.get('old_str') is None or params.get('new_str') is None:
                 return "Error: Missing 'old_str' or 'new_str' parameter for str_replace.", True
            return self.handle_str_replace(path=params['path'], old_str=params['old_str'], new_str=params['new_str'])

        elif command_name == "insert":
            params, error = self._get_params(tool_input, ['path', 'insert_line', 'new_str'])
            if error: return error, True
            if params.get('insert_line') is None or params.get('new_str') is None:
                 return "Error: Missing 'insert_line' or 'new_str' parameter for insert.", True
            return self.handle_insert(path=params['path'], insert_line=params['insert_line'], new_str=params['new_str'])

        elif command_name == "create":
            params, error = self._get_params(tool_input, ['path', 'file_text'])
            if error: return error, True
            if params.get('file_text') is None:
                 return "Error: Missing 'file_text' parameter for create.", True
            return self.handle_create(path=params['path'], file_text=params['file_text'])

        elif command_name == "undo_edit":
            params, error = self._get_params(tool_input, ['path']) # Only path is needed
            if error: return error, True
            return self.handle_undo_edit(path=params['path'])
            
        else:
            return f"Error: Unknown command '{command_name}'.", True

# Example usage (for testing this module directly):
if __name__ == '__main__':
    # Create a dedicated test directory
    test_base_dir = "_test_anthropic_editor"
    if os.path.exists(test_base_dir):
        shutil.rmtree(test_base_dir) # Clear previous test runs
    os.makedirs(test_base_dir, exist_ok=True)

    print(f"--- Test Environment: Real filesystem operations will be in ./{test_base_dir}/ ---")

    # Handler for filesystem tests, restricted to the test_base_dir
    fs_handler = AnthropicTextEditorHandler(allow_real_filesystem_access=True, base_path=test_base_dir)
    
    # Handler for memory-only tests
    mem_handler = AnthropicTextEditorHandler(allow_real_filesystem_access=False)


    print("\n--- Testing In-Memory Operations (using mem_handler) ---")
    memory_brief_path = "memory://interview_brief.md"
    
    # Create
    status, is_err = mem_handler.dispatch_command("create", {"path": memory_brief_path, "file_text": "Hello World!\nThis is the first brief version."})
    print(f"Create (mem): {status}, Error: {is_err}")
    
    # View
    content, is_err = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 1 (mem): Error: {is_err}\nContent:\n{content}")
    
    # Insert
    status, is_err = mem_handler.dispatch_command("insert", {"path": memory_brief_path, "insert_line": 0, "new_str": "Title: My Interview\n"})
    print(f"Insert at top (mem): {status}, Error: {is_err}")
    content, _ = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 2 (mem):\n{content}")

    status, is_err = mem_handler.dispatch_command("insert", {"path": memory_brief_path, "insert_line": 1, "new_str": "Subtitle: Details here\n"})
    print(f"Insert after line 1 (mem): {status}, Error: {is_err}")
    content, _ = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 3 (mem):\n{content}")

    # Str_replace
    status, is_err = mem_handler.dispatch_command("str_replace", {"path": memory_brief_path, "old_str": "World", "new_str": "Universe"})
    print(f"Replace 'World' (mem): {status}, Error: {is_err}")
    content, _ = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 4 (mem):\n{content}")

    # Undo (should revert the replace)
    status, is_err = mem_handler.dispatch_command("undo_edit", {"path": memory_brief_path})
    print(f"Undo 1 (mem): {status}, Error: {is_err}")
    content, _ = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 5 (mem - should be 'World' again):\n{content}")

    # Undo again (should revert the "Subtitle" insert)
    status, is_err = mem_handler.dispatch_command("undo_edit", {"path": memory_brief_path})
    print(f"Undo 2 (mem): {status}, Error: {is_err}")
    content, _ = mem_handler.dispatch_command("view", {"path": memory_brief_path})
    print(f"View 6 (mem):\n{content}")
    
    # View range
    content, is_err = mem_handler.dispatch_command("view", {"path": memory_brief_path, "view_range": [1,1]})
    print(f"View Range [1,1] (mem): Error: {is_err}\nContent:\n{content}")


    print(f"\n--- Testing File System Operations (using fs_handler, in ./{test_base_dir}/) ---")
    test_file_rel_path = "test_editor_file.txt" # Relative to base_path
    
    # Test path resolution outside base_path (should fail)
    print("Test: Accessing file outside base_path (../../../test_outside.txt)")
    status, is_err = fs_handler.dispatch_command("view", {"path": "../../../test_outside.txt"})
    print(f"View outside base (fs): {status}, Error: {is_err}")
    assert is_err # Should be an error

    # Create file
    status, is_err = fs_handler.dispatch_command("create", {"path": test_file_rel_path, "file_text": "Line 1: Hello from file.\nLine 2: This is a test."})
    print(f"Create file '{test_file_rel_path}' (fs): {status}, Error: {is_err}")

    # View file
    content, is_err = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file (fs): Error: {is_err}\nContent:\n{content}")

    # Insert into file
    status, is_err = fs_handler.dispatch_command("insert", {"path": test_file_rel_path, "insert_line": 0, "new_str": "Preamble: File Start\n"})
    print(f"Insert into file (fs): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after insert (fs):\n{content}")
    
    # Replace in file (backup should be created automatically by handler)
    status, is_err = fs_handler.dispatch_command("str_replace", {"path": test_file_rel_path, "old_str": "Hello", "new_str": "Greetings"})
    print(f"Replace in file (fs): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after replace (fs):\n{content}")
    # Check if backup exists (implementation detail, but good for testing the test)
    abs_test_file_path = os.path.join(fs_handler.base_path, test_file_rel_path)
    if os.path.exists(abs_test_file_path + BACKUP_SUFFIX):
        print(f"Backup file '{abs_test_file_path + BACKUP_SUFFIX}' found.")
    else:
        print(f"Error: Backup file NOT found for '{abs_test_file_path}'.")


    # Undo edit in file (should restore "Hello" from Greetings)
    status, is_err = fs_handler.dispatch_command("undo_edit", {"path": test_file_rel_path})
    print(f"Undo edit in file (fs): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after undo (fs - should be 'Hello' again):\n{content}")
    if os.path.exists(abs_test_file_path + BACKUP_SUFFIX):
        print(f"Error: Backup file '{abs_test_file_path + BACKUP_SUFFIX}' STILL exists after undo.")
    else:
        print(f"Correct: Backup file consumed by undo operation.")


    # Undo again (should restore original file content, before "Preamble")
    status, is_err = fs_handler.dispatch_command("undo_edit", {"path": test_file_rel_path}) # Will try to use backup from "insert" op
    print(f"Undo edit in file (fs attempt 2): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after 2nd undo (fs - should be preamble-less):\n{content}")


    # Create: Overwrite existing file (should create a backup of the "Preamble" version)
    status, is_err = fs_handler.dispatch_command("create", {"path": test_file_rel_path, "file_text": "Completely new content."})
    print(f"Create (overwrite) file '{test_file_rel_path}' (fs): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after overwrite (fs):\n{content}")
    assert "Completely new content." in content
    if os.path.exists(abs_test_file_path + BACKUP_SUFFIX):
        print(f"Backup file for overwrite found.")
    else:
        print(f"Error: Backup file NOT found after overwrite.")

    # Undo the overwrite
    status, is_err = fs_handler.dispatch_command("undo_edit", {"path": test_file_rel_path})
    print(f"Undo overwrite (fs): {status}, Error: {is_err}")
    content, _ = fs_handler.dispatch_command("view", {"path": test_file_rel_path})
    print(f"View file after undoing overwrite (fs - should be preamble version):\n{content}")
    assert "Preamble" in content # Should be back to the version before "Completely new content."
    

    # View directory (relative path from CWD, but resolves within base_path)
    # To view the base_path itself, send "." or "" as path
    print("View base directory (fs_handler, path='.')")
    content, is_err = fs_handler.dispatch_command("view", {"path": "."}) 
    print(f"View directory '.' (fs): Error: {is_err}\nContent:\n{content}")

    # Cleanup: Handled by shutil.rmtree at the start of the test block for next run.
    # You might want to uncomment below if you want to clean up after each individual run and not just before the next.
    # print(f"\n--- Cleaning up test directory ./{test_base_dir}/ ---")
    # shutil.rmtree(test_base_dir)
    # print("Cleaned up test directory and files.")