"""Helper class for timing and debug logging."""
import time
import json
from typing import Optional, Any, Dict, List
from pathlib import Path

import logging

logger = logging.getLogger('linkedinsight.utils.timing')

class TimingLogger:
    """Handles timing logs and debug callbacks for performance tracking."""

    def __init__(self, name: str):
        """Initialize timing logger.
        
        Args:
            name: Name/identifier for this logger instance
        """
        self.name = name
        self._timing_logs: Optional[List[Dict[str, Any]]] = None
        self._timing_file: Optional[Path] = None
        self._debug_callback = None
        
    @property
    def debug_callback(self):
        """Get debug callback."""
        return self._debug_callback

    @debug_callback.setter
    def debug_callback(self, callback):
        """Set debug callback."""
        self._debug_callback = callback

    def enable_timing_logs(self, output_file: str):
        """Enable detailed timing logs to JSON file.
        
        Args:
            output_file: Path to JSON output file
        """
        self._timing_logs = []
        self._timing_file = Path(output_file)
        logger.info(f"Enabled timing logs for {self.name}, writing to {output_file}")

    def log_timing(self, event: str, **kwargs):
        """Add a timing log entry if enabled.
        
        Args:
            event: Name of the event being logged
            **kwargs: Additional data to include in log entry
        """
        if self._timing_logs is not None:
            entry = {
                "timestamp": time.time(),
                "event": event,
                **kwargs
            }
            self._timing_logs.append(entry)
            
            # Write to file after each entry to preserve data
            if self._timing_file:
                with open(self._timing_file, 'w') as f:
                    json.dump(self._timing_logs, f, indent=2)
                    
    def log_duration(self, event: str, start_time: float, **kwargs):
        """Log an event with its duration.
        
        Args:
            event: Name of the event being logged
            start_time: Start time from time.time()
            **kwargs: Additional data to include in log entry
        """
        duration = time.time() - start_time
        self.log_timing(event, duration=duration, **kwargs)
        logger.info(f"[{self.name}] {event} completed in {duration:.3f} seconds")

    def call_debug(self, data: Any):
        """Call debug callback if set.
        
        Args:
            data: Data to pass to debug callback
        """
        if self._debug_callback:
            self._debug_callback(data) 