"""Initialize the output directories and files for Heroku deployment."""

import os
import shutil
import logging
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = Path(__file__).parent.parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.config import OUTPUTS_DIR

def initialize_outputs():
    """Initialize the output directories and files for Heroku deployment."""
    # Check if we're running on Heroku
    if not os.environ.get('HEROKU_APP_NAME'):
        logger.info("Not running on Heroku, skipping outputs initialization")
        return
    
    # Source outputs directory in the repository
    source_outputs_dir = os.path.join(root_dir, "outputs")
    
    # Check if the source outputs directory exists
    if not os.path.exists(source_outputs_dir):
        logger.error(f"Source outputs directory not found at {source_outputs_dir}")
        return
    
    # Check if the destination outputs directory already exists
    if os.path.exists(OUTPUTS_DIR) and os.path.isdir(OUTPUTS_DIR):
        # Check if there are already files in the directory
        if any(os.scandir(OUTPUTS_DIR)):
            logger.info(f"Outputs directory already exists at {OUTPUTS_DIR} with files")
            return
    
    # Copy the outputs directory to the destination path
    try:
        # Ensure the parent directory exists
        os.makedirs(os.path.dirname(OUTPUTS_DIR), exist_ok=True)
        
        # Copy the outputs directory recursively
        if os.path.exists(OUTPUTS_DIR):
            shutil.rmtree(OUTPUTS_DIR)
        
        shutil.copytree(source_outputs_dir, OUTPUTS_DIR)
        logger.info(f"Outputs directory copied from {source_outputs_dir} to {OUTPUTS_DIR}")
        
        # Verify the copy
        if os.path.exists(OUTPUTS_DIR) and os.path.isdir(OUTPUTS_DIR):
            # Count files
            file_count = sum([len(files) for _, _, files in os.walk(OUTPUTS_DIR)])
            logger.info(f"Outputs directory initialized with {file_count} files")
        else:
            logger.error(f"Failed to copy outputs directory to {OUTPUTS_DIR}")
            
    except Exception as e:
        logger.error(f"Error initializing outputs directory: {e}")

if __name__ == "__main__":
    initialize_outputs()
