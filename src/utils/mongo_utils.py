"""MongoDB utility functions for LinkedInsight."""
import logging
from typing import Dict, Any, Optional, List
from pymongo import Mongo<PERSON>lient
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Import project modules
from src.config import Config
from src.constants.collections import (
    CREATORS_COLLECTION,
    ANALYSES_COLLECTION,
    CONTENT_STRATEGIES_COLLECTION,
    INTERVIEW_SESSIONS_COLLECTION
)

# Configure logging
logger = logging.getLogger(__name__)


# MongoDB client instance (singleton)
_mongo_client = None

def get_mongo_client() -> MongoClient:
    """
    Get a MongoDB client instance.
    
    Returns:
        MongoClient: A MongoDB client instance
    
    Raises:
        ValueError: If MONGODB_URI is not set
        ConnectionFailure: If connection to MongoDB fails
    """
    global _mongo_client
    
    if _mongo_client is None:
        try:
            mongodb_uri = Config.get_mongodb_uri()
            _mongo_client = MongoClient(mongodb_uri)
            # Ping the server to check if the connection is valid
            _mongo_client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    return _mongo_client

def get_mongo_db() -> Database:
    """
    Get the MongoDB database instance.
    
    Returns:
        Database: A MongoDB database instance
    """
    client = get_mongo_client()
    # Extract database name from the connection URI
    # The format is typically: *********************************************
    db_name = Config.get_mongodb_uri().split('/')[-1].split('?')[0]
    
    # If no database name is specified in the URI, use a default name
    if not db_name:
        db_name = "linkedinsight"
    
    return client[db_name]

def setup_mongo_indexes() -> None:
    """
    Set up MongoDB indexes for the collections.
    
    This function should be called once during application startup.
    """
    db = get_mongo_db()
    
    # Set up indexes for creators collection
    db[CREATORS_COLLECTION].create_index("creator_name", unique=True)
    
    # Set up indexes for analyses collection
    db[ANALYSES_COLLECTION].create_index([
        ("creator_name", pymongo.ASCENDING),
        ("analysis_type", pymongo.ASCENDING),
        ("generated_at", pymongo.DESCENDING)
    ])
    db[ANALYSES_COLLECTION].create_index("creator_name")
    db[ANALYSES_COLLECTION].create_index("analysis_type")
    db[ANALYSES_COLLECTION].create_index("generated_at")

    # Set up indexes for content_strategies collection
    db[CONTENT_STRATEGIES_COLLECTION].create_index("clerk_user_id", unique=True)
    
    # Set up indexes for interview_sessions collection
    db[INTERVIEW_SESSIONS_COLLECTION].create_index("session_id", unique=True)
    db[INTERVIEW_SESSIONS_COLLECTION].create_index("updated_at")
    
    logger.info("MongoDB indexes have been set up")

def close_mongo_connection() -> None:
    """
    Close the MongoDB connection.
    
    This function should be called when the application is shutting down.
    """
    global _mongo_client
    
    if _mongo_client is not None:
        _mongo_client.close()
        _mongo_client = None
        logger.info("MongoDB connection closed")