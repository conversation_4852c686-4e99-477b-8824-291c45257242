#!/usr/bin/env python3
"""
Brief-to-pattern matching pipeline using vector embeddings.
Elegantly simple approach to finding relevant patterns across all creators.
"""

import asyncio
from typing import Dict, List, Any, Optional
from voyageai.client import Client as VoyageClient
import os
from dotenv import load_dotenv
import anthropic
from anthropic.types import TextBlock
from pymongo import AsyncMongoClient
import logging

logger = logging.getLogger(__name__)
load_dotenv()

# Initialize clients
voyage_client = VoyageClient(api_key=os.getenv("VOYAGE_AI_API_KEY"))
anthropic_client = anthropic.Client(api_key=os.getenv("ANTHROPIC_API_KEY"))

ESSENCE_PROMPT = """Extract the core essence of this content brief in 3-5 sentences. Include:
- Main argument or thesis
- Key themes and concepts (list 3-5)
- Target audience and their pain points
- Desired emotional impact
- Any contrarian or unique angles

Don't summarize everything - focus on what would help match the right writing patterns.

Brief:
{brief_text}

Core essence:"""


async def extract_brief_essence(brief_text: str, use_ai: bool = False) -> str:
    """Extract the essential themes and requirements from a brief."""
    if not use_ai:
        # Direct approach: Use first ~300-500 chars of brief
        # Most briefs have their core idea in the first paragraph
        words = brief_text.split()
        # Take roughly first 50-75 words (typical paragraph)
        excerpt_words = words[:75]
        essence = " ".join(excerpt_words)
        
        # Ensure we don't cut off mid-sentence
        if len(essence) > 500:
            # Find last period, question mark, or exclamation
            for punct in ['. ', '? ', '! ']:
                last_punct = essence.rfind(punct)
                if last_punct > 200:  # Ensure minimum length
                    essence = essence[:last_punct + 1]
                    break
            else:
                essence = essence[:500] + "..."
        
        logger.info(f"Using direct brief excerpt: {essence[:100]}...")
        return essence
    
    # Original AI approach (kept for comparison)
    try:
        response = anthropic_client.messages.create(
            model="claude-3-5-haiku-latest",
            max_tokens=400,
            temperature=0,
            messages=[{
                "role": "user",
                "content": ESSENCE_PROMPT.format(brief_text=brief_text)
            }]
        )
        
        # Access text from the first content block
        content = response.content[0]
        # Type guard for TextBlock
        if isinstance(content, TextBlock):
            essence = content.text.strip()
        else:
            # Fallback if structure is different
            essence = str(content).strip()
        logger.info(f"Extracted brief essence with AI: {essence}")
        return essence
        
    except Exception as e:
        logger.error(f"Error extracting brief essence: {e}")
        # Fallback to direct approach
        return await extract_brief_essence(brief_text, use_ai=False)


def create_strategy_context_text(content_strategy: Optional[Dict[str, Any]]) -> str:
    """Create a text representation of content strategy for embedding.
    
    Args:
        content_strategy: Strategy dict with industry, target_audience, etc.
        
    Returns:
        Text suitable for embedding that captures strategic context
    """
    if not content_strategy:
        return ""
    
    # Validate strategy is a dict
    if not isinstance(content_strategy, dict):
        logger.warning(f"Invalid strategy format in create_strategy_context_text: {type(content_strategy)}")
        return ""
    
    # Create a natural language representation optimized for semantic search
    parts = []
    
    try:
        # Safely extract and format each field
        if content_strategy.get("industry"):
            industry = str(content_strategy["industry"])[:200]  # Limit length
            parts.append(f"Industry focus: {industry}")
        
        if content_strategy.get("target_audience"):
            audience = str(content_strategy["target_audience"])[:200]
            parts.append(f"Written for {audience}")
        
        if content_strategy.get("content_goal"):
            goal = str(content_strategy["content_goal"])[:200]
            parts.append(f"Goal: {goal}")
        
        if content_strategy.get("differentiators"):
            diff = str(content_strategy["differentiators"])[:200]
            parts.append(f"Unique perspective: {diff}")
    except Exception as e:
        logger.warning(f"Error formatting strategy context: {e}")
        return ""
    
    return ". ".join(parts)


async def search_patterns(
    pattern_type: str,
    query_embedding: List[float],
    limit: int = 5,
    min_score: float = 0.7
) -> List[Dict[str, Any]]:
    """Search for patterns using vector similarity."""
    
    pipeline = [
        {
            "$vectorSearch": {
                "index": "pattern_embeddings_vector_index",
                "path": "embedding",
                "queryVector": query_embedding,
                "numCandidates": limit * 10,
                "limit": limit,
                "filter": {"pattern_type": pattern_type}
            }
        },
        {
            "$project": {
                "_id": 1,
                "pattern_name": 1,
                "searchable_text": 1,
                "creator_name": 1,
                "description": 1,
                "pattern_type": 1,
                "when_to_use": 1,
                "score": {"$meta": "vectorSearchScore"}
            }
        }
    ]
    
    results = []
    # Use sync client for now - async aggregate has issues
    from pymongo import MongoClient
    sync_client = MongoClient(os.getenv("MONGODB_URI"))
    uri = os.getenv("MONGODB_URI", "")
    db_name = uri.split("/")[-1].split("?")[0] or "linkedinsight"
    sync_db = sync_client[db_name]
    
    cursor = sync_db.pattern_embeddings.aggregate(pipeline)
    for doc in cursor:
        if doc.get("score", 0) >= min_score:
            results.append(doc)
    
    sync_client.close()
    return results


def apply_length_filter(
    results: Dict[str, List[Dict]], 
    desired_length: str
) -> Dict[str, List[Dict]]:
    """Filter patterns based on content length requirements."""
    # For now, return all results
    # Could add filtering based on target_length field
    _ = desired_length  # Mark as intentionally unused
    return results


def ensure_creator_diversity(
    results: Dict[str, List[Dict]],
    max_per_creator: int = 2
) -> Dict[str, List[Dict]]:
    """Ensure we don't over-index on a single creator."""
    diverse_results = {}
    
    for pattern_type, patterns in results.items():
        creator_counts = {}
        diverse_patterns = []
        
        for pattern in patterns:
            creator = pattern.get("creator_name", "unknown")
            count = creator_counts.get(creator, 0)
            
            if count < max_per_creator:
                diverse_patterns.append(pattern)
                creator_counts[creator] = count + 1
        
        diverse_results[pattern_type] = diverse_patterns
    
    return diverse_results


def apply_funnel_scoring(
    results: Dict[str, List[Dict]], 
    content_strategy: Dict[str, Any]
) -> Dict[str, List[Dict]]:
    """Apply subtle funnel-aware scoring adjustments based on strategy.
    
    Args:
        results: Pattern search results organized by type
        content_strategy: Content strategy with target audience, industry, etc.
        
    Returns:
        Results with adjusted scores based on funnel alignment
    """
    from src.strategy_config import infer_funnel_stage_from_goal, calculate_funnel_score_boost
    
    # Detect likely funnel stage from strategy goal with error handling
    target_funnel_stage = None
    try:
        if content_strategy and isinstance(content_strategy, dict):
            target_funnel_stage = infer_funnel_stage_from_goal(content_strategy.get("content_goal"))
    except Exception as e:
        logger.warning(f"Failed to infer funnel stage from strategy: {e}")
        target_funnel_stage = None
    
    if target_funnel_stage:
        logger.info(f"Inferred funnel stage from strategy: {target_funnel_stage.display_name}")
    else:
        logger.info("No funnel stage inferred from strategy")
    
    # Apply scoring adjustments using centralized logic
    adjusted_results = {}
    
    for pattern_type, patterns in results.items():
        adjusted_patterns = []
        
        for pattern in patterns:
            # Copy pattern to avoid mutation
            adjusted_pattern = pattern.copy()
            
            # Get pattern's funnel stage from metadata if available
            pattern_funnel_stage = pattern.get("funnel_stage") or pattern.get("theme_compatibility")
            
            # Calculate boosted score using centralized logic
            original_score = adjusted_pattern.get("score", 0.5)
            boosted_score = calculate_funnel_score_boost(
                base_score=original_score,
                pattern_funnel_stage=pattern_funnel_stage,
                target_funnel_stage=target_funnel_stage
            )
            
            # Track the boost amount for debugging
            boost = boosted_score - original_score
            if boost > 0:
                logger.debug(f"Pattern '{pattern.get('pattern_name')}' boosted by {boost:.3f} for funnel alignment")
            
            adjusted_pattern["score"] = boosted_score
            adjusted_pattern["funnel_boost"] = boost
            
            adjusted_patterns.append(adjusted_pattern)
        
        # Re-sort by adjusted score
        adjusted_patterns.sort(key=lambda p: p.get("score", 0), reverse=True)
        adjusted_results[pattern_type] = adjusted_patterns
    
    return adjusted_results


async def match_brief_to_patterns(
    brief_text: Optional[str] = None,
    idea_summary: Optional[str] = None,
    desired_length: str = "medium",
    content_strategy: Optional[Dict[str, Any]] = None,
    db = None
) -> Dict[str, List[Dict]]:
    """
    Main pipeline: Brief/Summary → Embedding → Search → Filter
    
    Args:
        brief_text: Full brief text (for pasted briefs)
        idea_summary: Pre-existing summary (from transcript processor)
        desired_length: Target content length
        content_strategy: Optional content strategy for strategic alignment
        db: MongoDB database connection
    
    Returns dict with pattern types as keys and lists of patterns as values.
    """
    # 1. Get text for embedding (prefer summary if available)
    if idea_summary:
        # Transcript processor flow - use existing summary
        essence = idea_summary
        logger.info("Using provided idea summary for pattern matching")
    elif brief_text:
        # Brief pasting flow - use first paragraph
        essence = await extract_brief_essence(brief_text, use_ai=False)
    else:
        raise ValueError("Either brief_text or idea_summary must be provided")
    
    # 2. Combine with strategy context if provided
    if content_strategy:
        strategy_text = create_strategy_context_text(content_strategy)
        if strategy_text:
            # Combine essence with strategy for richer semantic search
            combined_text = f"{essence}\n\n{strategy_text}"
            logger.info(f"Including strategy context in search: {strategy_text[:100]}...")
        else:
            combined_text = essence
    else:
        combined_text = essence
    
    # 3. Generate query embedding
    result = voyage_client.embed([combined_text], model="voyage-3.5", input_type="query")
    query_embedding = result.embeddings[0]
    
    # 4. Set up database if not provided
    if db is None:
        client = AsyncMongoClient(os.getenv("MONGODB_URI"))
        uri = os.getenv("MONGODB_URI", "")
        db_name = uri.split("/")[-1].split("?")[0] or "linkedinsight"
        db = client[db_name]
    
    # 5. Parallel vector searches
    search_tasks = [
        search_patterns("hook", query_embedding, limit=5),
        search_patterns("body", query_embedding, limit=5),
        search_patterns("ending", query_embedding, limit=5)
    ]
    
    results = await asyncio.gather(*search_tasks)
    
    # 6. Organize results by type
    pattern_results = {
        "hook": results[0],
        "body": results[1],
        "ending": results[2]
    }
    
    # 7. Apply filters
    filtered = apply_length_filter(pattern_results, desired_length)
    diverse = ensure_creator_diversity(filtered)
    
    # 8. Apply funnel-aware scoring if strategy provided
    if content_strategy:
        scored = apply_funnel_scoring(diverse, content_strategy)
        return scored
    
    return diverse


async def format_pattern_results(results: Dict[str, List[Dict]]) -> str:
    """Format results for display."""
    output = []
    
    for pattern_type, patterns in results.items():
        output.append(f"\n=== {pattern_type.upper()} PATTERNS ===")
        for i, pattern in enumerate(patterns, 1):
            creator = pattern.get('creator_name', 'Unknown')
            output.append(f"\n{i}. {pattern['pattern_name']} (by {creator})")
            output.append(f"   Score: {pattern.get('score', 0):.3f}")
            if pattern.get('description'):
                output.append(f"   Description: {pattern['description'][:100]}...")
            if pattern.get('when_to_use'):
                output.append(f"   When to use: {pattern['when_to_use'][:100]}...")
    
    return "\n".join(output)


# Test function
async def test_with_sample_brief():
    """Test the pipeline with the sample brief."""
    # Read sample brief
    with open("tests/sample_brief.md", "r") as f:
        brief_text = f.read()
    
    print("Processing brief...")
    results = await match_brief_to_patterns(brief_text)
    
    formatted = await format_pattern_results(results)
    print(formatted)
    
    # Save results
    import json
    with open("test_pattern_matches.json", "w") as f:
        # Convert to serializable format
        serializable = {}
        for k, v in results.items():
            serializable[k] = [
                {key: str(val) if key == "_id" else val 
                 for key, val in pattern.items()}
                for pattern in v
            ]
        json.dump(serializable, f, indent=2)
    
    print("\nResults saved to test_pattern_matches.json")


if __name__ == "__main__":
    asyncio.run(test_with_sample_brief())