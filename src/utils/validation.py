"""Validation utilities for common patterns across the application."""

from bson import ObjectId
from fastapi import HTTPException


def validate_object_id(id_string: str, field_name: str = "ID") -> ObjectId:
    """
    Validate and convert a string to MongoDB ObjectId.
    
    Args:
        id_string: The string to validate and convert
        field_name: Name of the field for error message (e.g., "transcript ID")
    
    Returns:
        ObjectId: Valid MongoDB ObjectId
        
    Raises:
        HTTPException: 400 error if the string is not a valid ObjectId format
    """
    try:
        return ObjectId(id_string)
    except Exception:
        raise HTTPException(status_code=400, detail=f"Invalid {field_name} format")