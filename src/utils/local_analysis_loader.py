"""
Utility module for loading local analysis files.

This module provides functions for loading JSON and markdown analysis files
from local directories, without requiring MongoDB.
"""

import os
import json
import re
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default directory for all analyses
DEFAULT_ANALYSES_DIR = os.path.join("LLM_friendly_Analyses")
DEFAULT_JSON_DIR = DEFAULT_ANALYSES_DIR
DEFAULT_MARKDOWN_DIR = DEFAULT_ANALYSES_DIR

def extract_creator_name(filename: str) -> Optional[str]:
    """
    Extract creator name from a filename.

    Args:
        filename: The filename to extract from

    Returns:
        The creator name or None if not found
    """
    # Pattern: creatorname_analysistype_timestamp.ext
    pattern = r'^([a-zA-Z0-9]+)_'
    match = re.match(pattern, filename)
    if match:
        return match.group(1)
    return None

def extract_timestamp(filename: str) -> Optional[datetime]:
    """
    Extract timestamp from a filename.

    Args:
        filename: The filename to extract from

    Returns:
        The timestamp as a datetime object or None if not found
    """
    # Pattern: creatorname_analysistype_YYYYMMDDHHMMSS.ext
    pattern = r'_(\d{14})'
    match = re.search(pattern, filename)
    if match:
        timestamp_str = match.group(1)
        try:
            return datetime.strptime(timestamp_str, '%Y%m%d%H%M%S')
        except ValueError:
            return None
    return None

def find_latest_analysis_file(
    creator_name: str,
    analysis_type: str,
    base_dir: str,
    is_json: bool = True
) -> Optional[str]:
    """
    Find the latest analysis file for a creator and analysis type.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (e.g., 'themes', 'hooks')
        base_dir: Base directory to search in
        is_json: Whether to look for JSON or markdown files

    Returns:
        Path to the latest file or None if not found
    """
    # Create the creator directory path
    creator_dir = os.path.join(base_dir, creator_name)

    # Check if the directory exists
    if not os.path.isdir(creator_dir):
        logger.warning(f"Creator directory not found: {creator_dir}")
        return None

    # File pattern to match
    file_ext = "json" if is_json else "md"

    # Different patterns for different analysis types
    if is_json:
        # For JSON files, we need to match the actual JSON files, not the raw response files
        file_pattern = f"{creator_name}_{analysis_type}_"
    else:
        # For markdown files
        file_pattern = f"{creator_name}_{analysis_type}_analysis_"

    # Find all matching files
    matching_files = []
    for filename in os.listdir(creator_dir):
        if filename.startswith(file_pattern) and filename.endswith(f".{file_ext}") and "_raw_response" not in filename:
            filepath = os.path.join(creator_dir, filename)
            timestamp = extract_timestamp(filename)
            if timestamp:
                matching_files.append((filepath, timestamp))

    # Sort by timestamp (newest first)
    matching_files.sort(key=lambda x: x[1], reverse=True)

    # Return the latest file or None
    return matching_files[0][0] if matching_files else None

def load_json_analysis(
    creator_name: str,
    analysis_type: str,
    base_dir: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Load the latest JSON analysis for a creator and analysis type.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (e.g., 'themes_json', 'hooks_json')
        base_dir: Base directory to search in (default: DEFAULT_JSON_DIR)

    Returns:
        The JSON content as a dictionary or None if not found
    """
    # Use default directory if not specified
    if base_dir is None:
        base_dir = DEFAULT_JSON_DIR

    # Find the latest file
    filepath = find_latest_analysis_file(creator_name, analysis_type, base_dir, is_json=True)

    if not filepath:
        logger.warning(f"No {analysis_type} JSON file found for {creator_name}")
        return None

    try:
        # Load the JSON content
        with open(filepath, 'r', encoding='utf-8') as f:
            content = json.load(f)

        logger.info(f"Loaded {analysis_type} JSON for {creator_name} from {filepath}")
        return content

    except Exception as e:
        logger.error(f"Error loading {analysis_type} JSON for {creator_name}: {e}")
        return None

def load_markdown_analysis(
    creator_name: str,
    analysis_type: str,
    base_dir: Optional[str] = None
) -> Optional[str]:
    """
    Load the latest markdown analysis for a creator and analysis type.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (e.g., 'body_llm_friendly')
        base_dir: Base directory to search in (default: DEFAULT_MARKDOWN_DIR)

    Returns:
        The markdown content as a string or None if not found
    """
    # Use default directory if not specified
    if base_dir is None:
        base_dir = DEFAULT_MARKDOWN_DIR

    # Find the latest file
    filepath = find_latest_analysis_file(creator_name, analysis_type, base_dir, is_json=False)

    if not filepath:
        logger.warning(f"No {analysis_type} markdown file found for {creator_name}")
        return None

    try:
        # Load the markdown content
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        logger.info(f"Loaded {analysis_type} markdown for {creator_name} from {filepath}")
        return content

    except Exception as e:
        logger.error(f"Error loading {analysis_type} markdown for {creator_name}: {e}")
        return None

def load_creator_analyses_local(
    creator_name: str,
    json_dir: Optional[str] = None,
    markdown_dir: Optional[str] = None
) -> Dict[str, Any]:
    """
    Load all available analyses for a creator from local files.

    Args:
        creator_name: Name of the creator
        json_dir: Directory for JSON analyses (default: DEFAULT_JSON_DIR)
        markdown_dir: Directory for markdown analyses (default: DEFAULT_MARKDOWN_DIR)

    Returns:
        Dictionary with all analyses
    """
    analyses = {}

    # Use default directories if not specified
    if json_dir is None:
        json_dir = DEFAULT_JSON_DIR
    if markdown_dir is None:
        markdown_dir = DEFAULT_MARKDOWN_DIR

    # Load JSON analyses
    json_types = {
        "themes": "themes_analysis_json",
        "hooks": "hook_analysis_json",
        "endings": "ending_analysis_json",
        "linguistic": "linguistic_analysis_json"
    }

    for key, file_type in json_types.items():
        content = load_json_analysis(creator_name, file_type, json_dir)
        if content:
            analyses[key] = content
            logger.info(f"Loaded {key} JSON analysis for {creator_name}")
        else:
            logger.warning(f"No {key} JSON analysis found for {creator_name}")

    # Load markdown body analysis
    body_content = load_markdown_analysis(creator_name, "body_llm_friendly", markdown_dir)
    if body_content:
        analyses["body"] = body_content
        logger.info(f"Loaded body markdown analysis for {creator_name}")
    else:
        logger.warning(f"No body markdown analysis found for {creator_name}")

    # Log the loaded analyses
    logger.info(f"Loaded analyses for {creator_name}: {list(analyses.keys())}")

    return analyses
