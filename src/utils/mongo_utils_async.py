"""Async MongoDB utility functions for LinkedInsight.

This module provides non-blocking access to MongoDB using the PyMongo
asynchronous driver introduced in PyMongo ≥ 4.13.  All functions mirror the
behaviour of their synchronous counterparts in `src/utils/mongo_utils.py`, but
return coroutine objects and must be awaited.
"""
from __future__ import annotations

import logging
from typing import Optional, <PERSON>ple

from pymongo import AsyncMongoClient  # PyMongo ≥ 4.13 async driver
from pymongo.asynchronous.database import AsyncDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from src.config import Config
from src.constants.collections import (
    CREATORS_COLLECTION,
    ANALYSES_COLLECTION,
    CONTENT_STRATEGIES_COLLECTION,
    CONTENT_PREFERENCES_COLLECTION,
    INTERVIEW_SESSIONS_COLLECTION,
    CONTENT_GENERATION_SESSIONS_COLLECTION,
    USER_AGREEMENTS_COLLECTION
)

logger = logging.getLogger(__name__)

# Singleton async client instance
_async_mongo_client: Optional[AsyncMongoClient] = None



async def get_async_mongo_client() -> AsyncMongoClient:  # noqa: D401
    """Return a cached instance of :class:`~pymongo.AsyncMongoClient`.

    This helper ensures we create *one* client for the lifetime of the
    application, following MongoDB driver best-practices.
    """
    global _async_mongo_client

    if _async_mongo_client is None:
        try:
            mongodb_uri = Config.get_mongodb_uri()
            _async_mongo_client = AsyncMongoClient(mongodb_uri)
            # Proactively verify the connection (optional but useful on startup)
            await _async_mongo_client.admin.command("ping")
            logger.info("Successfully connected to MongoDB (async)")
        except (ConnectionFailure, ServerSelectionTimeoutError) as exc:
            logger.error("Failed to connect to MongoDB (async): %s", exc)
            raise

    return _async_mongo_client


async def get_async_mongo_db() -> AsyncDatabase:  # noqa: D401
    """Return the default MongoDB database instance (async)."""
    client = await get_async_mongo_client()

    # Extract database name from URI, fallback to default
    uri_db_part = Config.get_mongodb_uri().split("/")[-1].split("?")[0]
    db_name = uri_db_part or "linkedinsight"

    return client[db_name]


async def setup_async_mongo_indexes() -> None:  # noqa: D401
    """Create required indexes asynchronously on startup."""
    db = await get_async_mongo_db()

    # Creators
    await db[CREATORS_COLLECTION].create_index("creator_name", unique=True)

    # Analyses
    await db[ANALYSES_COLLECTION].create_index(
        [
            ("creator_name", 1),
            ("analysis_type", 1),
            ("generated_at", -1),
        ]
    )
    await db[ANALYSES_COLLECTION].create_index("creator_name")
    await db[ANALYSES_COLLECTION].create_index("analysis_type")
    await db[ANALYSES_COLLECTION].create_index("generated_at")

    # Content strategies
    await db[CONTENT_STRATEGIES_COLLECTION].create_index(
        "clerk_user_id", unique=True
    )
    
    # Interview sessions
    await db[INTERVIEW_SESSIONS_COLLECTION].create_index("session_id", unique=True)
    await db[INTERVIEW_SESSIONS_COLLECTION].create_index("updated_at")

    # Content generation sessions
    await db[CONTENT_GENERATION_SESSIONS_COLLECTION].create_index("session_id", unique=True)
    await db[CONTENT_GENERATION_SESSIONS_COLLECTION].create_index("user_id")
    await db[CONTENT_GENERATION_SESSIONS_COLLECTION].create_index("updated_at")
    await db[CONTENT_GENERATION_SESSIONS_COLLECTION].create_index([("user_id", 1), ("updated_at", -1)])

    # Transcripts collection indexes
    await db["transcripts"].create_index("user_email")
    await db["transcripts"].create_index("createdAt")
    await db["transcripts"].create_index([("user_email", 1), ("createdAt", -1)])
    # Index for efficient idea ID lookups within transcripts
    await db["transcripts"].create_index("ideas.id")
    await db["transcripts"].create_index("generated_ideas.id")

    # User agreements collection indexes
    await db[USER_AGREEMENTS_COLLECTION].create_index("clerk_user_id", unique=True)
    await db[USER_AGREEMENTS_COLLECTION].create_index("user_email")
    await db[USER_AGREEMENTS_COLLECTION].create_index("accepted_at")

    logger.info("MongoDB indexes (async) have been set up")


async def get_user_content_strategy(clerk_user_id: str) -> Optional[dict]:
    """Fetch user's content strategy from MongoDB.
    
    Args:
        clerk_user_id: The Clerk user ID
        
    Returns:
        Content strategy dict if found, None otherwise
    """
    try:
        db = await get_async_mongo_db()
        strategy = await db[CONTENT_STRATEGIES_COLLECTION].find_one(
            {"clerk_user_id": clerk_user_id}
        )
        
        if strategy:
            # Remove MongoDB's _id field for cleaner data
            strategy.pop("_id", None)
            logger.info(f"Retrieved content strategy for user {clerk_user_id}")
        else:
            logger.info(f"No content strategy found for user {clerk_user_id}")
            
        return strategy
        
    except Exception as e:
        logger.error(f"Error fetching content strategy for user {clerk_user_id}: {e}")
        return None


async def get_user_preferences_and_strategy(clerk_user_id: str) -> Tuple[Optional[dict], Optional[dict]]:
    """Fetch user's content preferences and strategy from MongoDB.
    
    Args:
        clerk_user_id: The Clerk user ID
        
    Returns:
        Tuple of (preferences dict, strategy dict), either can be None
    """
    try:
        db = await get_async_mongo_db()
        
        # Fetch preferences
        preferences = None
        preferences_doc = await db[CONTENT_PREFERENCES_COLLECTION].find_one({
            "clerk_user_id": clerk_user_id
        })
        if preferences_doc:
            preferences_doc.pop("_id", None)
            preferences_doc.pop("clerk_user_id", None)  # Remove user ID for cleaner data
            preferences = preferences_doc
            logger.info(f"Retrieved content preferences for user {clerk_user_id}")
        
        # Fetch strategy using existing function
        strategy = await get_user_content_strategy(clerk_user_id)
        
        return preferences, strategy
        
    except Exception as e:
        logger.error(f"Error fetching preferences/strategy for user {clerk_user_id}: {e}")
        return None, None


async def close_async_mongo_connection() -> None:  # noqa: D401
    """Close the async MongoDB client and reset singleton."""
    global _async_mongo_client

    if _async_mongo_client is not None:
        _async_mongo_client.close()
        _async_mongo_client = None
        logger.info("MongoDB async connection closed") 