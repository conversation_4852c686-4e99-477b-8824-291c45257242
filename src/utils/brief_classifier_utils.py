"""
Utility functions for the brief classifier component.

This module provides functions for matching briefs to the most suitable creators
based on their style cards.
"""

import json
import logging
import os
from typing import Dict, List, Any, Optional
from pathlib import Path as FilePath # Use pathlib

from src.utils.anthropic_client import AnthropicClient
from src.models.brief_classifier_schema import CreatorRecommendation

# Configure logging
logger = logging.getLogger(__name__)

def load_prompt(prompt_filename: str) -> str:
    """
    Load a prompt from the prompts directory.

    Args:
        prompt_filename: Name of the prompt file

    Returns:
        The prompt text
    """
    # Assuming 'prompts' directory is relative to where the process is run
    # or configured via PYTHONPATH to be found directly.
    # For robustness, consider path relative to this file or project root.
    # current_script_dir = FilePath(__file__).parent
    # prompt_path = current_script_dir.parent / "prompts" / prompt_filename
    prompt_path = os.path.join("prompts", prompt_filename) # Existing path, ensure 'prompts' is accessible
    try:
        with open(prompt_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading prompt {prompt_filename}: {e}")
        raise

def load_creator_style_cards() -> List[Dict[str, Any]]:
    logger.info("Attempting to load creator style cards...")
    try:
        # Get the directory of the current script (src/utils/)
        current_script_dir = FilePath(__file__).parent
        # Go up one level (to src/) and then into 'data' directory
        style_cards_path = current_script_dir.parent / "data" / "creatorStyleCards.json"
        
        logger.info(f"Resolved path to creator style cards: {style_cards_path}")

        if not style_cards_path.is_file():
            logger.error(f"Creator style cards file not found at: {style_cards_path}")
            raise FileNotFoundError(f"Creator style cards file not found: {style_cards_path}")

        with open(style_cards_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        loaded_cards = data.get("cards", [])
        logger.info(f"Successfully loaded {len(loaded_cards)} creator style cards.")
        return loaded_cards
    except FileNotFoundError: # Catch specifically to provide better context
        logger.error(f"FileNotFoundError explicitly caught for creator style cards.", exc_info=True)
        raise # Re-raise to be caught by the router
    except Exception as e:
        logger.error(f"Generic error loading creator style cards: {e}", exc_info=True)
        raise # Re-raise to be caught by the router

async def match_creators_to_brief(
    brief: str,
    creator_style_cards: Optional[List[Dict[str, Any]]] = None,
    max_results: int = 2,
    model: str = "claude-3-5-haiku-latest"
) -> Dict[str, Any]:
    """
    Match a brief to the most suitable creators based on their style cards.

    Args:
        brief: The user's brief
        creator_style_cards: List of creator style cards (optional, will load if not provided)
        max_results: Maximum number of creators to return (default: 2)
        model: The Claude model to use (default: "claude-3-5-haiku-latest")

    Returns:
        Dictionary with recommended creators and reasoning
    """
    logger.info(f"Entering match_creators_to_brief with model: {model}, brief length: {len(brief)}")
    # Load creator style cards if not provided
    if creator_style_cards is None:
        # This will now use the updated load_creator_style_cards function
        creator_style_cards = load_creator_style_cards()

    # Load prompts
    system_prompt = load_prompt("brief_classifier_system_prompt.txt")
    user_prompt_template = load_prompt("brief_classifier_user_prompt.txt")

    # Format the user prompt
    user_prompt = user_prompt_template.format(
        brief=brief,
        creator_style_cards=json.dumps(creator_style_cards, indent=2)
    )

    # Initialize the Anthropic client (ensure timeout is set in AnthropicClient definition)
    client = AnthropicClient()

    # Create tool definition from Pydantic model
    schema = CreatorRecommendation.model_json_schema()
    # logger.info(f"Schema being sent to Claude: {schema}") # Can be verbose

    tool_definition = {
        "name": "recommend_creators",
        "description": "Recommend the most suitable creators for a given brief",
        "input_schema": schema
    }

    # Force Claude to use the tool by setting tool_choice
    tool_choice = {
        "type": "tool",
        "name": "recommend_creators"
    }

    try:
        logger.info(f"System prompt for Claude (snippet): {system_prompt[:200]}...")
        logger.info(f"User prompt for Claude (brief snippet): {brief[:200]}...")
        logger.info(f"Number of creator style cards for prompt: {len(creator_style_cards)}")
        logger.info(f"Calling Anthropic API with model {model}, tool_choice: {tool_choice}")
        
        response = await client.call_with_functions(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            tools=[tool_definition],
            tool_choice=tool_choice,
            model=model,
            max_tokens=1000,
            temperature=0.0
        )
        logger.info("Anthropic API call successful.")

        # Process the response to extract the function call
        result = extract_function_call_result(response)

        # Ensure we have at most max_results creators
        if "recommended_creators" in result and len(result.get("recommended_creators", [])) > max_results:
            logger.info(f"Trimming recommended_creators from {len(result['recommended_creators'])} to {max_results}")
            result["recommended_creators"] = result["recommended_creators"][:max_results]
        
        logger.info(f"Exiting match_creators_to_brief with result: {result.get('recommended_creators')}")
        return result

    except Exception as e:
        logger.error(f"Error during Anthropic API call or processing in match_creators_to_brief: {e}", exc_info=True)
        return {
            "recommended_creators": [],
            "reasoning": f"Error: {str(e)}",
            "error": str(e)
        }

def extract_function_call_result(response) -> Dict[str, Any]:
    """
    Extract the function call result from the Anthropic API response.

    Args:
        response: The response object from the Anthropic API

    Returns:
        Dictionary with recommended creators and reasoning
    """
    try:
        # Check if there's a tool call in the response
        for content_block in response.content:
            if hasattr(content_block, 'type') and content_block.type == 'tool_use':
                # Extract the tool name and input
                tool_name = content_block.name
                tool_input = content_block.input

                if tool_name == "recommend_creators":
                    # Return the function call parameters
                    return tool_input

        # If we didn't find a tool call, log an error and return an empty result
        logger.error("No tool call found in the Anthropic response content")
        return {
            "recommended_creators": [],
            "reasoning": "No tool call found in the response",
            "error": "No tool call found in the response content from LLM."
        }

    except Exception as e:
        logger.error(f"Error extracting function call result: {e}", exc_info=True)
        return {
            "recommended_creators": [],
            "reasoning": f"Error: {str(e)}",
            "error": str(e)
        }
