"""Async retry decorators for API calls."""
import asyncio
import functools
from typing import Any, Callable, Optional, TypeVar, Awaitable

import logging

logger = logging.getLogger('linkedinsight.utils.async_retry')

# Type variable for preserving function return type
T = TypeVar('T')

def retry_api_call_async(
    max_retries: int = 3,
    retry_delay: int = 5,
    error_message: str = "Operation failed"
) -> Callable[[Callable[..., Awaitable[T]]], Callable[..., Awaitable[T]]]:
    """Async decorator for retrying API calls with exponential backoff.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_delay: Initial delay between retries in seconds (doubles each retry)
        error_message: Message to log on failure
        
    Example:
        @retry_api_call_async(max_retries=3, retry_delay=5)
        async def make_api_call():
            # Async API call that might fail
            pass
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            current_delay = retry_delay
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:  # Last attempt
                        logger.error(f"{error_message} after {max_retries} attempts: {str(e)}")
                        raise ValueError(f"{error_message} after {max_retries} attempts: {str(e)}")
                    
                    logger.warning(
                        f"Error on attempt {attempt + 1}/{max_retries}, "
                        f"retrying in {current_delay}s: {str(e)}"
                    )
                    await asyncio.sleep(current_delay)
                    current_delay *= 2  # Exponential backoff
            
            # Should never reach here due to raise in except block
            raise RuntimeError("Unexpected error in retry_api_call_async")
        return wrapper
    return decorator 