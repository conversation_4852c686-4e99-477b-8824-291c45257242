"""Ensure proper indexes exist for draft library performance."""

import asyncio
import logging
from src.utils.mongo_utils_async import get_async_mongo_db

logger = logging.getLogger(__name__)

async def ensure_draft_indexes():
    """Create necessary indexes for draft library performance."""
    try:
        db = await get_async_mongo_db()
        drafts_collection = db["drafts"]
        
        # Get existing indexes
        existing_indexes = await drafts_collection.index_information()
        
        # Create compound index for user queries with sort (if not exists)
        if not any("user_id_1" in idx and "created_at_-1" in idx for idx in existing_indexes.keys()):
            try:
                await drafts_collection.create_index([
                    ("user_id", 1),
                    ("created_at", -1)
                ], name="user_date_idx")
                logger.info("Created user_date_idx")
            except Exception as e:
                logger.info(f"Index user_date_idx already exists: {e}")
        
        # Create text index for search functionality
        if not any("text" in str(idx) for idx in existing_indexes.values()):
            try:
                await drafts_collection.create_index([
                    ("content", "text"),
                    ("brief", "text")
                ], name="content_search_idx")
                logger.info("Created content_search_idx")
            except Exception as e:
                logger.info(f"Text index already exists: {e}")
        
        # Create index for session queries
        if not any("session_id_1" in idx and "version_1" in idx for idx in existing_indexes.keys()):
            try:
                await drafts_collection.create_index([
                    ("session_id", 1),
                    ("version", 1)
                ], name="session_version_idx")
                logger.info("Created session_version_idx")
            except Exception as e:
                logger.info(f"Index session_version_idx already exists: {e}")
        
        # Create index for user_id alone for count queries
        if "user_id_1" not in existing_indexes:
            try:
                await drafts_collection.create_index(
                    "user_id",
                    name="user_idx"
                )
                logger.info("Created user_idx")
            except Exception as e:
                logger.info(f"Index user_idx already exists: {e}")
        
        logger.info("Draft library index check completed")
        
    except Exception as e:
        logger.error(f"Error creating draft indexes: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(ensure_draft_indexes())