"""MongoDB retrieval functions for LinkedInsight."""
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import json

from pymongo.database import Database
from pymongo.collection import Collection

# Import project modules
from src.utils.mongo_utils_async import (
    get_async_mongo_db,
    CREATORS_COLLECTION,
    ANALYSES_COLLECTION,
)

# Configure logging
logger = logging.getLogger(__name__)

async def fetch_latest_markdown_content(creator_name: str, analysis_type: str) -> Optional[str]:
    """
    Fetch the latest markdown content for a given creator and analysis type from MongoDB.

    Args:
        creator_name: The name of the creator.
        analysis_type: The type of analysis (e.g., 'overview', 'themes').

    Returns:
        The markdown content as a string, or None if not found or an error occurs.
    """
    # Map analysis types to their MongoDB analysis_type patterns
    analysis_type_to_db_pattern = {
        "overview": "overview_md",
        "themes": "themes_md",
        "hooks": "hooks_md",
        "body": "body_md",
        "endings": "endings_md",  # Standard: use "endings" (plural) consistently
        "linguistic": "linguistic_md"
    }

    # Use lowercase analysis type for lookup and pattern matching
    db_pattern = analysis_type_to_db_pattern.get(analysis_type.lower())
    if not db_pattern:
        logger.warning(f"Invalid analysis type '{analysis_type}' requested for markdown fetching.")
        return None

    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query for the latest analysis of the specified type
        query = {
            "creator_name": creator_name,
            "analysis_type": db_pattern
        }

        # Debug: Log the query
        logger.info(f"MongoDB query for {creator_name} ({analysis_type}): {query}")

        # Sort by generated_at in descending order to get the latest
        result = await db[ANALYSES_COLLECTION].find_one(
            query,
            sort=[("generated_at", -1)]
        )

        if not result:
            logger.info(f"No markdown content found for {creator_name} ({analysis_type})")

            # Debug: Check if there are any documents for this creator
            count = await db[ANALYSES_COLLECTION].count_documents(  # type: ignore[arg-type]
                {"creator_name": creator_name}
            )
            logger.info(f"Total documents for {creator_name}: {count}")

            # Debug: Check if there are any documents with this analysis type
            count_type = await db[ANALYSES_COLLECTION].count_documents(  # type: ignore[arg-type]
                {"analysis_type": db_pattern}
            )
            logger.info(f"Total documents with analysis type {db_pattern}: {count_type}")

            return None

        # Debug: Log the found document
        logger.info(f"Found document for {creator_name} ({analysis_type}): ID={result.get('_id')}, generated_at={result.get('generated_at')}")

        # Return the content field from the document
        return result.get("content")

    except Exception as e:
        logger.error(f"Error fetching markdown content from MongoDB: {e}")
        return None

async def fetch_latest_json_content(creator_name: str, analysis_type: str) -> Optional[Dict[str, Any]]:
    """
    Fetch the latest JSON content for a given creator and analysis type from MongoDB.

    Args:
        creator_name: The name of the creator.
        analysis_type: The type of analysis (e.g., 'overview', 'themes').

    Returns:
        The JSON content as a dictionary, or None if not found or an error occurs.
    """
    # Map analysis types to their MongoDB analysis_type patterns
    analysis_type_to_db_pattern = {
        "overview": "overview_json",
        "themes": "themes_json"
    }

    # Use lowercase analysis type for lookup and pattern matching
    db_pattern = analysis_type_to_db_pattern.get(analysis_type.lower())
    if not db_pattern:
        logger.warning(f"Invalid analysis type '{analysis_type}' requested for JSON fetching.")
        return None

    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query for the latest analysis of the specified type
        query = {
            "creator_name": creator_name,
            "analysis_type": db_pattern
        }

        # Sort by generated_at in descending order to get the latest
        result = await db[ANALYSES_COLLECTION].find_one(
            query,
            sort=[("generated_at", -1)]
        )

        if not result:
            logger.info(f"No JSON content found for {creator_name} ({analysis_type})")
            return None

        # Handle the content field based on its type
        content = result.get("content", {})

        # If content is a string, try to parse it as JSON
        if isinstance(content, str):
            try:
                return json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON content for {creator_name} ({analysis_type}): {e}")
                return None
        # If content is already a dictionary, return it directly
        elif isinstance(content, dict):
            return content
        else:
            logger.error(f"Unexpected content type for {creator_name} ({analysis_type}): {type(content)}")
            return None

    except Exception as e:
        logger.error(f"Error fetching JSON content from MongoDB: {e}")
        return None

async def get_all_creators() -> List[Dict[str, Any]]:
    """
    Get a list of all creators from MongoDB.

    Returns:
        A list of creator documents.
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query for all creators
        creators_cursor = db[CREATORS_COLLECTION].find()
        creators: List[Dict[str, Any]] = []
        async for creator in creators_cursor:
            creators.append(creator)

        # Convert ObjectId to string for JSON serialization
        for creator in creators:
            if "_id" in creator:
                creator["_id"] = str(creator["_id"])

        return creators

    except Exception as e:
        logger.error(f"Error fetching creators from MongoDB: {e}")
        return []

async def get_creator_info(creator_name: str) -> Optional[Dict[str, Any]]:
    """
    Get information about a specific creator from MongoDB.

    Args:
        creator_name: The name of the creator.

    Returns:
        A dictionary with creator information, or None if not found or an error occurs.
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query for the creator
        creator = await db[CREATORS_COLLECTION].find_one({"creator_name": creator_name})

        if not creator:
            logger.info(f"Creator not found: {creator_name}")
            return None

        # Convert ObjectId to string for JSON serialization
        if "_id" in creator:
            creator["_id"] = str(creator["_id"])

        # Count the number of analyses for this creator
        posts_count = await db[ANALYSES_COLLECTION].count_documents(  # type: ignore[arg-type]
            {"creator_name": creator_name}
        )

        # Get the first and last post dates
        first_post = await db[ANALYSES_COLLECTION].find_one(
            {"creator_name": creator_name},
            sort=[("generated_at", 1)]
        )

        last_post = await db[ANALYSES_COLLECTION].find_one(
            {"creator_name": creator_name},
            sort=[("generated_at", -1)]
        )

        # Format the response to match the CreatorInfo schema
        creator_info = {
            "creator_name": creator_name,
            "posts_count": posts_count,
            "first_post_date": first_post["generated_at"].isoformat() if first_post else None,
            "last_post_date": last_post["generated_at"].isoformat() if last_post else None,
            "engagement": None,  # We don't have engagement data in MongoDB yet
            "last_analyzed_at": creator.get("last_analyzed_at")
        }

        return creator_info

    except Exception as e:
        logger.error(f"Error fetching creator info from MongoDB: {e}")
        return None

async def save_analysis_content(creator_name: str, analysis_type: str, content: str, is_json: bool = False, metadata: Dict[str, Any] = None) -> str:
    """
    Save analysis content (markdown or JSON) to MongoDB.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (overview, themes, etc.)
        content: The content to save (markdown text or JSON data)
        is_json: Whether the content is JSON data
        metadata: Additional metadata about the analysis

    Returns:
        The ID of the saved document as a string
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Convert analysis_type to MongoDB pattern
        db_analysis_type = f"{analysis_type}_{'json' if is_json else 'md'}"

        # Prepare document
        document = {
            "creator_name": creator_name,
            "analysis_type": db_analysis_type,
            "generated_at": datetime.now(),
            "content": content,
            "metadata": metadata or {}
        }

        # Insert document
        result = await db[ANALYSES_COLLECTION].insert_one(document)
        doc_id = str(result.inserted_id)

        logger.info(f"Saved {analysis_type} {'JSON' if is_json else 'Markdown'} for {creator_name} to MongoDB (ID: {doc_id})")
        return doc_id

    except Exception as e:
        logger.error(f"Error saving {analysis_type} content to MongoDB: {e}")
        return ""

async def get_document_by_id(doc_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a document by its ID from MongoDB.

    Args:
        doc_id: The MongoDB document ID

    Returns:
        The document as a dictionary, or None if not found
    """
    try:
        from bson.objectid import ObjectId

        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query by ID
        document = await db[ANALYSES_COLLECTION].find_one({"_id": ObjectId(doc_id)})

        if not document:
            logger.warning(f"No document found with ID: {doc_id}")
            return None

        return document

    except Exception as e:
        logger.error(f"Error retrieving document by ID: {e}")
        return None

async def get_available_analyses(creator_name: str) -> Dict[str, datetime]:
    """
    Get a dictionary of available analysis types and their generation timestamps for a creator.

    Args:
        creator_name: The name of the creator.

    Returns:
        A dictionary mapping analysis types to their generation timestamps.
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()

        # Query for all analyses for this creator
        pipeline = [
            {"$match": {"creator_name": creator_name}},
            {"$sort": {"generated_at": -1}},
            {"$group": {
                "_id": "$analysis_type",
                "generated_at": {"$first": "$generated_at"}
            }}
        ]

        cursor = db[ANALYSES_COLLECTION].aggregate(pipeline)
        results: List[Dict[str, Any]] = [result async for result in cursor]

        # Convert to dictionary mapping analysis_type to generated_at
        analyses = {}
        for result in results:
            analysis_type = result["_id"]
            # Convert MongoDB analysis_type to API analysis_type
            if analysis_type.endswith("_md"):
                api_type = analysis_type[:-3]  # Remove _md suffix
                analyses[api_type] = result["generated_at"]

        return analyses

    except Exception as e:
        logger.error(f"Error fetching available analyses from MongoDB: {e}")
        return {}
