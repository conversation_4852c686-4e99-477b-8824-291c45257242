#!/usr/bin/env python3
"""
JSON to Markdown Converter Utility for LinkedInsight.

This module provides utility functions for converting hierarchical JSON data to human-readable
markdown format, with outer keys as headings, inner keys as subheadings, and
proper formatting of text values including line breaks.
"""

import json
import os
import argparse
import logging
from typing import Dict, Any, Union, List, Optional
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

def json_to_markdown(json_data: Union[Dict[str, Any], List, Any],
                     heading_level: int = 1,
                     include_keys: Optional[List[str]] = None,
                     exclude_keys: Optional[List[str]] = None) -> str:
    """
    Convert JSON data to markdown format.

    Args:
        json_data: The JSON data to convert (dict, list, or primitive value)
        heading_level: The heading level to start with (default: 1)
        include_keys: Optional list of keys to include (if None, include all)
        exclude_keys: Optional list of keys to exclude

    Returns:
        Markdown formatted string
    """
    markdown_lines = []

    # Handle different types of JSON data
    if isinstance(json_data, dict):
        for key, value in json_data.items():
            # Skip excluded keys or keys not in include_keys if specified
            if (exclude_keys and key in exclude_keys) or \
               (include_keys and key not in include_keys):
                continue

            # Add heading for the key
            markdown_lines.append(f"{'#' * heading_level} {key}")
            markdown_lines.append("")  # Empty line after heading

            # Process the value based on its type
            if isinstance(value, dict):
                # Process nested dictionary with increased heading level
                for sub_key, sub_value in value.items():
                    markdown_lines.append(f"{'#' * (heading_level + 1)} {sub_key}")
                    markdown_lines.append("")  # Empty line after subheading

                    # Process the sub-value
                    if isinstance(sub_value, (dict, list)):
                        nested_md = json_to_markdown(sub_value, heading_level + 2)
                        markdown_lines.append(nested_md)
                    else:
                        # Format primitive values
                        formatted_value = format_value(sub_value)
                        markdown_lines.append(formatted_value)
                        markdown_lines.append("")  # Empty line after value

            elif isinstance(value, list):
                # Handle lists
                if all(isinstance(item, (str, int, float, bool, type(None))) for item in value):
                    # Simple list of primitives
                    for item in value:
                        markdown_lines.append(f"- {format_value(item)}")
                    markdown_lines.append("")  # Empty line after list
                else:
                    # List of complex objects
                    for item in value:
                        if isinstance(item, dict):
                            # Try to find a good name for the item
                            item_name = None
                            for name_key in ['name', 'title', 'id', 'framework_name']:
                                if name_key in item:
                                    item_name = item[name_key]
                                    break

                            if item_name:
                                markdown_lines.append(f"{'#' * (heading_level + 1)} {item_name}")
                                markdown_lines.append("")  # Empty line after item name

                                # Process other keys in the item
                                for sub_key, sub_value in item.items():
                                    if sub_key != name_key:  # Skip the name key as we already used it
                                        markdown_lines.append(f"{'#' * (heading_level + 2)} {sub_key}")
                                        markdown_lines.append("")  # Empty line after subheading

                                        # Process the sub-value
                                        if isinstance(sub_value, (dict, list)):
                                            nested_md = json_to_markdown(sub_value, heading_level + 3)
                                            markdown_lines.append(nested_md)
                                        else:
                                            formatted_value = format_value(sub_value)
                                            markdown_lines.append(formatted_value)
                                            markdown_lines.append("")  # Empty line after value
                            else:
                                # No good name found, use generic item
                                nested_md = json_to_markdown(item, heading_level + 1)
                                markdown_lines.append(nested_md)
                        else:
                            # Non-dict item in list
                            formatted_value = format_value(item)
                            markdown_lines.append(formatted_value)
                            markdown_lines.append("")  # Empty line after value
            else:
                # Handle primitive values (strings, numbers, booleans)
                formatted_value = format_value(value)
                markdown_lines.append(formatted_value)
                markdown_lines.append("")  # Empty line after value

    elif isinstance(json_data, list):
        # Handle top-level lists
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                # Try to find a good name for the item
                item_name = f"Item {i+1}"
                for name_key in ['name', 'title', 'id', 'framework_name']:
                    if name_key in item:
                        item_name = item[name_key]
                        break

                markdown_lines.append(f"{'#' * heading_level} {item_name}")
                markdown_lines.append("")  # Empty line after item name

                # Process the item
                nested_md = json_to_markdown(item, heading_level + 1)
                markdown_lines.append(nested_md)
            else:
                # Non-dict item in list
                markdown_lines.append(f"- {format_value(item)}")

        markdown_lines.append("")  # Empty line after list

    else:
        # Handle primitive values at the top level
        formatted_value = format_value(json_data)
        markdown_lines.append(formatted_value)
        markdown_lines.append("")  # Empty line after value

    return "\n".join(markdown_lines)

def format_value(value: Any) -> str:
    """
    Format a primitive value for markdown display.

    Args:
        value: The value to format

    Returns:
        Formatted string
    """
    if value is None:
        return "None"

    # Convert to string
    str_value = str(value)

    # Handle escaped newlines
    if "\\n" in str_value:
        str_value = str_value.replace("\\n", "\n")

    # Try to parse JSON strings
    if isinstance(value, str) and (
        (str_value.startswith('{') and str_value.endswith('}')) or
        (str_value.startswith('[') and str_value.endswith(']'))
    ):
        try:
            # Handle escaped JSON strings
            if '\\\\' in str_value or '\\n' in str_value:
                clean_str = str_value.replace('\\\\', '\\').replace('\\n', '\n')
                parsed_value = json.loads(clean_str)
                return json_to_markdown(parsed_value, heading_level=3)
        except:
            pass

    return str_value

def json_file_to_markdown(input_file: str,
                          output_file: Optional[str] = None,
                          include_keys: Optional[List[str]] = None,
                          exclude_keys: Optional[List[str]] = None) -> str:
    """
    Convert a JSON file to markdown and optionally save to a file.

    Args:
        input_file: Path to the input JSON file
        output_file: Optional path to save the markdown output
        include_keys: Optional list of keys to include (if None, include all)
        exclude_keys: Optional list of keys to exclude

    Returns:
        Markdown formatted string
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        # Process nested JSON strings in the data
        processed_data = preprocess_json_data(json_data)

        markdown_content = json_to_markdown(processed_data, include_keys=include_keys, exclude_keys=exclude_keys)

        if output_file:
            # Create directory if it doesn't exist
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"Markdown saved to {output_file}")

        return markdown_content

    except Exception as e:
        logger.error(f"Error converting JSON file to markdown: {e}")
        raise

def preprocess_json_data(json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Preprocess JSON data to handle nested JSON strings.

    Args:
        json_data: The JSON data to preprocess

    Returns:
        Preprocessed JSON data
    """
    result = {}

    for key, value in json_data.items():
        if isinstance(value, str):
            # Check if the value is a JSON string
            if (value.startswith('{') and value.endswith('}')) or (value.startswith('[') and value.endswith(']')):
                try:
                    # Handle escaped JSON strings
                    if '\\\\' in value or '\\n' in value:
                        clean_str = value.replace('\\\\', '\\').replace('\\n', '\n')
                        parsed_value = json.loads(clean_str)
                        result[key] = parsed_value
                        continue
                except:
                    pass

        # If not a JSON string or parsing failed, keep the original value
        result[key] = value

    return result

def get_default_output_path(input_file: str) -> str:
    """
    Generate a default output path for the markdown file.

    Args:
        input_file: Path to the input JSON file

    Returns:
        Default path for the output markdown file
    """
    # Get the base directory of the project
    project_root = Path(__file__).parent.parent.parent

    # Create @outputs/markdown directory if it doesn't exist
    output_dir = project_root / "@outputs" / "markdown"
    output_dir.mkdir(parents=True, exist_ok=True)

    # Get the filename without extension and add .md
    input_filename = os.path.basename(input_file)
    output_filename = os.path.splitext(input_filename)[0] + ".md"

    return str(output_dir / output_filename)

def main():
    """Command line interface for the JSON to markdown converter."""
    parser = argparse.ArgumentParser(description="Convert JSON files to human-readable markdown.")
    parser.add_argument("--input", "-i", required=True, help="Path to the input JSON file.")
    parser.add_argument("--output", "-o", help="Path to the output markdown file. If not provided, uses default location in @outputs/markdown/.")
    parser.add_argument("--include", nargs="+", help="List of keys to include (if not specified, includes all).")
    parser.add_argument("--exclude", nargs="+", help="List of keys to exclude.")
    parser.add_argument("--print", "-p", action="store_true", help="Print the markdown to stdout in addition to saving it.")

    args = parser.parse_args()

    try:
        # Determine output file path
        output_file = args.output
        if not output_file:
            output_file = get_default_output_path(args.input)
            logger.info(f"No output file specified. Using default: {output_file}")

        markdown_content = json_file_to_markdown(
            args.input,
            output_file,
            include_keys=args.include,
            exclude_keys=args.exclude
        )

        if args.print:
            print(markdown_content)

    except Exception as e:
        logger.error(f"Error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    # Configure basic logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    exit(main())
