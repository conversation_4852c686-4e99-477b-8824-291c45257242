"""Utilities for standardized API calls with caching across analysis modules.

This module provides functions for creating standardized API parameters
that leverage Anthropic's prompt caching feature to reduce token usage
and improve response times when making multiple API calls with similar content.
"""

import asyncio
import logging
import sys
import os
import time
import json
from typing import Dict, List, Any, Optional

# Add project root to path if needed
utils_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(utils_dir)
project_root = os.path.dirname(src_dir)
if project_root not in sys.path:
    sys.path.append(project_root)


from src.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constant for minimum cacheable size (in tokens)
MIN_CACHEABLE_SIZE = 2048  # Minimum size for <PERSON><PERSON><PERSON>'s caching to work

def create_cacheable_content_block(posts: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create a single cacheable content block from a list of posts.

    This function combines all posts into a single block and adds
    the cache_control parameter required for Anthropic's caching feature.

    Args:
        posts: List of post dictionaries, each containing at least 'title' and 'content'
              or a 'source' field with the post content

    Returns:
        A dictionary representing a content block with cache_control set
    """
    # Create a single large block with all posts
    all_posts_text = "ALL POSTS:\n\n"
    post_count = 0

    # Combine all posts into a single block for efficient caching
    for post in posts:
        post_count += 1

        # Handle different post structures
        if 'type' in post and post.get('type') == 'document':
            # Document block format (as used in citation analysis)
            post_title = post.get('title', f'Post {post_count}')
            post_content = post['source']['data']
        else:
            # Direct post format
            post_title = post.get('title', f'Post {post_count}')
            post_content = post.get('content', '')

        all_posts_text += f"{post_title}\n{post_content}\n\n"

    # Add the cache control parameter
    return {
        "type": "text",
        "text": all_posts_text,
        "cache_control": {"type": "ephemeral"}
    }

def create_api_params(
    system_prompt: str,
    posts: List[Dict[str, Any]],
    user_prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 10000,
    temperature: float = 1.0,
    thinking_enabled: bool = True,
    thinking_budget_tokens: int = 8000
) -> Dict[str, Any]:
    """Create standardized API parameters for Claude API calls with caching.

    Args:
        system_prompt: System prompt for Claude
        posts: List of posts to analyze
        user_prompt: User prompt/instructions for the analysis
        model: Claude model to use (defaults to config.DEFAULT_MODEL)
        max_tokens: Maximum tokens in response (default: 10000)
        temperature: Temperature for generation (default: 1.0)
        thinking_enabled: Whether to enable thinking (default: True)
        thinking_budget_tokens: Budget for thinking tokens (default: 8000)

    Returns:
        Dictionary of API parameters ready to pass to Anthropic client
    """
    # Create system blocks with system prompt
    system_blocks = [{"type": "text", "text": system_prompt}]

    # Add cacheable content block
    posts_block = create_cacheable_content_block(posts)
    system = system_blocks + [posts_block]

    # Prepare messages
    messages = [
        {
            "role": "user",
            "content": [{"type": "text", "text": user_prompt}]
        }
    ]

    # Prepare API parameters
    api_params = {
        "model": model or Config().DEFAULT_MODEL,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "system": system,
        "messages": messages
    }

    # Add thinking if enabled
    if thinking_enabled:
        api_params["thinking"] = {"type": "enabled", "budget_tokens": thinking_budget_tokens}

    logger.debug(f"Created API params with {len(posts)} posts in a cacheable block")
    return api_params

def extract_cache_metrics(response: Any) -> Dict[str, Any]:
    """Extract and log cache metrics from an Anthropic API response.

    Args:
        response: Response from Anthropic API call

    Returns:
        Dictionary with cache metrics
    """
    metrics = {
        "cache_enabled": False,
        "cache_created": False,
        "cache_read": False,
        "tokens_saved": 0,
        "percent_saved": 0,
    }

    # Check if response has usage information
    if not hasattr(response, 'usage') or not response.usage:
        logger.warning("No usage information in API response")
        return metrics

    usage = response.usage
    
    # Add basic token counts
    if hasattr(usage, 'input_tokens'):
        metrics['input_tokens'] = usage.input_tokens
    if hasattr(usage, 'output_tokens'):
        metrics['output_tokens'] = usage.output_tokens
    
    logger.debug(f"Raw usage data: {usage}")

    # Check for cache creation
    if hasattr(usage, 'cache_creation_input_tokens') and usage.cache_creation_input_tokens:
        metrics["cache_enabled"] = True
        metrics["cache_created"] = True
        metrics["creation_tokens"] = usage.cache_creation_input_tokens
        logger.info(f"Cache created with {usage.cache_creation_input_tokens} tokens")

    # Check for cache read
    if hasattr(usage, 'cache_read_input_tokens') and usage.cache_read_input_tokens:
        metrics["cache_enabled"] = True
        metrics["cache_read"] = True
        metrics["read_tokens"] = usage.cache_read_input_tokens

        # Calculate savings
        tokens_saved = usage.cache_read_input_tokens
        potential_tokens = tokens_saved + usage.input_tokens
        percent_saved = (tokens_saved / potential_tokens) * 100 if potential_tokens > 0 else 0

        metrics["tokens_saved"] = tokens_saved
        metrics["percent_saved"] = percent_saved
        metrics["potential_tokens"] = potential_tokens

        logger.info(f"Cache hit! Read {tokens_saved} tokens from cache")
        logger.info(f"Token savings: {tokens_saved} tokens ({percent_saved:.2f}%)")

    return metrics

async def call_with_caching(
    client: Any,
    system_prompt: str,
    posts: List[Dict[str, Any]],
    user_prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 10000,
    temperature: float = 1.0,
    thinking_enabled: bool = True,
    thinking_budget_tokens: int = 8000
) -> Dict[str, Any]:
    """Make an API call with caching enabled.

    This is a centralized function for making cached API calls to reduce code duplication
    across analysis scripts.

    Args:
        client: AnthropicClient instance
        system_prompt: System prompt for Claude
        posts: List of posts to analyze
        user_prompt: User prompt/instructions for the analysis
        model: Claude model to use (defaults to Config().DEFAULT_MODEL)
        max_tokens: Maximum tokens in response (default: 10000)
        temperature: Temperature for generation (default: 1.0)
        thinking_enabled: Whether to enable thinking (default: True)
        thinking_budget_tokens: Budget for thinking tokens (default: 8000)

    Returns:
        Dictionary with response, text content, and cache metrics
    """
    logger.info(f"Making API call with caching enabled (model: {model or Config().DEFAULT_MODEL})")

    # Create API parameters with caching
    api_params = create_api_params(
        system_prompt=system_prompt,
        posts=posts,
        user_prompt=user_prompt,
        model=model,
        max_tokens=max_tokens,
        temperature=temperature,
        thinking_enabled=thinking_enabled,
        thinking_budget_tokens=thinking_budget_tokens
    )

    # Make API call
    response = await client.client.messages.create(**api_params)

    # Extract text from the response
    response_text = ""
    for block in response.content:
        if block.type == 'text':
            response_text += block.text

    # Extract and log cache metrics
    cache_metrics = extract_cache_metrics(response)
    logger.info(f"Cache metrics: {json.dumps(cache_metrics, indent=2)}")

    # Return response info
    return {
        "response": response,
        "text": response_text,
        "cache_metrics": cache_metrics
    }

def create_cacheable_user_input_block(user_input: str) -> Dict[str, Any]:
    """Create a cacheable content block from user input.

    This function formats user input as a cacheable block for the agent.

    Args:
        user_input: The user's input text (brief or draft post)

    Returns:
        A dictionary representing a content block with cache_control set
    """
    # Format the user input
    formatted_input = f"USER INPUT:\n\n{user_input}"

    # Add the cache control parameter
    return {
        "type": "text",
        "text": formatted_input,
        "cache_control": {"type": "ephemeral"}
    }

async def call_with_optimized_caching(
    client: Any,
    unified_system_prompt: str,
    user_input: str,
    session_linguistic_pattern: Optional[Dict[str, Any]],
    step_specific_prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 10000,
    temperature: float = 1.0,
    thinking_enabled: bool = True,
    thinking_budget_tokens: int = 8000,
    step_name: Optional[str] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    content_strategy: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Make an API call with optimized prompt caching for linguistic cohesion.

    This implements the cache-optimized structure:
    CACHED PREFIX: unified_system_prompt + user_input + content_strategy + session_linguistic_pattern
    VARYING SUFFIX: step_specific_prompt

    Args:
        client: AnthropicClient instance
        unified_system_prompt: Single system prompt that works for all steps
        user_input: User's input text to be cached
        session_linguistic_pattern: Session linguistic pattern for cache consistency
        step_specific_prompt: Step-specific instructions and patterns (varies per step)
        model: Claude model to use (defaults to Config().DEFAULT_MODEL)
        max_tokens: Maximum tokens in response (default: 10000)
        temperature: Temperature for generation (default: 1.0)
        thinking_enabled: Whether to enable thinking (default: True)
        thinking_budget_tokens: Budget for thinking tokens (default: 8000)
        step_name: Current step name for logging
        tools: Optional tools for the request
        content_strategy: Optional user content strategy for strategic context

    Returns:
        Dictionary with response, text content, and cache metrics
    """
    logger.info(f"Making optimized caching agent API call (model: {model or Config().DEFAULT_MODEL}, step: {step_name})")

    # Build the unified cached prefix content
    cached_content_parts = [unified_system_prompt]
    
    # Add user input section
    if user_input:
        cached_content_parts.append(f"\nUSER BRIEF:\n{user_input}")
    else:
        logger.error("No user input provided to call_with_optimized_caching!")
        cached_content_parts.append(f"\nUSER BRIEF:\nNo user input available.")
    
    # Add content strategy if available
    if content_strategy:
        from src.utils.strategy_utils import format_strategy_for_prompt
        strategy_section = f"\n{format_strategy_for_prompt(content_strategy)}"
        cached_content_parts.append(strategy_section)
        logger.info(f"Including content strategy in cached prefix for {step_name}")
        logger.debug(f"Strategy section ({len(strategy_section)} chars) added to cached prefix")
    else:
        logger.info(f"No content strategy provided for {step_name} - using authentic voice focus")
    
    # Add session linguistic pattern if available
    if session_linguistic_pattern:
        # session_linguistic_pattern is now a UnifiedPattern instance
        pattern_name = getattr(session_linguistic_pattern, 'name', 'Session Linguistic Pattern')
        pattern_content = session_linguistic_pattern.to_prompt_text() if hasattr(session_linguistic_pattern, 'to_prompt_text') else str(session_linguistic_pattern)
        if pattern_content:
            linguistic_section = f"\nSESSION LINGUISTIC PATTERN: {pattern_name}\n{pattern_content}"
            cached_content_parts.append(linguistic_section)
            logger.info(f"Including session linguistic pattern in cached prefix: {pattern_name}")
            logger.debug(f"Linguistic pattern content ({len(pattern_content)} chars): {pattern_content[:200]}...")
            logger.debug(f"Full linguistic section ({len(linguistic_section)} chars) added to cached prefix")
        else:
            logger.warning("Session linguistic pattern provided but has no content")
    else:
        logger.warning(f"No session linguistic pattern provided for {step_name} - this will impact consistency")
    
    cached_prefix = "\n".join(cached_content_parts)
    
    # Check if prefix meets minimum size for caching
    prefix_tokens_estimate = len(cached_prefix) // 4  # Rough estimate: 4 chars per token
    if prefix_tokens_estimate < MIN_CACHEABLE_SIZE:
        logger.warning(f"Cached prefix too small for caching: ~{prefix_tokens_estimate} tokens (min: {MIN_CACHEABLE_SIZE})")
        logger.info("Adding padding to meet minimum cache size...")
        # Add detailed instructions to meet minimum size
        padding = "\n\n" + "=" * 50 + "\n\nDETAILED INSTRUCTIONS FOR LINGUISTIC PATTERN APPLICATION:\n\n"
        padding += "When applying the session linguistic pattern above, ensure you:\n"
        padding += "1. Use the exact sentence structures provided - they are crucial for consistency\n"
        padding += "2. Incorporate the vocabulary patterns naturally throughout your response\n"
        padding += "3. Apply the rhetorical devices to enhance persuasion and engagement\n"
        padding += "4. Maintain consistency with the pattern's tone and style throughout\n"
        padding += "5. Never deviate from the established voice - it's key to coherent output\n" * 10  # Repeat to ensure we hit minimum
        cached_prefix += padding
        logger.info(f"Padded cached prefix to ~{len(cached_prefix) // 4} tokens")
    
    # Create system blocks with the unified cached prefix
    system_blocks = [{
        "type": "text", 
        "text": cached_prefix,
        "cache_control": {"type": "ephemeral"}
    }]
    

    # Prepare messages with step-specific content
    messages = [
        {
            "role": "user",
            "content": [{"type": "text", "text": step_specific_prompt}]
        }]

    # Create request params
    params = {
        "model": model or Config().DEFAULT_MODEL,
        "system": system_blocks,
        "messages": messages,
        "max_tokens": max_tokens,
        "temperature": temperature,
    }

    # Add thinking parameters if enabled
    if thinking_enabled:
        params["thinking"] = {"type": "enabled", "budget_tokens": thinking_budget_tokens}

    # Add tools if provided
    if tools:
        params["tools"] = tools

    # Add extra headers for prompt caching only
    extra_headers = {"anthropic-beta": "prompt-caching-2024-07-31"}
    # Note: thinking is enabled via request params, not beta headers

    # Make the API call
    start_time = time.time()
    
    try:
        # Log the request size for monitoring
        cached_prefix_size = len(cached_prefix)
        step_prompt_size = len(step_specific_prompt)
        logger.info(f"Request composition - cached prefix: {cached_prefix_size} chars, "
                   f"step-specific: {step_prompt_size} chars, "
                   f"caching ratio: {cached_prefix_size/(cached_prefix_size+step_prompt_size)*100:.1f}%")
        
        # Debug: Log what's in the cached prefix
        logger.debug(f"Cached prefix contains: system prompt ({len(unified_system_prompt)} chars), "
                    f"user brief ({len(user_input)} chars), "
                    f"linguistic pattern ({'YES' if session_linguistic_pattern else 'NO'})")
        
        # Log first part of system blocks to verify content
        logger.debug(f"System blocks preview: {str(system_blocks[0])[:300]}...")

        # Log the actual params being sent
        
        # Make the request
        response = await client.client.messages.create(
            **params,
            extra_headers=extra_headers
        )
        
        response_time = time.time() - start_time
        
        # Extract the response text
        text_content = ""
        thinking_content = ""
        
        if hasattr(response, 'thinking') and response.thinking:
            thinking_content = response.thinking

        for content_block in response.content:
            if hasattr(content_block, 'text'):
                text_content += content_block.text

        # Extract cache metrics
        cache_info = extract_cache_metrics(response)
        
        # Log performance and cache metrics
        logger.info(
            f"Optimized caching API call completed in {response_time:.2f}s: "
            f"input_tokens={cache_info.get('input_tokens', 0)}, "
            f"output_tokens={cache_info.get('output_tokens', 0)}, "
            f"cache_creation_tokens={cache_info.get('cache_creation_input_tokens', 0)}, "
            f"cache_read_tokens={cache_info.get('cache_read_input_tokens', 0)}"
        )
        
        # Calculate cache efficiency
        total_input = cache_info.get('input_tokens', 0)
        cached_read = cache_info.get('cache_read_input_tokens', 0)
        cache_hit_rate = (cached_read / total_input * 100) if total_input > 0 else 0
        
        logger.info(f"Cache efficiency for {step_name}: {cache_hit_rate:.1f}% hit rate "
                   f"({cached_read}/{total_input} tokens from cache)")

        return {
            "response": response,
            "text": text_content,
            "thinking": thinking_content,
            "cache_metrics": cache_info,
            "response_time": response_time,
            "cache_hit_rate": cache_hit_rate
        }

    except Exception as e:
        logger.error(f"Optimized caching API call failed: {e}", exc_info=True)
        raise


async def call_with_agent_caching(
    client: Any,
    system_prompt: str,
    user_input: str,
    user_prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 10000,
    temperature: float = 1.0,
    thinking_enabled: bool = True,
    thinking_budget_tokens: int = 8000,
    step_name: Optional[str] = None,
    tools: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Make an API call with caching enabled for agent interactions.

    This is similar to call_with_caching but designed for agent interactions
    where we want to cache the user input rather than posts.

    Args:
        client: AnthropicClient instance
        system_prompt: System prompt for Claude
        user_input: User's input text to be cached
        user_prompt: Formatted prompt for the current step
        model: Claude model to use (defaults to Config().DEFAULT_MODEL)
        max_tokens: Maximum tokens in response (default: 10000)
        temperature: Temperature for generation (default: 1.0)
        thinking_enabled: Whether to enable thinking (default: True)
        thinking_budget_tokens: Budget for thinking tokens (default: 8000)

    Returns:
        Dictionary with response, text content, and cache metrics
    """
    logger.info(f"Making agent API call with caching enabled (model: {model or Config().DEFAULT_MODEL})")

    # Create system blocks with system prompt
    system_blocks = [{"type": "text", "text": system_prompt}]

    # Add cacheable user input block
    user_input_block = create_cacheable_user_input_block(user_input)
    system = system_blocks + [user_input_block]

    # Prepare messages
    messages = [
        {
            "role": "user",
            "content": [{"type": "text", "text": user_prompt}]
        }
    ]

    # Prepare API parameters
    api_params = {
        "model": model or Config().DEFAULT_MODEL,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "system": system,
        "messages": messages
    }

    # Add tools if provided
    if tools:
        api_params["tools"] = tools
        logger.info(f"Added {len(tools)} tools to API call: {[t['name'] for t in tools]}")

    # Add thinking if enabled and model supports it
    if thinking_enabled:
        # Only add thinking for models that support it (not Claude-3-5-Haiku)
        if not api_params["model"].startswith("claude-3-5-haiku"):
            api_params["thinking"] = {"type": "enabled", "budget_tokens": thinking_budget_tokens}
        else:
            logger.info(f"Thinking disabled for {api_params['model']} as it doesn't support this feature")
    
    logger.debug(f"Created API params with cacheable user input block")

    # Log the model being used
    logger.info(f"Using model for agent API call: {api_params['model']}")
    
    # Make API call with debug logging
    try:
        from src.utils.pipeline_debug_logger import log_api_call
        log_api_call(api_params)
        
        response = await client.client.messages.create(**api_params)
        
        # Log successful API call
        log_api_call(api_params, response=response)
        
    except Exception as e:
        # Log failed API call
        from src.utils.pipeline_debug_logger import log_api_call
        log_api_call(api_params, error=e)
        raise

    # Handle tool execution if tools are provided
    if tools:
        from src.utils.tool_execution_handler import handle_tool_execution
        response_text, final_response = await handle_tool_execution(
            client=client,
            response=response,
            api_params=api_params,
            tools=tools
        )
        response = final_response  # Update response to the final one
    else:
        # Extract text normally when no tools
        response_text = ""
        for block in response.content:
            if block.type == 'text':
                response_text += block.text

    # Extract and log cache metrics
    cache_metrics = extract_cache_metrics(response)
    logger.info(f"Agent cache metrics: {json.dumps(cache_metrics, indent=2)}")

    # Extract thinking tokens if available
    thinking_text = ""
    if thinking_enabled and hasattr(response, 'thinking') and response.thinking:
        thinking_text = response.thinking
        if step_name:
            logger.info(f"Captured thinking tokens for step: {step_name}")

    # Return response info
    return {
        "response": response,
        "text": response_text,
        "cache_metrics": cache_metrics,
        "thinking": thinking_text,
        "step_name": step_name
    }