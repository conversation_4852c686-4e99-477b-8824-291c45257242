"""Centralized error handling utilities for MongoDB operations."""

import logging
from functools import wraps
from typing import Callable, Any, Awaitable

from fastapi import HTTPException
from pymongo.errors import PyMongoError

logger = logging.getLogger(__name__)


def handle_mongo_errors(operation_name: str):
    """
    Decorator to handle MongoDB errors consistently across all router endpoints.
    
    Args:
        operation_name: Description of the operation for error messages (e.g., "fetching transcripts")
    
    Usage:
        @handle_mongo_errors("creating user session")
        async def create_session(...):
            # MongoDB operations here
    """
    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # Re-raise HTTPException directly (business logic errors)
                raise
            except PyMongoError as e:
                logger.error(f"MongoDB error {operation_name}: {e}")
                raise HTTPException(
                    status_code=500, 
                    detail=f"Database error {operation_name}"
                )
            except Exception as e:
                logger.error(f"Unexpected error {operation_name}: {e}")
                raise HTTPException(
                    status_code=500, 
                    detail="An unexpected error occurred"
                )
        return wrapper
    return decorator


def handle_mongo_errors_with_context(operation_name: str, context_getter: Callable[..., str] = None):
    """
    Enhanced decorator that includes dynamic context in error messages.
    
    Args:
        operation_name: Description of the operation
        context_getter: Function to extract context from function args (e.g., user_email)
    
    Usage:
        @handle_mongo_errors_with_context(
            "deleting transcript", 
            lambda *args, **kwargs: f"transcript {kwargs.get('transcript_id')} for user {kwargs.get('user_email')}"
        )
        async def delete_transcript(transcript_id: str, user_email: str, ...):
            # MongoDB operations here
    """
    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # Re-raise HTTPException directly (business logic errors)  
                raise
            except PyMongoError as e:
                context = ""
                if context_getter:
                    try:
                        context = f" for {context_getter(*args, **kwargs)}"
                    except Exception:
                        pass  # If context extraction fails, continue without it
                        
                logger.error(f"MongoDB error {operation_name}{context}: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Database error {operation_name}"
                )
            except Exception as e:
                context = ""
                if context_getter:
                    try:
                        context = f" for {context_getter(*args, **kwargs)}"
                    except Exception:
                        pass
                        
                logger.error(f"Unexpected error {operation_name}{context}: {e}")
                raise HTTPException(
                    status_code=500,
                    detail="An unexpected error occurred"
                )
        return wrapper
    return decorator