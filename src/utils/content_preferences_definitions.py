"""
Single source of truth for content preferences.
THIS FILE defines all preference options and their AI guidance.

To add/modify/remove options:
1. Edit this file
2. Run the eval script to test
3. If happy, update the frontend to match

Current frontend location:
linkedinsight-viewer/src/components/content-preferences/ContentPreferencesForm.tsx
"""

# The actual preference fields that exist in the system
# Each field maps to its allowed values and AI guidance

CONTENT_PREFERENCES = {
    "opinion_style": {
        "strongly_opinionated": {
            "label": "Strongly opinionated",
            "ai_guidance": "Take strong stances unapologetically, with conviction and certainty."
        },
        "balanced": {
            "label": "Balanced perspective", 
            "ai_guidance": "Present multiple perspectives fairly, acknowledging nuance and complexity."
        },
        "nuanced": {
            "label": "Nuanced and thoughtful",
            "ai_guidance": "Explore the gray areas thoughtfully, avoiding black-and-white thinking."
        }
    },
    
    "emoji_usage": {
        "none": {
            "label": "No emojis",
            "ai_guidance": "No emojis - maintain pure text format.",
            "count": 0
        },
        "occasional": {
            "label": "Use emojis occasionally",
            "ai_guidance": "3-4 emojis to accent key points without overwhelming.",
            "count": "3-4"
        },
        "frequent": {
            "label": "Use emojis frequently",
            "ai_guidance": "8+ emojis to create a vibrant, visually engaging post.",
            "count": "8+"
        }
    },
    
    "voice_distinctiveness": {
        "corporate_standard": {
            "label": "Corporate standard",
            "ai_guidance": "Professional, polished, and safely within business norms."
        },
        "professionally_personal": {
            "label": "Professionally personal",
            "ai_guidance": "Business-appropriate with glimpses of personality."
        },
        "authentically_distinct": {
            "label": "Authentically distinct",
            "ai_guidance": "Clearly your voice while maintaining professionalism."
        },
        "radically_individual": {
            "label": "Radically individual",
            "ai_guidance": "Unmistakably you - conventions be damned."
        }
    },
    
    "controversy_approach": {
        "play_it_safe": {
            "label": "Play it safe",
            "ai_guidance": "Avoid any topics that might ruffle feathers."
        },
        "thoughtful_challenger": {
            "label": "Thoughtful challenger",
            "ai_guidance": "Question assumptions respectfully with evidence."
        },
        "willing_to_debate": {
            "label": "Willing to debate",
            "ai_guidance": "Engage opposing views directly and confidently."
        },
        "industry_provocateur": {
            "label": "Industry provocateur",
            "ai_guidance": "Deliberately challenge industry sacred cows."
        }
    },
    
    "humor_style": {
        "serious_only": {
            "label": "Serious only",
            "ai_guidance": "No humor - maintain a serious, professional tone throughout.",
            "humor_instances": 0,
            "humor_description": "Zero humor"
        },
        "dry_wit": {
            "label": "Dry wit",
            "ai_guidance": "Wry observations that highlight ironies without announcing them. The humor whispers rather than shouts.",
            "humor_instances": "2-3",
            "humor_description": "subtle, understated observations"
        },
        "playful": {
            "label": "Playful",
            "ai_guidance": "Whimsical observations and unexpected comparisons that spark joy. Humor that makes people smile, not cringe.",
            "humor_instances": "3-4",
            "humor_description": "lighthearted jokes and fun metaphors"
        },
        "irreverent": {
            "label": "Irreverent",
            "ai_guidance": "Irreverent commentary that challenges conventional thinking through cutting observations. The humor should make sacred cows uncomfortable.",
            "humor_instances": "4-5",
            "humor_description": "sharp, provocative wit"
        }
    },
    
    "brag_level": {
        "maximum_brag": {
            "label": "Maximum brag",
            "ai_guidance": "Own your achievements boldly and frequently."
        },
        "healthy_confidence": {
            "label": "Healthy confidence",
            "ai_guidance": "Share wins naturally without overselling."
        },
        "modest_success": {
            "label": "Modest success",
            "ai_guidance": "Downplay achievements with humility."
        },
        "credit_the_universe": {
            "label": "Credit the universe",
            "ai_guidance": "Attribute success to luck, timing, and others."
        }
    },
    
    "readability_style": {
        "dense": {
            "label": "Dense & comprehensive",
            "ai_guidance": "Dense, comprehensive paragraphs with detailed sentences. Academic style.",
            "paragraph_guidance": "3-5 sentences per paragraph, complex ideas fully explored."
        },
        "balanced": {
            "label": "Balanced & readable",
            "ai_guidance": "Mixed paragraph lengths for natural flow and readability.",
            "paragraph_guidance": "2-3 sentences per paragraph, varied for rhythm."
        },
        "airy": {
            "label": "Airy & scannable",
            "ai_guidance": "Short, scannable paragraphs with frequent visual breaks.",
            "paragraph_guidance": "1-2 sentences max per paragraph. White space is your friend."
        }
    }
}

# Helper function to get all valid values for a preference field
def get_valid_values(field_name):
    """Get list of valid values for a preference field."""
    if field_name in CONTENT_PREFERENCES:
        return list(CONTENT_PREFERENCES[field_name].keys())
    return []

# Helper function to get AI guidance for a specific preference value
def get_ai_guidance(field_name, value):
    """Get AI guidance for a specific preference value."""
    if field_name in CONTENT_PREFERENCES and value in CONTENT_PREFERENCES[field_name]:
        return CONTENT_PREFERENCES[field_name][value].get("ai_guidance", "")
    return ""