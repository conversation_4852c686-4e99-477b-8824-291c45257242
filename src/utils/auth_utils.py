"""Authentication utility functions for LinkedInsight API.

This module provides shared authentication utilities to eliminate code duplication
across API routers, particularly for extracting and validating clerk_user_id from JWT tokens.
"""

import logging
from typing import Dict, Any
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)


def extract_clerk_user_id(current_user: Dict[str, Any]) -> str:
    """
    Extract and validate clerk_user_id from JWT token payload.
    
    This function consolidates the repeated authentication logic across all API routers
    that need to extract the user ID from the JWT token.
    
    Args:
        current_user: The user payload from JWT token (from get_current_user dependency)
        
    Returns:
        str: The clerk_user_id extracted from the token
        
    Raises:
        HTTPException: If clerk_user_id is missing from the token payload
        
    Example:
        ```python
        from src.utils.auth_utils import extract_clerk_user_id
        
        @router.post("/some-endpoint")
        async def some_endpoint(current_user: dict = Depends(get_current_user)):
            clerk_user_id = extract_clerk_user_id(current_user)
            # Use clerk_user_id for database operations...
        ```
    """
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        logger.error("Clerk User ID not found in token payload.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )
    
    return clerk_user_id


def extract_clerk_user_id_with_context(current_user: Dict[str, Any], context: str = "") -> str:
    """
    Extract and validate clerk_user_id with additional context for logging.
    
    This is a variant of extract_clerk_user_id that allows for more specific
    error logging with context about which operation failed.
    
    Args:
        current_user: The user payload from JWT token (from get_current_user dependency)
        context: Additional context for error logging (e.g., "for GET request")
        
    Returns:
        str: The clerk_user_id extracted from the token
        
    Raises:
        HTTPException: If clerk_user_id is missing from the token payload
        
    Example:
        ```python
        clerk_user_id = extract_clerk_user_id_with_context(current_user, "for GET request")
        ```
    """
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        error_msg = f"Clerk User ID not found in token payload"
        if context:
            error_msg += f" {context}"
        error_msg += "."
        
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )
    
    return clerk_user_id
