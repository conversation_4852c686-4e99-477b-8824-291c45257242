"""Logging configuration for the application."""
import logging
import os
import sys
from typing import Optional, Dict, Any
from enum import Enum
from datetime import datetime, UTC

# Log format with timestamp, level, and message
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

class ErrorCategory(Enum):
    """Categories for different types of errors."""
    API_AUTH = "API_AUTH"           # Authentication failures
    API_RATE_LIMIT = "API_RATE"     # Rate limits hit
    API_TIMEOUT = "API_TIMEOUT"     # Timeouts
    DATA_ERROR = "DATA_ERROR"       # Data processing errors
    PIPELINE_ERROR = "PIPELINE"     # Pipeline stage failures
    INTEGRATION_ERROR = "INTEGRATION"  # External service errors

class ErrorSeverity(Enum):
    """Severity levels for errors."""
    CRITICAL = "CRITICAL"  # Pipeline blocking, data loss risk
    ERROR = "ERROR"        # Stage failure, recoverable
    WARNING = "WARNING"    # Non-blocking issues

def setup_logging(
    log_level: str = 'INFO',
    log_file: Optional[str] = None,
    max_bytes: int = 10_000_000,  # 10MB
    backup_count: int = 5
) -> None:
    """Configure application-wide logging.

    Args:
        log_level: Logging level (default: INFO)
        log_file: Optional path to log file. If None, logs to stderr
        max_bytes: Maximum size of each log file
        backup_count: Number of backup files to keep
    """
    # Convert string log level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)

    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create formatters
    formatter = logging.Formatter(LOG_FORMAT)

    # Always use stderr for logging (works in both dev and prod)
    stream_handler = logging.StreamHandler(sys.stderr)
    stream_handler.setFormatter(formatter)
    root_logger.addHandler(stream_handler)

    # Create loggers for each component
    logging.getLogger('linkedinsight.api')
    logging.getLogger('linkedinsight.core.ideas_extraction')
    logging.getLogger('linkedinsight.core.brief_generation')
    logging.getLogger('linkedinsight.utils')
    logging.getLogger('linkedinsight.transcript_processing')

    # Disable debug logging for external libraries
    logging.getLogger('anthropic').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    logging.getLogger('pymongo').setLevel(logging.WARNING)

class EnhancedLogger(logging.Logger):
    """Enhanced logger with support for error categories and notifications."""

    def __init__(self, name: str, max_history: int = 1000):
        """Initialize enhanced logger.

        Args:
            name: Logger name
            max_history: Maximum number of errors to keep in history (default: 1000)
        """
        super().__init__(name)
        self._error_history: Dict[str, Any] = {}
        self._max_history = max_history

    def log_error(
        self,
        msg: str,
        category: ErrorCategory,
        severity: ErrorSeverity,
        error: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log an error with category and severity.

        Args:
            msg: Error message
            category: Error category from ErrorCategory enum
            severity: Error severity from ErrorSeverity enum
            error: Optional exception object
            context: Optional dictionary with additional context
        """
        # Format error details
        error_details = {
            'timestamp': datetime.now(UTC).isoformat(),
            'category': category.value,
            'severity': severity.value,
            'message': msg,
            'error': str(error) if error else None,
            'context': context or {}
        }

        # Add to error history with size limit
        self._error_history[error_details['timestamp']] = error_details
        if len(self._error_history) > self._max_history:
            # Remove oldest entries to maintain size limit
            oldest_keys = sorted(self._error_history.keys())[:-self._max_history]
            for key in oldest_keys:
                del self._error_history[key]

        # Log with appropriate level
        if severity == ErrorSeverity.CRITICAL:
            self.critical(f"{category.value} - {msg}", exc_info=error is not None)
            # TODO: Future - Add notification system integration here
        elif severity == ErrorSeverity.ERROR:
            self.error(f"{category.value} - {msg}", exc_info=error is not None)
        else:
            self.warning(f"{category.value} - {msg}", exc_info=error is not None)

    def get_error_history(self) -> Dict[str, Any]:
        """Get the error history.

        Returns:
            Dictionary of timestamped error entries
        """
        return self._error_history

def get_logger(name: str) -> EnhancedLogger:
    """Get an enhanced logger for a specific component.

    Args:
        name: Name of the component (e.g., 'storyd_processor.cli')

    Returns:
        EnhancedLogger instance for the component
    """
    # Register our enhanced logger class
    logging.setLoggerClass(EnhancedLogger)
    return logging.getLogger(name)  # type: ignore
