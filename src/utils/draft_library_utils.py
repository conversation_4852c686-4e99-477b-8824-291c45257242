"""Utilities for managing the draft library."""

import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from src.utils.mongo_utils_async import get_async_mongo_db

logger = logging.getLogger(__name__)

# Collection name for drafts
DRAFTS_COLLECTION = "drafts"


async def save_draft_to_library(
    user_id: str,
    content: str,
    brief: str,
    post_length: str,
    generation_result: Dict[str, Any],
    job_id: Optional[str] = None,
    session_id: Optional[str] = None,
    draft_type: Optional[str] = None
) -> str:
    """
    Save a generated draft to the draft library.
    
    Args:
        user_id: User's Clerk ID
        content: Generated post content
        brief: Original brief used for generation
        post_length: Target post length (short, medium, long)
        generation_result: Full generation result containing metadata
        job_id: Optional job ID reference
        session_id: Optional session ID for grouping drafts
        draft_type: Optional draft type (draft_a, draft_b)
    
    Returns:
        Draft ID of the saved draft
    """
    try:
        db = await get_async_mongo_db()
        
        # Extract metadata from the generation result
        enhanced_scratchpad = generation_result.get("enhanced_scratchpad", {})
        
        # Extract linguistic pattern info from the session state if available
        # TODO: Pass session state to get linguistic pattern details
        linguistic_pattern_name = None
        linguistic_pattern_id = None
        
        # Build metadata
        metadata = {
            "linguistic_pattern_name": linguistic_pattern_name,
            "linguistic_pattern_id": linguistic_pattern_id,
            "hook_pattern": enhanced_scratchpad.get("hook_creation", {}).get("selected_hook_pattern"),
            "body_framework": enhanced_scratchpad.get("body_linguistic_construction", {}).get("selected_body_structure"),
            "ending_pattern": enhanced_scratchpad.get("ending_creation", {}).get("selected_ending_pattern"),
            "word_count": len(content.split()),
            "character_count": len(content),
            "generation_time_ms": None  # TODO: Track generation time
        }
        
        # Generate unique draft ID
        draft_id = f"draft_{uuid.uuid4().hex[:12]}"
        
        # Get version number for this session (if session_id provided)
        version = 1
        if session_id:
            existing_count = await db[DRAFTS_COLLECTION].count_documents({
                "session_id": session_id,
                "user_id": user_id
            })
            version = existing_count + 1
        
        # Create draft document
        draft_doc = {
            "draft_id": draft_id,
            "session_id": session_id,
            "user_id": user_id,
            "content": content,
            "brief": brief,
            "post_length": post_length,
            "version": version,
            "metadata": metadata,
            "created_at": datetime.utcnow(),
            "job_id": job_id,
            "draft_type": draft_type,
            "explanation": generation_result.get("explanation")  # Add explanation from generation result
        }
        
        # Insert draft
        await db[DRAFTS_COLLECTION].insert_one(draft_doc)
        
        # Create indexes if they don't exist
        await db[DRAFTS_COLLECTION].create_index([("user_id", 1), ("created_at", -1)])
        await db[DRAFTS_COLLECTION].create_index([("session_id", 1), ("version", 1)])
        await db[DRAFTS_COLLECTION].create_index([("draft_id", 1)])
        
        logger.info(f"Saved draft {draft_id} to library for user {user_id}")
        return draft_id
        
    except Exception as e:
        logger.error(f"Error saving draft to library: {e}", exc_info=True)
        raise


async def get_draft_by_id(draft_id: str, user_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a specific draft by ID.
    
    Args:
        draft_id: Draft ID to retrieve
        user_id: User ID for authorization
    
    Returns:
        Draft document or None if not found
    """
    try:
        db = await get_async_mongo_db()
        draft = await db[DRAFTS_COLLECTION].find_one({
            "draft_id": draft_id,
            "user_id": user_id
        })
        return draft
    except Exception as e:
        logger.error(f"Error retrieving draft {draft_id}: {e}")
        return None


async def get_drafts_by_session(session_id: str, user_id: str) -> list:
    """
    Get all drafts for a specific session.
    
    Args:
        session_id: Session ID to filter by
        user_id: User ID for authorization
    
    Returns:
        List of draft documents
    """
    try:
        db = await get_async_mongo_db()
        cursor = db[DRAFTS_COLLECTION].find({
            "session_id": session_id,
            "user_id": user_id
        }).sort("version", 1)
        
        drafts = []
        async for draft in cursor:
            drafts.append(draft)
        return drafts
    except Exception as e:
        logger.error(f"Error retrieving drafts for session {session_id}: {e}")
        return []


async def get_user_drafts(
    user_id: str,
    limit: int = 20,
    skip: int = 0,
    filter_query: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Get paginated drafts for a user with optional filtering.
    
    Args:
        user_id: User ID
        limit: Number of drafts to return
        skip: Number of drafts to skip
        filter_query: Optional additional filter criteria
    
    Returns:
        Dictionary with drafts and pagination info
    """
    try:
        db = await get_async_mongo_db()
        
        # Build query
        query = {"user_id": user_id}
        if filter_query:
            query.update(filter_query)
        
        # Get total count
        total = await db[DRAFTS_COLLECTION].count_documents(query)
        
        # Get paginated results
        cursor = db[DRAFTS_COLLECTION].find(query).sort(
            "created_at", -1
        ).skip(skip).limit(limit)
        
        drafts = []
        async for draft in cursor:
            drafts.append(draft)
        
        return {
            "drafts": drafts,
            "total": total,
            "limit": limit,
            "skip": skip
        }
    except Exception as e:
        logger.error(f"Error retrieving user drafts: {e}")
        return {"drafts": [], "total": 0, "limit": limit, "skip": skip}