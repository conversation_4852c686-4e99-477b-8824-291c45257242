"""
Vector-based pattern selector using MongoDB Atlas Vector Search.

This module replaces the citation-based pattern selector with embedding similarity search
to find the most relevant patterns from our pre-indexed pattern database.
"""

import os
import logging
import time
from typing import Dict, List, Any, Optional, Literal
import voyageai
from pymongo import MongoClient
from src.utils.mongo_utils import get_mongo_client, get_mongo_db
from src.utils.dual_pattern_search import DualPatternSearcher
from src.constants.collections import PATTERN_COLLECTION
from src.utils.pattern_format import UnifiedPattern, PatternType, convert_mongodb_to_unified, batch_convert_to_unified

logger = logging.getLogger(__name__)

# Vector search configuration
VECTOR_SEARCH_INDEX = "pattern_embeddings_vector_index"

# Performance-tuned parameters
DEFAULT_NUM_CANDIDATES = 150  # Increased for better quality (was 100)
MIN_SIMILARITY_THRESHOLD = 0.7  # Minimum similarity score to include results
DEFAULT_MAX_SELECTIONS = {
    "hook": 2,  # One pattern per draft for focused differentiation
    "body": 2,  # One pattern per draft for focused differentiation
    "ending": 2  # One pattern per draft for focused differentiation
}

# Performance optimization settings
ENABLE_SIMILARITY_FILTERING = True  # Filter results by similarity threshold
CACHE_EMBEDDINGS = True  # Cache embeddings for repeated queries (future enhancement)


class VectorPatternSelector:
    """Select relevant patterns using vector similarity search."""
    
    def __init__(self):
        self.db = get_mongo_db()
        self.collection = self.db[PATTERN_COLLECTION]
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        self.dual_searcher = DualPatternSearcher(self.voyage_client, self._vector_search)
        
    async def select_session_linguistic_patterns(self, input_text: str) -> Dict[str, Any]:
        """
        Select two different linguistic patterns for draft variety within the session.
        
        Args:
            input_text: The input text for pattern selection (brief, idea with citations, etc.)
            
        Returns:
            Dict with 'draft_a' and 'draft_b' linguistic patterns, or empty dict if selection fails
        """
        try:
            start_time = time.time()
            logger.info(f"Selecting session linguistic patterns for input: {input_text[:100]}...")
            
            # Build query focused on linguistic style matching
            query_text = f"Content about: {input_text} Looking for linguistic style and voice patterns"
            
            # Generate embedding for query
            embedding_result = self.voyage_client.embed([query_text], model="voyage-3.5")
            query_embedding = embedding_result.embeddings[0]
            
            # Search for linguistic patterns - get top 2 for variety
            patterns = self._vector_search(
                query_embedding=query_embedding,
                analysis_type="linguistic_bundle",
                limit=2  # Two different linguistic patterns for draft variety
            )
            
            total_time = time.time() - start_time
            logger.info(f"Session linguistic pattern selection took {total_time:.3f}s, "
                       f"found {len(patterns)} pattern(s)")
            
            # Convert to unified patterns
            unified_patterns = batch_convert_to_unified(patterns, PatternType.LINGUISTIC)
            
            result = {}
            if len(unified_patterns) >= 2:
                result['draft_a'] = unified_patterns[0]
                result['draft_b'] = unified_patterns[1]
                logger.info(f"Selected linguistic patterns - draft_a: {unified_patterns[0].name} "
                           f"(score: {unified_patterns[0].score:.4f}), "
                           f"draft_b: {unified_patterns[1].name} "
                           f"(score: {unified_patterns[1].score:.4f})")
            elif len(unified_patterns) == 1:
                # If only one pattern found, use it for both drafts
                result['draft_a'] = unified_patterns[0]
                result['draft_b'] = unified_patterns[0]
                logger.warning(f"Only one linguistic pattern found, using for both drafts: {unified_patterns[0].name}")
            else:
                logger.warning("No linguistic patterns found for session")
                
            return result
                
        except Exception as e:
            logger.error(f"Session linguistic pattern selection failed: {e}", exc_info=True)
            return {}

    async def select_relevant_patterns(
        self,
        step_type: Literal["hook", "body", "ending"],
        context: Dict[str, Any],
        max_selections: Optional[Dict[str, int]] = None,
        session_linguistic_pattern: Optional[Any] = None  # UnifiedPattern instance
    ) -> Dict[str, Any]:
        """
        Select the most relevant patterns using vector similarity search.
        Now includes session linguistic patterns for cohesion across all steps.
        
        Args:
            step_type: The type of content being generated ("hook", "body", or "ending")
            context: Generation context including:
                - user_input: Original user input/brief
                - target_length: Target post length (short/medium/long)
                - generated_hook: (for body/ending) The hook already generated
                - generated_body: (for ending) The body already generated
            max_selections: Optional dict specifying max selections per pattern type
            session_linguistic_pattern: Session-wide linguistic pattern for voice consistency
        
        Returns:
            Dictionary containing selected patterns for the step + session linguistic pattern
        """
        if max_selections is None:
            max_selections = DEFAULT_MAX_SELECTIONS
            
        try:
            start_time = time.time()
            
            # For body generation, select body frameworks and include session linguistic pattern
            if step_type == "body":
                logger.info("Selecting body frameworks for body step + including session linguistic pattern")
                query_text = self._build_query_text(step_type, context)
                embedding_result = self.voyage_client.embed([query_text], model="voyage-3.5")
                query_embedding = embedding_result.embeddings[0]
                
                limit = max_selections.get(step_type, 5)
                patterns = self._vector_search(
                    query_embedding=query_embedding,
                    analysis_type="body",
                    limit=limit
                )
                
                # Format body frameworks and include session linguistic pattern
                result = self._format_patterns_for_step(step_type, patterns)
                if session_linguistic_pattern:
                    result["session_linguistic_pattern"] = session_linguistic_pattern
                    # session_linguistic_pattern is now a UnifiedPattern instance
                    pattern_name = session_linguistic_pattern.name if hasattr(session_linguistic_pattern, 'name') else 'unknown'
                    logger.info(f"Added session linguistic pattern to body: {pattern_name}")
                return result
            
            # For hook/ending, use original single search
            # Build query text based on context and step type
            query_start = time.time()
            query_text = self._build_query_text(step_type, context)
            query_build_time = time.time() - query_start
            logger.info(f"Vector search query for {step_type}: {query_text[:100]}...")
            
            # Generate embedding for query
            embedding_start = time.time()
            embedding_result = self.voyage_client.embed([query_text], model="voyage-3.5")
            query_embedding = embedding_result.embeddings[0]
            embedding_time = time.time() - embedding_start
            
            # Perform vector search
            search_start = time.time()
            limit = max_selections.get(step_type, 3)
            patterns = self._vector_search(
                query_embedding=query_embedding,
                analysis_type=step_type,
                limit=limit
            )
            search_time = time.time() - search_start
            
            total_time = time.time() - start_time
            
            # Log performance metrics
            logger.info(f"Vector search performance for {step_type}: "
                       f"total={total_time:.3f}s, query_build={query_build_time:.3f}s, "
                       f"embedding={embedding_time:.3f}s, search={search_time:.3f}s, "
                       f"found={len(patterns)} patterns")
            
            # Transform patterns to match expected format and include session linguistic pattern
            result = self._format_patterns_for_step(step_type, patterns)
            if session_linguistic_pattern:
                result["session_linguistic_pattern"] = session_linguistic_pattern
                # session_linguistic_pattern is now a UnifiedPattern instance
                pattern_name = session_linguistic_pattern.name if hasattr(session_linguistic_pattern, 'name') else 'unknown'
                logger.info(f"Added session linguistic pattern to {step_type}: {pattern_name}")
            return result
            
        except Exception as e:
            logger.error(f"Vector pattern selection failed for {step_type}: {e}", exc_info=True)
            return self._empty_response_for_step(step_type)
    
    def _build_query_text(self, step_type: str, context: Dict[str, Any]) -> str:
        """Build a query text that captures the context for vector search."""
        parts = []
        
        
        # Add step-specific context
        if step_type == "hook":
            # For hooks, focus on the user input
            user_input = context.get('user_input', '')
            if user_input:
                # Use full user input for better context
                parts.append(f"Content about: {user_input}")
            parts.append("Looking for engaging opening hook patterns")
            
        elif step_type == "body":
            # Add user input for context
            user_input = context.get('user_input', '')
            if user_input:
                parts.append(f"Content about: {user_input}")
            
            # For body, consider the hook that was generated
            hook = context.get('generated_hook', '')
            if hook:
                parts.append(f"Starting with hook: {hook}")
            parts.append("Looking for body structure and framework patterns")
            
            # Add target length preference
            length = context.get('target_length', 'medium')
            if length == 'short':
                parts.append("Concise body patterns for short posts")
            elif length == 'long':
                parts.append("Detailed body patterns for comprehensive posts")
                
        elif step_type == "ending":
            # Add user input for context
            user_input = context.get('user_input', '')
            if user_input:
                parts.append(f"Content about: {user_input}")
            
            # For endings, consider both hook and body
            hook = context.get('generated_hook', '')
            if hook:
                parts.append(f"Post starting with: {hook[:100]}...")
            
            body = context.get('generated_body', '')
            if body:
                # Take the last part of the body for context
                excerpt = body[-200:] if len(body) > 200 else body
                parts.append(f"Body ending with: ...{excerpt}")
            
            parts.append("Looking for compelling ending patterns and CTAs")
        
        return " ".join(parts)
    
    def _vector_search(
        self, 
        query_embedding: List[float], 
        analysis_type: str,
        limit: int
    ) -> List[Dict[str, Any]]:
        """Perform vector search with optional filtering."""
        
        # Use existing index filter fields
        search_filter = {"pattern_type": analysis_type}
        
        pipeline = [
            {
                "$vectorSearch": {
                    "index": VECTOR_SEARCH_INDEX,
                    "path": "embedding",
                    "queryVector": query_embedding,
                    "numCandidates": DEFAULT_NUM_CANDIDATES,
                    "limit": limit,
                    "filter": search_filter
                }
            },
            {
                "$project": {
                    "_id": 1,  # Include _id for pattern identification
                    # For frameworks (body), return framework-specific fields
                    "name": 1,  # Framework name
                    "pattern_name": 1,  # Fallback for compatibility
                    "description": 1,
                    "implementation_steps": 1,  # Rich framework steps
                    "example": 1,  # Rich framework example
                    # For legacy patterns (hook/ending)
                    "when_to_use": 1,
                    "searchable_text": 1,
                    # For linguistic bundles
                    "bundle_name": 1,
                    "bundle_description": 1,
                    "usage_context": 1,
                    "bundle_text": 1,
                    "pattern_type": 1,
                    "score": {"$meta": "vectorSearchScore"}
                }
            }
        ]
        
        try:
            mongo_start = time.time()
            results = list(self.collection.aggregate(pipeline))
            mongo_time = time.time() - mongo_start
            
            # Apply similarity threshold filtering for quality improvement
            if ENABLE_SIMILARITY_FILTERING:
                filtered_results = [r for r in results if r.get('score', 0) >= MIN_SIMILARITY_THRESHOLD]
                if len(filtered_results) < len(results):
                    logger.info(f"Similarity filtering: {len(results)} → {len(filtered_results)} patterns "
                               f"(threshold={MIN_SIMILARITY_THRESHOLD})")
                results = filtered_results
            
            # Log MongoDB query performance
            logger.info(f"MongoDB vector search took {mongo_time:.3f}s for {analysis_type}, "
                       f"returned {len(results)} quality patterns")
            
            # Log the scores for debugging
            for r in results:
                pattern_name = r.get('name') or r.get('pattern_name', 'unknown')
                logger.debug(
                    f"Pattern: {pattern_name} - Score: {r.get('score', 0):.4f}"
                )
            
            return results
            
        except Exception as e:
            logger.error(f"Vector search error for {analysis_type}: {e}", exc_info=True)
            # Log search parameters for debugging
            logger.error(f"Search parameters: index={VECTOR_SEARCH_INDEX}, "
                        f"collection={PATTERN_COLLECTION}, limit={limit}, "
                        f"embedding_dim={len(query_embedding) if query_embedding else 'None'}")
            return []
    
    def _format_patterns_for_step(
        self, 
        step_type: str, 
        patterns: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Format patterns using the new unified pattern format."""
        
        # Determine pattern type based on step
        pattern_type_map = {
            "hook": PatternType.HOOK,
            "body": PatternType.BODY,
            "ending": PatternType.ENDING
        }
        pattern_type = pattern_type_map.get(step_type, PatternType.HOOK)
        
        # Convert to unified patterns - this is the ONLY format we use now
        unified_patterns = batch_convert_to_unified(patterns, pattern_type)
        
        # Return unified patterns directly
        if step_type == "hook":
            return {"selected_archetypes": unified_patterns}
        elif step_type == "body":
            return {"selected_structures": unified_patterns}
        elif step_type == "ending":
            return {"selected_endings": unified_patterns}
        
        return {}
    
    # REMOVED: _build_citation_text method - no longer needed with UnifiedPattern
    
    def _empty_response_for_step(self, step_type: str) -> Dict[str, Any]:
        """Return an empty response appropriate for the step type."""
        if step_type == "hook":
            return {"selected_archetypes": []}
        elif step_type == "body":
            return {"selected_structures": [], "selected_frameworks": [], "selected_linguistic": []}
        elif step_type == "ending":
            return {"selected_endings": []}
        return {}


# Singleton instance
_vector_selector = None


async def select_relevant_patterns_vector(
    step_type: Literal["hook", "body", "ending"],
    context: Dict[str, Any],
    max_selections: Optional[Dict[str, int]] = None,
    session_linguistic_pattern: Optional[Any] = None  # UnifiedPattern instance
) -> Dict[str, Any]:
    """
    Public interface for vector-based pattern selection.
    
    Now supports session linguistic patterns for cohesion across all steps.
    Uses vector search for pattern selection.
    """
    global _vector_selector
    
    if _vector_selector is None:
        _vector_selector = VectorPatternSelector()
    
    return await _vector_selector.select_relevant_patterns(
        step_type=step_type,
        context=context,
        max_selections=max_selections,
        session_linguistic_pattern=session_linguistic_pattern
    )