"""Citation schema definition.

This schema defines the structure for citations used throughout the system.
Citations link extracted content back to specific moments in the transcript,
preserving speaker attribution and temporal context.
"""
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator


class Citation(BaseModel):
    """Citation model with strict validation for required fields.
    
    The Citation model intentionally accepts timestamps in any format that appears in the source transcript.
    This design decision allows us to:
    1. Preserve the original context and formatting from the source
    2. Support various transcript formats (different time notations, session markers, etc.)
    3. Maintain a direct link back to the source material without normalization
    
    Example timestamp formats supported:
    - Standard: [00:00:15]
    - 12-hour: [2:30 PM]
    - With context: [Session 1, 15:30]
    - With timezone: [3:15pm EST]
    - Duration style: [2h15m]
    - And any other format that meaningfully marks a position in the transcript
    """
    text: str = Field(..., description="The quote text without timestamp or speaker")
    timestamp: str = Field(..., description="The timestamp as it appears in the transcript")
    speaker: Optional[str] = Field(default="", description="The name of the speaker")

    @field_validator('speaker')
    @classmethod
    def validate_speaker_name(cls, v):
        """Clean speaker name if present."""
        return v.strip() if v else ""


# Schema for tool definitions
CITATION_SCHEMA = {
    "type": "object",
    "properties": {
        "text": {
            "type": "string",
            "description": "The quote text without timestamp or speaker"
        },
        "timestamp": {
            "type": "string",
            "description": "When this content appears in the transcript"
        },
        "speaker": {
            "type": "string",
            "description": "The name of the speaker"
        }
    },
    "required": ["text", "timestamp", "speaker"]
}

def create_citation_from_data(citation_data: dict) -> Citation:
    """Create a Citation object from raw citation data.
    
    Args:
        citation_data: Dictionary containing citation data (text, timestamp, speaker)
        
    Returns:
        Citation: A properly formatted Citation object
        
    Raises:
        ValueError: If required citation data is missing or invalid
    """
    try:
        return Citation(
            text=citation_data.get('text', ''),
            timestamp=citation_data.get('timestamp', ''),
            speaker=citation_data.get('speaker', '')
        )
    except Exception as e:
        raise ValueError(f"Failed to create Citation from data: {e}") from e 

def build_mini_transcript(citations: List[Citation]) -> str:
    """Build a mini-transcript from relevant citations.
    
    Args:
        citations: List of Citation objects supporting an idea
            
    Returns:
        str: A concatenated string of citation texts
    """
    if not citations:
        return ""
            
    # Join citation texts with double newlines for readability
    return "\n\n".join(citation.text for citation in citations)