"""Anthropic API client implementation with function calling support."""
from typing import Any, Dict, Optional
from anthropic import AsyncAnthropic
import asyncio
from functools import wraps
import logging
import logfire
import braintrust

# Import Config
import sys
import os

# Add the src directory to the Python path
src_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if src_dir not in sys.path:
    sys.path.append(src_dir)

from config import Config

logger = logging.getLogger(__name__)

# Get config instance
config = Config()

# Configure Logfire for Anthropic instrumentation
logfire.configure()
logfire.instrument_anthropic()
logger.info("Logfire instrumentation configured for Anthropic API")

# Initialize Braintrust (it will use BRAINTRUST_API_KEY from environment)
try:
    # This will automatically use the BRAINTRUST_API_KEY from environment
    logger.info("Braintrust instrumentation initialized")
except Exception as e:
    logger.warning(f"Braintrust initialization skipped: {e}")

def retry_async(retries: int = 3, delay: int = 1):
    """Decorator for retrying async functions on failure."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Use config values if available, otherwise use decorator defaults
            _retries = config.MAX_RETRIES if hasattr(config, 'MAX_RETRIES') else retries
            _delay = config.RETRY_DELAY if hasattr(config, 'RETRY_DELAY') else delay

            for attempt in range(_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == _retries - 1:
                        logger.error(f"Final attempt ({attempt + 1}) failed: {str(e)}")
                        raise
                    logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                    # Use exponential backoff based on configured delay
                    await asyncio.sleep(_delay * (2 ** attempt))
            return None # Should not be reached if retries >= 1 and final attempt raises
        return wrapper
    return decorator

class AnthropicClient:
    """Anthropic API client with function calling support."""

    def __init__(self):
        """Initialize client using configuration from config.py."""
        # Create the base client
        base_client = AsyncAnthropic(api_key=config.ANTHROPIC_API_KEY)
        
        # Wrap with Braintrust for automatic tracing
        try:
            self.client = braintrust.wrap_anthropic(base_client)
            logger.info("Anthropic client wrapped with Braintrust tracing")
        except Exception as e:
            # Fallback to unwrapped client if Braintrust isn't configured
            logger.warning(f"Braintrust wrapping failed, using base client: {e}")
            self.client = base_client

    # Apply decorator with values from config
    @retry_async(retries=config.MAX_RETRIES, delay=config.RETRY_DELAY)
    async def call_claude(
        self,
        *,
        system_prompt: str,
        user_prompt: str,
        model: Optional[str] = None, # Allow overriding default model
        max_tokens: int = 16000,
        temperature: float = 1.0,
        thinking_enabled: bool = True,
        thinking_budget_tokens: int = 12000
    ) -> Any:
        """Call Claude for text generation (without function calling).

        Args:
            system_prompt: System prompt for Claude
            user_prompt: User prompt/question
            model: Claude model to use. Defaults to config.DEFAULT_MODEL if None
            max_tokens: Maximum tokens in response
            temperature: Temperature for generation
            thinking_enabled: Whether to enable extended thinking
            thinking_budget_tokens: Budget for thinking tokens when enabled

        Returns:
            API response object from Anthropic
        """
        resolved_model = model or config.DEFAULT_MODEL
        logger.debug(f"Calling Anthropic API (text) with model: {resolved_model}")

        try:
            # Prepare messages
            messages = [
                {
                    "role": "user",
                    "content": [{"type": "text", "text": user_prompt}]
                }
            ]

            # Set up API arguments
            api_args = {
                "model": resolved_model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "system": system_prompt,
                "messages": messages,
            }

            # Add thinking if enabled
            if thinking_enabled:
                api_args["thinking"] = {"type": "enabled", "budget_tokens": thinking_budget_tokens}
                logger.debug(f"Extended thinking enabled with budget: {thinking_budget_tokens}")

            # Make the API call
            response = await self.client.messages.create(**api_args)
            logger.debug("Anthropic API call successful.")
            return response

        except Exception as e:
            logger.error(f"Anthropic API call failed: {str(e)}", exc_info=True)
            raise

    # Apply decorator with values from config
    @retry_async(retries=config.MAX_RETRIES, delay=config.RETRY_DELAY)
    async def call_with_functions(
        self,
        *,
        system_prompt: str,
        user_prompt: str,
        tools: list[Dict[str, Any]],
        tool_choice: Optional[Dict[str, str]] = None,
        model: Optional[str] = None, # Allow overriding default model
        max_tokens: int = 4000,
        temperature: float = 0,
        thinking: Optional[Dict[str, Any]] = None # Add thinking parameter
    ) -> Any:
        """Call Claude with function definitions.

        Args:
            system_prompt: System prompt for Claude
            user_prompt: User prompt/question
            tools: List of tool/function definitions
            tool_choice: Optional forced tool selection
            model: Claude model to use. Defaults to config.DEFAULT_MODEL if None.
            max_tokens: Maximum tokens in response
            temperature: Temperature for generation
            thinking: Optional dictionary to enable extended thinking.

        Returns:
            API response object from Anthropic
        """
        resolved_model = model or config.DEFAULT_MODEL
        logger.debug(f"Calling Anthropic API with model: {resolved_model}")

        try:
            # Prepare arguments, including the optional thinking parameter
            api_args = {
                "model": resolved_model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "system": system_prompt,
                "messages": [{"role": "user", "content": user_prompt}],
                "tools": tools, # Keep tools even if empty, required by the signature
                # "tool_choice": tool_choice, # Remove default inclusion
            }
            if thinking:
                api_args["thinking"] = thinking
                logger.debug(f"Extended thinking enabled: {thinking}")
            # Conditionally add tool_choice only if it's not None
            if tool_choice:
                api_args["tool_choice"] = tool_choice
                logger.debug(f"Tool choice specified: {tool_choice}")

            response = await self.client.messages.create(**api_args)
            logger.debug("Anthropic API call successful.")
            return response

        except Exception as e:
            logger.error(f"Anthropic API call failed: {str(e)}", exc_info=True)
            raise