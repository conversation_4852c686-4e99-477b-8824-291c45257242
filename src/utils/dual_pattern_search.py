"""
Dual pattern search utility for body generation.

Implements sequential search: brief → body frameworks, then (brief + body) → linguistic patterns.
"""

import logging
from typing import Dict, List, Any, Optional
import voyageai
import os
from src.utils.pattern_format import UnifiedPattern, PatternType, convert_mongodb_to_unified, batch_convert_to_unified

logger = logging.getLogger(__name__)


class DualPatternSearcher:
    """Handles sequential search for both body frameworks and linguistic patterns."""
    
    def __init__(self, voyage_client, vector_search_func):
        self.voyage_client = voyage_client
        self._vector_search = vector_search_func
    
    async def sequential_body_search(self, context: Dict[str, Any], session_linguistic_pattern: Optional[UnifiedPattern] = None) -> Dict[str, Any]:
        """Search for body frameworks and use session linguistic pattern for consistency."""
        try:
            # Step 1: Search for body frameworks only
            query_text_body = self._build_body_query(context)
            logger.info(f"Body framework search: {query_text_body[:100]}...")
            
            embedding_result = self.voyage_client.embed([query_text_body], model="voyage-3.5")
            query_embedding = embedding_result.embeddings[0]
            
            body_patterns = self._vector_search(
                query_embedding=query_embedding,
                analysis_type="body",
                limit=2
            )
            
            # Step 2: Use session linguistic pattern instead of searching
            if session_linguistic_pattern:
                logger.info(f"Using session linguistic pattern: {session_linguistic_pattern.name}")
                # Use the UnifiedPattern instance directly
                linguistic_patterns = [session_linguistic_pattern]
            else:
                logger.warning("No session linguistic pattern provided, body will lack linguistic guidance")
                linguistic_patterns = []
            
            logger.info(f"Body search complete: {len(body_patterns)} frameworks + session linguistic pattern")
            
            return self._format_dual_patterns(body_patterns, linguistic_patterns)
            
        except Exception as e:
            logger.error(f"Sequential body search failed: {e}", exc_info=True)
            return {"selected_structures": [], "selected_frameworks": [], "selected_linguistic": []}
    
    def _build_body_query(self, context: Dict[str, Any]) -> str:
        """Build query for body framework search."""
        parts = []
        
        user_input = context.get('user_input', '')
        if user_input:
            excerpt = user_input[:300] if len(user_input) > 300 else user_input
            parts.append(f"Content about: {excerpt}")
        
        hook = context.get('generated_hook', '')
        if hook:
            parts.append(f"Starting with hook: {hook}")
        
        parts.append("Looking for body structure and framework patterns")
        
        length = context.get('target_length', 'medium')
        if length == 'short':
            parts.append("Concise body patterns for short posts")
        elif length == 'long':
            parts.append("Detailed body patterns for comprehensive posts")
        
        return " ".join(parts)
    
    
    def _format_dual_patterns(self, body_patterns: List[Dict[str, Any]], linguistic_patterns: List[UnifiedPattern]) -> Dict[str, Any]:
        """Format both pattern types for body generation."""
        # Convert body patterns to unified format
        unified_body = batch_convert_to_unified(body_patterns, PatternType.BODY)
        
        # Linguistic patterns are already UnifiedPattern instances
        
        return {
            "selected_structures": unified_body,
            "selected_frameworks": [],  # Legacy field, kept empty
            "selected_linguistic": linguistic_patterns
        }
    
    # REMOVED: _build_citation_text method - no longer needed with UnifiedPattern