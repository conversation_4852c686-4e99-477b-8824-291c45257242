"""
Automatic pipeline debug logger - captures critical failures during agent runs.

This module automatically logs pipeline failures to timestamped files for easy debugging.
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class PipelineDebugLogger:
    """Automatic debug logger for pipeline failures."""
    
    def __init__(self):
        # Create debug directory with datetime
        self.debug_root = Path("@outputs/debug_logs")
        self.session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        self.session_dir = self.debug_root / f"pipeline_debug_{self.session_timestamp}"
        
        # Create directory
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        # Track current step and errors
        self.current_step = None
        self.step_errors = []
        self.step_start_time = None
        
    def start_step(self, step_name: str, context: Dict[str, Any] = None):
        """Mark the start of a pipeline step."""
        self.current_step = step_name
        self.step_start_time = datetime.now()
        self.step_errors = []
        
        # Log minimal step start info
        step_info = {
            "step": step_name,
            "start_time": self.step_start_time.isoformat(),
            "context": context or {}
        }
        
        self._save_debug_file(f"step_{step_name}_start.json", step_info)
        logger.info(f"🔄 Pipeline step started: {step_name}")
        
    def log_step_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log an error that occurred during the current step."""
        if not self.current_step:
            return
            
        error_info = {
            "step": self.current_step,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        
        self.step_errors.append(error_info)
        
        # Save immediately for critical errors
        self._save_debug_file(f"step_{self.current_step}_error.json", error_info)
        logger.error(f"❌ Pipeline error in {self.current_step}: {error}")
        
    def end_step(self, success: bool, result: Dict[str, Any] = None):
        """Mark the end of a pipeline step."""
        if not self.current_step:
            return
            
        end_time = datetime.now()
        duration = (end_time - self.step_start_time).total_seconds() if self.step_start_time else 0
        
        step_summary = {
            "step": self.current_step,
            "success": success,
            "start_time": self.step_start_time.isoformat() if self.step_start_time else None,
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "errors": self.step_errors,
            "result_keys": list(result.keys()) if result else []
        }
        
        self._save_debug_file(f"step_{self.current_step}_summary.json", step_summary)
        
        status = "✅" if success else "❌"
        logger.info(f"{status} Pipeline step completed: {self.current_step} ({duration:.2f}s)")
        
        # Reset for next step
        self.current_step = None
        self.step_errors = []
        self.step_start_time = None
        
    def log_api_call(self, api_params: Dict[str, Any], response: Any = None, error: Exception = None):
        """Log API call details for debugging."""
        if not self.current_step:
            return
            
        # Sanitize API params (remove sensitive data)
        safe_params = {
            "model": api_params.get("model"),
            "max_tokens": api_params.get("max_tokens"),
            "temperature": api_params.get("temperature"),
            "tools": [t.get("name") for t in api_params.get("tools", [])],
            "thinking_enabled": api_params.get("thinking") is not None,
            "prompt_length": len(str(api_params.get("messages", [])))
        }
        
        api_log = {
            "step": self.current_step,
            "timestamp": datetime.now().isoformat(),
            "api_params": safe_params,
            "success": error is None,
            "error": str(error) if error else None
        }
        
        self._save_debug_file(f"step_{self.current_step}_api_call.json", api_log)
        
    def _save_debug_file(self, filename: str, data: Dict[str, Any]):
        """Save debug data to file."""
        try:
            file_path = self.session_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.warning(f"Failed to save debug file {filename}: {e}")
            
    def get_session_summary(self) -> str:
        """Get a summary of the debug session."""
        return f"Debug session: {self.session_timestamp} -> {self.session_dir}"

# Global debug logger instance
_debug_logger: Optional[PipelineDebugLogger] = None

def get_debug_logger() -> PipelineDebugLogger:
    """Get or create the global debug logger instance."""
    global _debug_logger
    if _debug_logger is None:
        _debug_logger = PipelineDebugLogger()
    return _debug_logger

def reset_debug_logger():
    """Reset the global debug logger for a new session."""
    global _debug_logger
    _debug_logger = None

@contextmanager
def debug_step(step_name: str, context: Dict[str, Any] = None):
    """Context manager for automatic step debugging."""
    debug_logger = get_debug_logger()
    debug_logger.start_step(step_name, context)
    
    try:
        yield debug_logger
        debug_logger.end_step(success=True)
    except Exception as e:
        debug_logger.log_step_error(e, context)
        debug_logger.end_step(success=False)
        raise

def log_api_call(api_params: Dict[str, Any], response: Any = None, error: Exception = None):
    """Log an API call for debugging."""
    debug_logger = get_debug_logger()
    debug_logger.log_api_call(api_params, response, error)