"""
Tool execution handler for agent API calls.

Handles the complete tool execution flow: detecting tool calls,
executing tools, sending results back, and getting final responses.
"""

import json
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

async def handle_tool_execution(
    client: Any,
    response: Any,
    api_params: Dict[str, Any],
    tools: Optional[List[Dict[str, Any]]] = None
) -> tuple[str, Any]:
    """
    Handle tool execution for an API response.
    
    Args:
        client: AnthropicClient instance
        response: Initial API response
        api_params: Original API parameters
        tools: List of available tools
        
    Returns:
        Tuple of (final_response_text, final_response_object)
    """
    # Check if tools were used
    if not tools or not any(block.type == 'tool_use' for block in response.content):
        # No tools or no tool calls, extract text normally
        response_text = ""
        for block in response.content:
            if block.type == 'text':
                response_text += block.text
        return response_text, response
    
    # Process tool calls
    from src.utils.content_agent_tools import execute_tool
    
    initial_text = ""
    tool_results = []
    
    for block in response.content:
        if block.type == 'text':
            initial_text += block.text
        elif block.type == 'tool_use':
            logger.info(f"Executing tool: {block.name} with input: {block.input}")
            # Execute the tool
            tool_result = execute_tool(block.name, **block.input)
            tool_results.append({
                "tool_use_id": block.id,
                "content": json.dumps(tool_result)
            })
    
    # If tools were called, send results back for final response
    if tool_results:
        # Convert response content to serializable format
        serializable_content = []
        for block in response.content:
            if block.type == 'text':
                serializable_content.append({
                    "type": "text",
                    "text": block.text
                })
            elif block.type == 'tool_use':
                serializable_content.append({
                    "type": "tool_use",
                    "id": block.id,
                    "name": block.name,
                    "input": block.input
                })
        
        # Add assistant message with tool calls
        api_params["messages"].append({
            "role": "assistant",
            "content": serializable_content
        })
        
        # Add user message with tool results
        api_params["messages"].append({
            "role": "user", 
            "content": [
                {
                    "type": "tool_result",
                    "tool_use_id": result["tool_use_id"],
                    "content": result["content"]
                } for result in tool_results
            ]
        })
        
        # Get final response after tool execution
        logger.info(f"Sending tool results back for final response")
        final_response = await client.client.messages.create(**api_params)
        
        # Extract final text
        final_text = ""
        for block in final_response.content:
            if block.type == 'text':
                final_text += block.text
        
        # Log the final text structure for debugging
        logger.info(f"Final response text length: {len(final_text)} chars")
        logger.debug(f"Final response text preview (first 500 chars): {final_text[:500]}")
        
        # Check if response follows expected format
        if "OUTPUT:" not in final_text.upper():
            logger.warning("Final response after tool execution may not follow expected format (missing OUTPUT marker)")
        
        return final_text, final_response
    else:
        # Tools available but not used
        return initial_text, response