#!/usr/bin/env python3
"""
Convert LinkedIn posts from JSON format to CSV format for database import.

This script takes a JSON file containing LinkedIn posts and converts it to a CSV file
that can be imported using the csv_to_sqlite.py script.
"""

import json
import csv
import argparse
import os
import sys
import re
from datetime import datetime

def extract_creator_name(filename):
    """Extracts the creator name from the JSON filename."""
    # Get the base filename without extension
    base_name = os.path.basename(filename)
    name_without_ext = os.path.splitext(base_name)[0]
    return name_without_ext

def convert_json_to_csv(json_file, csv_file):
    """
    Convert a JSON file of LinkedIn posts to CSV format.
    
    Expected CSV format:
    Date Posted,Post URL,Content,Likes,Comments,Shares,Media URL
    """
    try:
        # Load JSON data
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Determine the structure of the JSON
        if isinstance(data, list):
            # Assume it's a list of posts
            posts = data
        elif isinstance(data, dict) and 'posts' in data:
            # Assume it has a 'posts' key containing the list of posts
            posts = data['posts']
        else:
            print(f"Error: Unrecognized JSON structure in {json_file}", file=sys.stderr)
            return False
        
        # Create CSV file
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow(['Date Posted', 'Post URL', 'Content', 'Likes', 'Comments', 'Shares', 'Media URL'])
            
            # Process each post
            for post in posts:
                try:
                    # Extract fields with fallbacks for missing data
                    date_posted = post.get('date', '')
                    post_url = post.get('url', '')
                    content = post.get('content', '')
                    likes = post.get('likes', 0)
                    comments = post.get('comments', 0)
                    shares = post.get('shares', 0)
                    media_url = post.get('media_url', '')
                    
                    # Write row to CSV
                    writer.writerow([date_posted, post_url, content, likes, comments, shares, media_url])
                    
                except Exception as e:
                    print(f"Error processing post: {e}", file=sys.stderr)
                    continue
        
        print(f"Successfully converted {json_file} to {csv_file}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON parsing error in {json_file}: {e}", file=sys.stderr)
        return False
    except Exception as e:
        print(f"Error converting {json_file} to CSV: {e}", file=sys.stderr)
        return False

def main():
    parser = argparse.ArgumentParser(description="Convert LinkedIn posts from JSON to CSV format.")
    parser.add_argument("--input", required=True, help="Path to the input JSON file.")
    parser.add_argument("--output", help="Path to the output CSV file. If not provided, will use the same name with .csv extension.")
    
    args = parser.parse_args()
    
    # Determine output filename if not provided
    if not args.output:
        creator_name = extract_creator_name(args.input)
        current_date = datetime.now().strftime("%Y-%m-%d")
        args.output = f"linkedin_posts_{creator_name}_{current_date}.csv"
    
    # Convert JSON to CSV
    success = convert_json_to_csv(args.input, args.output)
    
    if success:
        print(f"Conversion complete. CSV file saved to: {args.output}")
        print(f"You can now import this file using: python src/utils/csv_to_sqlite.py --input {args.output} --output data/db/linkedin_content.db")
    else:
        print("Conversion failed.", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
