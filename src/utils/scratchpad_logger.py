"""Utility for tracking and saving the entire agent generation process."""

import os
import json
import logging
import datetime
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Directory for generation logs
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "outputs")

# Dictionary to store complete generation history by conversation ID
_generation_history = {}

def record_step(
    step_name: str,
    input_text: str,
    scratchpad_before: Dict[str, Any],
    result: Dict[str, Any],
    conversation_id: Optional[str] = None
) -> None:
    """
    Record a single step in the generation process.

    Args:
        step_name: Current step (hook, body, ending)
        input_text: User's original input
        scratchpad_before: Scratchpad state before this step
        result: Result from the current step
        conversation_id: Optional conversation ID
    """
    # Generate a timestamp
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # Create a key for this generation session
    session_key = conversation_id or f"session_{timestamp}"

    # Initialize if this is the first step
    if session_key not in _generation_history:
        _generation_history[session_key] = {
            "input_text": input_text,
            "start_time": timestamp,
            "steps": []
        }

    # Add this step to the history
    _generation_history[session_key]["steps"].append({
        "step_name": step_name,
        "timestamp": timestamp,
        "scratchpad_before": scratchpad_before,
        "result": result
    })

    # Create a progressive log file that shows each step as it happens
    append_step_to_log(session_key, step_name, input_text, scratchpad_before, result, timestamp)

def append_step_to_log(
    session_key: str,
    step_name: str,
    input_text: str,
    scratchpad: Dict[str, Any],
    result: Dict[str, Any],
    timestamp: str
) -> str:
    """
    Append a single step to a progressive log file.

    Args:
        session_key: Key for the generation session
        step_name: Current step name
        input_text: User's original input
        scratchpad: Scratchpad state before this step
        result: Result from this step
        timestamp: Timestamp for this step

    Returns:
        Path to the log file
    """
    # Create logs directory if it doesn't exist
    os.makedirs(LOGS_DIR, exist_ok=True)

    # Create a unique filename for this session
    # Extract session timestamp from session_key
    session_timestamp = session_key.split('_')[-1] if '_' in session_key else timestamp
    log_filename = f"generation_process_{session_timestamp}.txt"
    log_path = os.path.join(LOGS_DIR, log_filename)

    # Calculate step number based on conventional order
    step_order = {"hook": 1, "body": 2, "ending": 3}
    step_number = step_order.get(step_name, 0)

    # Calculate character count of current post (try all possible draft keys)
    post_content = ""
    for key in scratchpad.keys():
        if key.startswith("current_draft_post_"):
            post_content = scratchpad[key]
            break
    if not post_content:
        post_content = scratchpad.get("current_draft_post", "")  # Fallback for compatibility
    char_count = len(post_content)

    # Get current progress from the enhanced scratchpad
    hook = "[Not set]"
    body = "[Not set]"
    ending = "[Not set]"
    linguistic_pattern = "[Not set]"

    # Check if this is an enhanced scratchpad
    if "generation_pipeline" in scratchpad:
        if "hook_creation" in scratchpad["generation_pipeline"] and scratchpad["generation_pipeline"]["hook_creation"]["selected_hook_pattern"]:
            hook = scratchpad["generation_pipeline"]["hook_creation"]["selected_hook_pattern"]

        if "body_linguistic_construction" in scratchpad["generation_pipeline"] and scratchpad["generation_pipeline"]["body_linguistic_construction"]["selected_body_structure"]:
            body = scratchpad["generation_pipeline"]["body_linguistic_construction"]["selected_body_structure"]

        if "ending_creation" in scratchpad["generation_pipeline"] and scratchpad["generation_pipeline"]["ending_creation"]["selected_ending_pattern"]:
            ending = scratchpad["generation_pipeline"]["ending_creation"]["selected_ending_pattern"]
    else:
        # Legacy scratchpad format
        hook = scratchpad.get("hook", "[Not set]")
        body = scratchpad.get("body", "[Not set]")
        ending = scratchpad.get("ending", "[Not set]")
    
    # Get session linguistic pattern if available
    if "session_linguistic_pattern" in scratchpad:
        linguistic_pattern = scratchpad.get("session_linguistic_pattern", "[Not set]")

    # Format the log entry for this step
    step_entry = f"""
{'='*80}
STEP {step_number}: {step_name.upper()} (timestamp: {timestamp})
{'='*80}

**CURRENT POST STATE:**
{'-'*40}
{post_content}
{'-'*40}
CHARACTER COUNT: {char_count} characters

**STEP RESULT:**
{'-'*40}
OUTPUT FROM {step_name.upper()} STEP:
{result.get("output", "[No output for this step]")}
{'-'*40}

REASONING:
{result.get("reasoning", "[No reasoning provided]")}

SELECTION:
{result.get("selection", "[No selection made]")}

**CURRENT PROGRESS:**
- Hook: {hook}
- Body: {body}
- Ending: {ending}
- Session Linguistic Pattern: {linguistic_pattern}

{'='*80}
"""

    # If this is the first step, write the header
    if step_name == "hook" or not os.path.exists(log_path):
        header = f"""
{'='*80}
GENERATION PROCESS (IN PROGRESS)
SESSION: {session_key}
STARTED: {timestamp}
{'='*80}

USER INPUT:
{input_text}

"""
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(header)

    # Append this step to the log
    try:
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(step_entry)
        logger.info(f"Appended {step_name} step to {log_path}")
        return log_path
    except Exception as e:
        logger.error(f"Error appending step to log: {e}")
        return ""

def save_generation_history(session_key: str) -> str:
    """
    Add a final summary to the log file after all steps are complete.

    Args:
        session_key: Key for the generation session

    Returns:
        Path to the saved file
    """
    if session_key not in _generation_history:
        logger.error(f"No generation history found for session: {session_key}")
        return ""

    # Get the session data
    session = _generation_history[session_key]
    start_time = session["start_time"]
    steps = session["steps"]

    # Find log file path
    session_timestamp = session_key.split('_')[-1] if '_' in session_key else start_time
    log_filename = f"generation_process_{session_timestamp}.txt"
    log_path = os.path.join(LOGS_DIR, log_filename)

    # Check if the file exists
    if not os.path.exists(log_path):
        logger.error(f"Log file not found: {log_path}")
        return ""

    # Get final post if available (from the last step)
    final_post = ""
    final_char_count = 0
    if steps and "result" in steps[-1]:
        # Check if this is an enhanced scratchpad
        if "scratchpad_before" in steps[-1]:
            scratchpad_before = steps[-1]["scratchpad_before"]
            # Try to find any draft-specific key
            for key in scratchpad_before.keys():
                if key.startswith("current_draft_post_"):
                    final_post = scratchpad_before[key]
                    break
            if not final_post and "current_draft_post" in scratchpad_before:
                final_post = scratchpad_before["current_draft_post"]  # Fallback
            if "result" in steps[-1] and "output" in steps[-1]["result"]:
                final_post += steps[-1]["result"]["output"]
        else:
            final_post = steps[-1]["result"].get("output", "")

        final_char_count = len(final_post)

    # Get current time
    current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # Format the final summary
    final_summary = f"""
{'='*80}
SUMMARY: GENERATION PROCESS COMPLETED
SESSION: {session_key}
STARTED: {start_time}
COMPLETED: {current_time}
{'='*80}

FINAL POST:
{'-'*40}
{final_post}
{'-'*40}
FINAL CHARACTER COUNT: {final_char_count} characters

{'='*80}
"""

    # Append the final summary to the log file
    try:
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(final_summary)
        logger.info(f"Added final summary to {log_path}")

        # Clean up memory
        if session_key in _generation_history:
            del _generation_history[session_key]

        return log_path
    except Exception as e:
        logger.error(f"Error adding final summary: {e}")
        return ""