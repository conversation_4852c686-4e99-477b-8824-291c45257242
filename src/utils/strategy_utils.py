"""Utilities for formatting and handling content strategy throughout the system.

This module provides helpers for formatting strategy data for prompt injection,
creating strategic context objects, and managing funnel stage classifications.
"""
from typing import Dict, Any, Optional


def format_strategy_for_prompt(strategy: Optional[Dict[str, Any]]) -> str:
    """Format content strategy for injection into prompts.
    
    Args:
        strategy: Content strategy dict from MongoDB
        
    Returns:
        Formatted string for prompt injection
    """
    if not strategy:
        return "<content_strategy>No content strategy defined. Focus on authentic voice and universal insights.</content_strategy>"
    
    # Extract fields with fallbacks
    industry = strategy.get("industry", "unspecified")
    offering = strategy.get("offering", "unspecified")
    target_audience = strategy.get("target_audience", "general professional audience")
    differentiators = strategy.get("differentiators", "unique perspective")
    content_goal = strategy.get("content_goal", "build authentic connection")
    
    return f"""<content_strategy>
    <industry>{industry}</industry>
    <offering>{offering}</offering>
    <target_audience>{target_audience}</target_audience>
    <differentiators>{differentiators}</differentiators>
    <content_goal>{content_goal}</content_goal>
</content_strategy>"""


def create_strategic_context(
    strategy: Optional[Dict[str, Any]], 
    application_intensity: float = 0.5
) -> Dict[str, Any]:
    """Create a strategic context object for agent consumption.
    
    Args:
        strategy: Content strategy from MongoDB
        application_intensity: How strongly to apply strategy (0.0 to 1.0)
        
    Returns:
        Strategic context dict with business profile and guidance
    """
    if not strategy:
        return {
            "business_profile": None,
            "application_guidance": "authentic_expression",
            "intensity": 0.1
        }
    
    return {
        "business_profile": {
            "industry": strategy.get("industry"),
            "offering": strategy.get("offering"),
            "target_audience": strategy.get("target_audience"),
            "differentiators": strategy.get("differentiators"),
            "content_goal": strategy.get("content_goal")
        },
        "funnel_guidance": {
            "top": "Build authentic connection and thought leadership",
            "middle": f"Demonstrate expertise in {strategy.get('industry', 'your field')}",
            "bottom": f"Show transformation relevant to {strategy.get('target_audience', 'your audience')}"
        },
        "application_guidance": get_application_guidance(application_intensity),
        "intensity": application_intensity
    }


def get_application_guidance(intensity: float) -> str:
    """Get strategic application guidance based on intensity level.
    
    Args:
        intensity: Application intensity (0.0 to 1.0)
        
    Returns:
        Guidance string for agents
    """
    if intensity < 0.3:
        return "authentic_expression"
    elif intensity < 0.7:
        return "contextual_adaptation"
    else:
        return "strategic_alignment"


def get_funnel_stage_prompt_section(funnel_stage: str) -> str:
    """Get prompt guidance specific to funnel stage.
    
    Args:
        funnel_stage: One of "top", "middle", "bottom"
        
    Returns:
        Prompt section with funnel-specific guidance
    """
    guidance = {
        "top": """Top of Funnel Content:
- Focus on universal themes and broad appeal
- Build authentic connection through vulnerability or insight
- Minimal strategic alignment needed - let personality shine""",
        
        "middle": """Middle Funnel Content:
- Demonstrate domain expertise naturally
- Use industry-relevant language and examples
- Address challenges your target audience faces (without selling)""",
        
        "bottom": """Bottom Funnel Content:
- Showcase specific transformations and results
- OK to mention your solution as part of the story
- Clear connection to your differentiators and value prop"""
    }
    
    return guidance.get(funnel_stage.lower(), guidance["middle"])


def create_strategy_cache_key(strategy: Dict[str, Any]) -> str:
    """Create a stable cache key for Anthropic prompt caching.
    
    Args:
        strategy: Content strategy dict
        
    Returns:
        Stable string key for caching
    """
    # Create a deterministic key from strategy components
    components = [
        strategy.get("industry", ""),
        strategy.get("offering", ""),
        strategy.get("target_audience", ""),
        strategy.get("differentiators", ""),
        strategy.get("content_goal", "")
    ]
    
    # Join with separator and truncate for reasonable length
    return "|".join(components)[:200]