"""Helper functions for calculating and processing statistics in LinkedInsight."""
import logging
import json
import re
from typing import Dict, Any, Optional

from src.utils.mongo_utils_async import (
    get_async_mongo_db,
    CREATORS_COLLECTION,
    ANALYSES_COLLECTION,
)

# Configure logging
logger = logging.getLogger(__name__)

async def get_creator_summary_stats(creator_name: str) -> Optional[Dict[str, Any]]:
    """
    Calculate summary statistics for a given creator.
    
    Args:
        creator_name: The name of the creator.
        
    Returns:
        A dictionary containing summary stats (postsAnalyzed, contentThemes, avgEngagement, maxEngagement),
        or None if the creator or their overview analysis is not found.
    """
    try:
        # Get MongoDB database (async)
        db = await get_async_mongo_db()
        
        # Initialize with default values
        posts_analyzed = 0
        content_themes = 0
        avg_engagement = 0
        max_engagement = 0
        
        # First try to get stats from the markdown content
        query = {
            "creator_name": creator_name,
            "analysis_type": "overview_md"
        }
        
        markdown_doc = await db[ANALYSES_COLLECTION].find_one(
            query,
            sort=[("generated_at", -1)]
        )
        
        if markdown_doc:
            # Get the markdown content
            markdown_content = markdown_doc.get("content", "")
            
            # Extract stats using regex from the markdown stats block
            stats_match = re.search(r"```stats\n([\s\S]*?)```", markdown_content)
            if stats_match and stats_match.group(1):
                stats_text = stats_match.group(1)
                
                # Parse each stat line
                posts_analyzed_match = re.search(r"posts_analyzed: (\d+)", stats_text)
                if posts_analyzed_match and posts_analyzed_match.group(1):
                    posts_analyzed = int(posts_analyzed_match.group(1))
                
                content_themes_match = re.search(r"content_themes: (\d+)", stats_text)
                if content_themes_match and content_themes_match.group(1):
                    content_themes = int(content_themes_match.group(1))
                
                # For engagement values, need to handle K/M suffixes and convert to integers
                avg_engagement_match = re.search(r"avg_engagement: ([0-9\.]+)([KM])?", stats_text)
                if avg_engagement_match:
                    avg_value = float(avg_engagement_match.group(1))
                    if avg_engagement_match.group(2) == 'K':
                        avg_value *= 1000
                    elif avg_engagement_match.group(2) == 'M':
                        avg_value *= 1000000
                    avg_engagement = int(avg_value)
                
                max_engagement_match = re.search(r"max_engagement: ([0-9\.]+)([KM])?", stats_text)
                if max_engagement_match:
                    max_value = float(max_engagement_match.group(1))
                    if max_engagement_match.group(2) == 'K':
                        max_value *= 1000
                    elif max_engagement_match.group(2) == 'M':
                        max_value *= 1000000
                    max_engagement = int(max_value)
        else:
            # Fallback: Try to get basic info from creator document
            creator = await db[CREATORS_COLLECTION].find_one({"creator_name": creator_name})
            if creator:
                posts_analyzed = creator.get("posts_count", 0)
                
                # Try to get engagement data
                if "engagement" in creator:
                    engagement = creator.get("engagement", {})
                    avg_engagement = int(engagement.get("avg_likes", 0))
                    max_engagement = int(engagement.get("max_likes", 0))
            else:
                # If we couldn't find any data, return None
                logger.info(f"No data found for creator: {creator_name}")
                return None
                
        # Return the compiled stats as numeric values (integers)
        stats = {
            "postsAnalyzed": posts_analyzed,
            "contentThemes": content_themes,
            "avgEngagement": avg_engagement,
            "maxEngagement": max_engagement
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating creator summary stats: {e}")
        return None