"""
Content agent tools for content generation pipeline.

This module provides tools that content generation agents can use during 
content creation, such as word counting and draft analysis.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

def count_draft_words(scratchpad_json: str) -> Dict[str, Any]:
    """
    Count words in the completed portions of the draft (hook only during body generation).
    
    This tool is designed to be called during body generation to help determine
    how many words have already been written in the hook, so the body can be
    sized appropriately to meet the total word count target.
    
    Args:
        scratchpad_json: JSON string of the current scratchpad
        
    Returns:
        Dictionary with current draft word count and message
    """
    try:
        # Handle None input gracefully
        if scratchpad_json is None:
            logger.warning("count_draft_words called with None input")
            return {
                "hook_word_count": 0,
                "message": "No draft available (input was None)"
            }
        
        # Use robust JSON parsing for tool input
        from src.utils.json_utils import extract_json_from_text
        scratchpad = extract_json_from_text(scratchpad_json)
        
        # Extract current draft components
        draft_parts = []
        pipeline = scratchpad.get("generation_pipeline", {})
        
        # Add hook if exists
        if "hook_creation" in pipeline:
            hook = pipeline["hook_creation"].get("generated_hook_text", "")
            if hook.strip():
                draft_parts.append(hook)
        
        # NOTE: We do NOT include body here because this tool is called DURING body generation
        # The body doesn't exist yet when this tool is called
        # This tool's purpose is to tell the LLM how many words are already in the draft (just the hook)
        
        # Combine and count
        current_draft = " ".join(draft_parts)
        word_count = len(current_draft.split()) if current_draft.strip() else 0
        
        logger.info(f"Draft word count tool called: {word_count} words")
        
        return {
            "hook_word_count": word_count,
            "message": f"The hook contains {word_count} words. Use this to calculate how many words to write in the body based on the target total length."
        }
        
    except Exception as e:
        logger.error(f"Error in count_draft_words tool: {e}")
        return {
            "hook_word_count": 0,
            "message": f"Error counting words: {str(e)}"
        }

# Tool registry for agent function calling
AVAILABLE_TOOLS = {
    "count_draft_words": {
        "name": "count_draft_words",
        "description": "Count words in the hook section to determine how many words to write in the body. Call this at the start of body generation to calculate remaining word budget.",
        "input_schema": {
            "type": "object",
            "properties": {
                "scratchpad_json": {
                    "type": "string",
                    "description": "Current scratchpad as JSON string"
                }
            },
            "required": ["scratchpad_json"]
        }
    }
}

# Tool function registry for execution
TOOL_FUNCTIONS = {
    "count_draft_words": count_draft_words
}

def get_tools_for_step(step: str) -> List[Dict[str, Any]]:
    """
    Get the list of tools available for a specific generation step.
    
    Args:
        step: The generation step name (hook, body, ending)
        
    Returns:
        List of tool definitions for the step
    """
    # Only body step gets word counting tools for now
    if step == "body":
        return [AVAILABLE_TOOLS["count_draft_words"]]
    
    return []

def execute_tool(tool_name: str, **kwargs) -> Dict[str, Any]:
    """
    Execute a tool function by name.
    
    Args:
        tool_name: Name of the tool to execute
        **kwargs: Arguments to pass to the tool function
        
    Returns:
        Tool execution result
    """
    if tool_name not in TOOL_FUNCTIONS:
        return {
            "error": f"Unknown tool: {tool_name}",
            "available_tools": list(TOOL_FUNCTIONS.keys())
        }
    
    try:
        return TOOL_FUNCTIONS[tool_name](**kwargs)
    except Exception as e:
        logger.error(f"Error executing tool {tool_name}: {e}")
        return {
            "error": f"Tool execution failed: {str(e)}"
        }