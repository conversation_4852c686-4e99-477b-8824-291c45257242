"""Async Claude integration with Citations support."""
import os
from typing import Any, Optional, Callable
from anthropic import AsyncAnthropic
import logfire
import logging

from src.core.common.tool_registry import ToolRegistry
from src.core.interfaces.llm_client import LLMClientInterface
from src.infrastructure.llm.anthropic import AnthropicClient

logger = logging.getLogger(__name__)

class CitationsAnthropic:
    """Async wrapper for Anthropic's Claude API with Citations support."""

    def __init__(self, llm_client: LLMClientInterface):
        """Initialize with LLM client.
        
        Args:
            llm_client: LLM client interface implementation.
        """
        self.llm_client = llm_client
        
        # Configure logfire
        logfire.configure()
        
        self.tools = ToolRegistry.get_all_tools()
        self._debug_callback: Optional[Callable[..., Any]] = None

    @property
    def client(self) -> AsyncAnthropic:
        """Get the standard Anthropic client."""
        return self.llm_client.get_standard_client()
        
    @property
    def beta_client(self) -> AsyncAnthropic:
        """Get the beta-enabled Anthropic client."""
        from src.config import Config
        # TODO: Add ENABLE_CLAUDE_THINKING to Config if we want to enable thinking later
        return self.llm_client.get_beta_client(enable_thinking=False)

    @property
    def debug_callback(self) -> Optional[Callable[..., Any]]:
        """Get the debug callback function."""
        return self._debug_callback

    @debug_callback.setter
    def debug_callback(self, value: Optional[Callable[..., Any]]) -> None:
        """Set the debug callback function."""
        self._debug_callback = value
        # Propagate to the LLM client
        self.llm_client.set_debug_callback(value)

    async def create_transcript_tool_call(
        self,
        *,  # Force keyword args
        transcript: str,
        model: str,
        system_prompt: str,
        user_prompt: str,
        tool_name: str,
        max_tokens: int = 4000,
        thinking: dict = None
    ) -> Any:
        """Create a tool call that works with transcript (always uses caching).
        
        Args:
            transcript: The transcript text to analyze
            model: The Claude model to use
            system_prompt: The system prompt for Claude
            user_prompt: The user prompt for Claude
            tool_name: The name of the tool to use
            max_tokens: Maximum tokens in the response
            thinking: Optional thinking parameter configuration (testing compatibility with tool_choice via interleaved thinking)
        """
        message_content = [
            {
                "type": "document",
                "cache_control": {"type": "ephemeral"},
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": transcript
                },
                "citations": {"enabled": True}
            },
            {"type": "text", "text": user_prompt}
        ]
        
        # Build API parameters
        params = {
            "model": model,
            "max_tokens": max_tokens,
            "system": system_prompt,
            "messages": [{"role": "user", "content": message_content}],
            "temperature": 1 if thinking else 0  # Must be 1 when thinking is enabled
        }
        
        # Add thinking parameter if provided
        if thinking:
            params["thinking"] = thinking
        
        # Handle tool_choice vs thinking compatibility
        params["tools"] = [self.tools[tool_name]]
        if not thinking:
            # Only force tool choice when thinking is disabled
            params["tool_choice"] = {"type": "tool", "name": tool_name}
            logger.info(f"Forcing tool choice: {tool_name}")
        else:
            logger.info(f"Thinking enabled: providing {tool_name} tool without forced choice")
        
        # Always use beta client for newest capabilities
        client = self.beta_client
        
        # Add interleaved thinking header if thinking is enabled
        if thinking:
            params["extra_headers"] = {"anthropic-beta": "interleaved-thinking-2025-05-14"}
            logger.info("Enabling interleaved thinking with beta header")
        
        # Log the request parameters for debugging
        logger.info(f"API call parameters: model={model}, max_tokens={max_tokens}, thinking={thinking is not None}")
        
        # Make the API call
        response = await client.messages.create(**params)

        # Debug logging
        logger.info("Response structure:")
        logger.info(f"Response type: {type(response)}")
        logger.info(f"Response dir: {dir(response)}")
        logger.info(f"Response model_fields_set: {response.model_fields_set if hasattr(response, 'model_fields_set') else 'no model_fields_set'}")
        logger.info(f"Response model_extra: {response.model_extra if hasattr(response, 'model_extra') else 'no model_extra'}")
        
        if hasattr(response, 'usage'):
            logger.info("Usage fields:")
            logger.info(f"Usage type: {type(response.usage)}")
            logger.info(f"Usage dir: {dir(response.usage)}")
            logger.info(f"Usage dict: {response.usage.__dict__ if hasattr(response.usage, '__dict__') else 'no __dict__'}")

        # Extract tool response
        tool_response = None
        
        # Debug: log all content blocks like in Anthropic example
        logger.info(f"Response has {len(response.content)} content blocks:")
        thinking_blocks = []
        tool_use_blocks = []
        text_blocks = []
        
        for i, block in enumerate(response.content):
            logger.info(f"Block {i}: type={block.type}")
            if block.type == "thinking":
                thinking_blocks.append(block)
                logger.info(f"Thinking block: {block.thinking[:200]}...")
            elif block.type == "tool_use":
                tool_use_blocks.append(block)
                logger.info(f"Tool use: {block.name} with input keys: {list(block.input.keys()) if hasattr(block, 'input') else 'no input'}")
            elif block.type == "text":
                text_blocks.append(block)
                logger.info(f"Text content (first 200 chars): {block.text[:200]}...")
        
        # Look for the requested tool response with detailed logging
        for block in response.content:
            if block.type == "tool_use":
                logger.info(f"Found tool_use block: name='{block.name}', looking for='{tool_name}', match={block.name == tool_name}")
                if block.name == tool_name:
                    tool_response = block.input
                    logger.info(f"Tool response found! Input type: {type(block.input)}")
        
        # If we found the main tool response, return it (even if empty)
        if tool_response is not None:
            logger.info(f"Returning tool response with {len(tool_response)} keys: {list(tool_response.keys())}")
            return tool_response
        
        # If no tool use but we have thinking/text, log more details for debugging
        if thinking_blocks or text_blocks:
            logger.info(f"No tool use found. Response contained {len(thinking_blocks)} thinking blocks and {len(text_blocks)} text blocks")
            if text_blocks:
                logger.info(f"Full text response: {text_blocks[0].text[:500]}...")
                
        raise ValueError(f"No tool response found for: {tool_name}")

    async def create_content_tool_call(
        self,
        *,  # Force keyword args
        model: str,
        system_prompt: str,
        user_prompt: str,
        tool_name: str,
        max_tokens: int = 4000,
        temperature: float = 0.0,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        brief: str
    ) -> Any:
        """Create a tool call for content generation.
        
        Args:
            model: The Claude model to use
            system_prompt: The system prompt for Claude
            user_prompt: The user prompt for Claude
            tool_name: The name of the tool to use
            max_tokens: Maximum tokens in the response
            temperature: Temperature parameter for generation
            top_p: Optional top_p parameter for generation
            top_k: Optional top_k parameter for generation
            brief: The brief text to generate content from
        """
        # Put brief in document for Citations, prompt in text block
        message_content = [
            {
                "type": "document",
                "cache_control": {"type": "ephemeral"},
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": brief  # Brief to cite from
                },
                "citations": {"enabled": True}
            },
            {"type": "text", "text": user_prompt}
        ]
        
        # Build the parameters
        params = {
            "model": model,
            "max_tokens": max_tokens,
            "system": system_prompt,
            "messages": [{"role": "user", "content": message_content}],
            "temperature": temperature
        }
        
        if top_p is not None:
            params["top_p"] = top_p
        if top_k is not None:
            params["top_k"] = top_k
        
        # Standard behavior - force the specific tool usage
        params["tools"] = [self.tools[tool_name]]
        params["tool_choice"] = {"type": "tool", "name": tool_name}
        
        # Make the API call
        response = await self.beta_client.messages.create(**params)
        
        # Extract tool response
        tool_response = None
        
        # Look for the requested tool response
        for block in response.content:
            if block.type == "tool_use" and block.name == tool_name:
                tool_response = block.input
        
        # If we found the main tool response, return it
        if tool_response:
            return tool_response
                
        raise ValueError(f"No tool response found for: {tool_name}") 