"""Centralized Anthropic client management."""
from typing import <PERSON><PERSON>, Dict, <PERSON>, Tu<PERSON>
from anthropic import AsyncAnthropic
import logfire
from src.core.interfaces.llm_client import LLMClientInterface


class AnthropicConfig:
    """Configuration for Anthropic API clients."""
    
    def __init__(self, api_key: str, beta_header: str):
        """Initialize configuration.
        
        Args:
            api_key: Anthropic API key
            beta_header: Anthropic beta header value
        """
        self.api_key = api_key
        self.beta_header = beta_header


class AnthropicClientFactory:
    """Factory for creating Anthropic API clients."""
    
    def __init__(self, config: AnthropicConfig):
        """Initialize factory with configuration.
        
        Args:
            config: Configuration for Anthropic API
        """
        self.config = config
    
    def create_standard_client(self) -> AsyncAnthropic:
        """Create standard Anthropic client.
        
        Returns:
            An AsyncAnthropic client without beta features
        """
        client = AsyncAnthropic(api_key=self.config.api_key)
        logfire.instrument_anthropic(client)
        return client
    
    def create_beta_client(self, enable_thinking: bool = False) -> AsyncAnthropic:
        """Create beta-enabled Anthropic client.
        
        Args:
            enable_thinking: Whether to enable Claude "thinking" stream
            
        Returns:
            An AsyncAnthropic client with beta features enabled
        """
        client = AsyncAnthropic(api_key=self.config.api_key)
        
        # Set up headers based on requested features
        # Note: thinking is now enabled via the thinking parameter directly in the API call,
        # not via headers, so we only need the standard beta header
        headers = {"anthropic-beta": self.config.beta_header}
        
        # Log if thinking will be enabled for this client
        if enable_thinking:
            print(f"DEBUG: Creating beta client that will use thinking parameter in requests")
            
        client = client.with_options(
            default_headers=headers
        )
        logfire.instrument_anthropic(client)
        return client
    
    def create_both_clients(self) -> Tuple[AsyncAnthropic, AsyncAnthropic]:
        """Create both standard and beta clients.
        
        Returns:
            A tuple of (standard_client, beta_client)
        """
        return (self.create_standard_client(), self.create_beta_client())


class AnthropicClient(LLMClientInterface):
    """Anthropic API client implementation."""
    
    def __init__(
        self,
        config: AnthropicConfig,
        standard_client: Optional[AsyncAnthropic] = None,
        beta_client: Optional[AsyncAnthropic] = None,
        client_factory: Optional[AnthropicClientFactory] = None
    ):
        """Initialize with configuration and optional client instances.
        
        Args:
            config: Configuration for Anthropic API
            standard_client: Optional pre-configured standard client
            beta_client: Optional pre-configured beta client
            client_factory: Optional factory for creating clients if not provided
        """
        self.config = config
        self._standard_client = standard_client
        self._beta_client = beta_client
        self._client_factory = client_factory or AnthropicClientFactory(config)
        
    def get_standard_client(self) -> AsyncAnthropic:
        """Get standard client, initializing if needed."""
        if self._standard_client is None:
            self._standard_client = self._client_factory.create_standard_client()
        return self._standard_client
        
    def get_beta_client(self, enable_thinking: bool = False) -> AsyncAnthropic:
        """Get beta-enabled client, initializing if needed.
        
        Args:
            enable_thinking: Whether to enable Claude "thinking" stream
        """
        if self._beta_client is None or enable_thinking:  # Always recreate if thinking is requested
            self._beta_client = self._client_factory.create_beta_client(enable_thinking=enable_thinking)
            if enable_thinking:
                print("DEBUG: Created beta client with thinking enabled")
        return self._beta_client
    
    def set_debug_callback(self, callback: Optional[Any]) -> None:
        """Set debug callback for both clients.
        
        Note: This is a custom extension not part of the standard AsyncAnthropic API.
        It's used for debugging purposes in this application.
        """
        # Get clients first to ensure they're initialized
        standard_client = self.get_standard_client()
        beta_client = self.get_beta_client()
        
        # Use setattr to avoid linter errors about unknown attributes
        if callback is not None:
            setattr(standard_client, 'debug_callback', callback)
            setattr(beta_client, 'debug_callback', callback)