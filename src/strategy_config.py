"""
Centralized configuration for content strategy features.
Single source of truth for funnel stages, mappings, and related logic.
"""

from enum import Enum
from typing import Dict, Optional, Any
from pydantic import BaseModel


class FunnelStage(str, Enum):
    """Standardized funnel stage values."""
    TOP = "top_of_funnel"
    MIDDLE = "middle_funnel"
    BOTTOM = "bottom_funnel"
    UNCLASSIFIED = "unclassified"
    
    @property
    def display_name(self) -> str:
        """Human-readable display name."""
        return {
            self.TOP: "Top of Funnel",
            self.MIDDLE: "Middle of Funnel", 
            self.BOTTOM: "Bottom Funnel",
            self.UNCLASSIFIED: "Not classified"
        }[self]
    
    @property
    def short_name(self) -> str:
        """Short abbreviation (TOFU/MOFU/BOFU)."""
        return {
            self.TOP: "TOFU",
            self.MIDDLE: "MOFU",
            self.BOTTOM: "BOFU",
            self.UNCLASSIFIED: "N/A"
        }[self]


class FunnelStageConfig(BaseModel):
    """Configuration for a funnel stage."""
    stage: FunnelStage
    display_name: str
    short_name: str
    tooltip: str
    score_boost_min: float  # Min score boost for pattern selection
    score_boost_max: float  # Max score boost for pattern selection
    
    
# Centralized funnel stage configurations
FUNNEL_STAGE_CONFIGS: Dict[FunnelStage, FunnelStageConfig] = {
    FunnelStage.TOP: FunnelStageConfig(
        stage=FunnelStage.TOP,
        display_name="📢 Top of Funnel",
        short_name="TOFU",
        tooltip="Broad appeal content for awareness and thought leadership",
        score_boost_min=0.02,
        score_boost_max=0.05
    ),
    FunnelStage.MIDDLE: FunnelStageConfig(
        stage=FunnelStage.MIDDLE,
        display_name="💡 Middle of Funnel",
        short_name="MOFU", 
        tooltip="Industry expertise and methodology for engaged prospects",
        score_boost_min=0.03,
        score_boost_max=0.07
    ),
    FunnelStage.BOTTOM: FunnelStageConfig(
        stage=FunnelStage.BOTTOM,
        display_name="💎 Bottom of Funnel",
        short_name="BOFU",
        tooltip="Case studies and transformations for decision-makers",
        score_boost_min=0.05,
        score_boost_max=0.10
    ),
    FunnelStage.UNCLASSIFIED: FunnelStageConfig(
        stage=FunnelStage.UNCLASSIFIED,
        display_name="📋 Not classified",
        short_name="N/A",
        tooltip="Funnel stage not yet determined",
        score_boost_min=0.0,
        score_boost_max=0.0
    )
}


def get_funnel_config(stage: str) -> FunnelStageConfig:
    """Get configuration for a funnel stage, with fallback to unclassified."""
    # Handle various input formats
    stage_lower = stage.lower() if stage else ""
    
    # Map common variations to enum values
    stage_mapping = {
        "top": FunnelStage.TOP,
        "top_of_funnel": FunnelStage.TOP,
        "top of funnel": FunnelStage.TOP,
        "tofu": FunnelStage.TOP,
        "middle": FunnelStage.MIDDLE,
        "middle_funnel": FunnelStage.MIDDLE,
        "middle funnel": FunnelStage.MIDDLE,
        "mofu": FunnelStage.MIDDLE,
        "bottom": FunnelStage.BOTTOM,
        "bottom_funnel": FunnelStage.BOTTOM,
        "bottom funnel": FunnelStage.BOTTOM,
        "bofu": FunnelStage.BOTTOM,
    }
    
    enum_value = stage_mapping.get(stage_lower, FunnelStage.UNCLASSIFIED)
    return FUNNEL_STAGE_CONFIGS[enum_value]


def infer_funnel_stage_from_patterns(patterns: Dict[str, Any]) -> FunnelStage:
    """
    Infer funnel stage based on selected patterns.
    Used in agent_service.py for determining likely funnel classification.
    """
    # Extract pattern names from the generation pipeline
    hook_pattern = patterns.get("generation_pipeline", {}).get("hook_creation", {}).get("selected_hook_pattern", "")
    body_pattern = patterns.get("generation_pipeline", {}).get("body_linguistic_construction", {}).get("selected_body_structure", "")
    ending_pattern = patterns.get("generation_pipeline", {}).get("ending_creation", {}).get("selected_ending_pattern", "")
    
    # Convert to lowercase for matching
    all_patterns = f"{hook_pattern} {body_pattern} {ending_pattern}".lower()
    
    # Bottom funnel indicators
    bottom_indicators = ["case study", "transformation", "results", "roi", "success story", "testimonial"]
    if any(indicator in all_patterns for indicator in bottom_indicators):
        return FunnelStage.BOTTOM
    
    # Middle funnel indicators  
    middle_indicators = ["expertise", "methodology", "framework", "process", "technical", "deep dive"]
    if any(indicator in all_patterns for indicator in middle_indicators):
        return FunnelStage.MIDDLE
    
    # Top funnel indicators (default for personal/vulnerable content)
    top_indicators = ["story", "vulnerable", "personal", "journey", "lesson", "insight"]
    if any(indicator in all_patterns for indicator in top_indicators):
        return FunnelStage.TOP
    
    # Default to top of funnel for broad appeal
    return FunnelStage.TOP


def infer_funnel_stage_from_goal(content_goal: Optional[str]) -> Optional[FunnelStage]:
    """
    Infer funnel stage from content goal text.
    Used in brief_to_patterns.py for pattern scoring.
    """
    if not content_goal:
        return None
        
    goal_lower = content_goal.lower()
    
    # Map content goals to funnel stages
    if any(term in goal_lower for term in ["awareness", "thought leadership", "brand", "visibility"]):
        return FunnelStage.TOP
    elif any(term in goal_lower for term in ["nurture", "educate", "consideration", "expertise"]):
        return FunnelStage.MIDDLE
    elif any(term in goal_lower for term in ["convert", "sales", "demo", "case study", "testimonial"]):
        return FunnelStage.BOTTOM
    
    return None


def calculate_funnel_score_boost(
    base_score: float,
    pattern_funnel_stage: Optional[str],
    target_funnel_stage: Optional[FunnelStage]
) -> float:
    """
    Calculate score boost for pattern based on funnel alignment.
    Used in brief_to_patterns.py for pattern ranking.
    
    Args:
        base_score: Original similarity score
        pattern_funnel_stage: Funnel stage from pattern metadata
        target_funnel_stage: Desired funnel stage from strategy
        
    Returns:
        Boosted score (always >= base_score)
    """
    if not target_funnel_stage or not pattern_funnel_stage:
        return base_score
    
    # Get configuration for target stage
    config = get_funnel_config(pattern_funnel_stage)
    
    # Check if pattern matches target stage
    pattern_enum = get_funnel_config(pattern_funnel_stage).stage
    if pattern_enum == target_funnel_stage:
        # Apply boost within configured range
        boost_range = config.score_boost_max - config.score_boost_min
        boost = config.score_boost_min + (boost_range * 0.5)  # Middle of range
        return base_score * (1 + boost)
    
    return base_score


# Export configuration for frontend
def get_frontend_config() -> Dict[str, Any]:
    """
    Get configuration formatted for frontend consumption.
    Used by the strategy config API endpoint.
    """
    return {
        "funnel_stages": {
            stage.value: {
                "displayName": config.display_name,
                "shortName": config.short_name,
                "tooltip": config.tooltip
            }
            for stage, config in FUNNEL_STAGE_CONFIGS.items()
        }
    }