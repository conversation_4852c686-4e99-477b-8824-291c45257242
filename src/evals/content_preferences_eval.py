"""
Evaluation script for testing content preferences reconciliation.
Tests the same brief with different content preference permutations to evaluate
how well the system adapts best practice patterns to user voice preferences.
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Any
import logging

from src.api.agent_service import AgentService
from src.api.schemas import ContentPreferences
from src.utils.mongo_utils_async import get_async_mongo_db
from src.utils.content_preferences_definitions import CONTENT_PREFERENCES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define test preference permutations using valid values from CONTENT_PREFERENCES
TEST_PERMUTATIONS = {
    "corporate_wit": {
        "name": "The Corporate Wit",
        "preferences": {
            "humor_style": "dry_wit", 
            "opinion_style": "balanced",
            "emoji_usage": "occasional",
            "voice_distinctiveness": "professionally_personal",
            "controversy_approach": "thoughtful_challenger",
            "brag_level": "healthy_confidence",
            "readability_style": "balanced"
        }
    },
    "bold_maverick": {
        "name": "The Bold Maverick",
        "preferences": {
            "humor_style": "irreverent",
            "opinion_style": "strongly_opinionated",
            "emoji_usage": "frequent",
            "voice_distinctiveness": "radically_individual",
            "controversy_approach": "industry_provocateur",
            "brag_level": "maximum_brag",
            "readability_style": "airy"
        }
    },
    "warm_mentor": {
        "name": "The Warm Mentor", 
        "preferences": {
            "humor_style": "playful",
            "opinion_style": "nuanced",
            "emoji_usage": "occasional",
            "voice_distinctiveness": "authentically_distinct",
            "controversy_approach": "thoughtful_challenger",
            "brag_level": "modest_success",
            "readability_style": "balanced"
        }
    },
    "energizer": {
        "name": "The Energizer",
        "preferences": {
            "humor_style": "playful",
            "opinion_style": "strongly_opinionated",
            "emoji_usage": "frequent",
            "voice_distinctiveness": "authentically_distinct",
            "controversy_approach": "willing_to_debate",
            "brag_level": "healthy_confidence",
            "readability_style": "airy"
        }
    },
    "data_scientist": {
        "name": "The Data Scientist",
        "preferences": {
            "humor_style": "serious_only",
            "opinion_style": "nuanced",
            "emoji_usage": "none",
            "voice_distinctiveness": "corporate_standard",
            "controversy_approach": "play_it_safe",
            "brag_level": "credit_the_universe",
            "readability_style": "dense"
        }
    }
}

class ContentPreferencesEvaluator:
    def __init__(self, test_brief: str, output_dir: str = None):
        self.test_brief = test_brief
        self.agent_service = AgentService()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = output_dir or f"outputs/content_pref_evals/{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
    async def run_single_permutation(self, permutation_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """Run the agent with a single preference permutation and capture complete output."""
        logger.info(f"Running permutation: {preferences['name']}")
        
        # Create mock user data with preferences
        user_data = {
            "clerk_user_id": f"test_user_{permutation_id}",
            "content_preferences": preferences["preferences"]
        }
        
        # Run the agent
        try:
            result = await self.agent_service.generate_content(
                input_text=self.test_brief,
                user_preferences=preferences["preferences"],
                post_length="medium",  # Keep consistent
                content_strategy=None,  # Could add strategy testing later
                conversation_id=f"eval_{permutation_id}_{self.timestamp}"
            )
            
            # Extract complete generation data
            generation_data = {
                "permutation_id": permutation_id,
                "permutation_name": preferences["name"],
                "preferences": preferences["preferences"],
                "timestamp": datetime.now().isoformat(),
                "brief": self.test_brief,
                "result": {
                    "generated_post": result.get("generated_post", ""),
                    "explanation": result.get("explanation", ""),
                    "thinking_logs": result.get("thinking_logs", {}),
                    "enhanced_scratchpad": result.get("enhanced_scratchpad", {}),
                    "conversation_id": result.get("conversation_id", "")
                }
            }
            
            # Save individual result
            output_file = os.path.join(self.output_dir, f"{permutation_id}_full_generation.json")
            with open(output_file, 'w') as f:
                json.dump(generation_data, f, indent=2)
                
            # Also save human-readable reasoning chain
            reasoning_file = os.path.join(self.output_dir, f"{permutation_id}_reasoning_chain.txt")
            self._save_reasoning_chain(generation_data, reasoning_file)
            
            return generation_data
            
        except Exception as e:
            logger.error(f"Error running permutation {permutation_id}: {e}")
            return {
                "permutation_id": permutation_id,
                "error": str(e)
            }
    
    def _save_reasoning_chain(self, generation_data: Dict[str, Any], output_file: str):
        """Extract and save the complete reasoning chain in a readable format."""
        with open(output_file, 'w') as f:
            f.write(f"CONTENT PREFERENCES EVALUATION - REASONING CHAIN\n")
            f.write(f"{'='*80}\n")
            f.write(f"Permutation: {generation_data['permutation_name']}\n")
            f.write(f"Preferences: {json.dumps(generation_data['preferences'], indent=2)}\n")
            f.write(f"Brief: {generation_data['brief']}\n")
            f.write(f"{'='*80}\n\n")
            
            # Extract thinking logs for each step
            thinking_logs = generation_data['result'].get('thinking_logs', {})
            
            # Hook step
            f.write("HOOK GENERATION\n")
            f.write("-"*40 + "\n")
            if 'hook' in thinking_logs:
                f.write("THINKING:\n")
                f.write(thinking_logs['hook'] + "\n\n")
            
            # Extract hook results from scratchpad
            scratchpad = generation_data['result'].get('enhanced_scratchpad', {})
            if 'generation_pipeline' in scratchpad:
                hook_data = scratchpad['generation_pipeline'].get('hook_creation', {})
                f.write(f"SELECTED PATTERN: {hook_data.get('selected_hook_pattern', 'N/A')}\n")
                f.write(f"GENERATED HOOK: {hook_data.get('generated_hook_text', 'N/A')}\n")
                f.write(f"DECISION SUMMARY: {hook_data.get('decision_summary_for_next_step', 'N/A')}\n")
            f.write("\n")
            
            # Body step
            f.write("BODY GENERATION\n")
            f.write("-"*40 + "\n")
            if 'body' in thinking_logs:
                f.write("THINKING:\n")
                f.write(thinking_logs['body'] + "\n\n")
                
            if 'generation_pipeline' in scratchpad:
                body_data = scratchpad['generation_pipeline'].get('body_linguistic_construction', {})
                f.write(f"SELECTED STRUCTURE: {body_data.get('selected_body_structure', 'N/A')}\n")
                f.write(f"LINGUISTIC PATTERNS: {body_data.get('selected_linguistic_patterns', 'N/A')}\n")
                f.write(f"DECISION SUMMARY: {body_data.get('decision_summary_for_next_step', 'N/A')}\n")
                f.write(f"GENERATED BODY:\n{body_data.get('generated_body_text', 'N/A')}\n")
            f.write("\n")
            
            # Ending step
            f.write("ENDING GENERATION\n")
            f.write("-"*40 + "\n")
            if 'ending' in thinking_logs:
                f.write("THINKING:\n")
                f.write(thinking_logs['ending'] + "\n\n")
                
            if 'generation_pipeline' in scratchpad:
                ending_data = scratchpad['generation_pipeline'].get('ending_creation', {})
                f.write(f"SELECTED PATTERN: {ending_data.get('selected_ending_pattern', 'N/A')}\n")
                f.write(f"GENERATED ENDING: {ending_data.get('generated_ending_text', 'N/A')}\n")
            f.write("\n")
            
            # Final output
            f.write("FINAL GENERATED POST\n")
            f.write("="*80 + "\n")
            f.write(generation_data['result'].get('generated_post', 'N/A') + "\n")
    
    async def run_all_permutations(self) -> Dict[str, Any]:
        """Run all permutations and create comparison outputs."""
        logger.info(f"Starting evaluation with {len(TEST_PERMUTATIONS)} permutations")
        logger.info(f"Output directory: {self.output_dir}")
        
        results = {}
        for perm_id, perm_data in TEST_PERMUTATIONS.items():
            result = await self.run_single_permutation(perm_id, perm_data)
            results[perm_id] = result
            
        # Save comparison summary
        self._save_comparison_summary(results)
        
        return results
    
    def _save_comparison_summary(self, results: Dict[str, Any]):
        """Create a summary comparing all permutations."""
        summary_file = os.path.join(self.output_dir, "comparison_summary.txt")
        
        with open(summary_file, 'w') as f:
            f.write("CONTENT PREFERENCES EVALUATION - COMPARISON SUMMARY\n")
            f.write("="*80 + "\n")
            f.write(f"Test Brief: {self.test_brief}\n")
            f.write(f"Timestamp: {self.timestamp}\n")
            f.write("="*80 + "\n\n")
            
            # Compare draft outputs
            for perm_id, result in results.items():
                if 'error' in result:
                    f.write(f"\n{result.get('permutation_name', perm_id)} - ERROR: {result['error']}\n")
                    continue
                    
                f.write(f"\n{result['permutation_name']}\n")
                f.write("-"*40 + "\n")
                f.write(f"Preferences: {json.dumps(result['preferences'], indent=2)}\n")
                f.write("\nGENERATED POST:\n")
                f.write(result['result'].get('generated_post', 'N/A') + "\n")
                f.write("\n" + "="*80 + "\n")
        
        # Also save a JSON comparison
        json_summary = os.path.join(self.output_dir, "comparison_summary.json")
        with open(json_summary, 'w') as f:
            json.dump({
                "test_brief": self.test_brief,
                "timestamp": self.timestamp,
                "permutations": results
            }, f, indent=2)


async def main():
    # Load test brief from file
    with open("/Users/<USER>/LinkedInsight/tests/sample_brief.md", "r") as f:
        test_brief = f.read()
    
    evaluator = ContentPreferencesEvaluator(test_brief)
    results = await evaluator.run_all_permutations()
    
    print(f"\nEvaluation complete! Results saved to: {evaluator.output_dir}")
    print(f"- Individual generation files: [permutation]_full_generation.json")
    print(f"- Reasoning chains: [permutation]_reasoning_chain.txt")
    print(f"- Comparison summary: comparison_summary.txt")


if __name__ == "__main__":
    asyncio.run(main())