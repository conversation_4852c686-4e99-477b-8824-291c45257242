"""Tool schema for citation extraction with metadata."""
from utils.citation_schema import CITATION_SCHEMA

CITATION_EXTRACTION_TOOL_SCHEMA = {
    "name": "extract_citations",
    "description": """Extract citations from text, preserving speaker and timestamp metadata.
For each citation, you must provide:
1. The exact quote text (without speaker/timestamp prefix)
2. The speaker's name who said it
3. The timestamp when it was said

Example response:
{
  "citations": [
    {
      "text": "Hello everyone",
      "speaker": "<PERSON>",
      "timestamp": "10:15"
    },
    {
      "text": "This is the key point",
      "speaker": "<PERSON>",
      "timestamp": "10:20"
    }
  ]
}""",
    "input_schema": {
        "type": "object",
        "properties": {
            "citations": {
                "type": "array",
                "description": "List of citations with metadata. Each citation must include speaker and timestamp.",
                "items": CITATION_SCHEMA,
                "minItems": 1
            }
        },
        "required": ["citations"]
    }
} 