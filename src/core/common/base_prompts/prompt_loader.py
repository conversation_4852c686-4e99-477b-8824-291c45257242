"""Ideas extraction prompt loader.

Simplified loader for the ideas extraction component.
"""

from pathlib import Path
from typing import Tuple, Literal


def load_component_prompt(variant: Literal["ideas"]) -> Tuple[str, str]:
    """Load ideas extraction prompts.
    
    Args:
        variant: Should be "ideas" (only supported variant)
        
    Returns:
        Tuple of (user_prompt, system_prompt)
    """
    if variant != "ideas":
        raise ValueError(f"Only 'ideas' variant is supported, got: {variant}")
        
    ideas_dir = Path(__file__).parent.parent.parent / "ideas_extraction"
    
    # Load system prompt
    system_prompt_path = ideas_dir / "ideas_extraction_system_prompt.txt"
    with open(system_prompt_path) as f:
        system_prompt = f.read()
    
    # Load user prompt from the prompt module
    from ...ideas_extraction.ideas_extraction_prompt import PROMPT
    
    return PROMPT, system_prompt