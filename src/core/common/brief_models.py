"""Models for brief generation and content suggestions.

This module contains the StreamedBrief model which represents content
streamed from <PERSON> during the brief generation process.
"""

from typing import Optional
from pydantic import BaseModel

class StreamedBrief(BaseModel):
    """Content streamed from <PERSON> during brief generation.
    
    Contains both the main content analysis and optional writing suggestions.
    """
    content: str  # Main content analysis from <PERSON>
    writing_suggestions: Optional[str] = None  # Optional writing guidance 