"""Registry of available <PERSON> tool schemas for ideas extraction and pattern selection.

This module provides a centralized registry of tool schemas used for Claude API calls.
Includes tools for ideas extraction and pattern selection, loaded on demand to avoid circular imports.
"""

class ToolRegistry:
    """Registry of all available Claude tool schemas."""
    
    @staticmethod
    def get_ideas_extraction_tools():
        """Get tool schemas for ideas extraction."""
        from src.core.ideas_extraction.common.citations_ideas_extraction_schema import (
            IDEAS_EXTRACTION_TOOL_SCHEMA
        )
        return {
            "extract_ideas": IDEAS_EXTRACTION_TOOL_SCHEMA,
        }
    
    @staticmethod
    def get_pattern_selection_tools():
        """Get tool schemas for pattern selection."""
        # Generic pattern extraction tool that works for any pattern type
        EXTRACT_PATTERNS_TOOL_SCHEMA = {
            "name": "extract_patterns",
            "description": "Extract the most relevant patterns from an analysis document with citations",
            "input_schema": {
                "type": "object",
                "properties": {
                    "patterns": {
                        "type": "array",
                        "description": "Array of selected patterns with their relevant details",
                        "items": {
                            "type": "object",
                            "properties": {
                                "pattern_info": {
                                    "type": "object",
                                    "description": "Key information about this pattern",
                                    "additionalProperties": True
                                },
                                "citations": {
                                    "type": "array",
                                    "description": "Verbatim quotes from the source document",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "text": {
                                                "type": "string",
                                                "description": "The exact text from the document"
                                            },
                                            "location": {
                                                "type": "string",
                                                "description": "Where this text comes from in the document"
                                            }
                                        },
                                        "required": ["text"]
                                    }
                                }
                            },
                            "required": ["pattern_info"]
                        }
                    },
                    "selection_reasoning": {
                        "type": "string",
                        "description": "Brief explanation of why these patterns were selected"
                    }
                },
                "required": ["patterns"]
            }
        }
        
        return {
            "extract_patterns": EXTRACT_PATTERNS_TOOL_SCHEMA,
        }
    
    @staticmethod
    def get_bundle_extraction_tools():
        """Get tool schemas for bundle extraction."""
        EXTRACT_LINGUISTIC_BUNDLES_TOOL_SCHEMA = {
            "name": "extract_linguistic_bundles",
            "description": "Discover natural linguistic style bundles from LinkedIn posts using contextual clustering",
            "input_schema": {
                "type": "object",
                "properties": {
                    "schema_version": {
                        "type": "string",
                        "description": "Version of the schema used",
                        "const": "1.0.0"
                    },
                    "creator_id": {
                        "type": "string",
                        "description": "Identifier for the creator being analyzed"
                    },
                    "generated_at": {
                        "type": "string",
                        "description": "ISO8601 timestamp of when analysis was generated"
                    },
                    "discovery_method": {
                        "type": "string",
                        "description": "Method used for bundle discovery",
                        "const": "organic_clustering"
                    },
                    "total_posts_analyzed": {
                        "type": "integer",
                        "description": "Total number of posts analyzed"
                    },
                    "discovered_bundle_count": {
                        "type": "integer",
                        "description": "Number of bundles discovered"
                    },
                    "bundles": {
                        "type": "array",
                        "description": "Array of discovered linguistic style bundles",
                        "items": {
                            "type": "object",
                            "properties": {
                                "bundle_name": {
                                    "type": "string",
                                    "description": "Discovered organic name for the bundle"
                                },
                                "bundle_description": {
                                    "type": "string",
                                    "description": "What makes these posts cluster together"
                                },
                                "usage_context": {
                                    "type": "string",
                                    "description": "When this linguistic style emerges"
                                },
                                "post_count_in_bundle": {
                                    "type": "integer",
                                    "description": "Number of posts in this bundle"
                                },
                                "representative_posts": {
                                    "type": "array",
                                    "description": "Posts that exemplify this bundle",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "post_id": {"type": "string"},
                                            "excerpt": {"type": "string"},
                                            "why_representative": {"type": "string"},
                                            "linguistic_markers": {
                                                "type": "array",
                                                "items": {"type": "string"}
                                            }
                                        },
                                        "required": ["post_id", "excerpt", "why_representative"]
                                    }
                                },
                                "audience_relationship": {
                                    "type": "string",
                                    "description": "How creator positions themselves"
                                },
                                "emotional_intent": {
                                    "type": "string",
                                    "description": "Underlying emotional goal"
                                },
                                "content_characteristics": {
                                    "type": "array",
                                    "description": "Types of content that trigger this style",
                                    "items": {"type": "string"}
                                },
                                "1_overall_voice_tone": {
                                    "type": "object",
                                    "description": "Voice and tone analysis for this bundle",
                                    "additionalProperties": True
                                },
                                "2_sentence_rhythm_and_flow": {
                                    "type": "object", 
                                    "description": "Sentence patterns and rhythm for this bundle",
                                    "additionalProperties": True
                                },
                                "3_paragraph_dynamics": {
                                    "type": "object",
                                    "description": "Paragraph structure and dynamics for this bundle",
                                    "additionalProperties": True
                                },
                                "4_vocabulary_and_phrasing": {
                                    "type": "object",
                                    "description": "Vocabulary and phrasing patterns for this bundle",
                                    "additionalProperties": True
                                },
                                "5_rhetorical_signature": {
                                    "type": "object",
                                    "description": "Rhetorical devices and persuasion patterns for this bundle",
                                    "additionalProperties": True
                                },
                                "6_structural_emphasis_techniques": {
                                    "type": "object",
                                    "description": "Structural emphasis and formatting techniques for this bundle",
                                    "additionalProperties": True
                                },
                                "7_linguistic_dos": {
                                    "type": "array",
                                    "description": "Linguistic rules to follow for this bundle",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "rule": {"type": "string"},
                                            "priority": {"type": "number"},
                                            "example": {"type": "string"}
                                        },
                                        "required": ["rule", "priority"]
                                    }
                                },
                                "8_linguistic_donts": {
                                    "type": "array",
                                    "description": "Linguistic rules to avoid for this bundle",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "rule": {"type": "string"},
                                            "priority": {"type": "number"},
                                            "rationale": {"type": "string"}
                                        },
                                        "required": ["rule", "priority"]
                                    }
                                },
                                "9_mid_body_reader_engagement_linguistics": {
                                    "type": "object",
                                    "description": "Reader engagement techniques for this bundle",
                                    "additionalProperties": True
                                },
                                "10_transition_techniques": {
                                    "type": "object",
                                    "description": "Transition patterns and techniques for this bundle",
                                    "additionalProperties": True
                                },
                                "11_contextual_application_rules": {
                                    "type": "array",
                                    "description": "Rules for when and how to apply this bundle",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "when": {"type": "string"},
                                            "apply": {"type": "string"},
                                            "avoid": {"type": "string"},
                                            "example": {"type": "string"}
                                        },
                                        "required": ["when", "apply"]
                                    }
                                },
                                "12_overall_linguistic_intent_summary": {
                                    "type": "string",
                                    "description": "Summary of the strategic intent behind this bundle's style"
                                },
                                "contextual_triggers": {
                                    "type": "array",
                                    "description": "What prompts this style and how it manifests",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "trigger": {"type": "string"},
                                            "response": {"type": "string"},
                                            "examples": {
                                                "type": "array",
                                                "items": {"type": "string"}
                                            }
                                        },
                                        "required": ["trigger", "response"]
                                    }
                                }
                            },
                            "required": ["bundle_name", "bundle_description", "usage_context", "post_count_in_bundle"]
                        }
                    },
                    "cross_bundle_analysis": {
                        "type": "object",
                        "description": "Analysis across all bundles",
                        "properties": {
                            "style_variation_drivers": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "consistent_elements": {
                                "type": "array", 
                                "items": {"type": "string"}
                            },
                            "bundle_boundaries": {
                                "type": "string"
                            },
                            "evolution_patterns": {
                                "type": "string"
                            }
                        },
                        "additionalProperties": True
                    },
                    "discovery_insights": {
                        "type": "object",
                        "description": "Insights from the bundle discovery process",
                        "properties": {
                            "primary_finding": {"type": "string"},
                            "bundle_naturalness": {"type": "string"},
                            "outlier_posts": {"type": "integer"},
                            "confidence_level": {"type": "string"}
                        },
                        "additionalProperties": True
                    }
                },
                "required": ["schema_version", "creator_id", "bundles"]
            }
        }
        
        EXTRACT_BODY_FRAMEWORKS_TOOL_SCHEMA = {
            "name": "extract_body_frameworks",
            "description": "Extract complete body structure frameworks from analysis with verbatim examples preserved",
            "input_schema": {
                "type": "object",
                "properties": {
                    "frameworks": {
                        "type": "array",
                        "description": "Array of complete framework bundles with all original details preserved",
                        "items": {
                            "type": "object",
                            "properties": {
                                "framework_name": {
                                    "type": "string",
                                    "description": "Exact framework name from the document"
                                },
                                "framework_description": {
                                    "type": "string", 
                                    "description": "Complete framework description verbatim"
                                },
                                "when_to_use": {
                                    "type": "string",
                                    "description": "Usage context and scenarios verbatim"
                                },
                                "target_length": {
                                    "type": "string",
                                    "description": "Character count range and optimization details"
                                },
                                "structure_components_sequence": {
                                    "type": "array",
                                    "description": "Exact component sequence from original analysis",
                                    "items": {"type": "string"}
                                },
                                "component_functions": {
                                    "type": "object",
                                    "description": "Exact component explanations from original analysis",
                                    "additionalProperties": True
                                },
                                "creator_example_full": {
                                    "type": "string",
                                    "description": "THE COMPLETE LINKEDIN POST EXAMPLE (preserve every character, line break, formatting)"
                                },
                                "engagement_metrics": {
                                    "type": "object",
                                    "description": "Exact performance data if available",
                                    "additionalProperties": True
                                },
                                "character_count_guidelines": {
                                    "type": "object",
                                    "description": "Exact optimization guidance from analysis",
                                    "additionalProperties": True
                                },
                                "theme_compatibility": {
                                    "type": "object",
                                    "description": "Exact compatibility scores from analysis",
                                    "additionalProperties": True
                                },
                                "implementation_steps": {
                                    "type": "array",
                                    "description": "Exact implementation guidance from analysis",
                                    "items": {"type": "string"}
                                },
                                "source_citations": {
                                    "type": "array",
                                    "description": "Citations for every preserved element",
                                    "items": {"type": "string"}
                                }
                            },
                            "required": ["framework_name", "framework_description", "creator_example_full"]
                        }
                    },
                    "extraction_summary": {
                        "type": "string",
                        "description": "Brief summary of framework extraction process"
                    }
                },
                "required": ["frameworks"]
            }
        }
        
        return {
            "extract_linguistic_bundles": EXTRACT_LINGUISTIC_BUNDLES_TOOL_SCHEMA,
            "extract_body_frameworks": EXTRACT_BODY_FRAMEWORKS_TOOL_SCHEMA,
        }
    
    @staticmethod
    def get_all_tools():
        """Get all available tool schemas."""
        return {
            **ToolRegistry.get_ideas_extraction_tools(),
            **ToolRegistry.get_pattern_selection_tools(),
            **ToolRegistry.get_bundle_extraction_tools(),
        } 