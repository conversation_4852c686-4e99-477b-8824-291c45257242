"""Prompt for extracting LinkedIn post ideas."""
from pathlib import Path
from .citations_ideas_extraction_models import load_outline_guidance, load_scoring_criteria

PROMPT = f"""Please analyze this transcript and identify valuable LinkedIn post ideas from the conversation. Look for compelling themes, insights, and content opportunities that emerge from the discussion.

Look for:
- Important themes that emerge across the conversation
- Valuable insights and perspectives shared
- Interesting patterns in how problems or solutions are discussed
- Unique viewpoints that deserve to be shared
- Stories and experiences that could resonate with others
- Practical advice or lessons learned
- Industry insights and trends discussed

For each post idea, provide:
   - The post concept and its natural category (include funnel stage if strategy context is available)
   - An engagement score:
{load_scoring_criteria()}
   - An engagement_rationale explaining your score
   - The intended audience or purpose
   - Supporting citations from the transcript
   - List citations in order of importance/relevance
   - A meaty summary (3-5 sentences) that maps out the idea or story: what it's really about, why it matters, what makes it interesting, and the different angles for turning this into compelling content
   - Strategic notes for content creation (if strategy context is available)
   {load_outline_guidance()}

Ensure each post idea:
   - Represents a valuable insight or theme from the conversation
   - Is supported by relevant transcript quotes
   - Has clear connections to the discussion
   - Could be developed into compelling LinkedIn content
   - Would provide value to the intended audience

Use the extract_ideas tool to return your findings."""