"""Async implementation of citations-based idea extraction."""
import asyncio
import time
import traceback
import json
import re # Keep for now, might be needed later but removing from PDF parsing
import fitz # PyMuPDF
import logging
from typing import List, Optional, Dict

from src.infrastructure.llm.citations_claude import CitationsAnthropic
from src.utils.enhanced_logging import get_logger
from src.core.common.base_prompts.prompt_loader import load_component_prompt
from src.utils.async_retry import retry_api_call_async
from src.utils.timing import TimingLogger
from src.utils.citation_schema import Citation  # Import shared Citation model
from src.config import Config
from src.core.ideas_extraction.citations_ideas_extraction_models import ExtractedIdeas, IdeaItem, PostOutline
from src.core.ideas_extraction.common.citations_ideas_extraction_schema import (
    IDEAS_EXTRACTION_TOOL_SCHEMA
)
# --- Add MongoDB related imports ---
from pymongo.errors import PyMongoError
from src.core.models.transcript_document import TranscriptDocument
from src.utils.mongo_utils_async import get_async_mongo_db
from src.utils.strategy_utils import format_strategy_for_prompt, create_strategic_context
# ---------------------------------

logger = get_logger('linkedinsight.core.ideas_extraction.ideas_extractor')

def extract_text_from_pdf(pdf_file):
    """Extracts text using PyMuPDF and cleans whitespace."""
    try:
        pdf_file.seek(0)
        pdf_data = pdf_file.read()
        doc = fitz.open(stream=pdf_data, filetype="pdf")

        cleaned_page_texts = []
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)  # type: ignore[attr-defined]
            raw_page_text = page.get_text("text", sort=True)  # type: ignore[attr-defined]
            if raw_page_text:
                # Normalise all whitespace to single spaces
                cleaned_page = re.sub(r"\s+", " ", raw_page_text).strip()

                if cleaned_page.strip():
                    cleaned_page_texts.append(cleaned_page.strip())
            else:
                 logger.warning(f"Page {page_num} had no text extracted.")

        # Join the cleaned text of all pages with a single space
        final_text = ' '.join(cleaned_page_texts)

        if not final_text:
            logger.warning("PyMuPDF parsing resulted in empty text after basic cleanup.")
            raise ValueError("No text content could be extracted from the PDF.")

        logger.info(f"Final cleaned PDF text (first 100 chars): {final_text[:100]}")
        return final_text

    except Exception as e:
        logger.error(f"Error during PDF processing with PyMuPDF: {e}", exc_info=True)
        raise Exception(f"Failed to process PDF: {e}") from e

class IdeasExtractor:
    """Handles parallel extraction of content ideas using Claude's citations feature."""

    def __init__(
        self,
        citations_client: CitationsAnthropic
    ):
        """Initialize with CitationsAnthropic client.

        Args:
            citations_client: CitationsAnthropic client for interacting with Claude.
        """
        Config.validate()

        self.client = citations_client
        self.timing = TimingLogger("AsyncCitationsExtractor")

        # Load prompts at initialization time
        self.extraction_prompt, self.system_prompt = load_component_prompt("ideas")

    @property
    def debug_callback(self):
        """Get debug callback."""
        return self.timing.debug_callback

    @debug_callback.setter
    def debug_callback(self, callback):
        """Set debug callback and propagate to client."""
        self.timing.debug_callback = callback
        if hasattr(self, 'client'):
            self.client.debug_callback = callback

    def enable_timing_logs(self, output_file: str):
        """Enable detailed timing logs to JSON file."""
        self.timing.enable_timing_logs(output_file)


    @retry_api_call_async(max_retries=3, retry_delay=5, error_message="Claude API error in idea extraction")
    async def extract_ideas_from_transcript(self, transcript_content: str, strategy_context: Optional[dict] = None) -> List[IdeaItem]:
        """Extract derived ideas with citations."""
        start_time = time.time()
        self.timing.log_timing("idea_extraction_start")
        logger.info("🔍 Starting idea extraction at %.3f", start_time)

        # Prepare API parameters
        # Use the exact same model for both idea extraction types to enable caching
        api_params = {
            "model": "claude-sonnet-4-20250514", # Using user-specified model name
            "max_tokens": 8000,
            "system_prompt": self.system_prompt,
            "user_prompt": self.extraction_prompt,
            "tool_name": "extract_ideas",
            "transcript": transcript_content
        }
        
        # Add strategy context to user prompt if provided
        if strategy_context:
            # Format strategy using the utility
            formatted_strategy = format_strategy_for_prompt(strategy_context)
            strategic_context = create_strategic_context(strategy_context)
            
            # Build the strategy prompt section with XML tags
            strategy_prompt = f"\n\n{formatted_strategy}\n\n"
            strategy_prompt += f"""<funnel_guidance>
    <tofu>{strategic_context['funnel_guidance']['top']}</tofu>
    <mofu>{strategic_context['funnel_guidance']['middle']}</mofu>
    <bofu>{strategic_context['funnel_guidance']['bottom']}</bofu>
</funnel_guidance>

<strategy_application>
    <guidance_type>{strategic_context['application_guidance']}</guidance_type>
    <intensity>{strategic_context['intensity']}</intensity>
</strategy_application>

Use this strategy to:
1. Categorize ideas by funnel stage (TOFU/MOFU/BOFU) based on their alignment
2. Add strategic_notes field explaining how each idea serves the content strategy
3. Prioritize ideas that resonate with the target audience while maintaining authenticity
"""
            
            api_params["user_prompt"] = self.extraction_prompt + strategy_prompt

        # Thinking is removed from idea extraction phase as requested

        # Use the correct tool schema
        api_params["tool_name"] = "extract_ideas"
        tool_data = await self.client.create_transcript_tool_call(**api_params)

        self.timing.log_duration("idea_extraction_complete", start_time)
        logger.info("✅ Idea extraction completed")

        if not tool_data or 'ideas' not in tool_data:
            raise ValueError("No valid ideas found in response")

        # Thinking logging removed from idea extraction phase

        # Convert to IdeaItems
        ideas = []
        invalid_format_count = 0
        parse_error_count = 0
        for i, idea in enumerate(tool_data['ideas']):
            try:
                if not isinstance(idea, dict):
                    invalid_format_count += 1 # Count invalid items
                    continue

                ideas.append(IdeaItem(
                    idea=idea.get('idea', ''),
                    category=idea.get('category', 'Uncategorized'),
                    engagement=idea.get('engagement', 3),
                    engagement_rationale=idea.get('engagement_rationale'),
                    summary=idea.get('summary', ''),
                    strategic_notes=idea.get('strategic_notes'),
                    citations=idea.get('citations', []),
                    suggested_outlines=[
                        PostOutline(**outline)
                        for outline in idea.get('suggested_outlines', [])
                    ]
                ))
            except Exception as e:
                parse_error_count += 1 # Count parsing errors

        # Log summary of issues AFTER the loop
        if invalid_format_count > 0:
            logger.warning(f"Skipped {invalid_format_count} items due to invalid format.")
        if parse_error_count > 0:
             logger.warning(f"Failed to parse {parse_error_count} valid idea structures.")

        return ideas

    async def extract_ideas(self, transcript_content: str, user_email: str, project_id: Optional[str] = None, strategy_context: Optional[dict] = None) -> ExtractedIdeas:
        """Extract ideas from transcript.

        Args:
            transcript_content: The text content of the transcript.
            user_email: The email address of the user submitting the transcript.
            project_id: Optional ID of the project associated with this transcript (for StorYD Studio users).

        Returns:
            ExtractedIdeas: An object containing the extracted ideas.
        """
        # Log the project_id for debugging
        logger.info(f"🔍 extract_ideas called with project_id: {project_id} for user: {user_email}")
        try:
            start_time = time.time()
            self.timing.log_timing("idea_extraction_start")
            logger.info("🎬 Starting idea extraction at %.3f", start_time)
            
            # Extract ideas from transcript with strategy context
            ideas = await self.extract_ideas_from_transcript(transcript_content, strategy_context)
            
            logger.info(f"✅ Completed idea extraction with {len(ideas)} ideas")

            self.timing.log_duration("idea_extraction_complete", start_time, idea_count=len(ideas))
            logger.info(f"📊 Results: {len(ideas)} ideas extracted")

            # Initialize transcript_id_str to None
            transcript_id_str = None
            
            # --- Persist results to MongoDB ---
            try:
                # Create the document model instance with user_email
                # Ideas already have status="new" by default from IdeaItem model
                transcript_doc = TranscriptDocument(
                    transcript_content=transcript_content,
                    user_email=user_email, # Pass the user_email
                    ideas=ideas
                )

                # Get MongoDB database
                db = await get_async_mongo_db()
                # Get the collection (using a constant or config might be better later)
                collection_name = "transcripts"
                transcripts_collection = db[collection_name]

                # Convert Pydantic model to dict for insertion
                doc_to_insert = transcript_doc.model_dump()

                # Insert the document
                logger.info(f"💾 Attempting to save transcript for user {user_email} to MongoDB...")
                insert_result = await transcripts_collection.insert_one(doc_to_insert)
                transcript_oid = insert_result.inserted_id
                logger.info(f"💾 Successfully saved document to {collection_name} with ID: {transcript_oid}")

                # Expose the transcript_id in the extraction result so the frontend can persist it
                transcript_id_str = str(transcript_oid)

            except PyMongoError as e:
                logger.error(f"❌ MongoDB Error: Failed to save transcript document for user {user_email}: {e}")
                pass # Logged the error, continue to return ideas
            except Exception as e:
                logger.error(f"❌ Error preparing or saving transcript document for user {user_email}: {e}")
                logger.error(f"Full error trace: {traceback.format_exc()}")
                pass # Logged the error, continue
            # -------------------------------------
            
            result = ExtractedIdeas(
                ideas=ideas
            )

            # Add progress info (consider if this is still needed if data is persisted)
            result.progress_info = {
                'ideas': [idea.idea[:50] + "..." for idea in ideas]
            }

            # Attach the MongoDB transcript ID if available
            if transcript_id_str:
                result.transcript_id = transcript_id_str

            return result

        except Exception as e:
            self.timing.log_timing("idea_extraction_error", error=str(e))
            logger.error(f"❌ Error in idea extraction: {str(e)}")
            logger.error(f"Full error: {traceback.format_exc()}")
            raise ValueError(f"Failed to extract ideas: {str(e)}")