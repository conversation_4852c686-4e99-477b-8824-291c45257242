"""Models for citations-based idea extraction."""
from typing import Dict, List, Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator, field_validator
from src.utils.citation_schema import Citation
from uuid import uuid4


def load_scoring_criteria() -> str:
    """Load the value scoring criteria."""
    criteria_path = Path(__file__).parent / "common" / "value_scoring_criteria.txt"
    return criteria_path.read_text()


def load_outline_guidance() -> str:
    """Load the outline guidance for content ideas."""
    guidance_path = Path(__file__).parent / "common" / "outline_guidance.txt"
    return guidance_path.read_text()


class PostOutline(BaseModel):
    """Outline format for a content post."""
    title: str = Field(description="Title/type of the outline format")
    throughline: str = Field(description="Core narrative thread that connects the sections")
    sections: List[str] = Field(
        description="Ordered sections that make up the outline",
        min_length=3,
        max_length=5
    )


class IdeaItem(BaseModel):
    """Individual content idea with metadata."""
    id: str = Field(default_factory=lambda: str(uuid4()), description="Unique identifier for the idea")
    idea: str = Field(description="The main idea content (maximum 7 words)")
    category: str = Field(description="Category of the idea, including funnel stage (TOFU/MOFU/BOFU) when strategy is provided")
    engagement: int = Field(description="Engagement score (1-5)", ge=1, le=5)
    engagement_rationale: Optional[str] = Field(
        default=None,
        description="Explanation of why this engagement score was assigned"
    )
    mentioned_by: Optional[str] = Field(description="Who mentioned this idea", default=None)
    summary: Optional[str] = Field(description="Detailed summary of the idea", default=None)
    strategic_notes: Optional[str] = Field(
        default=None,
        description="Strategic guidance for content creation based on user's content strategy"
    )
    citations: List[Citation] = Field(
        description="Citations supporting this idea",
        min_length=1
    )
    suggested_outlines: List[PostOutline] = Field(
        default_factory=list,
        description="List of exactly 3 suggested post outlines generated by Claude",
        min_length=3,
        max_length=3
    )
    generated_brief: Optional[str] = Field(
        default=None,
        description="The full text content of the brief generated based on the selected outline. Null if not generated."
    )
    status: str = "new"

    @field_validator('idea')
    @classmethod
    def validate_idea_length(cls, v: str) -> str:
        """Validate that idea is not longer than 7 words."""
        words = v.split()
        if len(words) > 7:
            raise ValueError("Idea title must not be longer than 7 words")
        return v


class ExtractedIdeas(BaseModel):
    """Collection of extracted content ideas."""
    ideas: List[IdeaItem] = Field(default_factory=list)
    progress_info: Dict[str, List[str]] = Field(default_factory=dict)
    # Reference to the transcript document stored in MongoDB
    transcript_id: Optional[str] = None