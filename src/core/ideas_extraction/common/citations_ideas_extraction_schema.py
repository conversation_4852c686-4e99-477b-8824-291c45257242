"""Tool schemas for idea extraction with citations support."""
from src.utils.citation_schema import CITATION_SCHEMA

# Schema for post outline suggestions
POST_OUTLINE_SCHEMA = {
    "type": "object",
    "properties": {
        "title": {
            "type": "string",
            "description": "A descriptive name for this outline (3-7 words explaining its approach and benefits)"
        },
        "throughline": {
            "type": "string",
            "description": "The core narrative thread that connects the sections and reflects the natural flow of the conversation"
        },
        "sections": {
            "type": "array",
            "items": {"type": "string"},
            "minItems": 3,
            "maxItems": 5,
            "description": "Ordered list of body section names only - do not include hooks, endings, or CTAs"
        }
    },
    "required": ["title", "throughline", "sections"]
}

# Base schema for ideas
BASE_IDEA_SCHEMA = {
    "type": "object",
    "properties": {
        "idea": {
            "type": "string",
            "description": "The core idea as a concise title (maximum 7 words). This should be brief and impactful."
        },
        "category": {
            "type": "string",
            "description": "When strategy context is available: use ONLY 'TOFU', 'MOFU', or 'BOFU'. When no strategy context: use a descriptive category name."
        },
        "engagement": {"type": "integer", "minimum": 1, "maximum": 5},
        "engagement_rationale": {
            "type": "string",
            "description": "Brief explanation of why this engagement score was assigned, considering both value criteria and strategy alignment"
        },
        "summary": {"type": "string"},
        "citations": {
            "type": "array",
            "description": "Supporting citations for this idea with metadata",
            "items": CITATION_SCHEMA,
            "minItems": 1
        },
        "suggested_outlines": {
            "type": "array",
            "description": "List of 3 suggested post outlines that naturally organize the content",
            "items": POST_OUTLINE_SCHEMA,
            "minItems": 3,
            "maxItems": 3
        },
        "strategic_notes": {
            "type": "string",
            "description": "Strategic guidance for content creation based on user's content strategy (optional)"
        }
    },
    "required": ["idea", "category", "engagement", "engagement_rationale", "citations", "summary"]
}

# Schema for ideas extraction
IDEAS_EXTRACTION_TOOL_SCHEMA = {
    "name": "extract_ideas",
    "description": """Extract content ideas from interview transcripts with citations.
Each idea must include:
1. The core idea text
2. Its category
3. Engagement score (1-5)
4. Engagement rationale (explanation of the score)
5. Supporting citations
6. List of exactly 3 suggested outlines (each with 3-5 sections)""",
    "input_schema": {
        "type": "object",
        "properties": {
            "ideas": {
                "type": "array",
                "items": BASE_IDEA_SCHEMA
            }
        },
        "required": ["ideas"]
    }
}