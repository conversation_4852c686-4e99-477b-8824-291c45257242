"""LLM client interface for dependency injection."""
from abc import ABC, abstractmethod
from typing import Op<PERSON>
from anthropic import AsyncAnthropic


class LLMClientInterface(ABC):
    """Interface for LLM client implementations."""
    
    @abstractmethod
    def get_standard_client(self) -> AsyncAnthropic:
        """Get standard Anthropic client.
        
        Returns:
            An AsyncAnthropic client without beta features
        """
        pass
        
    @abstractmethod
    def get_beta_client(self, enable_thinking: bool = False) -> AsyncAnthropic:
        """Get beta-enabled Anthropic client.
        
        Args:
            enable_thinking: Whether to enable <PERSON> "thinking" stream
            
        Returns:
            An AsyncAnthropic client with beta features enabled
        """
        pass
    
    @abstractmethod
    def set_debug_callback(self, callback: Optional[callable]) -> None:
        """Set debug callback for both clients.
        
        Args:
            callback: Debug callback function
        """
        pass