"""Pydantic model for representing a generated brief document in MongoDB."""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

# Define the structure for individual blocks within the brief
class BlockData(BaseModel):
    type: str  # e.g., 'text', 'citation', 'thinking'
    content: str

class GeneratedBriefDocument(BaseModel):
    """Structure for storing generated briefs in MongoDB."""
    user_email: str  # User's email
    brief_blocks: List[BlockData] # Store content as structured blocks
    idea_id: str  # Reference to the idea this brief is for
    idea_title: str  # Title of the idea
    transcript_id: Optional[str] = None  # Reference to the transcript (if available)
    project_id: Optional[str] = None  # Optional project_id field for StorYD Studio users
    outline_title: Optional[str] = None  # Title of the outline used
    createdAt: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        # Configuration for MongoDB serialization
        pass
