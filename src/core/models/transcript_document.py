"""Pydantic model for representing a transcript document in MongoDB."""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field

# Import IdeaItem with correct src prefix
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem

class TranscriptDocument(BaseModel):
    """Structure for storing transcript and extracted ideas in MongoDB."""
    transcript_content: str
    user_email: str  # Add field to store the user's email
    ideas: List[IdeaItem]
    generated_ideas: List[IdeaItem] = Field(default_factory=list)  # Ideas that have been used to generate content
    createdAt: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        # Example config if needed later, e.g., for handling MongoDB ObjectId
        # allow_population_by_field_name = True
        # json_encoders = {
        #     ObjectId: str
        # }
        pass 