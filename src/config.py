"""Configuration management for Anthropic API integration and data paths."""
import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger(__name__)

# File and directory paths
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


CSV_DIR = os.path.join(ROOT_DIR, "data/csv")
PROMPTS_DIR = os.path.join(ROOT_DIR, "prompts")
TEMPLATES_DIR = os.path.join(ROOT_DIR, "templates")
OUTPUTS_DIR = os.path.join(ROOT_DIR, "outputs")

class Config:
    """Configuration management."""

    _instance = None
    ANTHROPIC_API_KEY: str | None = None
    # JWT_SECRET_KEY removed - using Clerk for authentication
    MONGODB_URI: str | None = None         # MongoDB connection string
    DEFAULT_MODEL: str | None = None
    CLAUDE_BETA_HEADER: str | None = None  # Added Claude beta header for tool calling
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 1

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._load_config()
        return cls._instance

    @classmethod
    def _load_config(cls):
        """Load configuration from environment variables."""
        # Try to load from .env file in the root directory
        # Assuming this script is run from the project root or config is loaded early
        env_path = Path(ROOT_DIR) / '.env'
        if env_path.exists():
            load_dotenv(dotenv_path=env_path, override=True)
            logger.debug(f"Loaded .env file from: {env_path}")
        else:
            logger.debug(f".env file not found at: {env_path}")


        # Required settings
        cls.ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
        if not cls.ANTHROPIC_API_KEY:
            raise ValueError("ANTHROPIC_API_KEY environment variable is required")

        # JWT_SECRET_KEY removed - using Clerk for authentication instead

        # MongoDB connection string
        cls.MONGODB_URI = os.getenv('MONGODB_URI')
        if not cls.MONGODB_URI:
            logger.warning("MONGODB_URI environment variable is not set. MongoDB features will be unavailable.")

        # Optional settings with defaults
        cls.DEFAULT_MODEL = os.getenv('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514') # Updated default model
        cls.CLAUDE_BETA_HEADER = os.getenv('CLAUDE_BETA_HEADER', 'tools-2024-04-04') # Default beta header for tool calling
        cls.MAX_RETRIES = int(os.getenv('ANTHROPIC_MAX_RETRIES', '3'))
        cls.RETRY_DELAY = int(os.getenv('ANTHROPIC_RETRY_DELAY', '1'))

    @classmethod
    def get_anthropic_config(cls) -> dict:
        """Get Anthropic client configuration."""
        return {
            "api_key": cls.ANTHROPIC_API_KEY,
            "default_model": cls.DEFAULT_MODEL,
            "max_retries": cls.MAX_RETRIES,
            "retry_delay": cls.RETRY_DELAY
        }

    # get_jwt_secret_key removed - using Clerk for authentication

    @classmethod
    def get_mongodb_uri(cls) -> str:
        """Get MongoDB connection URI."""
        if not cls.MONGODB_URI:
            raise ValueError("MONGODB_URI not loaded")
        return cls.MONGODB_URI
    
    @classmethod
    def validate(cls) -> None:
        """Validate that required configuration is loaded."""
        if not cls.ANTHROPIC_API_KEY:
            raise ValueError("ANTHROPIC_API_KEY is required")
        # JWT validation removed - using Clerk
        # MongoDB URI validation is optional since it logs a warning

# Model selection mapping for content generation steps
STEP_MODEL_MAPPING = {
    "hook": "claude-sonnet-4-20250514",
    "body": "claude-sonnet-4-20250514", 
    "ending": "claude-sonnet-4-20250514",
    "default": "claude-sonnet-4-20250514"
}

# Initialize config immediately so it loads on import
Config()