"""Utility for processing citations from Anthropic API responses."""

def process_citations(content_blocks):
    """
    Process Anthropic API content blocks with citations into structured markdown.
    
    Args:
        content_blocks: List of content blocks from Anthropic API response
        
    Returns:
        String containing processed markdown with inline citation references
    """
    # Process the response to handle citations
    processed_text = ""
    citations = {}
    citation_counter = 1
    
    # First pass: extract all citations and build the citation mapping
    for block in content_blocks:
        if block.type == 'text' and block.citations:
            for citation in block.citations:
                cite = citation.cited_text.strip()
                source = citation.document_title  # e.g., "Post ID: 5"
                
                # Store the citation with a reference number
                cite_ref = f"[cite: {citation_counter}]"
                citations[cite_ref] = {
                    "text": cite,
                    "source": source
                }
                citation_counter += 1
    
    # Second pass: process the text content with inline citation references
    for block in content_blocks:
        if block.type == 'thinking':
            continue  # Skip thinking blocks
            
        if block.type == 'text':
            if block.citations:
                # It's a citation block
                continue  # Skip direct citation blocks in main content
            else:
                # Regular text block
                processed_text += block.text
    
    # Add the citation section (Section 2)
    processed_text += "\n\n## Section 2: Grounding Citations\n\n"
    
    # Add all citations in order
    for i in range(1, citation_counter):
        cite_ref = f"[cite: {i}]"
        if cite_ref in citations:
            citation = citations[cite_ref]
            processed_text += f"### {cite_ref}\n\n"
            processed_text += f"> {citation['text']}\n> \n"
            processed_text += f"> — {citation['source']}\n\n"
    
    return processed_text