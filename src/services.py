"""Service layer for managing application dependencies."""
from typing import Optional, AsyncGenerator
from src.config import Config
import asyncio
from src.core.ideas_extraction.ideas_extractor import IdeasExtractor

def get_citations_client():
    """Get or create the shared CitationsAnthropic client instance."""
    if not hasattr(get_citations_client, '_instance'):
        from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig
        from src.infrastructure.llm.citations_claude import CitationsAnthropic
        
        # Create config object first
        config = AnthropicConfig(
            api_key=Config.ANTHROPIC_API_KEY,
            beta_header=Config.CLAUDE_BETA_HEADER
        )
        
        # Create client with config
        client = AnthropicClient(config=config)
        get_citations_client._instance = CitationsAnthropic(client)
    
    return get_citations_client._instance

def _reset_extractor_instance(_: bool) -> None:
    """Reset extractor instance when dev mode changes."""
    if hasattr(get_extractor, '_instance'):
        delattr(get_extractor, '_instance')

def get_extractor():
    """Get the configured ideas extractor instance.
    
    Returns:
        IdeasExtractor instance configured with citations client.
    """

    if not hasattr(get_extractor, '_instance'):
        # Always use real extractor for now - can add mock mode later if needed
        get_extractor._instance = IdeasExtractor(get_citations_client())
    
    return get_extractor._instance

