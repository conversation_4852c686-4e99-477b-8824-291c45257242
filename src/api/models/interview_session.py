"""MongoDB models for interview sessions."""

from datetime import datetime
from typing import Dict, List, Optional, Any

from src.constants.collections import INTERVIEW_SESSIONS_COLLECTION


# Schema for MongoDB document
INTERVIEW_SESSION_SCHEMA = {
    "session_id": str,  # Unique identifier for the session
    "created_at": datetime,  # Creation timestamp
    "updated_at": datetime,  # Last update timestamp
    "conversation_history": [
        {
            "role": str,  # "user" or "assistant"
            "content": str,  # Message content
            "timestamp": datetime  # Message timestamp
        }
    ],
    "brief_content": str  # Current content of the brief
}