"""Pydantic schemas for Draft Library."""

from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from typing import Dict, List, Optional, Any
from enum import Enum
import datetime


class DraftMetadata(BaseModel):
    """Metadata for an individual draft."""
    linguistic_pattern_name: Optional[str] = Field(None, description="Name of linguistic pattern used")
    linguistic_pattern_id: Optional[str] = Field(None, description="ID of linguistic pattern used")
    hook_pattern: Optional[str] = Field(None, description="Hook pattern selected")
    body_framework: Optional[str] = Field(None, description="Body framework used")
    ending_pattern: Optional[str] = Field(None, description="Ending pattern selected")
    word_count: Optional[int] = Field(None, description="Total word count")
    character_count: Optional[int] = Field(None, description="Total character count")
    generation_time_ms: Optional[int] = Field(None, description="Time taken to generate in milliseconds")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )


class Draft(BaseModel):
    """Individual draft with full content and metadata."""
    draft_id: str = Field(..., description="Unique draft identifier")
    session_id: Optional[str] = Field(None, description="Parent session ID")
    user_id: str = Field(..., description="User who created this draft")
    content: str = Field(..., description="Full generated draft content")
    brief: str = Field(..., description="Brief used to generate this draft")
    post_length: Optional[str] = Field(None, description="Target post length (short, medium, long)")
    version: int = Field(1, description="Version number within session")
    draft_type: Optional[str] = Field(None, description="Draft type (e.g., draft_a, draft_b)")
    job_id: Optional[str] = Field(None, description="Associated job ID")
    metadata: DraftMetadata = Field(default_factory=DraftMetadata, description="Draft metadata")
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    updated_at: Optional[datetime.datetime] = Field(None, description="Last update timestamp")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "draftId": "draft_xyz789",
                "sessionId": "session_abc123",
                "userId": "user_2NxAkjhKM9Pz8bFabcdef",
                "content": "Strategic thinking isn't just about planning...",
                "brief": "Write about strategic thinking in business",
                "postLength": "medium",
                "version": 1,
                "metadata": {
                    "linguisticPatternName": "Strategic Philosophy Insights",
                    "hookPattern": "Contrarian Hook",
                    "bodyFramework": "Problem-Solution Bridge",
                    "endingPattern": "Challenge CTA",
                    "wordCount": 287,
                    "characterCount": 1543
                },
                "createdAt": "2024-01-20T10:30:00Z"
            }
        }
    )


class DraftCreate(BaseModel):
    """Schema for creating a new draft."""
    session_id: str = Field(..., description="Parent session ID")
    content: str = Field(..., description="Draft content")
    brief: str = Field(..., description="Brief used")
    post_length: str = Field(..., description="Post length")
    metadata: Optional[DraftMetadata] = Field(None, description="Draft metadata")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )


class DraftUpdate(BaseModel):
    """Schema for updating a draft."""
    content: Optional[str] = Field(None, description="Updated content")
    metadata: Optional[DraftMetadata] = Field(None, description="Updated metadata")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )


class DraftListResponse(BaseModel):
    """Response for listing drafts."""
    drafts: List[Draft] = Field(..., description="List of drafts")
    total: int = Field(..., description="Total number of drafts")
    page: int = Field(..., description="Current page")
    page_size: int = Field(..., description="Items per page")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )


class DraftFilter(BaseModel):
    """Filter options for searching drafts."""
    session_id: Optional[str] = Field(None, description="Filter by session")
    linguistic_pattern_name: Optional[str] = Field(None, description="Filter by linguistic pattern")
    date_from: Optional[datetime.datetime] = Field(None, description="Filter by date range start")
    date_to: Optional[datetime.datetime] = Field(None, description="Filter by date range end")
    search_text: Optional[str] = Field(None, description="Search in content or brief")
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )