"""Braintrust middleware for request-level tracing"""

from fastapi import Request
from fastapi.responses import Response
import braintrust
import time
from typing import Callable
import logging
import os

logger = logging.getLogger(__name__)

# Initialize Braintrust logger once
if os.getenv("BRAINTRUST_API_KEY"):
    try:
        braintrust.init_logger(project="LinkedInsight")
        logger.info("Braintrust logger initialized")
    except Exception as e:
        logger.warning(f"Failed to initialize Braintrust logger: {e}")

async def braintrust_middleware(request: Request, call_next: Callable) -> Response:
    """Middleware to create Braintrust spans for each request"""
    
    # Skip if Braintrust isn't configured
    if not os.getenv("BRAINTRUST_API_KEY"):
        return await call_next(request)
    
    # Create a parent span for this request
    start_time = time.time()
    
    try:
        # Use start_span to create a parent span that child LLM calls will nest under
        with braintrust.start_span(
            name=f"{request.method} {request.url.path}",
            type="http_request"
        ) as span:
            # Log request details
            span.log(input={
                "method": request.method,
                "path": str(request.url.path),
                "query_params": dict(request.query_params),
                "headers": {k: v for k, v in request.headers.items() if k.lower() != "authorization"}
            })
            
            # Process the request - all LLM calls within will be nested under this span
            response = await call_next(request)
            
            # Log response details
            span.log(output={
                "status_code": response.status_code,
                "duration_ms": (time.time() - start_time) * 1000
            })
            
            return response
            
    except Exception as e:
        # If span exists, log the error
        if 'span' in locals():
            span.log(error={
                "message": str(e),
                "type": type(e).__name__,
                "duration_ms": (time.time() - start_time) * 1000
            })
        logger.error(f"Request failed: {str(e)}")
        raise