"""FastAPI router for transcript processing endpoints."""
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from src.api.auth import get_current_user_email
from src.utils.mongo_utils_async import get_async_mongo_db
from src.utils.validation import validate_object_id
from src.utils.error_handlers import handle_mongo_errors
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem
from pydantic import BaseModel
from datetime import datetime

# Simple response models
class TranscriptListResponse(BaseModel):
    id: str
    createdAt: Optional[datetime] = None
    snippet: str
    implicitCount: int = 0
    generatedCount: int = 0
    project_id: Optional[str] = None

class TranscriptDetailResponse(BaseModel):
    id: str
    createdAt: Optional[datetime] = None
    transcript_content: str
    ideas: List[IdeaItem] = []  # implicit ideas only
    generated_ideas: List[IdeaItem] = []
    user_email: str
    project_id: Optional[str] = None

class TranscriptIdeaUpdateRequest(BaseModel):
    status: Optional[str] = None
    generated_brief: Optional[str] = None

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/transcripts", tags=["transcripts"])


@router.get("/", response_model=List[TranscriptListResponse])
@handle_mongo_errors("fetching transcripts")
async def list_transcripts(
    project_id: Optional[str] = Query(None),
    user_email: str = Depends(get_current_user_email),
    db = Depends(get_async_mongo_db)
):
    """List transcripts for the authenticated user."""
    try:
        transcripts_collection = db.transcripts
        
        # Build query filter
        if project_id and project_id != 'alpha-tester':
            logger.info(f"Filtering transcripts by project_id: {project_id} for user: {user_email}")
            query_filter = {
                'user_email': user_email,
                'project_id': project_id
            }
        else:
            logger.info(f"Fetching all transcripts for user: {user_email}")
            query_filter = {'user_email': user_email}

        # MongoDB aggregation pipeline for counts
        pipeline = [
            {"$match": query_filter},
            {
                "$addFields": {
                    "id": {"$toString": "$_id"},
                    "snippet": {
                        "$cond": {
                            "if": {"$gt": [{"$strLenCP": "$transcript_content"}, 250]},
                            "then": {"$concat": [{"$substr": ["$transcript_content", 0, 250]}, "..."]},
                            "else": "$transcript_content"
                        }
                    },
                    "implicitCount": {"$size": {"$ifNull": ["$ideas", []]}},
                    "generatedCount": {"$size": {"$ifNull": ["$generated_ideas", []]}}
                }
            },
            {
                "$project": {
                    "id": 1,
                    "createdAt": 1,
                    "snippet": 1,
                    "explicitCount": 1,
                    "implicitCount": 1,
                    "generatedCount": 1,
                    "project_id": 1
                }
            },
            {"$sort": {"createdAt": -1}}
        ]

        cursor = transcripts_collection.aggregate(pipeline)
        results = await cursor.to_list(length=None)
        
        logger.info(f"Found {len(results)} transcripts for user: {user_email}")
        return results

    except HTTPException:
        raise


@router.get("/{transcript_id}", response_model=TranscriptDetailResponse)
@handle_mongo_errors("fetching transcript")
async def get_transcript(
    transcript_id: str,
    user_email: str = Depends(get_current_user_email),
    db = Depends(get_async_mongo_db)
):
    """Get a specific transcript by ID."""
    try:
        # Validate ObjectId format
        object_id = validate_object_id(transcript_id, "transcript ID")

        transcripts_collection = db.transcripts
        
        # Find document with user ownership check
        query_filter = {
            '_id': object_id,
            'user_email': user_email
        }
        
        transcript_doc = await transcripts_collection.find_one(query_filter)
        
        if not transcript_doc:
            raise HTTPException(status_code=404, detail="Transcript not found")

        # Convert to response format
        ideas_list = transcript_doc.get('ideas', [])
        generated_ideas_list = transcript_doc.get('generated_ideas', [])
        
        logger.info(f"Transcript {transcript_id} has {len(ideas_list)} ideas and {len(generated_ideas_list)} generated_ideas")
        
        result = {
            'id': str(transcript_doc['_id']),
            'createdAt': transcript_doc.get('createdAt'),
            'transcript_content': transcript_doc['transcript_content'],
            'ideas': ideas_list,  # implicit ideas only
            'generated_ideas': generated_ideas_list,
            'user_email': transcript_doc.get('user_email'),
            'project_id': transcript_doc.get('project_id')
        }

        return result

    except HTTPException:
        raise


@router.delete("/{transcript_id}")
@handle_mongo_errors("deleting transcript")
async def delete_transcript(
    transcript_id: str,
    user_email: str = Depends(get_current_user_email),
    db = Depends(get_async_mongo_db)
):
    """Delete a transcript by ID."""
    try:
        # Validate ObjectId format
        object_id = validate_object_id(transcript_id, "transcript ID")

        transcripts_collection = db.transcripts
        
        result = await transcripts_collection.delete_one({
            '_id': object_id, 
            'user_email': user_email
        })
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Transcript not found")

        return {"message": "Transcript deleted successfully"}

    except HTTPException:
        raise


@router.patch("/{transcript_id}/ideas/{idea_id}")
@handle_mongo_errors("updating idea")
async def update_transcript_idea(
    transcript_id: str,
    idea_id: str,
    update_data: TranscriptIdeaUpdateRequest,
    user_email: str = Depends(get_current_user_email),
    db = Depends(get_async_mongo_db)
):
    """Update a specific idea within a transcript."""
    try:
        # Validate ObjectId format
        object_id = validate_object_id(transcript_id, "transcript ID")

        transcripts_collection = db.transcripts
        
        # Find the transcript
        transcript_doc = await transcripts_collection.find_one({
            '_id': object_id,
            'user_email': user_email
        })

        if not transcript_doc:
            raise HTTPException(status_code=404, detail="Transcript not found")

        # Find the idea location
        idea_found = False
        idea_location = None
        idea_index = None

        # Check different idea arrays (only ideas and generated_ideas now)
        for field_name in ['ideas', 'generated_ideas']:
            ideas_list = transcript_doc.get(field_name, [])
            for i, idea in enumerate(ideas_list):
                if str(idea.get('id')) == idea_id:
                    idea_found = True
                    idea_location = field_name
                    idea_index = i
                    logger.info(f"Found idea {idea_id} in {field_name} at index {i}")
                    break
            if idea_found:
                break

        if not idea_found:
            raise HTTPException(status_code=404, detail="Idea not found in transcript")

        # Build update fields
        update_fields = {}
        update_dict = update_data.model_dump(exclude_unset=True)
        
        for field, value in update_dict.items():
            update_fields[f"{idea_location}.{idea_index}.{field}"] = value

        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")

        # Update the document
        result = await transcripts_collection.update_one(
            {'_id': object_id},
            {'$set': update_fields}
        )

        if result.modified_count == 0:
            return {"message": "Idea state matches update request (no change needed)"}

        return {"message": "Idea updated successfully"}

    except HTTPException:
        raise