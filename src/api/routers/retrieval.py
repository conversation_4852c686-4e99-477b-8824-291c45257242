"""API routes for retrieving data (health, auth, creator info, job status, results)."""

import logging
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends, Query, Path as FastAPIPath

from src.api.auth import get_current_user
from src.api.schemas import (
    JobStatusResponse
)
# Import MongoDB retrieval functions
from src.utils.mongo_retrieval import (
    get_creator_info,
    fetch_latest_json_content,
    get_all_creators,
    fetch_latest_markdown_content
)
from src.utils.stats_helpers import get_creator_summary_stats

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Data Retrieval"])

# --- Helper function for fetching markdown ---

async def _fetch_latest_markdown_content(
    creator_name: str, analysis_type: str
) -> Optional[str]:
    """Internal helper to find and read the latest markdown content for a given type.

    Args:
        creator_name: The name of the creator.
        analysis_type: The type of analysis (e.g., 'overview', 'themes').

    Returns:
        The markdown content as a string, or None if not found or an error occurs.
    """
    # Use the MongoDB retrieval function
    return await fetch_latest_markdown_content(creator_name, analysis_type)

# --- Retrieval Endpoints will be moved here ---

# Example placeholder (will be replaced)
@router.get("/placeholder-retrieval")
async def placeholder_retrieval():
    return {"message": "Retrieval routes will be here"}

# Health check endpoint removed - handled in main.py
# @router.get("/health", response_model=HealthResponse)
# async def health_check():
#     """Check the API health status. This endpoint is public and doesn't require authentication."""
#     # Note: Added specific endpoint list relevant to this router potentially
#     # Or keep the original list from main.py? Keeping original for now.
#     return {
#         "status": "ok",
#         "version": "0.1.0", # Consider making version dynamic/configurable
#         "endpoints": [
#             "/health",
#             "/api/auth/me",
#             "/api/analyze/overview", # These analyze routes are now in analysis.py
#             "/api/analyze/themes",
#             "/api/analyze/hooks",
#             "/api/analyze/body",
#             "/api/analyze/endings",
#             "/api/analyze/linguistic",
#             "/api/creator/{creator_name}",
#             "/api/job/{job_id}",
#             "/api/results/{analysis_type}/{creator_name}",
#             "/api/markdown/{analysis_type}/{creator_name}"
#         ]
#     }

# Authentication test endpoint - protected with authentication
@router.get("/api/auth/me")
async def get_current_user_info(user_data: dict = Depends(get_current_user)):
    """Get the current user information from the JWT token. Useful for testing authentication."""
    return {
        "authenticated": True,
        "user": {
            "id": user_data.get("sub"),
            "email": user_data.get("email"),
            "name": user_data.get("name"),
            "issued_at": user_data.get("iat"),
            "expires_at": user_data.get("exp")
        }
    }

# Get all creators endpoint - protected with authentication
@router.get("/api/creators")
async def get_all_creator_information(
    user_data: dict = Depends(get_current_user)
):
    """Get information about all creators. Requires authentication."""
    try:
        creators = await get_all_creators()
        return {"creators": creators}
    except Exception as e:
        logger.error(f"Error getting all creators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Creator info endpoint - protected with authentication
@router.get("/api/creator/{creator_name}")
async def get_creator_information(
    creator_name: str = FastAPIPath(..., description="Name of the creator"),
    user_data: dict = Depends(get_current_user)
):
    """Get information about a specific creator. Requires authentication."""
    try:
        creator_info = await get_creator_info(creator_name)

        if not creator_info:
            # Return empty data for unknown creators instead of 404
            return {
                "creator_name": creator_name,
                "posts_count": 0,
                "first_post_date": None,
                "last_post_date": None,
                "engagement": None
            }

        return creator_info

    except Exception as e:
        logger.error(f"Error getting creator info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Job status endpoint deprecated - analysis runner removed

# Results endpoint deprecated - content analysis features removed

# Creator summary stats endpoint - protected with authentication
@router.get("/api/creator/{creator_name}/summary-stats")
async def get_creator_summary_statistics(
    creator_name: str = FastAPIPath(..., description="Name of the creator"),
    user_data: dict = Depends(get_current_user)
):
    """
    Get summary statistics for a specific creator.
    
    This endpoint returns structured statistics about a creator's content including:
    - Number of posts analyzed
    - Number of content themes identified
    - Average engagement as a raw number (not formatted)
    - Maximum engagement as a raw number (not formatted)
    
    The frontend should format the engagement numbers as needed (e.g., "1.2K", "3.4M").
    
    Requires authentication.
    """
    try:
        logger.info(f"API request for creator summary stats: {creator_name}")
        
        # Use the helper function to get stats
        stats = await get_creator_summary_stats(creator_name)
        
        if not stats:
            raise HTTPException(
                status_code=404,
                detail=f"Summary statistics not found for creator: {creator_name}"
            )
            
        # Format the response according to the Pydantic model
        response = {
            "creatorName": creator_name,
            "stats": stats
        }
        
        return response
        
    except HTTPException:
        # Re-raise HTTPException directly
        raise
    except Exception as e:
        # Catch any unexpected errors
        logger.error(f"Unexpected error in get_creator_summary_statistics endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error retrieving creator statistics")

# Markdown content endpoint - protected with authentication
@router.get("/api/markdown/{analysis_type}/{creator_name}")
async def get_markdown_content(
    analysis_type: str = FastAPIPath(..., description="Type of analysis (e.g., overview, themes)"),
    creator_name: str = FastAPIPath(..., description="Name of the creator"),
    user_data: dict = Depends(get_current_user)
):
    """Get raw markdown content for a specific creator and analysis type. Requires authentication."""
    try:
        # Validate analysis type (use lowercase for comparison)
        valid_types = ["themes", "hooks", "body", "endings", "linguistic", "overview"]
        if analysis_type.lower() not in valid_types:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid analysis type. Must be one of: {', '.join(valid_types)}"
            )

        logger.info(f"API request for markdown content: {creator_name} ({analysis_type})")

        # Use the helper function to fetch content
        markdown_content = await _fetch_latest_markdown_content(creator_name, analysis_type)

        if markdown_content is None:
            # If helper returns None, it means file wasn't found or error occurred
            raise HTTPException(
                status_code=404,
                detail=f"Markdown content not found for {creator_name} ({analysis_type})"
            )

        # Return the content if found
        return {"content": markdown_content}

    except HTTPException:
        # Re-raise HTTPException directly
        raise
    except Exception as e:
        # Catch any unexpected errors
        logger.error(f"Unexpected error in get_markdown_content endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error retrieving markdown content")