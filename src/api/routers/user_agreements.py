"""User agreements and terms acceptance router."""
from datetime import datetime
from typing import Optional, Dict

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel
from pymongo.asynchronous.database import AsyncDatabase

from src.constants.collections import USER_AGREEMENTS_COLLECTION
from src.api.auth import get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db

router = APIRouter()


class TermsAcceptanceRequest(BaseModel):
    """Request model for accepting terms."""
    terms_version: str = "1.0"
    accepted: bool = True


class TermsStatusResponse(BaseModel):
    """Response model for terms acceptance status."""
    has_accepted: bool
    accepted_at: Optional[datetime] = None
    terms_version: Optional[str] = None


@router.get("/terms-status")
async def get_terms_status(
    current_user: Dict = Depends(get_current_user),
    db: AsyncDatabase = Depends(get_async_mongo_db)
):
    """Check if the current user has accepted the terms."""
    clerk_user_id = current_user["sub"]
    
    # Get the user agreement from MongoDB
    agreement = await db[USER_AGREEMENTS_COLLECTION].find_one(
        {"clerk_user_id": clerk_user_id}
    )
    
    if agreement:
        return {
            "data": {
                "has_accepted": True,
                "accepted_at": agreement.get("accepted_at"),
                "terms_version": agreement.get("terms_version")
            }
        }
    
    return {
        "data": {
            "has_accepted": False,
            "accepted_at": None,
            "terms_version": None
        }
    }


@router.post("/accept-terms")
async def accept_terms(
    acceptance: TermsAcceptanceRequest,
    request: Request,
    current_user: Dict = Depends(get_current_user),
    db: AsyncDatabase = Depends(get_async_mongo_db)
):
    """Record user's acceptance of terms."""
    if not acceptance.accepted:
        raise HTTPException(
            status_code=400,
            detail="Terms must be accepted to proceed"
        )
    
    clerk_user_id = current_user["sub"]
    user_email = current_user.get("email", "")
    
    # Prepare the agreement document
    agreement_doc = {
        "clerk_user_id": clerk_user_id,
        "user_email": user_email,
        "terms_version": acceptance.terms_version,
        "accepted_at": datetime.utcnow(),
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent", "")
    }
    
    # Upsert the agreement (update if exists, insert if not)
    await db[USER_AGREEMENTS_COLLECTION].update_one(
        {"clerk_user_id": clerk_user_id},
        {"$set": agreement_doc},
        upsert=True
    )
    
    return {
        "data": {
            "success": True,
            "accepted_at": agreement_doc["accepted_at"],
            "terms_version": agreement_doc["terms_version"]
        }
    }