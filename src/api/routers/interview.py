"""API routes for interview-related operations."""

import logging
import uuid
from fastapi import APIRouter, Depends
from typing import <PERSON>ple, Optional

# Import schemas
from src.api.schemas import InterviewTurnRequest, InterviewTurnResponse
from src.utils.anthropic_text_editor_handler import AnthropicTextEditorHandler
from src.api.auth import get_current_user

# Import interview service
from src.api.services.interview_service import (
    create_interview_session,
    get_interview_session,
    update_conversation_history,
    update_brief_content,
    get_conversation_history,
    get_brief_content
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/interview", tags=["Interview"])

# Import shared components from interview_core
from .interview_core import (
    INTERVIEW_AGENT_SYSTEM_PROMPT,
    TEXT_EDITOR_TOOL_DEFINITION,
    get_memory_path,
    extract_text_from_response
)

async def initialize_session(session_id: str = None, user_id: Optional[str] = None) -> <PERSON><PERSON>[str, AnthropicTextEditorHandler]:
    """
    Initialize a new interview session or validate an existing one.
    
    Args:
        session_id (str, optional): The session ID to validate. If None, a new session is created.
        user_id (str, optional): The user ID (clerk_user_id) for fetching content strategy.
        
    Returns:
        Tuple[str, AnthropicTextEditorHandler]: The session ID and text editor handler.
    """
    # Create a new text editor handler for this request
    text_editor_handler = AnthropicTextEditorHandler(allow_real_filesystem_access=False)
    
    # If no session_id provided, create a new one in MongoDB
    if not session_id:
        session_id = await create_interview_session(user_id=user_id)
    else:
        # Validate that the session exists in MongoDB
        session = await get_interview_session(session_id)
        if not session:
            # Session not found, create a new one
            session_id = await create_interview_session(user_id=user_id)
    
    # Use the canonical memory path for the brief
    canonical_path = get_memory_path(session_id)
    
    # Get the current brief content from MongoDB
    brief_content = await get_brief_content(session_id)
    
    # If no content exists in MongoDB, start with minimal content
    if not brief_content:
        brief_content = ""
    
    # Create or overwrite the file to ensure we have a clean state
    result, is_error = text_editor_handler.dispatch_command("create", {
        "path": canonical_path,
        "file_text": brief_content
    })
    
    logger.info(f"Initialized brief at {canonical_path} with content length: {len(brief_content)}")
    
    return session_id, text_editor_handler

@router.post("/turn", response_model=InterviewTurnResponse)
async def process_interview_turn(
    request: InterviewTurnRequest,
    current_user: Optional[dict] = Depends(get_current_user)
):
    """
    Process a turn in the interview conversation.
    
    This endpoint now returns immediately with session and message IDs.
    The actual response is streamed via the /stream/{session_id}/{message_id} endpoint.
    
    Args:
        request (InterviewTurnRequest): The user's message and optional session ID.
        current_user: Optional user context from authentication
        
    Returns:
        InterviewTurnResponse: Contains session_id and message_id for streaming.
    """
    try:
        # Extract user ID if authenticated
        user_id = current_user.get("sub") if current_user else None
        
        # Initialize or validate the session
        session_id, _ = await initialize_session(request.session_id, user_id=user_id)
        
        # Generate a unique message ID for this turn
        message_id = str(uuid.uuid4())
        
        # Add the user message to the conversation history in MongoDB
        await update_conversation_history(session_id, request.user_message, is_user=True)
        
        # Return immediately with IDs for streaming
        # The frontend will use these to connect to the SSE endpoint
        return InterviewTurnResponse(
            session_id=session_id,
            agent_message="",  # Empty since streaming will provide the content
            updated_brief_content="",  # Empty since streaming will provide updates
            error=None,
            message_id=message_id  # New field for streaming reference
        )
        
    except Exception as e:
        logger.exception(f"Error initializing interview turn: {e}")
        return InterviewTurnResponse(
            session_id=request.session_id or str(uuid.uuid4()),
            agent_message="I'm sorry, but I encountered an error while processing your message. Please try again.",
            updated_brief_content="",
            error=str(e)
        )