"""API Router for matching briefs to creators."""

import logging
from fastapi import APIRouter, HTTPException, status

from src.api.schemas import CreatorMatcherRequest, CreatorMatcherResponse
from src.utils.brief_classifier_utils import match_creators_to_brief, load_creator_style_cards

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/creator-matcher",
    tags=["Creator Matcher"]
)

# Accept both `/api/creator-matcher/` *and* `/api/creator-matcher` to prevent
# FastAPI from issuing an automatic 307 redirect (which strips CORS headers
# and breaks the browser request).
@router.post("", include_in_schema=False)  # no-slash variant
@router.post(
    "/",
    response_model=CreatorMatcherResponse,
    summary="Match Brief to Creators",
    description="Analyzes a brief and returns the two most suitable creators based on their style cards.",
    status_code=status.HTTP_200_OK
)
async def match_brief_to_creators(request: CreatorMatcherRequest):
    """
    Analyzes a brief and returns the two most suitable creators.
    
    Args:
        request: The request containing the brief to analyze
        
    Returns:
        A response containing the recommended creators and reasoning
        
    Raises:
        HTTPException: If the brief is empty or if an error occurs during processing
    """
    try:
        # Validate the brief
        if not request.brief or request.brief.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Brief cannot be empty"
            )
            
        logger.info(f"Matching brief to creators: {request.brief[:100]}...")
        
        # Load creator style cards
        creator_style_cards = load_creator_style_cards()
        logger.info(f"Loaded {len(creator_style_cards)} creator style cards")
        
        # Match creators to brief
        result = await match_creators_to_brief(
            brief=request.brief,
            creator_style_cards=creator_style_cards,
            model="claude-3-5-haiku-latest"  # Use Haiku for faster response
        )
        
        # Check if we got a valid result
        if "error" in result:
            logger.error(f"Error matching creators to brief: {result['error']}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error matching creators to brief: {result['error']}"
            )
            
        # Return the result
        return {
            "recommended_creators": result.get("recommended_creators", []),
            "reasoning": result.get("reasoning", "No reasoning provided")
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log and convert other exceptions to HTTP exceptions
        logger.error(f"Unexpected error matching brief to creators: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )
