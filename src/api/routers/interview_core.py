"""Core constants and utilities for interview functionality."""

import os
from typing import Optional
import anthropic
from datetime import datetime
from src.config import Config

# Constants
INTERVIEW_MODEL = "claude-sonnet-4-20250514"
INTERVIEW_MAX_TOKENS = 2000

# Tool definition
TEXT_EDITOR_TOOL_DEFINITION = {
    "type": "text_editor_20250124",
    "name": "str_replace_editor"
}

# Anthropic client singleton
_anthropic_client: Optional[anthropic.Anthropic] = None

def get_anthropic_client() -> anthropic.Anthropic:
    """Get or create a shared Anthropic client instance."""
    global _anthropic_client
    if _anthropic_client is None:
        config = Config()
        _anthropic_client = anthropic.Anthropic(api_key=config.ANTHROPIC_API_KEY)
    return _anthropic_client

def get_memory_path(session_id: str) -> str:
    """Generate a memory path for the interview brief based on the session ID."""
    return f"memory://{session_id}/brief_content.md"

def load_interview_system_prompt() -> str:
    """Load the interview agent system prompt from file."""
    prompt_path = os.path.join(os.path.dirname(__file__), "../../../prompts/interview_agent_system_prompt.txt")
    with open(prompt_path, 'r', encoding='utf-8') as f:
        prompt = f.read()
    
    # Add current date to system prompt
    prompt += f"\n\nCurrent date: {datetime.now().strftime('%Y-%m-%d')}"
    return prompt

# Load system prompt at module level
INTERVIEW_AGENT_SYSTEM_PROMPT = load_interview_system_prompt()

def extract_text_from_response(response) -> str:
    """Extract text content from an Anthropic API response."""
    for c in response.content:
        if hasattr(c, "type") and c.type == "text":
            return c.text
    return ""