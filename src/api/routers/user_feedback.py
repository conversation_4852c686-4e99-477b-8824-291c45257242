"""User feedback submission router."""

import logging
import os
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultipart
from typing import Dict

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from pymongo.errors import PyMongoError
from pymongo.asynchronous.collection import AsyncCollection

from src.api.auth import get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db
from src.constants.collections import USER_FEEDBACK_COLLECTION

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["User Feedback"]
)


class FeedbackRequest(BaseModel):
    """Request model for user feedback submission."""
    feedback: str = Field(..., description="User's feedback text", min_length=1, max_length=5000)
    url: str = Field(..., description="URL where the feedback was submitted from", max_length=2000)

    class Config:
        """Pydantic model configuration."""
        json_schema_extra = {
            "example": {
                "feedback": "The content generation feature works great, but it would be helpful to have more customization options for tone.",
                "url": "https://app.linkedinsight.com/content-generation"
            }
        }


class FeedbackResponse(BaseModel):
    """Response model for successful feedback submission."""
    success: bool = Field(..., description="Whether the feedback was successfully saved")
    feedback_id: str = Field(..., description="Unique identifier for the submitted feedback")
    submitted_at: datetime = Field(..., description="When the feedback was submitted")

    class Config:
        """Pydantic model configuration."""
        json_schema_extra = {
            "example": {
                "success": True,
                "feedback_id": "507f1f77bcf86cd799439011",
                "submitted_at": "2024-01-15T10:30:00Z"
            }
        }


async def send_feedback_email(feedback: str, user_email: str, url: str, submitted_at: datetime):
    """Send email notification when feedback is submitted."""
    try:
        # Get SMTP configuration from environment variables
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME")
        smtp_password = os.getenv("SMTP_PASSWORD")
        recipient_email = os.getenv("FEEDBACK_RECIPIENT_EMAIL")
        
        if not all([smtp_username, smtp_password, recipient_email]):
            logger.warning("SMTP configuration incomplete - skipping email notification")
            return
        
        # Create email message
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = recipient_email
        msg['Subject'] = f"New Feedback from LinkedInsight User"
        
        # Email body
        body = f"""
New feedback has been submitted to LinkedInsight:

From: {user_email}
Submitted: {submitted_at.strftime('%Y-%m-%d %H:%M:%S UTC')}
Page URL: {url}

Feedback:
{feedback}

---
This notification was sent automatically from LinkedInsight.
        """.strip()
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()
        
        logger.info(f"Feedback email notification sent to {recipient_email}")
        
    except Exception as e:
        logger.error(f"Failed to send feedback email notification: {str(e)}")
        # Don't raise the exception - email failure shouldn't break feedback submission


@router.post(
    "/feedback",
    response_model=FeedbackResponse,
    summary="Submit User Feedback",
    description="Allows authenticated users to submit feedback about the application.",
    status_code=status.HTTP_201_CREATED
)
async def submit_feedback(
    feedback_request: FeedbackRequest,
    current_user: Dict = Depends(get_current_user)
):
    """
    Submit user feedback to the system.
    
    The feedback is associated with the authenticated user and stored in MongoDB
    for later review and analysis.
    
    Args:
        feedback_request: The feedback data including text and URL
        current_user: The authenticated user information from JWT token
        
    Returns:
        FeedbackResponse with success status and feedback ID
        
    Raises:
        HTTPException: If user authentication fails or database operation fails
    """
    # Extract user information from JWT token
    clerk_user_id = current_user.get("sub")
    user_email = current_user.get("email", "")
    
    if not clerk_user_id:
        logger.error("Clerk User ID not found in token payload.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    # Get MongoDB connection
    db = await get_async_mongo_db()
    collection: AsyncCollection = db[USER_FEEDBACK_COLLECTION]

    # Prepare feedback document
    feedback_doc = {
        "clerk_user_id": clerk_user_id,
        "user_email": user_email,
        "feedback": feedback_request.feedback,
        "url": feedback_request.url,
        "submitted_at": datetime.utcnow(),
        "status": "pending",  # Default status for new feedback
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }

    try:
        # Insert feedback document into MongoDB
        insert_result = await collection.insert_one(feedback_doc)
        
        if not insert_result.inserted_id:
            logger.error(f"Failed to insert feedback for user {clerk_user_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save feedback to database."
            )
        
        feedback_id = str(insert_result.inserted_id)
        logger.info(f"Successfully saved feedback {feedback_id} for user {clerk_user_id}")
        
        # Send email notification (async, but don't wait for it)
        await send_feedback_email(
            feedback=feedback_request.feedback,
            user_email=user_email,
            url=feedback_request.url,
            submitted_at=feedback_doc["submitted_at"]
        )
        
        return FeedbackResponse(
            success=True,
            feedback_id=feedback_id,
            submitted_at=feedback_doc["submitted_at"]
        )

    except PyMongoError as e:
        logger.error(f"MongoDB error while saving feedback for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database operation failed: {str(e)}"
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error while saving feedback for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred while saving the feedback."
        )