"""FastAPI router for ideas extraction endpoints."""
import hashlib
import logging
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks

from src.api.auth import get_current_user_email, get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db, CONTENT_STRATEGIES_COLLECTION
from src.utils.validation import validate_object_id
from src.constants.collections import EXTRACTION_JOBS_COLLECTION
from src.utils.error_handlers import handle_mongo_errors
from src.utils.datetime_utils import generate_job_id, get_current_timestamp
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem
from pydantic import BaseModel

# Simple request/response models
class IdeasExtractionRequest(BaseModel):
    transcript: str

class IdeasExtractionJobResponse(BaseModel):
    job_id: str
    status: str
    polling_url: str
    ideas: List[IdeaItem] = []

class IdeasExtractionStatusResponse(BaseModel):
    status: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None
    ideas: Optional[List[IdeaItem]] = None
    transcript_id: Optional[str] = None

from src.services import get_extractor

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/ideas", tags=["ideas"])



@router.post("/extract", response_model=IdeasExtractionJobResponse)
async def extract_ideas(
    request: IdeasExtractionRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Extract ideas from a transcript asynchronously."""
    try:
        transcript = request.transcript
        user_email = current_user.get("email")
        clerk_user_id = current_user.get("sub")
        
        if not transcript:
            raise HTTPException(status_code=400, detail="Missing required field: transcript")
        
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found in token")

        # Generate unique job ID
        job_id = generate_job_id()
        
        # Calculate transcript hash for validation
        transcript_hash = hashlib.md5(transcript[:100].encode()).hexdigest()
        
        # Create job with initial status
        job_data = {
            'transcript': transcript,
            'transcript_hash': transcript_hash,
            'user_email': user_email,
            'clerk_user_id': clerk_user_id,
            'status': 'processing',
            'created_at': get_current_timestamp()
        }
        
        # Store job in MongoDB
        db = await get_async_mongo_db()
        jobs_collection = db[EXTRACTION_JOBS_COLLECTION]
        await jobs_collection.insert_one({**job_data, 'job_id': job_id})
        
        # Start background processing
        background_tasks.add_task(
            process_extraction_job, 
            job_id, 
            transcript, 
            user_email,
            clerk_user_id
        )
        
        return {
            'job_id': job_id,
            'status': 'processing',
            'polling_url': f'/api/ideas/status/{job_id}',
            'ideas': []  # Empty until processing completes
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ideas extraction failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to extract ideas from transcript")


@router.get("/status/{job_id}", response_model=IdeasExtractionStatusResponse)
async def get_extraction_status(
    job_id: str,
    transcript_hash: Optional[str] = None,
    current_user: dict = Depends(get_current_user),
    db = Depends(get_async_mongo_db)
):
    """Get the status or results of an ideas extraction job."""
    try:
        # Get job from MongoDB with user ownership check
        jobs_collection = db[EXTRACTION_JOBS_COLLECTION]
        job_data = await jobs_collection.find_one({
            'job_id': job_id,
            'clerk_user_id': current_user.get("sub")
        })
        
        if not job_data:
            raise HTTPException(status_code=400, detail="Invalid or expired job ID")

        # TODO: Hash validation temporarily disabled for MVP
        # Re-enable when frontend/backend hash algorithms are aligned
        # if transcript_hash and job_data.get('transcript_hash'):
        #     cached_hash = job_data.get('transcript_hash')
        #     if transcript_hash != cached_hash:
        #         logger.warning(f"Transcript mismatch for job {job_id}")
        #         raise HTTPException(
        #             status_code=400, 
        #             detail="Transcript mismatch - results are for a different transcript"
        #         )

        status = job_data.get('status')

        if status == 'completed':
            return job_data.get('result', {})
        elif status == 'failed':
            return {
                'status': status,
                'error': job_data.get('error', 'Unknown error')
            }
        else:
            return {
                'status': status,
                'message': 'Ideas extraction in progress'
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get ideas extraction status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get ideas extraction status")


@router.post("/mark-generated")
@handle_mongo_errors("marking idea as generated")
async def mark_idea_generated(
    idea_id: str,
    transcript_id: str,
    user_email: str = Depends(get_current_user_email),
    db = Depends(get_async_mongo_db)
):
    """Mark an idea as generated within its transcript document."""
    try:
        if not idea_id or not transcript_id:
            raise HTTPException(
                status_code=400, 
                detail="Missing required fields: idea_id and transcript_id"
            )

        logger.info(f"User {user_email} marking idea {idea_id} as generated in transcript {transcript_id}")

        # Validate transcript ID format
        t_object_id = validate_object_id(transcript_id, "transcript ID")

        transcripts_collection = db.transcripts
        
        # Find the transcript
        transcript_doc = await transcripts_collection.find_one({
            '_id': t_object_id,
            'user_email': user_email
        })

        if not transcript_doc:
            raise HTTPException(status_code=404, detail="Transcript not found")

        # Find the idea to move
        idea_to_move = None
        source_list = None
        
        # Check ideas
        for list_name in ['ideas']:
            ideas_list = transcript_doc.get(list_name, [])
            for idea in ideas_list:
                if str(idea.get('id')) == idea_id:
                    idea_to_move = idea
                    source_list = list_name
                    break
            if idea_to_move:
                break

        if not idea_to_move:
            # Check if already in generated_ideas
            generated_ideas = transcript_doc.get('generated_ideas', [])
            if any(str(idea.get('id')) == idea_id for idea in generated_ideas):
                return {'status': 'already_generated'}
            else:
                raise HTTPException(status_code=404, detail="Idea not found in transcript")

        # Atomically move idea from source list to generated_ideas
        update_result = await transcripts_collection.update_one(
            {'_id': t_object_id},
            {
                '$pull': {source_list: {'id': idea_id}},
                '$addToSet': {'generated_ideas': idea_to_move}
            }
        )

        if update_result.modified_count > 0:
            logger.info(f"Successfully moved idea {idea_id} to generated_ideas")
            return {'status': 'success'}
        else:
            # Double-check if it's now in generated_ideas
            current_doc = await transcripts_collection.find_one({'_id': t_object_id})
            if current_doc and any(str(idea.get('id')) == idea_id for idea in current_doc.get('generated_ideas', [])):
                return {'status': 'already_generated'}
            else:
                raise HTTPException(status_code=500, detail="Failed to update transcript document")

    except HTTPException:
        raise


async def process_extraction_job(job_id: str, transcript: str, user_email: str, clerk_user_id: str):
    """Process the extraction job asynchronously."""
    try:
        logger.info(f"Processing extraction job {job_id} for user: {user_email}")

        # Update job status in MongoDB
        db = await get_async_mongo_db()
        jobs_collection = db[EXTRACTION_JOBS_COLLECTION]
        
        await jobs_collection.update_one(
            {'job_id': job_id},
            {'$set': {
                'status': 'processing',
                'started_at': get_current_timestamp()
            }}
        )
        
        # Fetch user's content strategy if available
        strategy_context = None
        if clerk_user_id:
            strategies_collection = db[CONTENT_STRATEGIES_COLLECTION]
            strategy_doc = await strategies_collection.find_one({"clerk_user_id": clerk_user_id})
            if strategy_doc:
                # Convert MongoDB document to dict, excluding _id
                strategy_context = {k: v for k, v in strategy_doc.items() if k != '_id'}
                logger.info(f"Found content strategy for user {clerk_user_id}")

        # Get the actual extractor and process
        extractor = get_extractor()
        result = await extractor.extract_ideas(transcript, user_email, strategy_context=strategy_context)
        
        # Convert to dict for JSON serialization
        if hasattr(result, 'model_dump'):  # Pydantic v2
            result_data = result.model_dump()
        else:  # Pydantic v1
            result_data = result.dict()
        
        # Update job with results in MongoDB
        await jobs_collection.update_one(
            {'job_id': job_id},
            {'$set': {
                'status': 'completed',
                'result': result_data,
                'completed_at': get_current_timestamp()
            }}
        )

    except Exception as e:
        logger.error(f"Ideas extraction job {job_id} failed: {str(e)}", exc_info=True)
        
        # Update job with error in MongoDB
        db = await get_async_mongo_db()
        jobs_collection = db[EXTRACTION_JOBS_COLLECTION]
        
        await jobs_collection.update_one(
            {'job_id': job_id},
            {'$set': {
                'status': 'failed',
                'error': str(e),
                'failed_at': get_current_timestamp()
            }}
        )