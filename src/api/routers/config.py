"""
Configuration endpoints for frontend consumption.
Provides centralized configuration data to eliminate frontend hardcoding.
"""

from fastapi import APIRouter
from typing import Dict, Any
import logging

from src.strategy_config import get_frontend_config

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/config", tags=["Configuration"])


@router.get("/strategy")
async def get_strategy_configuration() -> Dict[str, Any]:
    """
    Get strategy configuration for frontend use.
    
    Returns funnel stage display names, colors, tooltips, etc.
    This endpoint is public and heavily cached since the data is static.
    
    Returns:
        Dictionary with funnel stage configurations
    """
    try:
        config = get_frontend_config()
        logger.info("Returning strategy configuration to frontend")
        return config
    except Exception as e:
        logger.error(f"Error retrieving strategy configuration: {e}")
        # Return a safe default configuration
        return {
            "funnel_stages": {
                "unclassified": {
                    "displayName": "Not classified",
                    "shortName": "N/A",
                    "tooltip": "Funnel stage not determined",
                    "badgeColor": "#6B7280"
                }
            }
        }