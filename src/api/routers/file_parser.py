"""File parsing router for LinkedInsight API."""

from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import logging
from io import BytesIO
import os

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["file-parser"])

@router.post("/parse-file")
async def parse_file(file: UploadFile = File(...)):
    """
    Parse an uploaded file and extract its text content.
    Supports .docx, .pdf, and other text files.
    """
    try:
        # Validate file size (10MB limit)
        max_size = 10 * 1024 * 1024  # 10MB
        if file.size and file.size > max_size:
            raise HTTPException(status_code=413, detail="File too large. Maximum size is 10MB.")
        
        # Read file content
        content = await file.read()
        file_bytes = BytesIO(content)
        
        # Get file extension and type
        filename = file.filename or ""
        file_extension = filename.lower().split('.')[-1] if '.' in filename else ""
        content_type = file.content_type or ""
        
        logger.info(f"Parsing file: {filename}, type: {content_type}, extension: {file_extension}")
        
        # Parse based on file type
        if file_extension == "docx" or content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            text = await parse_docx(file_bytes)
        elif file_extension == "pdf" or content_type == "application/pdf":
            text = await parse_pdf(file_bytes)
        elif file_extension in ["txt", "md"] or content_type.startswith("text/"):
            # For text files, decode the bytes
            text = content.decode('utf-8', errors='ignore')
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file_extension}. Supported types: .docx, .pdf, .txt, .md"
            )
        
        if not text.strip():
            raise HTTPException(status_code=400, detail="No text content found in file.")
        
        logger.info(f"Successfully parsed file: {filename}, extracted {len(text)} characters")
        
        return JSONResponse(content={"text": text})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error parsing file {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to parse file: {str(e)}")

async def parse_docx(file_bytes: BytesIO) -> str:
    """Extract text from a DOCX file."""
    try:
        # Try to import python-docx
        try:
            from docx import Document
        except ImportError:
            raise HTTPException(
                status_code=500, 
                detail="DOCX parsing not available. Please install python-docx: pip install python-docx"
            )
        
        # Parse the document
        doc = Document(file_bytes)
        text_parts = []
        
        # Extract text from paragraphs
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_parts.append(paragraph.text)
        
        # Extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text)
        
        return "\n".join(text_parts)
        
    except Exception as e:
        logger.error(f"Error parsing DOCX: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to parse DOCX file: {str(e)}")

async def parse_pdf(file_bytes: BytesIO) -> str:
    """Extract text from a PDF file."""
    try:
        # Try to import PyMuPDF
        try:
            import fitz  # PyMuPDF
        except ImportError:
            raise HTTPException(
                status_code=500,
                detail="PDF parsing not available. Please install PyMuPDF: pip install PyMuPDF"
            )
        
        # Open the PDF document
        doc = fitz.open(stream=file_bytes.read(), filetype="pdf")
        text_parts = []
        
        # Extract text from each page
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            if text.strip():
                text_parts.append(text)
        
        doc.close()
        return "\n".join(text_parts)
        
    except Exception as e:
        logger.error(f"Error parsing PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to parse PDF file: {str(e)}")