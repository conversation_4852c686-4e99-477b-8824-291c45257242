"""API Router for managing user content strategies."""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from pymongo.errors import PyMongoError

from src.api.schemas import ContentStrategy # Renamed Pydantic model
from src.api.auth import get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db, CONTENT_STRATEGIES_COLLECTION

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/content-strategy",
    tags=["Content Strategy"]
)

@router.post(
    "/",
    response_model=ContentStrategy,
    summary="Create or Update User Content Strategy",
    description="Creates a new content strategy for the authenticated user or updates an existing one.",
    status_code=status.HTTP_200_OK # Return 200 for update, 201 for create is also an option but upsert simplifies to 200
)
async def create_or_update_strategy(
    strategy_payload: ContentStrategy,
    current_user: dict = Depends(get_current_user)
):
    """
    Creates or updates the content strategy for the authenticated user.
    Uses `clerk_user_id` from the JWT token to associate the strategy.
    The `clerk_user_id` in the payload is ignored; the one from the token is authoritative.
    """
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        logger.error("Clerk User ID not found in token payload.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    db = await get_async_mongo_db()
    collection = db[CONTENT_STRATEGIES_COLLECTION]

    # Prepare the data with clerk_user_id from the token
    try:
        # LOG 1: What did Pydantic parse from the request initially?
        logger.info(f"LOG 1: Received strategy_payload (Pydantic model): {strategy_payload}")
        
        # Create a dict with the data from the payload, using snake_case for MongoDB
        # model_dump default for by_alias is False. If Pydantic model uses Field(alias=...), 
        # this will dump using the Python field names (e.g., target_audience).
        strategy_data = strategy_payload.model_dump(by_alias=False) 
        
        # Set the clerk_user_id from the token
        strategy_data["clerk_user_id"] = clerk_user_id

        # LOG 2: What is being sent to MongoDB for the $set operation?
        logger.info(f"LOG 2: Data for MongoDB $set operation (strategy_data): {strategy_data}")

    except Exception as e:
        logger.error(f"Error processing strategy payload: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid strategy data format: {str(e)}"
        )

    try:
        # Make sure to await the update_one operation
        update_result = await collection.update_one(
            {"clerk_user_id": clerk_user_id},
            {"$set": strategy_data},
            upsert=True
        )
        # LOG 3: What did MongoDB report from the update?
        logger.info(f"LOG 3: MongoDB update_one result: matched_count={update_result.matched_count}, modified_count={update_result.modified_count}, upserted_id={update_result.upserted_id}")

        # Fetch the document to return the latest version
        saved_strategy_doc = await collection.find_one({"clerk_user_id": clerk_user_id})
        
        # LOG 4: What was fetched from MongoDB immediately after upsert?
        logger.info(f"LOG 4: Document fetched from MongoDB after upsert (saved_strategy_doc): {saved_strategy_doc}")
        
        if not saved_strategy_doc:
            logger.error(f"Failed to retrieve strategy after upsert for user: {clerk_user_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save or retrieve content strategy after update."
            )

        # Convert to Pydantic model (handles _id removal and field name conversion)
        response_model_instance = ContentStrategy.model_validate(saved_strategy_doc)
        
        # LOG 5: What does the Pydantic model look like before FastAPI serializes it for response?
        logger.info(f"LOG 5: Pydantic model instance for response (response_model_instance): {response_model_instance}")
        
        return response_model_instance

    except PyMongoError as e:
        logger.error(f"MongoDB error while creating/updating strategy for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database operation failed: {e}"
        )
    except HTTPException: # Re-raise HTTPException directly
        raise
    except Exception as e:
        logger.error(f"Unexpected error while creating/updating strategy for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred while creating/updating the strategy."
        )


@router.get(
    "/",
    response_model=ContentStrategy,
    summary="Get User Content Strategy",
    description="Retrieves the content strategy for the authenticated user."
)
async def get_strategy(current_user: dict = Depends(get_current_user)):
    """
    Retrieves the content strategy for the authenticated user.
    Uses `clerk_user_id` from the JWT token.
    """
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        logger.error("Clerk User ID not found in token payload for GET request.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    db = await get_async_mongo_db()
    collection = db[CONTENT_STRATEGIES_COLLECTION]

    try:
        strategy_doc = await collection.find_one({"clerk_user_id": clerk_user_id})
        if strategy_doc:
            # Convert to Pydantic model (handles _id removal and field name conversion)
            return ContentStrategy.model_validate(strategy_doc)
        else:
            logger.info(f"No content strategy found for user: {clerk_user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content strategy not found for this user."
            )
    except PyMongoError as e:
        logger.error(f"MongoDB error while retrieving strategy for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database operation failed: {e}"
        )
    except HTTPException: # Re-raise HTTPException directly
        raise
    except Exception as e:
        logger.error(f"Unexpected error while retrieving strategy for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred while retrieving the strategy."
        )