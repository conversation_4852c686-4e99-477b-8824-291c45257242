"""API Router for managing user content preferences."""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from pymongo.errors import PyMongoError
from pymongo.asynchronous.collection import AsyncCollection

from src.api.schemas import ContentPreferences
from src.api.auth import get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db, CONTENT_PREFERENCES_COLLECTION
from src.utils.content_preferences_definitions import CONTENT_PREFERENCES
from src.utils.auth_utils import extract_clerk_user_id, extract_clerk_user_id_with_context

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/content-preferences",
    tags=["Content Preferences"]
)

@router.get(
    "/options",
    summary="Get Content Preference Options",
    description="Retrieves all available content preference options and their metadata."
)
async def get_preference_options():
    """
    Returns all available content preference options.
    No authentication required as these are public configuration options.
    """
    # Transform the definitions into a frontend-friendly format
    # Only send value and label - that's all the frontend needs
    options = {}
    
    for field_name, field_options in CONTENT_PREFERENCES.items():
        options[field_name] = []
        for value, metadata in field_options.items():
            options[field_name].append({
                "value": value,
                "label": metadata["label"]
            })
    
    return options

@router.post(
    "/",
    response_model=ContentPreferences,
    summary="Create or Update User Content Preferences",
    description="Creates new content preferences for the authenticated user or updates existing ones.",
    status_code=status.HTTP_200_OK
)
async def create_or_update_preferences(
    preferences_payload: ContentPreferences,
    current_user: dict = Depends(get_current_user)
):
    """
    Creates or updates the content preferences for the authenticated user.
    Uses `clerk_user_id` from the JWT token to associate the preferences.
    The `clerk_user_id` in the payload is ignored; the one from the token is authoritative.
    """
    clerk_user_id = extract_clerk_user_id(current_user)

    db = await get_async_mongo_db()
    collection: AsyncCollection = db[CONTENT_PREFERENCES_COLLECTION]

    # Prepare the data with clerk_user_id from the token
    try:
        logger.info(f"Received preferences_payload: {preferences_payload}")
        
        # Create a dict with the data from the payload, using snake_case for MongoDB
        preferences_data = preferences_payload.model_dump(by_alias=False) 
        
        # Set the clerk_user_id from the token
        preferences_data["clerk_user_id"] = clerk_user_id

        logger.info(f"Data for MongoDB $set operation: {preferences_data}")

    except Exception as e:
        logger.error(f"Error processing preferences payload: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid preferences data format: {str(e)}"
        )

    try:
        # Upsert the preferences document
        update_result = await collection.update_one(
            {"clerk_user_id": clerk_user_id},
            {"$set": preferences_data},
            upsert=True
        )
        logger.info(f"MongoDB update_one result: matched_count={update_result.matched_count}, modified_count={update_result.modified_count}, upserted_id={update_result.upserted_id}")

        # Fetch the document to return the latest version
        saved_preferences_doc = await collection.find_one({"clerk_user_id": clerk_user_id})
        
        logger.info(f"Document fetched from MongoDB after upsert: {saved_preferences_doc}")
        
        if not saved_preferences_doc:
            logger.error(f"Failed to retrieve preferences after upsert for user: {clerk_user_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save or retrieve content preferences after update."
            )

        # Convert to Pydantic model (handles _id removal and field name conversion)
        response_model_instance = ContentPreferences.model_validate(saved_preferences_doc)
        
        logger.info(f"Pydantic model instance for response: {response_model_instance}")
        
        return response_model_instance

    except PyMongoError as e:
        logger.error(f"MongoDB error while creating/updating preferences for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database operation failed: {e}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error while creating/updating preferences for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred while creating/updating the preferences."
        )


@router.get(
    "/",
    response_model=ContentPreferences,
    summary="Get User Content Preferences",
    description="Retrieves the content preferences for the authenticated user."
)
async def get_preferences(current_user: dict = Depends(get_current_user)):
    """
    Retrieves the content preferences for the authenticated user.
    Uses `clerk_user_id` from the JWT token.
    """
    clerk_user_id = extract_clerk_user_id_with_context(current_user, "for GET request")

    db = await get_async_mongo_db()
    collection = db[CONTENT_PREFERENCES_COLLECTION]

    try:
        preferences_doc = await collection.find_one({"clerk_user_id": clerk_user_id})
        if preferences_doc:
            # Convert to Pydantic model (handles _id removal and field name conversion)
            return ContentPreferences.model_validate(preferences_doc)
        else:
            logger.info(f"No content preferences found for user: {clerk_user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content preferences not found for this user."
            )
    except PyMongoError as e:
        logger.error(f"MongoDB error while retrieving preferences for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database operation failed: {e}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error while retrieving preferences for user {clerk_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred while retrieving the preferences."
        )