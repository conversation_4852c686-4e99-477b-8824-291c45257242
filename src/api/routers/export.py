from fastapi import APIRouter, Path as FastAPIPath, HTTPException, Response, Depends
import logging
import markdown # Added for Markdown to HTML conversion
from weasyprint import HTML # Corrected import: Was WeasyPrint

# Import the helper function
from src.api.routers.retrieval import _fetch_latest_markdown_content
# Import auth dependency
from src.api.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()

# Define the standard analysis types
ANALYSIS_TYPES = ["Overview", "Themes", "Hooks", "Body", "Endings", "Linguistic"]

@router.get("/api/export/pdf/all/{creator_name}")
async def export_all_analyses_pdf(
    creator_name: str = FastAPIPath(..., description="The name of the creator"),
    user_data: dict = Depends(get_current_user) # Added authentication dependency
):
    """
    Endpoint to export all analyses for a creator as a combined PDF.
    Fetches markdown, converts to HTML, then to PDF, and returns the PDF.
    Requires authentication.
    """
    logger.info(f"Received authenticated request for PDF export: creator={creator_name}, user={user_data.get('sub')}")

    aggregated_markdown_chunks = []
    found_any_content = False # Keep track if any content was actually found

    for analysis_type in ANALYSIS_TYPES:
        logger.debug(f"Fetching markdown for type: {analysis_type}")
        content = await _fetch_latest_markdown_content(creator_name, analysis_type)

        # Add heading for the section
        # Use title case for consistency in headings
        heading = f"# {analysis_type.title()} Analysis\n\n"
        aggregated_markdown_chunks.append(heading)

        if content:
            logger.debug(f"Content found for type: {analysis_type}")
            aggregated_markdown_chunks.append(content)
            found_any_content = True
        else:
            logger.info(f"No content found for type: {analysis_type} for creator: {creator_name}")
            aggregated_markdown_chunks.append(f"*No {analysis_type.title()} analysis available.*")

        # Add separator between sections (except after the last one)
        if analysis_type != ANALYSIS_TYPES[-1]:
            aggregated_markdown_chunks.append("\n\n---\n\n") # Horizontal rule

    # Combine all chunks
    combined_markdown = "".join(aggregated_markdown_chunks)

    # Handle case where absolutely no content was found for any type
    if not found_any_content:
        logger.warning(f"No analysis content found for any type for creator: {creator_name}")
        raise HTTPException(status_code=404, detail=f"No analysis content found for creator: {creator_name}")

    # Convert aggregated markdown to HTML
    try:
        html_content = markdown.markdown(combined_markdown, extensions=['fenced_code', 'tables']) # Added extensions for better formatting
    except Exception as e:
        logger.error(f"Error converting Markdown to HTML for {creator_name}: {e}")
        raise HTTPException(status_code=500, detail="Error processing analysis content for PDF generation.")

    # Generate PDF from HTML using WeasyPrint
    try:
        logger.info(f"Generating PDF for {creator_name}...")
        pdf_bytes = HTML(string=html_content).write_pdf()
        logger.info(f"PDF generation successful for {creator_name}.")
    except Exception as e:
        logger.error(f"Error generating PDF using WeasyPrint for {creator_name}: {e}")
        # Consider more specific error handling based on WeasyPrint exceptions if needed
        raise HTTPException(status_code=500, detail="Error generating PDF file.")

    # Prepare filename (Task 5 - Basic implementation here)
    # Sanitize creator name simply by replacing spaces
    safe_creator_name = creator_name.replace(" ", "_").lower()
    filename = f"{safe_creator_name}_complete_analysis.pdf"

    # Return the PDF as a response
    return Response(
        content=pdf_bytes,
        media_type="application/pdf",
        headers={
            # Suggest download with the constructed filename
            "Content-Disposition": f"attachment; filename=\"{filename}\""
        }
    ) 