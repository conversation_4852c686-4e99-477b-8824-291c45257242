from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import json
from anthropic import Anthropic  # Note: sync client like interview agent
import os
import logging
import uuid
from datetime import datetime

from ..auth import get_current_user, get_current_user_from_token, get_current_user_optional
from ...config import Config
from ...constants.collections import EDITOR_SESSIONS_COLLECTION
from ..services.agent_stream_handler import create_agent_stream
from ..services.session_service import (
    create_session,
    get_session,
    update_conversation_history,
    update_session_field,
    get_conversation_history,
    get_session_field
)
from ...utils.mongo_utils_async import get_user_preferences_and_strategy
from ...utils.agent_utils.process_step import format_user_preferences

logger = logging.getLogger(__name__)


router = APIRouter()
config = Config()

# Initialize Anthropic client (sync, like interview agent)
anthropic_client = Anthropic(api_key=config.ANTHROPIC_API_KEY)

class EditorChatRequest(BaseModel):
    sessionId: Optional[str] = None
    message: str
    currentContent: str
    originalContent: Optional[str] = None
    metadata: Optional[dict] = {}

class EditorChatResponse(BaseModel):
    sessionId: str
    messageId: str

@router.post("/chat", response_model=EditorChatResponse)
async def editor_chat(
    request: EditorChatRequest,
    current_user: dict = Depends(get_current_user)
):
    """Handle editor chat messages and return streaming response info"""
    try:
        # Generate message ID
        message_id = str(uuid.uuid4())
        
        # Create session if needed or validate existing one
        session_id = request.sessionId
        if not session_id:
            # No session ID provided, create a new one
            session_id = await create_session(
                collection_name=EDITOR_SESSIONS_COLLECTION,
                extra_fields={
                    "post_content": request.currentContent,
                    "original_content": request.originalContent or request.currentContent,
                    "clerk_user_id": current_user.get("sub")  # Get user ID from JWT token
                }
            )
            logger.info(f"Created new editor session: {session_id}")
        else:
            # Validate that the session exists in MongoDB
            session = await get_session(EDITOR_SESSIONS_COLLECTION, session_id)
            if not session:
                # Session not found, create a new one
                session_id = await create_session(
                    collection_name=EDITOR_SESSIONS_COLLECTION,
                    extra_fields={
                        "post_content": request.currentContent,
                        "original_content": request.originalContent or request.currentContent,
                        "clerk_user_id": current_user.get("sub")  # Get user ID from JWT token
                    }
                )
                logger.info(f"Session {request.sessionId} not found, created new session: {session_id}")
            else:
                logger.info(f"Using existing session: {session_id}")
                # If this is an existing session but originalContent is provided, update it
                if request.originalContent and not session.get('original_content'):
                    await update_session_field(
                        EDITOR_SESSIONS_COLLECTION,
                        session_id,
                        "original_content",
                        request.originalContent
                    )
                # Also update clerk_user_id if missing
                if not session.get('clerk_user_id'):
                    await update_session_field(
                        EDITOR_SESSIONS_COLLECTION,
                        session_id,
                        "clerk_user_id",
                        current_user.get("sub")
                    )
        
        # Always update the post content with what's in the editor
        await update_session_field(
            EDITOR_SESSIONS_COLLECTION,
            session_id,
            "post_content",
            request.currentContent
        )
        
        # Add user message to conversation history in MongoDB
        await update_conversation_history(
            EDITOR_SESSIONS_COLLECTION,
            session_id,
            request.message,
            is_user=True
        )
        
        return EditorChatResponse(
            sessionId=session_id,
            messageId=message_id
        )
        
    except Exception as e:
        logger.exception(f"Error in editor chat: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/stream/{session_id}/{message_id}")
async def editor_stream(
    session_id: str,
    message_id: str,
    token: Optional[str] = Query(None, description="JWT token for EventSource compatibility"),
    current_user: Optional[dict] = Depends(get_current_user_optional)
):
    """Stream editor responses using SSE with authentication"""
    
    # Handle authentication from either header or query param
    if not current_user:
        if token:
            # Validate token from query parameter
            try:
                current_user = await get_current_user_from_token(token)
            except Exception as e:
                async def error_generator():
                    yield f"event: error\ndata: {json.dumps({'error': 'Invalid authentication token'})}\n\n"
                return StreamingResponse(
                    error_generator(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                    }
                )
        else:
            # No authentication provided
            async def error_generator():
                yield f"event: error\ndata: {json.dumps({'error': 'Authentication required'})}\n\n"
            return StreamingResponse(
                error_generator(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*",
                }
            )
    
    # Validate session exists and belongs to authenticated user
    session = await get_session(EDITOR_SESSIONS_COLLECTION, session_id)
    if not session or session.get("clerk_user_id") != current_user.get("sub"):
        async def error_generator():
            yield f"event: error\ndata: {json.dumps({'error': 'Session not found'})}\n\n"
        return StreamingResponse(
            error_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
    
    # Load system prompt
    system_prompt_path = os.path.join(
        os.path.dirname(__file__), 
        "../../../prompts/editor_agent/editor_agent_system_prompt.txt"
    )
    with open(system_prompt_path, 'r') as f:
        system_prompt = f.read().strip().replace("SESSION_ID", session_id)
    
    # Add current date to system prompt
    system_prompt += f"\n\nCurrent date: {datetime.now().strftime('%Y-%m-%d')}"
    
    # Fetch and inject user preferences if clerk_user_id is available
    clerk_user_id = session.get("clerk_user_id")
    if clerk_user_id:
        preferences, strategy = await get_user_preferences_and_strategy(clerk_user_id)
        if preferences or strategy:
            # Format preferences using the existing utility
            formatted_preferences = format_user_preferences(preferences, content_strategy=strategy)
            if formatted_preferences:
                # Inject preferences at the end of system prompt
                system_prompt += f"\n\nUSER CONTENT PREFERENCES & STRATEGY:\n{formatted_preferences}"
                logger.info(f"Injected user preferences for editor session {session_id}")
        else:
            logger.info(f"No preferences or strategy found for user {clerk_user_id} in session {session_id}")
    else:
        logger.info(f"No clerk_user_id found in session {session_id}, skipping preferences")
    
    # Get messages from MongoDB
    messages = await get_conversation_history(EDITOR_SESSIONS_COLLECTION, session_id)
    
    # Get current content from MongoDB
    current_content = await get_session_field(
        EDITOR_SESSIONS_COLLECTION,
        session_id,
        "post_content",
        ""
    )
    logger.info(f"Editor session {session_id} initial content: {current_content[:100] if current_content else 'EMPTY'}")
    
    # Callbacks for the shared handler
    async def save_message(text: str, tool_uses: List[Dict[str, Any]]):
        # Save to MongoDB using the exact pattern from interview service
        await update_conversation_history(
            EDITOR_SESSIONS_COLLECTION,
            session_id,
            text if text else None,
            is_user=False,
            tool_uses=tool_uses if tool_uses else None
        )
    
    async def save_content(content: str):
        await update_session_field(
            EDITOR_SESSIONS_COLLECTION,
            session_id,
            "post_content",
            content
        )
    
    async def save_tool_results(tool_results: List[Dict[str, Any]]):
        # Save tool results as a user message
        await update_conversation_history(
            EDITOR_SESSIONS_COLLECTION,
            session_id,
            tool_results,
            is_user=True
        )
    
    # Use the shared streaming handler - exactly like interview agent
    return await create_agent_stream(
        client=anthropic_client,
        session_id=session_id,
        message_id=message_id,
        system_prompt=system_prompt,
        messages=messages,
        memory_path=f"memory://{session_id}/post.md",
        initial_content=current_content,
        model="claude-sonnet-4-20250514",
        max_tokens=4000,
        event_name_for_update="post_update",  # Editor uses post_update instead of brief_update
        on_save_message=save_message,
        on_save_content=save_content,
        on_save_tool_results=save_tool_results
    )

@router.delete("/session/{session_id}")
async def clear_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Clear editor session data"""
    # Note: We don't actually delete from MongoDB, just like interview service
    # Sessions remain for history/analytics purposes
    return {"status": "session cleared"}