"""API Router for Draft Library - stores all generated drafts with metadata."""

import logging
import uuid
import re
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pymongo.errors import PyMongoError

from src.api.draft_schemas import (
    Draft,
    DraftCreate,
    DraftUpdate,
    DraftListResponse,
    DraftFilter,
    DraftMetadata
)
from src.api.auth import get_current_user
from src.utils.mongo_utils_async import get_async_mongo_db

logger = logging.getLogger(__name__)

# Collection name for drafts
DRAFTS_COLLECTION = "drafts"

router = APIRouter(
    prefix="/api/v1/draft-library",
    tags=["Draft Library"]
)


@router.post(
    "/drafts",
    response_model=Draft,
    summary="Save a new draft",
    description="Saves a generated draft with metadata to the library."
)
async def create_draft(
    draft_data: DraftCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new draft in the library."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        
        # Generate unique draft ID
        draft_id = f"draft_{uuid.uuid4().hex[:12]}"
        
        # Get the current version number for this session
        existing_drafts_count = await db[DRAFTS_COLLECTION].count_documents({
            "session_id": draft_data.session_id,
            "user_id": clerk_user_id
        })
        
        # Create draft document
        draft_doc = {
            "draft_id": draft_id,
            "session_id": draft_data.session_id,
            "user_id": clerk_user_id,
            "content": draft_data.content,
            "brief": draft_data.brief,
            "post_length": draft_data.post_length,
            "version": existing_drafts_count + 1,
            "metadata": draft_data.metadata.model_dump() if draft_data.metadata else {},
            "created_at": datetime.utcnow()
        }
        
        # Calculate word and character counts if not provided
        if "word_count" not in draft_doc["metadata"]:
            draft_doc["metadata"]["word_count"] = len(draft_data.content.split())
        if "character_count" not in draft_doc["metadata"]:
            draft_doc["metadata"]["character_count"] = len(draft_data.content)
        
        # Insert draft
        await db[DRAFTS_COLLECTION].insert_one(draft_doc)
        
        # Create index for efficient queries (if not exists)
        await db[DRAFTS_COLLECTION].create_index([
            ("user_id", 1),
            ("created_at", -1)
        ])
        await db[DRAFTS_COLLECTION].create_index([
            ("session_id", 1),
            ("version", 1)
        ])
        
        return Draft(**draft_doc)
        
    except PyMongoError as e:
        logger.error(f"Database error creating draft: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.get(
    "/drafts",
    response_model=DraftListResponse,
    summary="List user's drafts",
    description="Lists all drafts for the authenticated user with filtering options."
)
async def list_drafts(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(12, ge=1, le=100, description="Items per page", alias="limit"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    linguistic_pattern: Optional[str] = Query(None, description="Filter by linguistic pattern name"),
    search: Optional[str] = Query(None, max_length=100, description="Search in content or brief"),
    date_from: Optional[datetime] = Query(None, description="Filter by date range start"),
    date_to: Optional[datetime] = Query(None, description="Filter by date range end"),
    current_user: dict = Depends(get_current_user)
):
    """List drafts with filtering and pagination."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        
        # Build filter query
        filter_query = {"user_id": clerk_user_id}
        
        if session_id:
            filter_query["session_id"] = session_id
        
        if linguistic_pattern:
            filter_query["metadata.linguistic_pattern_name"] = {
                "$regex": linguistic_pattern,
                "$options": "i"
            }
        
        if search:
            # Escape special regex characters to prevent ReDoS attacks
            safe_search = re.escape(search)
            search_regex = {"$regex": safe_search, "$options": "i"}
            filter_query["$or"] = [
                {"content": search_regex},
                {"brief": search_regex}
            ]
        
        if date_from:
            filter_query["created_at"] = {"$gte": date_from}
        
        if date_to:
            if "created_at" in filter_query:
                filter_query["created_at"]["$lte"] = date_to
            else:
                filter_query["created_at"] = {"$lte": date_to}
        
        # Get total count
        total = await db[DRAFTS_COLLECTION].count_documents(filter_query)
        
        # Get paginated results
        skip = (page - 1) * limit
        cursor = db[DRAFTS_COLLECTION].find(filter_query).sort(
            "created_at", -1
        ).skip(skip).limit(limit)
        
        drafts = []
        async for doc in cursor:
            drafts.append(Draft(**doc))
        
        return DraftListResponse(
            drafts=drafts,
            total=total,
            page=page,
            page_size=limit
        )
        
    except PyMongoError as e:
        logger.error(f"Database error listing drafts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.get(
    "/drafts/{draft_id}",
    response_model=Draft,
    summary="Get a specific draft",
    description="Retrieves a specific draft by ID."
)
async def get_draft(
    draft_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get a specific draft by ID."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        
        draft_doc = await db[DRAFTS_COLLECTION].find_one({
            "draft_id": draft_id,
            "user_id": clerk_user_id
        })
        
        if not draft_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Draft not found"
            )
        
        return Draft(**draft_doc)
        
    except PyMongoError as e:
        logger.error(f"Database error retrieving draft: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.delete(
    "/drafts/{draft_id}",
    summary="Delete a draft",
    description="Deletes a specific draft from the library."
)
async def delete_draft(
    draft_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete a draft."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        
        result = await db[DRAFTS_COLLECTION].delete_one({
            "draft_id": draft_id,
            "user_id": clerk_user_id
        })
        
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Draft not found"
            )
        
        return {"success": True, "message": "Draft deleted successfully"}
        
    except PyMongoError as e:
        logger.error(f"Database error deleting draft: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.get(
    "/sessions/{session_id}/drafts",
    response_model=List[Draft],
    summary="Get all drafts for a session",
    description="Retrieves all drafts generated for a specific session."
)
async def get_session_drafts(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get all drafts for a specific session."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        
        cursor = db[DRAFTS_COLLECTION].find({
            "session_id": session_id,
            "user_id": clerk_user_id
        }).sort("version", 1)
        
        drafts = []
        async for doc in cursor:
            drafts.append(Draft(**doc))
        
        return drafts
        
    except PyMongoError as e:
        logger.error(f"Database error retrieving session drafts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )