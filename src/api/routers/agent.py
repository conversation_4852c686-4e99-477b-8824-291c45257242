"""API routes for agent-related operations."""

import logging
from fastapi import APIRouter, HTTPException, Depends, Path as FastAPIPath, status

# Assuming schemas and services will be needed
from src.api.schemas import AgentRequest, AgentJobResponse, AgentJobStatusResponse, AgentResponse
# from src.api.agent_service import AgentService # Assuming an AgentService exists or will be created
from src.api.agent_job_manager import AgentJobManager # MongoDB-based manager
from src.api.auth import get_current_user
import os
from src.utils.datetime_utils import get_current_datetime
from src.utils.auth_utils import extract_clerk_user_id
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/agent", tags=["Agent"])

# Always use MongoDB job manager
USE_MONGODB_JOBS = True
logger.info(f"Agent job manager mode: MongoDB")

# Placeholder for agent endpoints
# --- Agent endpoints ---

# Agent content generation endpoint - creates a job
@router.post("/generate", response_model=AgentJobResponse)
async def generate_content(request: AgentRequest, current_user: dict = Depends(get_current_user)):
    """Create a job for agent content generation."""
    try:
        # Get user ID from JWT token
        clerk_user_id = extract_clerk_user_id(current_user)

        # Log the request details
        logger.info(f"[Agent API] Received request with input_text length: {len(request.input_text) if request.input_text else 'None'}")
        logger.info(f"[Agent API] Draft ID: {request.draft_id}, Post length: {request.post_length}")

        # Create a new job with user context
        job_id = await AgentJobManager.create_job(
            input_text=request.input_text,
            clerk_user_id=clerk_user_id,
            post_length=request.post_length,
            draft_id=request.draft_id
        )

        # Return job information
        return {
            "job_id": job_id,
            "status": "pending",
            "created_at": get_current_datetime(),
            "polling_url": f"/api/agent/status/{job_id}" # Note: Keep full path for polling URL? Or adjust client?
        }

    except Exception as e:
        logger.error(f"Error creating agent job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Agent job status endpoint
@router.get("/status/{job_id}", response_model=AgentJobStatusResponse)
async def get_agent_job_status(
    job_id: str = FastAPIPath(..., description="The job ID to check"),
    current_user: dict = Depends(get_current_user)
):
    """Get the status of an agent job."""
    try:
        # Get job status with user ownership check
        job_status = await AgentJobManager.get_job_status(job_id, current_user.get("sub"))

        if not job_status:
            raise HTTPException(status_code=404, detail=f"Job not found: {job_id}")

        # Convert string dates to datetime objects
        created_at = datetime.fromisoformat(job_status["created_at"])
        updated_at = datetime.fromisoformat(job_status["updated_at"]) if "updated_at" in job_status else None
        completed_at = datetime.fromisoformat(job_status["completed_at"]) if "completed_at" in job_status else None

        # Prepare response
        response = {
            "job_id": job_status["job_id"],
            "status": job_status["status"],
            "created_at": created_at,
            "updated_at": updated_at,
            "completed_at": completed_at,
            "duration": job_status.get("duration"),
            "result": job_status.get("result"),
            "error": job_status.get("error"),
            "current_generation_step": job_status.get("current_generation_step")
        }

        # Log the response for debugging
        if job_status["status"] == "completed":
            logger.info(f"Returning completed job result with keys: {job_status.get('result', {}).keys() if isinstance(job_status.get('result'), dict) else 'not a dict'}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent job status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Agent job cancellation endpoint
@router.delete("/cancel/{job_id}")
async def cancel_agent_job(
    job_id: str = FastAPIPath(..., description="The job ID to cancel"),
    current_user: dict = Depends(get_current_user)
):
    """Cancel an agent job."""
    try:
        # Cancel the job with user ownership check
        success = await AgentJobManager.cancel_job(job_id, current_user.get("sub"))
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Job not found or could not be cancelled: {job_id}")
        
        return {"message": f"Job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling agent job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Agent result endpoint - for backward compatibility
@router.post("/result", response_model=AgentResponse) # Consider changing to GET?
async def get_agent_result(
    job_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get the result of a completed agent job."""
    try:
        # Get job status with user ownership check
        job_status = await AgentJobManager.get_job_status(job_id, current_user.get("sub"))

        if not job_status:
            raise HTTPException(status_code=404, detail=f"Job not found: {job_id}")

        if job_status["status"] != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed (status: {job_status['status']})"
            )

        # Return the result
        result = job_status.get("result", {})
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent result: {e}")
        raise HTTPException(status_code=500, detail="Internal server error") 