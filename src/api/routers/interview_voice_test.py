"""Temporary test endpoint for voice SSE without auth."""

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
import asyncio
import json
import logging

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/interview", tags=["Interview Test"])

@router.get("/voice/test-stream/{session_id}")
async def test_stream_voice_transcripts(session_id: str):
    """Test SSE endpoint without authentication."""
    
    async def event_generator():
        try:
            logger.info(f"Test SSE stream started for session {session_id}")
            
            # Send a test message every 2 seconds
            for i in range(5):
                await asyncio.sleep(2)
                yield f"event: test_message\ndata: {json.dumps({'count': i, 'message': f'Test message {i}'})}\n\n"
            
            yield f"event: stream_complete\ndata: {json.dumps({'status': 'complete'})}\n\n"
            
        except Exception as e:
            logger.error(f"Error in test stream: {str(e)}")
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        }
    )