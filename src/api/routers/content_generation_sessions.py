"""API Router for managing content generation sessions."""

import logging
import uuid
import re
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from pymongo.errors import PyMongoError

from src.api.content_generation_session_schemas import (
    ContentGenerationSession,
    ContentGenerationSessionCreate,
    ContentGenerationSessionUpdate,
    ContentGenerationSessionResponse,
    ContentGenerationStatus
)
from src.api.auth import get_current_user
from src.api.services.session_service import (
    create_session,
    get_session,
    update_session_field,
    get_session_field
)
from src.utils.mongo_utils_async import (
    get_async_mongo_db,
    CONTENT_GENERATION_SESSIONS_COLLECTION
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/content-sessions",
    tags=["Content Generation Sessions"]
)

# Validation pattern for draft_id (draft_ prefix + 12 hex characters)
DRAFT_ID_PATTERN = re.compile(r'^draft_[a-f0-9]{12}$')

def validate_draft_id(draft_id: str) -> bool:
    """Validate that draft_id follows the expected format to prevent NoSQL injection."""
    return bool(DRAFT_ID_PATTERN.match(draft_id))

@router.post(
    "/",
    response_model=ContentGenerationSessionResponse,
    summary="Create Content Generation Session",
    description="Creates a new content generation session for the authenticated user.",
    status_code=status.HTTP_201_CREATED
)
async def create_content_session(
    session_data: ContentGenerationSessionCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new content generation session."""
    logger.info(f"POST /api/v1/content-sessions/ endpoint hit with data: {session_data}")
    logger.info(f"Current user: {current_user}")
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        logger.error("Clerk User ID not found in token payload.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        # Create session using existing session service
        session_id = await create_session(
            CONTENT_GENERATION_SESSIONS_COLLECTION,
            extra_fields={
                "user_id": clerk_user_id,
                "brief": session_data.brief,
                "post_length": session_data.post_length,
                "generated_posts": {},
                "status": ContentGenerationStatus.DRAFT.value,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "include_strategy": session_data.include_strategy
            }
        )

        # Retrieve the created session
        session_doc = await get_session(CONTENT_GENERATION_SESSIONS_COLLECTION, session_id)
        if not session_doc:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve created session"
            )

        # Convert to response model
        session = ContentGenerationSession(**session_doc)
        
        logger.info(f"Created content generation session {session_id} for user {clerk_user_id}")
        
        return ContentGenerationSessionResponse(
            success=True,
            session=session,
            message="Session created successfully"
        )

    except PyMongoError as e:
        logger.error(f"Database error creating session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )
    except Exception as e:
        logger.error(f"Unexpected error creating session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@router.get(
    "/{session_id}",
    response_model=ContentGenerationSessionResponse,
    summary="Get Content Generation Session",
    description="Retrieves a content generation session by ID for the authenticated user."
)
async def get_content_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get a content generation session by ID."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        # Get session from database
        db = await get_async_mongo_db()
        
        # Debug: log what we're looking for
        logger.info(f"Looking for session {session_id} with user_id {clerk_user_id}")
        
        # Check if session exists without user filter first
        session_exists = await db[CONTENT_GENERATION_SESSIONS_COLLECTION].find_one({
            "session_id": session_id
        })
        logger.info(f"Session exists (no user filter): {session_exists is not None}")
        if session_exists:
            logger.info(f"Session user_id: {session_exists.get('user_id')}")
        
        session_doc = await db[CONTENT_GENERATION_SESSIONS_COLLECTION].find_one({
            "session_id": session_id,
            "user_id": clerk_user_id  # Ensure user can only access their own sessions
        })

        if not session_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # Convert to response model
        session = ContentGenerationSession(**session_doc)
        
        return ContentGenerationSessionResponse(
            success=True,
            session=session,
            message="Session retrieved successfully"
        )

    except PyMongoError as e:
        logger.error(f"Database error retrieving session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.put(
    "/{session_id}",
    response_model=ContentGenerationSessionResponse,
    summary="Update Content Generation Session",
    description="Updates a content generation session for the authenticated user."
)
async def update_content_session(
    session_id: str,
    update_data: ContentGenerationSessionUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update a content generation session."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        # Verify session exists and belongs to user
        db = await get_async_mongo_db()
        existing_session = await db[CONTENT_GENERATION_SESSIONS_COLLECTION].find_one({
            "session_id": session_id,
            "user_id": clerk_user_id
        })

        if not existing_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # Update fields that are provided
        update_fields = {}
        if update_data.brief is not None:
            update_fields["brief"] = update_data.brief
        if update_data.post_length is not None:
            update_fields["post_length"] = update_data.post_length
        if update_data.status is not None:
            update_fields["status"] = update_data.status.value

        # Always update timestamp
        update_fields["updated_at"] = datetime.utcnow()

        # Handle generated_posts separately to merge instead of replace
        if update_data.generated_posts is not None:
            # Merge new posts with existing ones (using draft_id, not creator)
            for draft_id, post in update_data.generated_posts.items():
                # Validate draft_id to prevent NoSQL injection
                if not validate_draft_id(draft_id):
                    logger.warning(f"Invalid draft_id format: {draft_id}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid draft_id format: {draft_id}"
                    )
                await db[CONTENT_GENERATION_SESSIONS_COLLECTION].update_one(
                    {"session_id": session_id, "user_id": clerk_user_id},
                    {"$set": {f"generated_posts.{draft_id}": post}}
                )
        
        # Handle explanations separately to merge instead of replace
        if update_data.explanations is not None:
            # Merge new explanations with existing ones
            for draft_id, explanation in update_data.explanations.items():
                # Validate draft_id to prevent NoSQL injection (same validation as above)
                if not validate_draft_id(draft_id):
                    logger.warning(f"Invalid draft_id format: {draft_id}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid draft_id format: {draft_id}"
                    )
                await db[CONTENT_GENERATION_SESSIONS_COLLECTION].update_one(
                    {"session_id": session_id, "user_id": clerk_user_id},
                    {"$set": {f"explanations.{draft_id}": explanation}}
                )
        
        # Update other fields
        if update_fields:
            await db[CONTENT_GENERATION_SESSIONS_COLLECTION].update_one(
                {"session_id": session_id, "user_id": clerk_user_id},
                {"$set": update_fields}
            )

        # Retrieve updated session
        updated_session = await db[CONTENT_GENERATION_SESSIONS_COLLECTION].find_one({
            "session_id": session_id,
            "user_id": clerk_user_id
        })

        session = ContentGenerationSession(**updated_session)
        
        logger.info(f"Updated content generation session {session_id} for user {clerk_user_id}")
        
        return ContentGenerationSessionResponse(
            success=True,
            session=session,
            message="Session updated successfully"
        )

    except PyMongoError as e:
        logger.error(f"Database error updating session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.get(
    "/",
    response_model=List[ContentGenerationSession],
    summary="List User Sessions",
    description="Lists all content generation sessions for the authenticated user."
)
async def list_user_sessions(
    limit: int = Query(default=10, le=50, description="Maximum number of sessions to return"),
    skip: int = Query(default=0, ge=0, description="Number of sessions to skip"),
    current_user: dict = Depends(get_current_user)
):
    """List all content generation sessions for the authenticated user."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        cursor = db[CONTENT_GENERATION_SESSIONS_COLLECTION].find({
            "user_id": clerk_user_id
        }).sort("updated_at", -1).skip(skip).limit(limit)

        sessions = []
        async for session_doc in cursor:
            sessions.append(ContentGenerationSession(**session_doc))

        return sessions

    except PyMongoError as e:
        logger.error(f"Database error listing sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )


@router.delete(
    "/{session_id}",
    summary="Delete Content Generation Session",
    description="Deletes a content generation session for the authenticated user."
)
async def delete_content_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete a content generation session."""
    clerk_user_id = current_user.get("sub")
    if not clerk_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token: User ID missing."
        )

    try:
        db = await get_async_mongo_db()
        result = await db[CONTENT_GENERATION_SESSIONS_COLLECTION].delete_one({
            "session_id": session_id,
            "user_id": clerk_user_id
        })

        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        logger.info(f"Deleted content generation session {session_id} for user {clerk_user_id}")
        
        return {"success": True, "message": "Session deleted successfully"}

    except PyMongoError as e:
        logger.error(f"Database error deleting session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )