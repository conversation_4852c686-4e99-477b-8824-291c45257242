"""Pydantic schemas for LinkedInsight API."""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from enum import Enum
import datetime

# Job status enum
class JobStatus(str, Enum):
    """Possible job statuses."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

# Citation model
class Citation(BaseModel):
    """Citation model for examples of content."""
    content: str = Field(..., description="Full content of the post")
    engagement: str = Field(..., description="Engagement metrics for this post (e.g., '12,453 (Likes: 11,872, Comments: 581)')")


# Hook model
class Hook(BaseModel):
    """Hook analysis data model."""
    type: str = Field(..., description="Type/category of hook")
    description: str = Field(..., description="Description of this hook type")
    example: str = Field(..., description="Short example of the hook")
    citation: Citation

# Body structure model
class BodyStructure(BaseModel):
    """Body structure analysis data model."""
    type: str = Field(..., description="Type/category of body structure")
    description: str = Field(..., description="Description of this body structure type")
    example: str = Field(..., description="Short example of the body structure")
    citation: Citation

# Ending model
class Ending(BaseModel):
    """Ending analysis data model."""
    type: str = Field(..., description="Type/category of ending")
    description: str = Field(..., description="Description of this ending type")
    example: str = Field(..., description="Short example of the ending")
    citation: Citation

# Linguistic pattern model
class LinguisticPattern(BaseModel):
    """Linguistic pattern analysis data model."""
    type: str = Field(..., description="Type/category of linguistic pattern")
    description: str = Field(..., description="Description of this linguistic pattern")
    example: str = Field(..., description="Short example of the linguistic pattern")
    citation: Citation

# Job status response
class JobStatusResponse(BaseModel):
    """Response model for a job status request."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    updated_at: Optional[datetime.datetime] = Field(None, description="When the job was last updated")
    completed_at: Optional[datetime.datetime] = Field(None, description="When the job completed")
    duration: Optional[float] = Field(None, description="Duration in seconds if completed")
    result_path: Optional[str] = Field(None, description="Path to the result file if completed")
    error_message: Optional[str] = Field(None, description="Error message if failed")

# Agent request model
class AgentRequest(BaseModel):
    """Request model for the content generation agent."""
    input_text: str = Field(..., description="User's input (brief, idea with citations, or draft post)")
    post_length: Optional[str] = Field(None, description="Desired post length (short, medium, long)")
    conversation_id: Optional[str] = Field(None, description="ID of an existing conversation")
    conversation_history: Optional[List[Dict[str, str]]] = Field(None, description="Previous messages in the conversation")
    draft_id: Optional[str] = Field(None, description="Draft identifier (draft_a, draft_b) for pattern variety")

# Agent job response model
class AgentJobResponse(BaseModel):
    """Response model for an agent job request."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    polling_url: str = Field(..., description="URL to poll for job status")

# Agent job status response model
class AgentJobStatusResponse(BaseModel):
    """Response model for an agent job status request."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    updated_at: Optional[datetime.datetime] = Field(None, description="When the job was last updated")
    completed_at: Optional[datetime.datetime] = Field(None, description="When the job completed")
    duration: Optional[float] = Field(None, description="Duration in seconds if completed")
    result: Optional[Dict[str, Any]] = Field(None, description="Result data if completed")
    error: Optional[str] = Field(None, description="Error message if failed")
    current_generation_step: Optional[str] = Field(None, description="Current step in the content generation process")

# Agent response model
class AgentResponse(BaseModel):
    """Response model for the content generation agent."""
    conversation_id: str = Field(..., description="Unique identifier for the conversation")
    generated_post: Optional[str] = Field(None, description="Generated LinkedIn post")
    explanation: Optional[str] = Field(None, description="Explanation of techniques used")
    questions: Optional[List[str]] = Field(None, description="Follow-up questions for the user")
    error: Optional[str] = Field(None, description="Error message if something went wrong")

# Health check response
class HealthResponse(BaseModel):
    """Health check response."""
    status: str = Field(..., description="API status")
    version: str = Field(..., description="API version")
    endpoints: List[str] = Field(..., description="Available endpoints")

# Content Strategy Model
class ContentStrategy(BaseModel):
    """Pydantic model for user content strategy data."""
    clerk_user_id: Optional[str] = Field(None, description="Clerk User ID for associating the strategy with the user")
    industry: str = Field(..., description="User's industry")
    offering: str = Field(..., description="User's product/service offering")
    target_audience: str = Field(..., description="User's ideal customer profile (ICP)")
    differentiators: str = Field(..., description="User's key differentiators")
    content_goal: str = Field(..., description="User's primary content goal")

    class Config:
        """Pydantic model configuration."""
        populate_by_name = True

        # Convert snake_case to camelCase for all fields
        alias_generator = lambda field_name: ''.join(
            word.capitalize() if i > 0 else word
            for i, word in enumerate(field_name.split('_'))
        )

        json_schema_extra = {
            "example": {
                "clerkUserId": "user_2NxAkjhKM9Pz8bFabcdef",
                "industry": "SaaS",
                "offering": "AI-powered content creation tool for B2B marketers",
                "targetAudience": "CMOs at mid-sized technology companies",
                "differentiators": "Advanced AI contextual understanding, 50+ industry-specific templates",
                "contentGoal": "Generate qualified leads and build thought leadership"
            }
        }

# Content Preferences Model
class ContentPreferences(BaseModel):
    """Pydantic model for user content preferences data."""
    clerk_user_id: Optional[str] = Field(None, description="Clerk User ID for associating the preferences with the user")
    transcript_name: str = Field(..., description="User's name as it appears in transcripts")
    opinion_style: str = Field(..., description="Opinion style preference (strongly_opinionated, balanced, nuanced)")
    emoji_usage: str = Field(..., description="Emoji usage preference (frequent, occasional, none)")
    voice_distinctiveness: str = Field(..., description="Voice distinctiveness preference (corporate_standard, professionally_personal, authentically_distinct, radically_individual)")
    controversy_approach: str = Field(..., description="Controversy approach preference (play_it_safe, thoughtful_challenger, willing_to_debate, industry_provocateur)")
    humor_style: str = Field(..., description="Humor style preference (serious_only, dry_wit, playful, irreverent)")
    brag_level: str = Field(..., description="Brag level preference (maximum_brag, healthy_confidence, modest_success, credit_the_universe)")
    readability_style: str = Field(..., description="Readability style preference (dense, balanced, airy)")

    class Config:
        """Pydantic model configuration."""
        populate_by_name = True

        # Convert snake_case to camelCase for all fields
        alias_generator = lambda field_name: ''.join(
            word.capitalize() if i > 0 else word
            for i, word in enumerate(field_name.split('_'))
        )

        json_schema_extra = {
            "example": {
                "clerkUserId": "user_2NxAkjhKM9Pz8bFabcdef",
                "transcriptName": "John Smith",
                "opinionStyle": "balanced",
                "emojiUsage": "occasional",
                "voiceDistinctiveness": "professionally_personal",
                "controversyApproach": "thoughtful_challenger",
                "humorStyle": "dry_wit",
                "bragLevel": "healthy_confidence",
                "readabilityStyle": "balanced"
            }
        }

# Interactive Interviewing Agent Models
class InterviewTurnRequest(BaseModel):
    """Request model for the interactive interviewing agent conversation turn."""
    session_id: Optional[str] = Field(None, description="Optional session ID. If not provided, a new session will be created.")
    user_message: str = Field(..., description="The user's message for this turn of the conversation")
    
    class Config:
        """Pydantic model configuration."""
        populate_by_name = True
        
        # Convert snake_case to camelCase for all fields
        alias_generator = lambda field_name: ''.join(
            word.capitalize() if i > 0 else word
            for i, word in enumerate(field_name.split('_'))
        )
        
        json_schema_extra = {
            "example": {
                "sessionId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "userMessage": "I want to create a post about AI-powered productivity tools for remote teams."
            }
        }

class InterviewTurnResponse(BaseModel):
    """Response model for the interactive interviewing agent conversation turn."""
    session_id: str = Field(..., description="The session ID for this conversation")
    agent_message: str = Field(..., description="The agent's response message")
    updated_brief_content: str = Field(..., description="The current state of the brief in Markdown format")
    completed: bool = Field(False, description="Whether the interview process is complete")
    error: Optional[str] = Field(None, description="Error message if something went wrong")
    message_id: Optional[str] = Field(None, description="Message ID for streaming responses")
    
    class Config:
        """Pydantic model configuration."""
        populate_by_name = True
        
        # Convert snake_case to camelCase for all fields
        alias_generator = lambda field_name: ''.join(
            word.capitalize() if i > 0 else word
            for i, word in enumerate(field_name.split('_'))
        )
        
        json_schema_extra = {
            "example": {
                "sessionId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "agentMessage": "Great topic! Could you tell me more about the specific productivity challenges your target audience faces?",
                "updatedBriefContent": "# LinkedIn Post Brief\n\n## Topic\nAI-powered productivity tools for remote teams\n\n## Target Audience\n(To be determined)",
                "completed": False,
                "error": None
            }
        }