"""Pydantic schemas for Content Generation Sessions."""

from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from typing import Dict, List, Optional, Any
from enum import Enum
import datetime


class ContentGenerationStatus(str, Enum):
    """Content generation session status."""
    DRAFT = "draft"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


class ContentGenerationSessionCreate(BaseModel):
    """Schema for creating a new content generation session."""
    brief: str = Field(..., description="Content brief provided by user")
    post_length: str = Field("medium", description="Desired post length (short, medium, long)")
    include_strategy: bool = Field(True, description="Whether to include user's content strategy")

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "brief": "Create content about AI productivity tools for marketing professionals",
                "postLength": "medium",
                "includeStrategy": True
            }
        }
    )


class ContentGenerationSessionUpdate(BaseModel):
    """Schema for updating content generation session."""
    brief: Optional[str] = Field(None, description="Updated content brief")
    post_length: Optional[str] = Field(None, description="Updated post length")
    generated_posts: Optional[Dict[str, str]] = Field(None, description="Generated posts by draft ID (draft_a, draft_b, etc.)")
    explanations: Optional[Dict[str, str]] = Field(None, description="Explanations by draft ID (draft_a, draft_b, etc.)")
    status: Optional[ContentGenerationStatus] = Field(None, description="Session status")

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "generatedPosts": {
                    "draft_a": "AI is transforming how we work...",
                    "draft_b": "The future of marketing automation..."
                },
                "explanations": {
                    "draft_a": "This post uses the personal experience hook pattern...",
                    "draft_b": "This post leverages the thought leadership pattern..."
                },
                "status": "completed"
            }
        }
    )


class ContentGenerationSession(BaseModel):
    """Complete content generation session model."""
    session_id: str = Field(..., description="Unique session identifier")
    user_id: str = Field(..., description="Clerk user ID")
    brief: str = Field(..., description="Content brief")
    post_length: str = Field("medium", description="Post length preference")
    generated_posts: Dict[str, str] = Field(default_factory=dict, description="Generated posts by draft ID (draft_a, draft_b, etc.)")
    explanations: Dict[str, str] = Field(default_factory=dict, description="Explanations by draft ID (draft_a, draft_b, etc.)")
    status: ContentGenerationStatus = Field(ContentGenerationStatus.DRAFT, description="Current session status")
    content_strategy: Optional[Dict[str, Any]] = Field(None, description="User's content strategy context")
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow, description="Session creation timestamp")
    updated_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow, description="Last update timestamp")

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "sessionId": "session_abc123",
                "userId": "user_2NxAkjhKM9Pz8bFabcdef",
                "brief": "Create content about AI productivity tools for marketing professionals",
                "creators": ["creator1", "creator2"],
                "postLength": "medium",
                "generatedPosts": {
                    "creator1": "AI is transforming how we work...",
                    "creator2": "The future of marketing automation..."
                },
                "status": "completed"
            }
        }
    )


class ContentGenerationSessionResponse(BaseModel):
    """Response schema for content generation session operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    session: Optional[ContentGenerationSession] = Field(None, description="Session data")
    message: Optional[str] = Field(None, description="Success or error message")

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "success": True,
                "session": {
                    "sessionId": "session_abc123",
                    "userId": "user_2NxAkjhKM9Pz8bFabcdef",
                    "brief": "Create content about AI productivity tools",
                    "creators": ["creator1", "creator2"],
                    "status": "completed"
                },
                "message": "Session created successfully"
            }
        }
    )