"""Shared streaming handler for agents with text editor tool support.

This is the ACTUAL working pattern from the interview agent, extracted for reuse.
"""

import json
import logging
from typing import AsyncGenerator, List, Dict, Any, Optional
from fastapi.responses import StreamingResponse
import anthropic

from ...utils.anthropic_text_editor_handler import AnthropicTextEditorHandler

logger = logging.getLogger(__name__)


async def create_agent_stream(
    client: anthropic.Anthropic,  # Note: sync client, not async
    session_id: str,
    message_id: str,
    system_prompt: str,
    messages: List[Dict[str, Any]],
    memory_path: str,
    initial_content: str,
    model: str = "claude-sonnet-4-20250514",
    max_tokens: int = 4000,
    event_name_for_update: str = "brief_update",  # or "post_update" for editor
    on_save_message: Optional[callable] = None,
    on_save_content: Optional[callable] = None,
    on_save_tool_results: Optional[callable] = None
) -> StreamingResponse:
    """Create a streaming response for agent interactions with text editor tool.
    
    This follows the EXACT pattern from interview_stream.py that we know works.
    """
    
    async def event_generator() -> AsyncGenerator[str, None]:
        try:
            # Initialize text editor exactly like interview agent
            text_editor_handler = AnthropicTextEditorHandler(allow_real_filesystem_access=False)
            text_editor_handler.dispatch_command("create", {
                "path": memory_path,
                "file_text": initial_content
            })
            
            # Tool definition - same as interview agent
            tools = [{
                "type": "text_editor_20250124",
                "name": "str_replace_editor"
            }]
            
            # Main conversation loop - exactly like interview agent
            full_response_text = ""
            content_updated = False
            final_tool_uses = []  # Keep track of final tool uses for saving
            final_tool_results = []  # Keep track of final tool results for saving
            
            while True:
                collected_tool_uses = []
                current_tool_use = None
                
                # Stream the response - using sync client in stream context
                with client.messages.stream(
                    model=model,
                    system=system_prompt,
                    messages=messages,
                    tools=tools,
                    max_tokens=max_tokens,
                ) as stream:
                    for event in stream:
                        # Handle text content
                        if event.type == "content_block_delta" and hasattr(event.delta, 'type') and event.delta.type == "text_delta":
                            text = event.delta.text
                            full_response_text += text
                            yield f"event: content_delta\ndata: {json.dumps({'text': text})}\n\n"
                        
                        # Handle tool use start
                        elif event.type == "content_block_start" and event.content_block.type == "tool_use":
                            current_tool_use = {
                                "id": event.content_block.id,
                                "name": event.content_block.name,
                                "input": {},
                                "input_json": ""
                            }
                            yield f"event: tool_call_start\ndata: {json.dumps({'tool_id': event.content_block.id, 'tool_name': event.content_block.name})}\n\n"
                        
                        # Accumulate tool input
                        elif event.type == "content_block_delta" and event.delta.type == "input_json_delta" and current_tool_use:
                            current_tool_use["input_json"] += event.delta.partial_json
                        
                        # Finalize tool use
                        elif event.type == "content_block_stop" and current_tool_use:
                            try:
                                current_tool_use["input"] = json.loads(current_tool_use["input_json"])
                            except json.JSONDecodeError:
                                logger.error(f"Failed to parse tool input: {current_tool_use['input_json']}")
                            collected_tool_uses.append({k: v for k, v in current_tool_use.items() if k != "input_json"})
                            current_tool_use = None
                
                # Decision point: what to do with the response
                if not collected_tool_uses:
                    # No tools used, we're done
                    break
                
                # Process tool uses exactly like interview agent
                tool_results = []
                for tool_use in collected_tool_uses:
                    if tool_use["name"] == "str_replace_editor":
                        tool_input = tool_use["input"]
                        command_name = tool_input.get("command")
                        command_params = {k: v for k, v in tool_input.items() if k != "command"}
                        command_params["path"] = memory_path
                        
                        result_text, is_error = text_editor_handler.dispatch_command(command_name, command_params)
                        
                        # For view commands, we need to show the content to the AI
                        # but we can abbreviate it for the user interface
                        if command_name == "view" and not is_error:
                            # Keep the full content for the AI to see
                            pass  # result_text remains unchanged
                        elif not is_error and command_name != "view":
                            content_updated = True
                        
                        tool_results.append({
                            "type": "tool_result",
                            "tool_use_id": tool_use["id"],
                            "content": result_text,
                            "is_error": is_error
                        })
                        
                        # Emit tool result
                        # For view commands, show abbreviated message to user but keep full content for AI
                        if command_name == "view" and not is_error:
                            display_result = "Document viewed successfully"
                        elif not is_error and command_name != "view":
                            display_result = "Successfully updated"
                        else:
                            display_result = result_text
                        
                        yield f"event: tool_call_result\ndata: {json.dumps({'tool_id': tool_use['id'], 'tool_name': tool_use['name'], 'result': display_result, 'is_error': is_error})}\n\n"
                
                # If content was updated, emit the update
                if content_updated:
                    updated_content, _ = text_editor_handler.dispatch_command("view", {"path": memory_path})
                    yield f"event: {event_name_for_update}\ndata: {json.dumps({'content': updated_content})}\n\n"
                    
                    if on_save_content:
                        await on_save_content(updated_content)
                
                # Keep track of what we need to save
                if collected_tool_uses:
                    final_tool_uses = collected_tool_uses
                    final_tool_results = tool_results
                
                # If we have text and tools, we're done
                if full_response_text:
                    break
                
                # Continue conversation with tool results
                messages.append({
                    "role": "assistant",
                    "content": [{"type": "tool_use", "id": tu["id"], "name": tu["name"], "input": tu["input"]} 
                               for tu in collected_tool_uses]
                })
                messages.append({
                    "role": "user",
                    "content": tool_results
                })
            
            # Save assistant message if callback provided
            if on_save_message and (full_response_text or final_tool_uses):
                await on_save_message(full_response_text, final_tool_uses)
                
                # If there were tool uses, save the corresponding tool results
                if on_save_tool_results and final_tool_uses and final_tool_results:
                    await on_save_tool_results(final_tool_results)
            
            # Send completion
            yield f"event: stream_complete\ndata: {json.dumps({'status': 'complete', 'message_id': message_id})}\n\n"
            
        except Exception as e:
            logger.error(f"Error in agent streaming: {str(e)}", exc_info=True)
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )