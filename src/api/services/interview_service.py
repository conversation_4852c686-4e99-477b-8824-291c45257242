"""Service for managing interview sessions in MongoDB."""

import logging
from typing import Dict, List, Optional, Any

from src.api.models.interview_session import INTERVIEW_SESSIONS_COLLECTION
from src.api.services.session_service import (
    create_session,
    get_session,
    update_conversation_history as update_conversation_history_generic,
    update_session_field,
    get_conversation_history as get_conversation_history_generic,
    get_session_field
)

logger = logging.getLogger(__name__)

async def create_interview_session(user_id: Optional[str] = None) -> str:
    """
    Create a new interview session in MongoDB.
    
    Args:
        user_id: Optional user ID (clerk_user_id) to fetch content strategy
    
    Returns:
        str: The session ID.
    """
    extra_fields = {"brief_content": ""}
    if user_id:
        extra_fields["user_id"] = user_id
    
    return await create_session(
        collection_name=INTERVIEW_SESSIONS_COLLECTION,
        extra_fields=extra_fields
    )

async def get_interview_session(session_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve an interview session from MongoDB.
    
    Args:
        session_id (str): The session ID.
        
    Returns:
        Optional[Dict[str, Any]]: The session document, or None if not found.
    """
    return await get_session(INTERVIEW_SESSIONS_COLLECTION, session_id)

async def update_conversation_history(session_id: str, message: Any, is_user: bool, tool_uses: Optional[List[Dict]] = None) -> None:
    """
    Add a message to the conversation history in MongoDB.
    
    Args:
        session_id (str): The session ID.
        message (Any): The message content (string for simple messages, complex for tool results).
        is_user (bool): True if the message is from the user, False if from the agent.
        tool_uses (Optional[List[Dict]]): Tool uses for assistant messages.
        
    Raises:
        ValueError: If the session could not be updated.
        Exception: For other database errors.
    """
    await update_conversation_history_generic(
        INTERVIEW_SESSIONS_COLLECTION,
        session_id,
        message,
        is_user,
        tool_uses
    )

async def update_brief_content(session_id: str, content: str) -> None:
    """
    Update the brief content in MongoDB.
    
    Args:
        session_id (str): The session ID.
        content (str): The new brief content.
        
    Raises:
        ValueError: If the session could not be updated.
        Exception: For other database errors.
    """
    await update_session_field(
        INTERVIEW_SESSIONS_COLLECTION,
        session_id,
        "brief_content",
        content
    )

async def get_conversation_history(session_id: str) -> List[Dict[str, Any]]:
    """
    Get the conversation history for a session.
    
    Args:
        session_id (str): The session ID.
        
    Returns:
        List[Dict[str, Any]]: The conversation history formatted for the Anthropic API.
    """
    return await get_conversation_history_generic(INTERVIEW_SESSIONS_COLLECTION, session_id)

async def get_brief_content(session_id: str) -> str:
    """
    Get the brief content for a session.
    
    Args:
        session_id (str): The session ID.
        
    Returns:
        str: The brief content.
        
    Raises:
        Exception: For database errors.
    """
    return await get_session_field(
        INTERVIEW_SESSIONS_COLLECTION,
        session_id,
        "brief_content",
        ""
    )

async def get_interview_session_context(session_id: str) -> Dict[str, Any]:
    """
    Get the session context including content strategy.
    
    Args:
        session_id (str): The session ID.
        
    Returns:
        Dict containing session metadata and content strategy.
    """
    from src.api.services.session_service import get_session_context
    return await get_session_context(INTERVIEW_SESSIONS_COLLECTION, session_id)