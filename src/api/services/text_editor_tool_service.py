"""Shared service for handling text editor tool operations in streaming agents."""

import json
import logging
from typing import Dict, Any, Tuple, List, AsyncGenerator, Optional
from ...utils.anthropic_text_editor_handler import AnthropicTextEditorHandler

logger = logging.getLogger(__name__)

# Tool definition used by all agents
TEXT_EDITOR_TOOL_DEFINITION = {
    "type": "text_editor_20250124",
    "name": "str_replace_editor"
}


class TextEditorToolService:
    """Service for handling text editor tool operations during streaming."""
    
    def __init__(self, session_id: str, initial_content: str, memory_path: Optional[str] = None):
        """Initialize the text editor service.
        
        Args:
            session_id: The session ID
            initial_content: Initial content for the document
            memory_path: Optional custom memory path (defaults to memory://{session_id}/document.md)
        """
        self.session_id = session_id
        self.memory_path = memory_path or f"memory://{session_id}/document.md"
        self.handler = AnthropicTextEditorHandler(allow_real_filesystem_access=False)
        self.content_updated = False
        
        # Initialize the document
        self.handler.dispatch_command("create", {
            "path": self.memory_path,
            "file_text": initial_content
        })
    
    def process_tool_use(self, tool_use: Dict[str, Any]) -> Tuple[str, bool, bool]:
        """Process a single tool use and return result, error status, and update status.
        
        Args:
            tool_use: The tool use to process
            
        Returns:
            Tuple of (result_text, is_error, was_updated)
        """
        if tool_use["name"] != "str_replace_editor":
            return "", True, False
        
        tool_input = tool_use["input"]
        command_name = tool_input.get("command")
        command_params = {k: v for k, v in tool_input.items() if k != "command"}
        command_params["path"] = self.memory_path
        
        result_text, is_error = self.handler.dispatch_command(command_name, command_params)
        
        # For view commands, don't send full content
        if command_name == "view" and not is_error:
            return "Document viewed successfully", is_error, False
        
        was_updated = not is_error and command_name != "view"
        if was_updated:
            self.content_updated = True
            
        return result_text, is_error, was_updated
    
    async def create_tool_completion_handler(
        self, 
        event_name: str = "content_update",
        on_content_update: Optional[Any] = None
    ):
        """Create a completion handler for processing tool uses.
        
        Args:
            event_name: The event name to emit for content updates
            on_content_update: Optional callback when content is updated
            
        Returns:
            An async generator function for handling tool completions
        """
        async def handle_tool_completion(full_text: str, tool_uses: List[Dict[str, Any]]) -> AsyncGenerator[str, None]:
            """Process tool uses and emit results."""
            self.content_updated = False
            
            for tool_use in tool_uses:
                result_text, is_error, was_updated = self.process_tool_use(tool_use)
                
                # Emit tool result
                result_msg = "Successfully updated" if not is_error and tool_use['name'] == 'str_replace_editor' else result_text
                yield f"event: tool_call_result\ndata: {json.dumps({'tool_id': tool_use['id'], 'tool_name': tool_use['name'], 'result': result_msg, 'is_error': is_error})}\n\n"
            
            # If content was updated, emit the new content
            if self.content_updated:
                updated_content, _ = self.handler.dispatch_command("view", {"path": self.memory_path})
                yield f"event: {event_name}\ndata: {json.dumps({'content': updated_content})}\n\n"
                
                if on_content_update:
                    await on_content_update(updated_content)
        
        return handle_tool_completion
    
    def get_current_content(self) -> str:
        """Get the current content of the document.
        
        Returns:
            The current document content
        """
        content, _ = self.handler.dispatch_command("view", {"path": self.memory_path})
        return content