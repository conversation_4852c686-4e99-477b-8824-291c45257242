"""Generic service for managing agent sessions in MongoDB."""

import logging
import uuid
from typing import Dict, List, Optional, Any

from src.utils.mongo_utils_async import get_async_mongo_db, get_user_content_strategy
from src.utils.datetime_utils import create_mongo_timestamp_fields, update_mongo_timestamp_field, get_current_datetime

logger = logging.getLogger(__name__)

async def create_session(collection_name: str, extra_fields: Optional[Dict[str, Any]] = None) -> str:
    """
    Create a new session in MongoDB, automatically fetching user's content strategy if user_id is provided.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        extra_fields (Optional[Dict[str, Any]]): Additional fields to include in the session document.
                                                 If 'user_id' is included, strategy will be auto-fetched.
    
    Returns:
        str: The session ID.
    """
    # Generate a unique session ID
    session_id = str(uuid.uuid4())
    
    # Create a new session document
    session_document = {
        "session_id": session_id,
        **create_mongo_timestamp_fields(),
        "conversation_history": []
    }
    
    # Add any extra fields
    if extra_fields:
        session_document.update(extra_fields)
    
    # Auto-fetch content strategy if user_id is provided and include_strategy is not False
    include_strategy = extra_fields.get("include_strategy", True) if extra_fields else True
    
    if extra_fields and extra_fields.get("user_id") and include_strategy:
        user_id = extra_fields["user_id"]
        content_strategy = await get_user_content_strategy(user_id)
        session_document["content_strategy"] = content_strategy
        logger.info(f"Fetched content strategy for user {user_id}: {'Found' if content_strategy else 'None'}")
    else:
        # Always include the field, even if None
        session_document["content_strategy"] = None
        if not include_strategy:
            logger.info(f"Content strategy fetch disabled by include_strategy flag")
    
    try:
        # Get MongoDB database
        db = await get_async_mongo_db()
        
        # Insert the session document
        await db[collection_name].insert_one(session_document)
        logger.info(f"Created new session in {collection_name}: {session_id}")
        
        return session_id
    except Exception as e:
        logger.error(f"Error creating session in {collection_name}: {e}")
        raise

async def get_session(collection_name: str, session_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a session from MongoDB.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        
    Returns:
        Optional[Dict[str, Any]]: The session document, or None if not found.
    """
    try:
        # Get MongoDB database
        db = await get_async_mongo_db()
        
        # Query for the session
        session = await db[collection_name].find_one({"session_id": session_id})
        
        if not session:
            logger.warning(f"No session found in {collection_name} with ID: {session_id}")
            return None
            
        return session
    except Exception as e:
        logger.error(f"Error retrieving session from {collection_name}: {e}")
        raise



async def update_conversation_history(
    collection_name: str,
    session_id: str, 
    message: Any, 
    is_user: bool, 
    tool_uses: Optional[List[Dict]] = None
) -> None:
    """
    Add a message to the conversation history in MongoDB.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        message (Any): The message content (string for simple messages, complex for tool results).
        is_user (bool): True if the message is from the user, False if from the agent.
        tool_uses (Optional[List[Dict]]): Tool uses for assistant messages.
        
    Raises:
        ValueError: If the session could not be updated.
        Exception: For other database errors.
    """
    try:
        # Get MongoDB database
        db = await get_async_mongo_db()
        
        # Create the message document
        message_doc = {
            "role": "user" if is_user else "assistant",
            "timestamp": get_current_datetime()
        }
        
        # Handle different message types
        if isinstance(message, str):
            # Simple text message
            message_doc["content"] = message
            message_doc["content_type"] = "text"
        elif isinstance(message, list):
            # Complex content (tool results)
            message_doc["content"] = message
            message_doc["content_type"] = "complex"
        else:
            # Default to string representation
            message_doc["content"] = str(message)
            message_doc["content_type"] = "text"
        
        # Add tool uses if provided (for assistant messages)
        if tool_uses and not is_user:
            message_doc["tool_uses"] = tool_uses
        
        # Update the session document
        result = await db[collection_name].update_one(
            {"session_id": session_id},
            {
                "$push": {"conversation_history": message_doc},
                "$set": update_mongo_timestamp_field()
            }
        )
        
        if result.modified_count == 0:
            raise ValueError(f"Failed to update conversation history for session in {collection_name}: {session_id}")
    except Exception as e:
        logger.error(f"Error updating conversation history in {collection_name}: {e}")
        raise

async def update_session_field(
    collection_name: str,
    session_id: str,
    field_name: str,
    field_value: Any
) -> None:
    """
    Update a specific field in the session document.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        field_name (str): The field to update.
        field_value (Any): The new value for the field.
        
    Raises:
        ValueError: If the session could not be updated.
        Exception: For other database errors.
    """
    try:
        # Get MongoDB database
        db = await get_async_mongo_db()
        
        # Update the session document
        result = await db[collection_name].update_one(
            {"session_id": session_id},
            {
                "$set": {
                    field_name: field_value,
                    **update_mongo_timestamp_field()
                }
            }
        )
        
        if result.modified_count == 0:
            raise ValueError(f"Failed to update {field_name} for session in {collection_name}: {session_id}")
    except Exception as e:
        logger.error(f"Error updating {field_name} in {collection_name}: {e}")
        raise

async def get_conversation_history(collection_name: str, session_id: str) -> List[Dict[str, Any]]:
    """
    Get the conversation history for a session.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        
    Returns:
        List[Dict[str, Any]]: The conversation history formatted for the Anthropic API.
    """
    try:
        # Get the session
        session = await get_session(collection_name, session_id)
        
        if not session:
            logger.warning(f"No session found in {collection_name} with ID: {session_id}")
            return []
        
        # Format the conversation history for the Anthropic API
        formatted_conversation = []
        i = 0
        while i < len(session.get("conversation_history", [])):
            message = session["conversation_history"][i]
            
            # Check if this is an assistant message with tool uses
            if message["role"] == "assistant" and "tool_uses" in message:
                # This is a tool-using assistant message
                # Build content array
                content = []
                
                # Add text FIRST if present (required by Anthropic API)
                if message.get("content") and message.get("content_type") == "text":
                    content.append({"type": "text", "text": message["content"]})
                
                # Then add tool uses
                for tu in message["tool_uses"]:
                    content.append({
                        "type": "tool_use", 
                        "id": tu["id"], 
                        "name": tu["name"], 
                        "input": tu["input"]
                    })
                
                formatted_msg = {
                    "role": "assistant",
                    "content": content
                }
                formatted_conversation.append(formatted_msg)
                
                # Check if next message is a user message with tool results
                if i + 1 < len(session.get("conversation_history", [])):
                    next_msg = session["conversation_history"][i + 1]
                    if next_msg["role"] == "user" and next_msg.get("content_type") == "complex":
                        # This is the tool results message
                        formatted_conversation.append({
                            "role": "user",
                            "content": next_msg["content"]
                        })
                        i += 2  # Skip both messages
                        continue
                    else:
                        # No tool results found, but tool uses require them
                        # This shouldn't happen in normal operation
                        logger.warning(f"Tool uses found without corresponding tool results for session {session_id} in {collection_name}")
                        i += 1
                        continue
            else:
                # Regular text message
                formatted_conversation.append({
                    "role": message["role"],
                    "content": message["content"]
                })
                i += 1
        
        logger.info(f"Formatted {len(formatted_conversation)} messages for session {session_id} in {collection_name}")
        return formatted_conversation
    except Exception as e:
        logger.error(f"Error getting conversation history from {collection_name}: {e}")
        return []

async def get_session_field(
    collection_name: str,
    session_id: str,
    field_name: str,
    default_value: Any = None
) -> Any:
    """
    Get a specific field from the session document.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        field_name (str): The field to retrieve.
        default_value (Any): Default value if field doesn't exist.
        
    Returns:
        Any: The field value or default_value.
        
    Raises:
        Exception: For database errors.
    """
    try:
        # Get the session
        session = await get_session(collection_name, session_id)
        
        if not session:
            logger.warning(f"No session found in {collection_name} with ID: {session_id}")
            return default_value
        
        return session.get(field_name, default_value)
    except Exception as e:
        logger.error(f"Error getting {field_name} from {collection_name}: {e}")
        raise


async def get_session_context(collection_name: str, session_id: str) -> Dict[str, Any]:
    """
    Get the session context including content strategy.
    
    Args:
        collection_name (str): The MongoDB collection to use.
        session_id (str): The session ID.
        
    Returns:
        Dict containing session metadata and content strategy.
    """
    session = await get_session(collection_name, session_id)
    
    if not session:
        return {
            "session_id": session_id,
            "content_strategy": None,
            "exists": False
        }
    
    return {
        "session_id": session_id,
        "content_strategy": session.get("content_strategy"),
        "user_id": session.get("user_id"),
        "exists": True,
        "created_at": session.get("created_at"),
        "updated_at": session.get("updated_at")
    }