"""Pydantic schemas for transcript processing API."""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from enum import Enum
import datetime
from src.utils.citation_schema import Citation

# Job status enum (reuse from main schemas)
class JobStatus(str, Enum):
    """Possible job statuses."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

# Transcript Processing Models
class TranscriptUploadRequest(BaseModel):
    """Request model for transcript upload and processing."""
    transcript_content: str = Field(..., description="The transcript text content to process")
    user_email: str = Field(..., description="Email address of the user submitting the transcript")
    project_id: Optional[str] = Field(None, description="Optional project ID for StorYD Studio users")

class IdeaOutline(BaseModel):
    """Outline for a content idea."""
    title: str = Field(..., description="Title/type of the outline format")
    throughline: str = Field(..., description="Core narrative thread that connects the sections")
    sections: List[str] = Field(..., description="Ordered sections that make up the outline")

class ExtractedIdea(BaseModel):
    """Individual extracted idea with metadata."""
    id: str = Field(..., description="Unique identifier for the idea")
    idea: str = Field(..., description="The main idea content")
    category: str = Field(..., description="Category of the idea")
    engagement: int = Field(..., description="Engagement score (1-5)")
    summary: Optional[str] = Field(None, description="Detailed summary of the idea")
    suggested_outlines: List[IdeaOutline] = Field(..., description="List of suggested post outlines")
    status: str = Field(default="new", description="Processing status of the idea")
    citations: Optional[List[Citation]] = Field(default=None, description="Supporting citations from transcript")

class TranscriptProcessingResponse(BaseModel):
    """Response model for transcript processing job creation."""
    job_id: str = Field(..., description="Unique identifier for the processing job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    polling_url: str = Field(..., description="URL to poll for job status")

class TranscriptJobStatusResponse(BaseModel):
    """Response model for transcript job status."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    updated_at: Optional[datetime.datetime] = Field(None, description="When the job was last updated")
    completed_at: Optional[datetime.datetime] = Field(None, description="When the job completed")
    duration: Optional[float] = Field(None, description="Duration in seconds if completed")
    ideas: Optional[List[ExtractedIdea]] = Field(None, description="Extracted ideas if completed")
    transcript_id: Optional[str] = Field(None, description="MongoDB transcript document ID")
    error: Optional[str] = Field(None, description="Error message if failed")

class BriefGenerationRequest(BaseModel):
    """Request model for generating a brief from selected idea and outline."""
    transcript_id: str = Field(..., description="MongoDB transcript document ID")
    idea_id: str = Field(..., description="ID of the selected idea")
    outline_index: int = Field(..., description="Index of the selected outline (0-2)")

class BriefGenerationResponse(BaseModel):
    """Response model for brief generation job creation."""
    job_id: str = Field(..., description="Unique identifier for the brief generation job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    polling_url: str = Field(..., description="URL to poll for job status")

class BriefJobStatusResponse(BaseModel):
    """Response model for brief generation job status."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current job status")
    created_at: datetime.datetime = Field(..., description="When the job was created")
    updated_at: Optional[datetime.datetime] = Field(None, description="When the job was last updated")
    completed_at: Optional[datetime.datetime] = Field(None, description="When the job completed")
    duration: Optional[float] = Field(None, description="Duration in seconds if completed")
    brief_content: Optional[str] = Field(None, description="Generated brief content if completed")
    error: Optional[str] = Field(None, description="Error message if failed")