"""Local agent service for content generation using local analysis files."""

import os
import logging
import uuid
import datetime
import json
from typing import Dict, List, Optional, Any, Callable

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import project modules
from src.config import PROMPTS_DIR
from src.utils.local_analysis_loader import load_creator_analyses_local
from src.utils.agent_utils.process_step import process_step

class LocalAgentService:
    """Service for the content generation agent using local analysis files."""

    @staticmethod
    def load_prompt_template(filename: str) -> str:
        """
        Load a prompt template from the prompts directory.

        Args:
            filename: Name of the prompt template file

        Returns:
            The prompt template as a string
        """
        prompt_path = os.path.join(PROMPTS_DIR, "agent_prompts", filename)
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error loading prompt template {filename}: {e}")
            return ""

    @staticmethod
    def initialize_enhanced_scratchpad(input_text: str, creator_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """
        Initialize the enhanced scratchpad structure.

        Args:
            input_text: User's brief or draft post
            creator_analyses: Dictionary with creator analyses

        Returns:
            Dictionary with the enhanced scratchpad structure
        """
        return {
            "user_input_brief": input_text,
            "creator_style_guides": creator_analyses,
            "current_draft_post": "",
            "generation_pipeline": {
                "hook_creation": {
                    "generated_hook_text": None,
                    "decision_summary_for_next_step": None
                },
                "body_linguistic_construction": {
                    "generated_body_text": None,
                    "decision_summary_for_next_step": None
                },
                "ending_creation": {
                    "generated_ending_text": None
                }
            }
        }

    @staticmethod
    def format_explanation_from_enhanced_scratchpad(pipeline: Dict[str, Any], creator_name: str) -> str:
        """
        Format an explanation from the enhanced scratchpad.

        Args:
            pipeline: The generation pipeline from the enhanced scratchpad
            creator_name: Name of the creator

        Returns:
            Formatted explanation as a string
        """
        explanation = f"# Content Generation Explanation for {creator_name}\n\n"


        # Hook creation
        hook_data = pipeline.get("hook_creation", {})
        if hook_data.get("generated_hook_text"):
            explanation += f"## Hook Creation\n\n"
            if hook_data.get("decision_summary_for_next_step"):
                explanation += f"{hook_data.get('decision_summary_for_next_step')}\n\n"

        # Body creation
        body_data = pipeline.get("body_linguistic_construction", {})
        if body_data.get("generated_body_text"):
            explanation += f"## Body Structure & Style\n\n"
            if body_data.get("decision_summary_for_next_step"):
                explanation += f"{body_data.get('decision_summary_for_next_step')}\n\n"

        # Ending creation
        ending_data = pipeline.get("ending_creation", {})
        if ending_data.get("generated_ending_text"):
            explanation += f"## Ending\n\n"
            explanation += "Completed the post with an engaging ending and call-to-action.\n\n"

        return explanation

    @staticmethod
    async def generate_content(
        input_text: str,
        creator_name: str,
        json_dir: Optional[str] = None,
        markdown_dir: Optional[str] = None,
        update_step_callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Generate content based on user input and creator analysis using local files.

        Args:
            input_text: User's brief or draft post
            creator_name: Name of the creator to emulate
            json_dir: Directory for JSON analyses
            markdown_dir: Directory for markdown analyses
            update_step_callback: Callback function to update generation step

        Returns:
            Dictionary with generated content and metadata
        """
        try:
            # Generate a session ID for logging purposes
            generation_session_id = f"{creator_name}_{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"

            # Load creator analyses from local files
            if update_step_callback: update_step_callback("loading_analyses")
            logger.info(f"Loading analyses for {creator_name} from local files")
            
            analyses = load_creator_analyses_local(creator_name, json_dir, markdown_dir)
            
            if not analyses:
                logger.error(f"No analyses found for {creator_name}")
                return {
                    "error": f"No analyses found for {creator_name}"
                }

            # Initialize enhanced scratchpad
            scratchpad = LocalAgentService.initialize_enhanced_scratchpad(input_text, analyses)

            # Load system prompt
            system_prompt = LocalAgentService.load_prompt_template("system_prompt.txt")
            
            # Initialize thinking logs
            thinking_logs = {}

            # Step 1: Hook creation
            if update_step_callback: update_step_callback("creating_hook")
            logger.info(f"Processing hook creation for {creator_name}")

            # Load the prompt template
            hook_prompt_template = LocalAgentService.load_prompt_template("hook_prompt.txt")

            hook_result = await process_step(
                "hook", input_text, creator_name, analyses.get("hooks", {}),
                scratchpad, hook_prompt_template, system_prompt, generation_session_id
            )

            # Update scratchpad fields - only store essential information
            # NOTE: We no longer store selected_hook_pattern to keep scratchpad minimal
            scratchpad["generation_pipeline"]["hook_creation"]["generated_hook_text"] = hook_result.get("output")
            scratchpad["generation_pipeline"]["hook_creation"]["decision_summary_for_next_step"] = hook_result.get("decision_summary", "")

            # Update current draft post
            scratchpad["current_draft_post"] += hook_result.get("output", "") + "\n\n"

            # Save thinking logs if available
            if "thinking" in hook_result:
                thinking_logs["hook_creation"] = hook_result["thinking"]

            # Step 2: Body creation (with linguistic styling)
            if update_step_callback: update_step_callback("developing_body")
            logger.info(f"Processing body creation for {creator_name}")

            # Create a combined data object with both body and linguistic analyses
            combined_analysis = {
                "body_analysis_data": analyses.get("body", {}),
                "linguistic_analysis_data": analyses.get("linguistic", {})
            }

            # Load the prompt template
            body_prompt_template = LocalAgentService.load_prompt_template("body_prompt.txt")

            body_result = await process_step(
                "body", input_text, creator_name, combined_analysis,
                scratchpad, body_prompt_template, system_prompt, generation_session_id
            )

            # Update scratchpad fields - only store essential information
            # NOTE: We no longer store selected_body_structure to keep scratchpad minimal
            scratchpad["generation_pipeline"]["body_linguistic_construction"]["generated_body_text"] = body_result.get("output")
            scratchpad["generation_pipeline"]["body_linguistic_construction"]["decision_summary_for_next_step"] = body_result.get("decision_summary", "")

            # Update current draft post
            scratchpad["current_draft_post"] += body_result.get("output", "") + "\n\n"

            # Save thinking logs if available
            if "thinking" in body_result:
                thinking_logs["body_creation"] = body_result["thinking"]

            # Step 3: Ending creation
            if update_step_callback: update_step_callback("crafting_ending")
            logger.info(f"Processing ending creation for {creator_name}")

            # Load the prompt template
            ending_prompt_template = LocalAgentService.load_prompt_template("ending_prompt.txt")

            ending_result = await process_step(
                "ending", input_text, creator_name, analyses.get("endings", {}),
                scratchpad, ending_prompt_template, system_prompt, generation_session_id
            )

            # Update scratchpad fields - only store essential information
            # NOTE: We no longer store selected_ending_pattern to keep scratchpad minimal
            scratchpad["generation_pipeline"]["ending_creation"]["generated_ending_text"] = ending_result.get("output")

            # Update current draft post
            scratchpad["current_draft_post"] += ending_result.get("output", "")

            # Save thinking logs if available
            if "thinking" in ending_result:
                thinking_logs["ending_creation"] = ending_result["thinking"]

            # Final step: Complete
            if update_step_callback: update_step_callback("completed")
            logger.info(f"Content generation completed for {creator_name}")

            # Return the final result
            return {
                "generated_post": scratchpad["current_draft_post"],
                "explanation": LocalAgentService.format_explanation_from_enhanced_scratchpad(scratchpad["generation_pipeline"], creator_name),
                "thinking_logs": thinking_logs,
                "enhanced_scratchpad": scratchpad["generation_pipeline"]
            }

        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return {
                "error": f"Failed to generate content: {str(e)}"
            }
