"""Job manager for agent content generation using MongoDB for persistence."""

import logging
import uuid
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

from src.utils.mongo_utils_async import get_async_mongo_db
from src.utils.datetime_utils import get_current_datetime_iso
from src.api.agent_service import AgentService

AGENT_JOBS_COLLECTION = "agent_jobs"

class AgentJobManager:
    """MongoDB-backed job manager for agent content generation."""
    
    @classmethod
    async def create_job(cls, input_text: str, clerk_user_id: str, post_length: Optional[str] = None, draft_id: Optional[str] = None) -> str:
        """Create a new agent job in MongoDB."""
        job_id = str(uuid.uuid4())
        
        logger.info(f"Creating job {job_id} with input length: {len(input_text) if input_text else 0}")
        
        # Create job document
        job_doc = {
            "_id": job_id,
            "input_text": input_text,
            "clerk_user_id": clerk_user_id,
            "post_length": post_length,
            "draft_id": draft_id,
            "status": "pending",
            "created_at": get_current_datetime_iso(),
            "updated_at": get_current_datetime_iso(),
            "current_generation_step": None
        }
        
        # Save to MongoDB
        db = await get_async_mongo_db()
        collection = db[AGENT_JOBS_COLLECTION]
        await collection.insert_one(job_doc)
        
        logger.info(f"Job {job_id} saved to MongoDB")
        
        # Start processing asynchronously
        asyncio.create_task(cls._process_job(job_id))
        
        return job_id
    
    @classmethod
    async def get_job_status(cls, job_id: str, clerk_user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get job status from MongoDB with optional user ownership check."""
        db = await get_async_mongo_db()
        collection = db[AGENT_JOBS_COLLECTION]
        
        # Build query with optional user check
        query = {"_id": job_id}
        if clerk_user_id:
            query["clerk_user_id"] = clerk_user_id
            
        job = await collection.find_one(query)
        if not job:
            return None
            
        # Remove MongoDB _id from response
        job["job_id"] = job.pop("_id")
        
        # Calculate duration if completed
        if job.get("completed_at") and job.get("created_at"):
            try:
                created = datetime.fromisoformat(job["created_at"])
                completed = datetime.fromisoformat(job["completed_at"])
                job["duration"] = (completed - created).total_seconds()
            except ValueError:
                job["duration"] = None
                
        return job
    
    @classmethod
    async def _process_job(cls, job_id: str) -> None:
        """Process a job (reads from MongoDB)."""
        logger.info(f"Starting to process job: {job_id}")
        
        # Small delay to ensure write propagation
        await asyncio.sleep(0.1)
        
        # Always read from MongoDB
        db = await get_async_mongo_db()
        collection = db[AGENT_JOBS_COLLECTION]
        
        job = await collection.find_one({"_id": job_id})
        if not job:
            logger.error(f"Job {job_id} not found in MongoDB")
            return
            
        # Verify input_text exists
        if not job.get("input_text"):
            logger.error(f"Job {job_id} has no input_text!")
            await collection.update_one(
                {"_id": job_id},
                {"$set": {
                    "status": "failed",
                    "error": "No input text provided",
                    "updated_at": get_current_datetime_iso()
                }}
            )
            return
            
        logger.info(f"Processing job {job_id} with input length: {len(job['input_text'])}")
        
        try:
            # Update status to running
            await collection.update_one(
                {"_id": job_id},
                {"$set": {
                    "status": "running",
                    "updated_at": get_current_datetime_iso(),
                    "current_generation_step": "initializing"
                }}
            )
            
            # Get user preferences and content strategy
            user_preferences = None
            content_strategy = None
            if job.get("clerk_user_id"):
                from src.utils.mongo_utils_async import CONTENT_PREFERENCES_COLLECTION, get_user_content_strategy
                
                # Fetch preferences
                preferences_doc = await db[CONTENT_PREFERENCES_COLLECTION].find_one({
                    "clerk_user_id": job["clerk_user_id"]
                })
                if preferences_doc:
                    preferences_doc.pop("_id", None)
                    user_preferences = preferences_doc
                
                # Fetch content strategy
                content_strategy = await get_user_content_strategy(job["clerk_user_id"])
                if content_strategy:
                    logger.info(f"Loaded content strategy for user {job['clerk_user_id']}")
                else:
                    logger.info(f"No content strategy found for user {job['clerk_user_id']}")
            
            # Create step callback - wrap async MongoDB update in sync callback
            def update_step_callback(step_name: str):
                # Create a new task to handle the async update with error handling
                async def update_step():
                    try:
                        await collection.update_one(
                            {"_id": job_id},
                            {"$set": {
                                "current_generation_step": step_name,
                                "updated_at": get_current_datetime_iso()
                            }}
                        )
                        logger.info(f"Job {job_id} step updated to: {step_name}")
                    except Exception as e:
                        logger.error(f"Failed to update job {job_id} step to {step_name}: {e}")
                
                asyncio.create_task(update_step())
            
            # Generate content
            result = await AgentService.generate_content(
                input_text=job["input_text"],
                user_preferences=user_preferences,
                update_step_callback=update_step_callback,
                post_length=job.get("post_length"),
                draft_id=job.get("draft_id"),
                content_strategy=content_strategy
            )
            
            # Update job with results
            await collection.update_one(
                {"_id": job_id},
                {"$set": {
                    "status": "completed",
                    "result": result,
                    "completed_at": get_current_datetime_iso(),
                    "updated_at": get_current_datetime_iso(),
                    "current_generation_step": "completed"
                }}
            )
            
            logger.info(f"Job {job_id} completed successfully")
            
            # Save draft to library if successful
            if isinstance(result, dict) and result.get("generated_post"):
                try:
                    from src.utils.draft_library_utils import save_draft_to_library
                    
                    await save_draft_to_library(
                        user_id=job["clerk_user_id"],
                        content=result["generated_post"],
                        brief=job["input_text"],
                        post_length=job.get("post_length", "medium"),
                        generation_result=result,
                        job_id=job_id,
                        session_id=job.get("session_id"),
                        draft_type=job.get("draft_id")
                    )
                except Exception as e:
                    logger.error(f"Failed to save draft to library: {e}")
            
        except Exception as e:
            logger.error(f"Error processing job {job_id}: {str(e)}", exc_info=True)
            await collection.update_one(
                {"_id": job_id},
                {"$set": {
                    "status": "failed",
                    "error": "Content generation failed",
                    "updated_at": get_current_datetime_iso(),
                    "current_generation_step": "failed"
                }}
            )
    
    @classmethod
    async def cancel_job(cls, job_id: str, clerk_user_id: Optional[str] = None) -> bool:
        """Cancel a job with optional user ownership check."""
        db = await get_async_mongo_db()
        collection = db[AGENT_JOBS_COLLECTION]
        
        # Build query with optional user check
        query = {"_id": job_id}
        if clerk_user_id:
            query["clerk_user_id"] = clerk_user_id
            
        job = await collection.find_one(query)
        if not job:
            logger.warning(f"Attempted to cancel non-existent job: {job_id}")
            return False
        
        # Only cancel if pending or running
        if job["status"] in ["pending", "running"]:
            await collection.update_one(
                {"_id": job_id},
                {"$set": {
                    "status": "cancelled",
                    "updated_at": get_current_datetime_iso(),
                    "completed_at": get_current_datetime_iso(),
                    "current_generation_step": "cancelled"
                }}
            )
            logger.info(f"Job {job_id} cancelled successfully")
            return True
        else:
            logger.info(f"Job {job_id} cannot be cancelled - status: {job['status']}")
            return False