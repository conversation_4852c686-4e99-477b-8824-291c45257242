"""Utility functions for LinkedInsight API."""

import uuid
import json
import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import sys
from pathlib import Path as FilePath

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = FilePath(__file__).parent.parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.config import OUTPUTS_DIR

def generate_job_id() -> str:
    """Generate a unique job ID."""
    return str(uuid.uuid4())

def get_result_path(creator_name: str, analysis_type: str) -> str:
    """Get the path where analysis results should be stored."""
    # Create creator directory if it doesn't exist
    creator_dir = os.path.join(OUTPUTS_DIR, creator_name)
    os.makedirs(creator_dir, exist_ok=True)

    # Generate result filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"{creator_name}_{analysis_type}_{timestamp}.json"

    return os.path.join(creator_dir, filename)

def format_number(num: int) -> str:
    """Format numbers for display (e.g., 1200 -> 1.2K)."""
    if num >= 1000000:
        return f"{num/1000000:.1f}M"
    elif num >= 1000:
        return f"{num/1000:.1f}K"
    else:
        return str(num)

def load_json_result(file_path: str) -> Optional[Dict[str, Any]]:
    """Load analysis result from a JSON file at the exact specified path.
    
    This function attempts to load and parse a JSON file from the exact path provided.
    It does NOT attempt any path guessing, reconstruction, or fallback mechanisms if
    the file is not found at the specified path.
    
    Args:
        file_path: The exact path to the JSON file. Must be the complete, correct path.
                  No path manipulation or guessing will be performed.
    
    Returns:
        Dict[str, Any]: The parsed JSON content as a Python dictionary if successful.
        None: If the file doesn't exist, contains invalid JSON, or if any other error occurs.
              Error details will be logged.
    
    Error handling:
        - If the file doesn't exist: Logs an error and returns None
        - If the file contains invalid JSON: Logs an error and returns None
        - If any other error occurs (e.g., permission issues): Logs an error and returns None
    """
    if not os.path.exists(file_path):
        logger.error(f"Result file not found at specified path: {file_path}")
        return None

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {file_path}: {e}")
        return None
    except Exception as e:  # Catches other IOErrors, etc.
        logger.error(f"Error loading result from {file_path}: {e}")
        return None

def get_metadata_stats(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract common metadata and stats from analysis data."""
    creator = raw_data.get("creator", "")
    posts_analyzed = raw_data.get("posts_analyzed", 0)

    # Calculate stats (will be different for each analysis type)
    content_items = len(raw_data.get("results", {}).get("items", []))
    avg_engagement = format_number(raw_data.get("results", {}).get("metadata", {}).get("avg_engagement", 0))
    max_engagement = format_number(raw_data.get("results", {}).get("metadata", {}).get("max_engagement", 0))

    return {
        "creator": creator,
        "stats": {
            "postsAnalyzed": posts_analyzed,
            "contentThemes": content_items,
            "avgEngagement": avg_engagement,
            "maxEngagement": max_engagement
        }
    }

def create_citation(content: str, engagement: Dict[str, int]) -> Dict[str, str]:
    """Create a standardized citation object."""
    likes = engagement.get("likes", 0)
    comments = engagement.get("comments", 0)
    total = likes + comments

    return {
        "content": content,
        "engagement": f"{format_number(total)} (Likes: {format_number(likes)}, Comments: {format_number(comments)})"
    }

def transform_theme_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw theme analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract themes
        themes_data = raw_data.get("results", {}).get("themes", [])

        transformed_themes = []
        for theme in themes_data:
            # Format theme data
            engagement_data = theme.get("engagement", {})
            avg_likes = engagement_data.get("avg_likes", 0)

            transformed_theme = {
                "title": theme.get("name", ""),
                "engagement": f"{format_number(avg_likes)} avg. engagement",
                "description": theme.get("description", ""),
                "example": theme.get("example_text", "No example available"),
                "citation": create_citation(
                    theme.get("example_full", "No citation available"),
                    {
                        "likes": engagement_data.get("max_likes", 0),
                        "comments": engagement_data.get("max_comments", 0)
                    }
                )
            }
            transformed_themes.append(transformed_theme)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_themes)

        # Add themes to result
        result["themes"] = transformed_themes

        return result
    except Exception as e:
        logger.error(f"Error transforming theme data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "themes": []
        }

def transform_hook_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw hook analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract hooks
        hooks_data = raw_data.get("results", {}).get("hook_types", [])

        transformed_hooks = []
        for hook in hooks_data:
            # Format hook data
            engagement_data = hook.get("engagement", {})

            transformed_hook = {
                "type": hook.get("name", ""),
                "description": hook.get("description", ""),
                "example": hook.get("example_text", "No example available"),
                "citation": create_citation(
                    hook.get("example_full", "No citation available"),
                    {
                        "likes": engagement_data.get("likes", 0),
                        "comments": engagement_data.get("comments", 0)
                    }
                )
            }
            transformed_hooks.append(transformed_hook)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_hooks)

        # Add hooks to result
        result["hooks"] = transformed_hooks

        return result
    except Exception as e:
        logger.error(f"Error transforming hook data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "hooks": []
        }

def transform_body_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw body structure analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract body structures
        patterns_data = raw_data.get("results", {}).get("patterns", [])

        transformed_patterns = []
        for pattern in patterns_data:
            # Format pattern data
            engagement_data = pattern.get("engagement", {})

            transformed_pattern = {
                "type": pattern.get("name", ""),
                "description": pattern.get("description", ""),
                "example": pattern.get("example_text", "No example available"),
                "citation": create_citation(
                    pattern.get("example_full", "No citation available"),
                    {
                        "likes": engagement_data.get("likes", 0),
                        "comments": engagement_data.get("comments", 0)
                    }
                )
            }
            transformed_patterns.append(transformed_pattern)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_patterns)

        # Add body patterns to result
        result["body"] = transformed_patterns

        return result
    except Exception as e:
        logger.error(f"Error transforming body structure data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "body": []
        }

def transform_ending_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw ending analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract endings
        endings_data = raw_data.get("results", {}).get("ending_types", [])

        transformed_endings = []
        for ending in endings_data:
            # Format ending data
            engagement_data = ending.get("engagement", {})

            transformed_ending = {
                "type": ending.get("name", ""),
                "description": ending.get("description", ""),
                "example": ending.get("example_text", "No example available"),
                "citation": create_citation(
                    ending.get("example_full", "No citation available"),
                    {
                        "likes": engagement_data.get("likes", 0),
                        "comments": engagement_data.get("comments", 0)
                    }
                )
            }
            transformed_endings.append(transformed_ending)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_endings)

        # Add endings to result
        result["endings"] = transformed_endings

        return result
    except Exception as e:
        logger.error(f"Error transforming ending data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "endings": []
        }

def transform_linguistic_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw linguistic analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract linguistic patterns
        patterns_data = raw_data.get("results", {}).get("patterns", [])

        transformed_patterns = []
        for pattern in patterns_data:
            # Format pattern data
            engagement_data = pattern.get("engagement", {})

            transformed_pattern = {
                "type": pattern.get("name", ""),
                "description": pattern.get("description", ""),
                "example": pattern.get("example_text", "No example available"),
                "citation": create_citation(
                    pattern.get("example_full", "No citation available"),
                    {
                        "likes": engagement_data.get("likes", 0),
                        "comments": engagement_data.get("comments", 0)
                    }
                )
            }
            transformed_patterns.append(transformed_pattern)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_patterns)

        # Add linguistic patterns to result
        result["linguistic"] = transformed_patterns

        return result
    except Exception as e:
        logger.error(f"Error transforming linguistic data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "linguistic": []
        }

def transform_overview_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform raw overview analysis data to frontend format."""
    try:
        # Get base stats
        result = get_metadata_stats(raw_data)

        # Extract themes if available
        themes_data = raw_data.get("results", {}).get("themes", [])

        transformed_themes = []
        for theme in themes_data:
            # Format theme data
            engagement_data = theme.get("engagement", {})
            avg_likes = engagement_data.get("avg_likes", 0)

            transformed_theme = {
                "title": theme.get("name", ""),
                "engagement": f"{format_number(avg_likes)} avg. engagement",
                "description": theme.get("description", "")
            }
            transformed_themes.append(transformed_theme)

        # Update content count
        result["stats"]["contentThemes"] = len(transformed_themes)

        # Add themes to result
        result["themes"] = transformed_themes

        return result
    except Exception as e:
        logger.error(f"Error transforming overview data: {e}")
        return {
            "creator": raw_data.get("creator", ""),
            "stats": {
                "postsAnalyzed": raw_data.get("posts_analyzed", 0),
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            },
            "themes": []
        }

def transform_all_data(data_by_type: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """Combine results from all analysis types into a single comprehensive result."""
    try:
        # Start with base data from any available analysis
        first_key = next(iter(data_by_type.keys()), None)
        if not first_key:
            logger.error("No data available to transform")
            return {
                "creator": "",
                "stats": {
                    "postsAnalyzed": 0,
                    "contentThemes": 0,
                    "avgEngagement": "0",
                    "maxEngagement": "0"
                }
            }

        # Use the first available analysis for base stats
        result = {
            "creator": data_by_type[first_key].get("creator", ""),
            "stats": data_by_type[first_key].get("stats", {})
        }

        # Merge in each specific analysis type
        if "themes" in data_by_type:
            result["themes"] = data_by_type["themes"].get("themes", [])

        if "hooks" in data_by_type:
            result["hooks"] = data_by_type["hooks"].get("hooks", [])

        if "body" in data_by_type:
            result["body"] = data_by_type["body"].get("body", [])

        if "ending" in data_by_type:
            result["endings"] = data_by_type["ending"].get("endings", [])

        if "linguistic" in data_by_type:
            result["linguistic"] = data_by_type["linguistic"].get("linguistic", [])

        # Count total patterns found across all analyses
        total_patterns = sum([
            len(result.get("themes", [])),
            len(result.get("hooks", [])),
            len(result.get("body", [])),
            len(result.get("endings", [])),
            len(result.get("linguistic", []))
        ])

        # Update content count in stats
        result["stats"]["contentThemes"] = total_patterns

        return result
    except Exception as e:
        logger.error(f"Error transforming combined data: {e}")
        return {
            "creator": "",
            "stats": {
                "postsAnalyzed": 0,
                "contentThemes": 0,
                "avgEngagement": "0",
                "maxEngagement": "0"
            }
        }