"""Agent service for content generation."""

import os
import logging
import uuid
import datetime
from datetime import datetime as dt
from typing import Dict, List, Optional, Any, Callable

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import project modules
from src.config import PROMPTS_DIR
from src.utils.agent_utils import process_step

class AgentSessionState:
    """Session state for maintaining context across the 3-step generation process."""
    
    def __init__(self, user_brief: str, session_id: str, content_strategy: Optional[Dict[str, Any]] = None):
        self.user_brief = user_brief
        self.session_id = session_id
        self.session_linguistic_patterns: Dict[str, Any] = {}  # {'draft_a': UnifiedPattern, 'draft_b': UnifiedPattern}
        self.content_strategy = content_strategy
        self.created_at = datetime.datetime.now()
        
    def set_session_linguistic_patterns(self, patterns: Dict[str, Any]) -> None:
        """Store the session linguistic patterns for draft variety."""
        self.session_linguistic_patterns = patterns
        # Patterns are now UnifiedPattern instances, not dicts
        draft_a_pattern = patterns.get('draft_a')
        draft_b_pattern = patterns.get('draft_b')
        draft_a_name = draft_a_pattern.name if draft_a_pattern else 'none'
        draft_b_name = draft_b_pattern.name if draft_b_pattern else 'none'
        logger.info(f"Session {self.session_id}: Set linguistic patterns - draft_a: {draft_a_name}, draft_b: {draft_b_name}")
        
    def get_session_linguistic_pattern(self, draft_id: Optional[str] = None) -> Optional[Any]:
        """Get the session linguistic pattern for a specific draft."""
        if draft_id and draft_id in self.session_linguistic_patterns:
            return self.session_linguistic_patterns[draft_id]
        elif draft_id is None and self.session_linguistic_patterns:
            # Backward compatibility: return draft_a pattern if no draft_id specified
            return self.session_linguistic_patterns.get('draft_a')
        return None
    
    def get_session_linguistic_patterns(self) -> Dict[str, Any]:
        """Get all session linguistic patterns."""
        return self.session_linguistic_patterns


class AgentService:
    """Service for the content generation agent."""

    @staticmethod
    def initialize_enhanced_scratchpad(input_text: str, creator_analyses: Dict[str, Any], draft_id: Optional[str] = None, content_strategy: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Initialize an enhanced scratchpad with the new structure for improved reasoning traces.

        Args:
            input_text: User's brief or draft post (stored once in API cache, not duplicated here)
            creator_analyses: Dictionary with all loaded analyses for the creator (DEPRECATED - will be removed)
            draft_id: Optional draft identifier
            content_strategy: Optional content strategy (industry, target audience, etc.)

        Returns:
            Dictionary with the enhanced scratchpad structure
        """
        scratchpad = {
            "generation_pipeline": {
                "hook_creation": {
                    "selected_hook_pattern": None,
                    "generated_hook_text": None,
                    # REMOVED: detailed_reasoning_for_selection_and_text - reduces scratchpad size
                    "decision_summary_for_next_step": None
                },
                "body_linguistic_construction": {
                    "selected_body_structure": None,
                    "selected_linguistic_patterns": None,
                    "generated_body_text": None,
                    # REMOVED: detailed_reasoning_for_selection_and_text - reduces scratchpad size
                    "decision_summary_for_next_step": None
                },
                "ending_creation": {
                    "selected_ending_pattern": None,
                    "generated_ending_text": None
                    # REMOVED: detailed_reasoning_for_selection_and_text - reduces scratchpad size
                }
            },
            f"current_draft_post_{draft_id or 'default'}": "",
            "final_explanation_to_user_components": {}
        }
        
        # Add content strategy if provided
        if content_strategy:
            scratchpad["content_strategy"] = content_strategy
            
        return scratchpad

    @staticmethod
    async def generate_content(
        input_text: str,
        user_preferences: Optional[Dict[str, Any]] = None,
        conversation_id: Optional[str] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        update_step_callback: Optional[Callable[[str], None]] = None,
        post_length: Optional[str] = None,
        draft_id: Optional[str] = None,
        content_strategy: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate content based on user input using vector search for pattern selection.

        Args:
            input_text: User's brief or draft post
            user_preferences: User's content preferences (tone, style, etc.)
            conversation_id: ID of an existing conversation
            conversation_history: Previous messages in the conversation
            update_step_callback: Callback function to update generation step
            post_length: Desired post length (short, medium, long)
            draft_id: ID of the draft being generated
            content_strategy: User's content strategy (industry, target audience, etc.)

        Returns:
            Dictionary with generated content and/or questions
        """
        try:
            # Reset debug logger for new session
            from src.utils.pipeline_debug_logger import reset_debug_logger, get_debug_logger
            reset_debug_logger()
            debug_logger = get_debug_logger()
            
            # Create a new conversation ID if none provided
            if not conversation_id:
                conversation_id = str(uuid.uuid4())

            # Initialize conversation history if none provided
            if not conversation_history:
                conversation_history = []

            # Generate a session ID for logging purposes
            generation_session_id = f"session_{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"
            
            # Create session state to manage context across all steps
            session_state = AgentSessionState(input_text, generation_session_id, content_strategy)
            
            # Log session start
            logger.info(f"🚀 Starting agent generation session: {generation_session_id}")
            logger.info(f"📊 {debug_logger.get_session_summary()}")

            # Log user preferences if provided
            if user_preferences:
                logger.info(f"User preferences for session {generation_session_id}: {user_preferences}")
            else:
                logger.info(f"No user preferences provided for session {generation_session_id}")

            # Creator analyses no longer needed - using vector search for pattern selection
            analyses = {}

            # Initialize enhanced scratchpad
            scratchpad = AgentService.initialize_enhanced_scratchpad(input_text, analyses, draft_id, content_strategy)
            
            # Inject user preferences into scratchpad for access in all prompts
            if user_preferences:
                scratchpad["user_content_preferences"] = user_preferences

            # Session Linguistic Pattern Selection - NEW for draft variety
            logger.info(f"Selecting session linguistic patterns for draft variety")
            from src.utils.vector_pattern_selector import VectorPatternSelector
            vector_selector = VectorPatternSelector()
            session_linguistic_patterns = await vector_selector.select_session_linguistic_patterns(input_text)
            
            if session_linguistic_patterns:
                session_state.set_session_linguistic_patterns(session_linguistic_patterns)
                # Patterns are now UnifiedPattern instances
                draft_a_name = session_linguistic_patterns.get('draft_a').name if session_linguistic_patterns.get('draft_a') else 'none'
                draft_b_name = session_linguistic_patterns.get('draft_b').name if session_linguistic_patterns.get('draft_b') else 'none'
                logger.info(f"Session linguistic patterns selected - draft_a: {draft_a_name}, draft_b: {draft_b_name}")
            else:
                logger.warning(f"No session linguistic patterns found for brief: {input_text[:100]}...")

            # Load the system prompt once for all steps
            system_prompt = AgentService.load_prompt_template("content_agent_system_prompt.txt")

            # Step 2: Hook creation
            if update_step_callback: update_step_callback("creating_hook")
            logger.info(f"Processing hook creation")

            # Load the prompt template for hook creation
            hook_prompt_template = AgentService.load_prompt_template("hook_prompt.txt")

            hook_result = await process_step(
                "hook", input_text, analyses.get("hook", {}),
                scratchpad, hook_prompt_template, system_prompt, generation_session_id, post_length, draft_id, session_state
            )

            # Update scratchpad fields - only store essential information
            scratchpad["generation_pipeline"]["hook_creation"]["selected_hook_pattern"] = hook_result.get("selection")
            scratchpad["generation_pipeline"]["hook_creation"]["generated_hook_text"] = hook_result.get("output")
            # REMOVED: detailed_reasoning_for_selection_and_text - verbose reasoning not needed in scratchpad
            scratchpad["generation_pipeline"]["hook_creation"]["decision_summary_for_next_step"] = hook_result.get("decision_summary", "")

            # Update current draft post
            current_draft_key = f"current_draft_post_{draft_id or 'default'}"
            scratchpad[current_draft_key] += hook_result.get("output", "") + "\n\n"

            # Step 3: Body creation
            if update_step_callback: update_step_callback("developing_body")
            logger.info(f"Processing body creation")

            # Use empty analysis data since we now use vector search for pattern selection
            body_analysis = analyses.get("body", {})

            # Load the prompt template for body creation
            body_prompt_template = AgentService.load_prompt_template("body_prompt.txt")

            body_result = await process_step(
                "body", input_text, body_analysis,
                scratchpad, body_prompt_template, system_prompt, generation_session_id, post_length, draft_id, session_state
            )

            # Update scratchpad fields - only store essential information
            scratchpad["generation_pipeline"]["body_linguistic_construction"]["selected_body_structure"] = body_result.get("selection")
            scratchpad["generation_pipeline"]["body_linguistic_construction"]["generated_body_text"] = body_result.get("output")
            # REMOVED: detailed_reasoning_for_selection_and_text - verbose reasoning not needed in scratchpad
            scratchpad["generation_pipeline"]["body_linguistic_construction"]["decision_summary_for_next_step"] = body_result.get("decision_summary", "")

            # Update current draft post
            scratchpad[current_draft_key] += body_result.get("output", "") + "\n\n"

            # Step 4: Ending creation
            if update_step_callback: update_step_callback("crafting_ending")
            logger.info(f"Processing ending creation")

            # Load the prompt template for ending creation
            ending_prompt_template = AgentService.load_prompt_template("ending_prompt.txt")

            ending_result = await process_step(
                "ending", input_text, analyses.get("ending", {}),
                scratchpad, ending_prompt_template, system_prompt, generation_session_id, post_length, draft_id, session_state
            )

            # Update scratchpad fields - only store essential information
            scratchpad["generation_pipeline"]["ending_creation"]["selected_ending_pattern"] = ending_result.get("selection")
            scratchpad["generation_pipeline"]["ending_creation"]["generated_ending_text"] = ending_result.get("output")
            # REMOVED: detailed_reasoning_for_selection_and_text - verbose reasoning not needed in scratchpad

            # Update current draft post
            scratchpad[current_draft_key] += ending_result.get("output", "")

            # Note: Linguistic refinement step has been removed


            # Collect thinking tokens from all steps
            thinking_logs = {}
            for step_result in [hook_result, body_result, ending_result]:
                if "thinking" in step_result and step_result["thinking"] and "step_name" in step_result:
                    step_name = step_result["step_name"]
                    thinking_logs[step_name] = step_result["thinking"]

            # Return the final result
            return {
                "conversation_id": conversation_id,
                "generated_post": scratchpad[current_draft_key],
                "explanation": AgentService.format_explanation_from_enhanced_scratchpad(scratchpad["generation_pipeline"], content_strategy),
                "thinking_logs": thinking_logs,
                "enhanced_scratchpad": scratchpad["generation_pipeline"]  # Include enhanced scratchpad for debugging
            }

        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return {
                "conversation_id": conversation_id or str(uuid.uuid4()),
                "error": f"Failed to generate content: {str(e)}"
            }

    @staticmethod
    def format_explanation_from_enhanced_scratchpad(generation_pipeline: Dict[str, Any], content_strategy: Optional[Dict[str, Any]] = None) -> str:
        """
        Format the reasoning from the enhanced scratchpad into a human-readable explanation.
        Now uses only decision summaries for a more concise explanation.

        Args:
            generation_pipeline: The generation_pipeline section of the enhanced scratchpad
            content_strategy: Optional content strategy for funnel stage inference

        Returns:
            Formatted explanation string
        """
        explanation = "# How This Post Was Created\n\n"
        
        # Add funnel stage if content strategy is provided
        if content_strategy:
            funnel_stage = AgentService._infer_funnel_stage(generation_pipeline, content_strategy)
            # Just show the funnel stage emoji and name, not the full strategy line
            if funnel_stage and "Not classified" not in funnel_stage:
                explanation += f"{funnel_stage}\n\n"
        
        explanation += "This post was created through a step-by-step process where each component builds on the previous ones:\n\n"


        # Hook Creation
        if "hook_creation" in generation_pipeline and generation_pipeline["hook_creation"]["decision_summary_for_next_step"]:
            explanation += "**Hook Creation:** " + generation_pipeline["hook_creation"]["decision_summary_for_next_step"] + "\n\n"

        # Body & Linguistic Style
        if "body_linguistic_construction" in generation_pipeline and generation_pipeline["body_linguistic_construction"]["decision_summary_for_next_step"]:
            explanation += "**Body Structure & Style:** " + generation_pipeline["body_linguistic_construction"]["decision_summary_for_next_step"] + "\n\n"

        # Ending Creation - note: ending doesn't have decision_summary_for_next_step since it's the last step
        if "ending_creation" in generation_pipeline:
            ending_pattern = generation_pipeline["ending_creation"].get("selected_ending_pattern", "")
            if ending_pattern:
                explanation += f"**Ending:** Completed the post with a '{ending_pattern}' pattern to drive engagement.\n\n"

        explanation += "This coherent approach ensures that each element of the post works together to deliver a compelling message with a distinctive style."

        return explanation

    @staticmethod
    def _infer_funnel_stage(generation_pipeline: Dict[str, Any], content_strategy: Dict[str, Any]) -> str:
        """
        Infer the funnel stage based on the generated content and patterns used.
        
        Args:
            generation_pipeline: The generation pipeline with pattern selections
            content_strategy: User's content strategy
            
        Returns:
            Funnel stage display name (e.g., "Top of Funnel")
        """
        try:
            from src.strategy_config import infer_funnel_stage_from_patterns, infer_funnel_stage_from_goal, FunnelStage
            
            # First try to infer from patterns
            scratchpad = {"generation_pipeline": generation_pipeline}
            funnel_stage = infer_funnel_stage_from_patterns(scratchpad)
            
            # If we got a clear classification, use it
            if funnel_stage.value != "unclassified":
                return funnel_stage.display_name
            
            # Otherwise, try to infer from content goal as fallback
            if content_strategy and isinstance(content_strategy, dict):
                goal_stage = infer_funnel_stage_from_goal(content_strategy.get("content_goal"))
                if goal_stage:
                    return goal_stage.display_name
            
            # Default to the current stage
            return funnel_stage.display_name
        except Exception as e:
            logger.warning(f"Failed to infer funnel stage: {e}")
            # Return a safe default
            return "Top of Funnel"

    @staticmethod
    def format_explanation(reasoning_dict: Dict[str, str]) -> str:
        """
        Legacy method for formatting reasoning into a human-readable explanation.
        This is kept for backward compatibility but will be deprecated.

        Args:
            reasoning_dict: Dictionary with reasoning for each step

        Returns:
            Formatted explanation string
        """
        explanation = "# How This Post Was Created\n\n"


        if "hook" in reasoning_dict:
            explanation += "## Hook Creation\n" + reasoning_dict["hook"] + "\n\n"

        if "body" in reasoning_dict:
            explanation += "## Body Structure & Linguistic Style\n" + reasoning_dict["body"] + "\n\n"

        if "ending" in reasoning_dict:
            explanation += "## Ending Creation\n" + reasoning_dict["ending"] + "\n\n"

        return explanation

    @staticmethod
    def load_prompt_template(filename: str) -> str:
        """
        Load a prompt template from file.

        Args:
            filename: Name of the prompt template file

        Returns:
            Prompt template string
        """
        prompt_path = os.path.join(PROMPTS_DIR, "agent_prompts", filename)
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                prompt = f.read()
            
            # Date is now injected via user prompts instead of system prompt
            # This keeps the system prompt cleaner and reduces redundancy
            
            return prompt
        except FileNotFoundError:
            logger.error(f"Prompt template not found: {prompt_path}")
            return "ERROR: Prompt template not found."

