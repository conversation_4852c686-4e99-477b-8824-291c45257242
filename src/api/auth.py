"""Authentication utilities for the LinkedInsight API."""

import os
import logging
import jwt
from jwt import PyJWKClient
from typing import Dict, Optional, Any, List
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Configure logging
logger = logging.getLogger(__name__)

# Initialize security for Bearer token
security = HTTPBearer()
# Optional security for endpoints that support multiple auth methods
security_optional = HTTPBearer(auto_error=False)

# Get Clerk issuer URL from environment variable (REQUIRED)
# This is the URL of your Clerk instance, e.g. https://your-instance.clerk.accounts.dev
CLERK_ISSUER = os.getenv("CLERK_ISSUER")
if not CLERK_ISSUER:
    raise ValueError("CLERK_ISSUER environment variable is required for JWT validation")

# Get expected audience for JWT validation (optional but recommended)
CLERK_AUDIENCE = os.getenv("CLERK_AUDIENCE", "")

# Get JWKS URL from environment variable or construct it from the issuer
JWKS_URL = os.getenv("CLERK_JWKS_URL", "")
if not J<PERSON><PERSON>_URL and CLERK_ISSUER:
    JWKS_URL = f"{CLERK_ISSUER.rstrip('/')}/.well-known/jwks.json"

# Initialize PyJWT's JWKS client with caching
# This replaces our manual JWKS fetching and caching logic
jwks_client: Optional[PyJWKClient] = None
if JWKS_URL:
    try:
        jwks_client = PyJWKClient(
            JWKS_URL,
            cache_keys=True,  # Enable key caching
            lifespan=3600,    # Cache for 1 hour
            headers={"User-Agent": "LinkedInsight-API/1.0"}  # Optional: identify our service
        )
        logger.info(f"Initialized PyJWKClient with JWKS URL: {JWKS_URL}")
    except Exception as e:
        logger.error(f"Failed to initialize PyJWKClient: {e}")
        raise ValueError(f"Failed to initialize JWKS client: {e}")

# DEPRECATED: Manual JWKS fetching is replaced by PyJWKClient
# This function is kept for backwards compatibility but should not be used
def get_jwks_keys() -> List[Dict[str, Any]]:
    """
    DEPRECATED: Use jwks_client instead.
    
    This function is replaced by PyJWT's PyJWKClient which handles
    caching, key rotation, and JWK-to-PEM conversion automatically.
    """
    logger.warning("get_jwks_keys() is deprecated. Use jwks_client instead.")
    return []

# DEPRECATED: Manual key extraction is replaced by PyJWKClient
# This function is kept for backwards compatibility but should not be used
def get_key_for_token(token: str) -> Optional[str]:
    """
    DEPRECATED: Use jwks_client.get_signing_key_from_jwt() instead.
    
    This function is replaced by PyJWT's PyJWKClient which handles
    key extraction, caching, and JWK-to-PEM conversion automatically.
    """
    logger.warning("get_key_for_token() is deprecated. Use jwks_client.get_signing_key_from_jwt() instead.")
    
    if not jwks_client:
        return None
        
    try:
        signing_key = jwks_client.get_signing_key_from_jwt(token)
        # Return the key in PEM format for backwards compatibility
        if hasattr(signing_key.key, 'public_bytes'):
            from cryptography.hazmat.primitives import serialization
            return signing_key.key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ).decode('utf-8')
        return None
    except Exception as e:
        logger.error(f"Error getting key for token: {str(e)}")
        return None

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Verify the JWT token and return the user information.

    This function is used as a dependency in FastAPI endpoints to authenticate requests.
    """
    token = credentials.credentials

    if not jwks_client:
        logger.error("JWKS client not initialized")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service not configured",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # Use PyJWT's JWKS client to get the signing key
        # This handles key rotation, caching, and JWK-to-PEM conversion automatically
        signing_key = jwks_client.get_signing_key_from_jwt(token)
        
        # Verify the token with strict validation
        decode_options = {
            "verify_signature": True,
            "verify_aud": bool(CLERK_AUDIENCE),  # Verify audience if configured
            "verify_iss": True,  # Always verify issuer
            "verify_iat": True,
            "verify_exp": True,  # Verify expiration
        }
        
        decode_kwargs = {
            "algorithms": ["RS256"],
            "issuer": CLERK_ISSUER,
            "options": decode_options,
            "leeway": 30  # Allow 30 seconds of clock skew
        }
        
        # Add audience if configured
        if CLERK_AUDIENCE:
            decode_kwargs["audience"] = CLERK_AUDIENCE
            
        # Use the key from the signing_key object
        payload = jwt.decode(token, signing_key.key, **decode_kwargs)

        # Log successful authentication
        logger.debug(f"Successfully authenticated user: {payload.get('sub')}")

        # Return the payload which contains user information
        return payload

    except jwt.ExpiredSignatureError:
        # Token has expired
        logger.warning("Authentication failed: Token expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.exceptions.PyJWKClientError as e:
        # JWKS client error (network, parsing, key not found, etc.)
        logger.warning(f"Authentication failed: JWKS error - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: Could not verify signature",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError as e:
        # Token is invalid
        logger.warning(f"Authentication failed: Invalid token - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        # Unexpected error
        logger.error(f"Authentication error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during authentication",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_optional)) -> Optional[Dict[str, Any]]:
    """
    Optional authentication - returns None if no valid auth provided.
    Used for endpoints that support multiple auth methods (header + query param).
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except Exception:
        # Return None instead of raising exception
        return None


async def get_current_user_from_token(token: str) -> Dict[str, Any]:
    """
    Validate a JWT token directly (used for EventSource endpoints).
    
    Args:
        token: JWT token string
        
    Returns:
        User payload from JWT token
        
    Raises:
        HTTPException: If token is invalid
    """
    # Remove "Bearer " prefix if present
    if token.startswith("Bearer "):
        token = token[7:]
    
    # Use the same validation logic as get_current_user
    return await get_current_user(HTTPAuthorizationCredentials(scheme="Bearer", credentials=token))


async def get_current_user_email(current_user: Dict[str, Any] = Depends(get_current_user)) -> str:
    """
    Extract email from authenticated user JWT payload.
    
    Args:
        current_user: User payload from JWT token
        
    Returns:
        User's email address
        
    Raises:
        HTTPException: If email is not found in token
    """
    # Log minimal authentication info without exposing PII
    logger.debug(f"User authenticated: sub={current_user.get('sub')}")
    
    email = current_user.get("email")
    if not email:
        logger.error(f"User {current_user.get('sub')} is authenticated but has no email address")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User email not found in token"
        )
    
    return email
