"""FastAPI application for LinkedInsight API."""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Path as FastAPIPath, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any
import os
import sys
import logging
from pathlib import Path as FilePath
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = FilePath(__file__).parent.parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.config import Config
from src.api.schemas import HealthResponse
from src.utils.mongo_utils_async import setup_async_mongo_indexes

# Import Routers
from src.api.routers import retrieval as retrieval_router
from src.api.routers import agent as agent_router
from src.api.routers import export as export_router
from src.api.routers import content_strategy as content_strategy_router
from src.api.routers import content_preferences as content_preferences_router
# from src.api.routers import creator_matcher as creator_matcher_router  # deprecated
from src.api.routers import interview as interview_router
from src.api.routers import interview_stream as interview_stream_router
from src.api.routers import transcripts as transcripts_router
from src.api.routers import ideas as ideas_router
from src.api.routers import editor as editor_router
from src.api.routers import content_generation_sessions as content_generation_sessions_router
from src.api.routers import draft_library as draft_library_router
from src.api.routers import config as config_router
from src.api.routers import file_parser as file_parser_router
from src.api.routers import user_agreements as user_agreements_router
from src.api.routers import user_feedback as user_feedback_router

# Initialize HTTP bearer token scheme for JWT tokens
security = HTTPBearer()

# Initialize FastAPI app
app = FastAPI(
    title="LinkedInsight API",
    description="API for LinkedIn content analysis",
    version="0.1.0",
)

# Get frontend URL from environment variable
FRONTEND_URL = os.getenv("FRONTEND_URL", "")
FRONTEND_URLS = []

# Add the frontend URL if it's set
if FRONTEND_URL:
    FRONTEND_URLS.append(FRONTEND_URL)

# Add localhost URLs for development
FRONTEND_URLS.extend([
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:5176",
    "http://localhost:5177",
])

# Add Vercel URLs
FRONTEND_URLS.extend([
    "https://linkedinsight-viewer.vercel.app",
    "https://*.vercel.app",
    "https://linkedinsight-viewer-cd1gyy4mv-benawises-projects.vercel.app",
    "https://linkedinsight-viewer-cppe5wsmt-benawises-projects.vercel.app",
    "https://linkedinsight-viewer-nehvoseyx-benawises-projects.vercel.app",
    "https://linkedinsight-viewer-bjvsmeydk-benawises-projects.vercel.app"
])

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info(f"Incoming request: {request.method} {request.url}")
    logger.info(f"Headers: {dict(request.headers)}")
    response = await call_next(request)
    logger.info(f"Response status: {response.status_code}")
    return response

# ----------------------------------
# Configure CORS
# ----------------------------------
# In production we may deploy the frontend under dynamic Vercel preview URLs
# (e.g. https://linkedinsight-viewer-abc123.vercel.app). FastAPI's CORS middleware
# does not support wildcard matching in the `allow_origins` list, so we provide
# the known domains in `allow_origins` *and* a regex that matches any Vercel
# sub-domain.  This guarantees that both our production domain and preview builds
# are accepted during the pre-flight CORS check.

app.add_middleware(
    CORSMiddleware,
    allow_origins=FRONTEND_URLS,          # Explicitly allow known domains
    allow_origin_regex=r"https://.*\.vercel\.app",  # Any Vercel sub-domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],                # Accept all request headers, including Authorization
)

# Log the effective CORS configuration for debugging purposes
logger.info("CORS middleware configured")
logger.info(f"Allowed origins: {FRONTEND_URLS}")
logger.info("Allowed origin regex: https://.*\\.vercel\\.app")

# Add Braintrust middleware for request-level tracing
from src.api.middleware.braintrust_middleware import braintrust_middleware
app.middleware("http")(braintrust_middleware)
logger.info("Braintrust middleware configured")

# Include routers
app.include_router(content_generation_sessions_router.router)
app.include_router(retrieval_router.router)
app.include_router(agent_router.router)
app.include_router(export_router.router)
app.include_router(content_strategy_router.router)
app.include_router(content_preferences_router.router)
# app.include_router(creator_matcher_router.router)  # deprecated
app.include_router(interview_router.router)
app.include_router(interview_stream_router.router)
app.include_router(transcripts_router.router)
app.include_router(ideas_router.router)
app.include_router(editor_router.router, prefix="/api/agent/editor", tags=["editor"])
app.include_router(draft_library_router.router)
app.include_router(config_router.router)
app.include_router(file_parser_router.router)
app.include_router(user_agreements_router.router, prefix="/api/user", tags=["user"])
app.include_router(user_feedback_router.router)

# Custom exception handler to ensure CORS headers are included in error responses
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions and ensure CORS headers are included."""
    origin = request.headers.get("origin", "")
    
    # Determine if the origin is allowed
    allowed = False
    if origin in FRONTEND_URLS:
        allowed = True
    elif origin and origin.endswith(".vercel.app"):
        allowed = True
    
    headers = {}
    if allowed:
        headers = {
            "Access-Control-Allow-Origin": origin,
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Authorization, Content-Type, Accept",
        }
    
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=headers
    )

# OPTIONS handler for preflight requests
@app.options("/{rest_of_path:path}")
async def preflight_handler(request: Request, rest_of_path: str):
    """Handle preflight OPTIONS requests."""
    origin = request.headers.get("origin", "")
    
    # Determine if the origin is allowed
    allowed = False
    if origin in FRONTEND_URLS:
        allowed = True
    elif origin and origin.endswith(".vercel.app"):
        allowed = True
    
    if allowed:
        return JSONResponse(
            content={"message": "OK"},
            headers={
                "Access-Control-Allow-Origin": origin,
                "Access-Control-Allow-Credentials": "true",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
                "Access-Control-Allow-Headers": "Authorization, Content-Type, Accept",
            }
        )
    else:
        return JSONResponse(
            content={"message": "Origin not allowed"},
            status_code=403
        )

# Setup MongoDB indexes on startup
@app.on_event("startup")
async def startup_db_client():
    """Initialize MongoDB indexes on startup."""
    try:
        logger.info("Setting up MongoDB indexes (async)...")
        # Prefer async index creation to avoid blocking.
        await setup_async_mongo_indexes()
        logger.info("MongoDB indexes setup complete.")
    except Exception as e:
        logger.error(f"Error setting up MongoDB indexes: {e}")
        # Don't fail startup if MongoDB setup fails
        # This allows the API to still function with file-based fallback

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Check the API health status."""
    return {
        "status": "ok",
        "version": "0.1.0",
        "endpoints": [
            "/api/creators",
            "/api/creator/{creator_name}",
            "/api/creator/{creator_name}/summary-stats",
            "/api/job/{job_id}",
            "/api/results/{analysis_type}/{creator_name}",
            "/api/markdown/{analysis_type}/{creator_name}",
            "/api/analyze/{analysis_type}/{creator_name}",
            "/api/agent/generate",
            "/api/agent/job/{job_id}",
            "/api/export/pdf/{creator_name}",
            "/api/creator-matcher",
            "/api/interview/turn",
            "/api/ideas/extract",
            "/api/ideas/status/{job_id}",
            "/api/transcripts/",
            "/api/transcripts/{transcript_id}"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True)