# LinkedInsight TODO List

This document tracks active development tasks for the LinkedInsight project, organized by functional area.

## Core Infrastructure

- [ ] **Analysis Pipeline Improvements**
  - [ ] Modify run_all_analyses.py to automatically update the frontend creators list
  - [ ] Add gisenberg to the creators.json file for immediate testing
  - [ ] Fix naming inconsistency in LLM-friendly analysis prompts (markdown files in LLM_JSON_analyses_prompts directory)
- [ ] **Testing**
  - [ ] Test the agent implementation with various input types
- [ ] **Observability**
  - [ ] Add Logfire instrumentation for LLM monitoring
  - [ ] Instrument FastAPI endpoints to track performance
  - [ ] Instrument MongoDB operations to monitor database performance

## Agent Features

- [ ] **Model Selection**
  - [ ] Add toggle to switch agent between Claude 3.7 and Gemini 2.5 Pro
  - [ ] Implement backend support for multiple model providers
  - [ ] Add model selection UI component

- [ ] **Unified Agent**
  - [ ] Use MongoDB's native Voyager AI embedding model plus BM25
  - [ ] Implement similarity search to find top three most relevant creators by themes/style/voice
  - [ ] Create API endpoint for unified agent
  - [ ] Update frontend to support unified agent mode

- [ ] **Interviewing Agent**
  - [ ] Start with <PERSON> using detailed system/user prompts
  - [ ] Implement two-pane UI (chat conversation and live brief draft)
  - [ ] Design backend API endpoints for interview agent
  - [ ] Implement stateful conversation handling

## User Experience

- [ ] **Progress Feedback**
  - [ ] Add text to progress animation (re: which step it's in/what it's doing)
- [ ] **Content Management**
  - [ ] Add ability to edit generated posts before saving
- [ ] **Analytics**
  - [ ] Implement analytics to track agent usage and performance

## Quality Improvements

- [ ] **Hallucination Reduction**
  - [ ] Implement better prompt engineering with explicit factual constraints
  - [ ] Create a critic/editor agent to review and fact-check generated content
  - [ ] Add verification against creator's actual content

- [ ] **Research Capabilities**
  - [ ] Integrate with Perplexity API for fact-checking and research
  - [ ] Implement a tool interface for the agent to request information
  - [ ] Add citation capabilities to reference sources

- [ ] **Interactive Questioning**
  - [ ] Define a taxonomy of appropriate question types
  - [ ] Implement logic for when questions are appropriate vs. when to proceed
  - [ ] Create guidelines for question format and specificity

## Technical Architecture

- [ ] **Process Transparency**
  - [ ] Implement detailed logging of agent's thinking and editing process
  - [ ] Convert scratchpad into a versioned document with history
  - [ ] Record thinking processes alongside content changes
  - [ ] Implement string manipulation tools (search, replace, insert, delete)
  - [ ] Create a visualization of the agent's editing process
  - [ ] Show user the post as it gets constructed by the agent in real-time

- [ ] **Performance Optimization**
  - [ ] Refine agent prompts based on thinking token analysis
  - [ ] Implement separated concerns architecture for agent (reasoning vs. draft content)
  - [ ] Add more comprehensive testing for agent with diverse inputs
  - [ ] Optimize caching implementation for better token savings

## Development Guidelines

- Keep implementations as simple as possible
- Focus on the basic input → process → output flow
- Ensure clear loading states and error messages
- Maintain proper authentication headers in API requests
- Provide clear error feedback to users
- Consider potential delays in API responses
