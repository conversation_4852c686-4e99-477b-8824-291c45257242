import React, { useEffect, useState, useRef } from 'react';
import { AsanaTaskCreator } from './asana/AsanaTaskCreator';
import { <PERSON><PERSON>, LoadingSpinner } from '../ui/primitives';
import { toast } from 'sonner';
import { transcriptApi } from '../../services/api';
import { StructuredBriefRenderer } from './StructuredBriefRenderer';
import {
  formatContentForAsana,
  copyBriefRich,
} from '../../utils/briefCopy';

interface StreamingBriefProps {
  sessionId: string;
  isDevMode: boolean;
  title: string;
  projectId?: string; // Make projectId optional
  isAlphaUser?: boolean; // Add isAlphaUser prop
  transcriptId?: string; // Add transcriptId prop
  onGenerated?: () => void; // Single callback for when action completes (marks generated AND navigates)
  onBack: () => void; // Add onBack prop for navigation
}

type Block = {
  type: 'text' | 'citation' | 'thinking';
  content: string;
}

// Mock content for development mode
const mockBlocks: Block[] = [
  {
    type: 'text',
    content: '# Introduction & Context\n\nThis is a mock brief to demonstrate the styling and layout of the content. The brief will contain both regular text blocks and citations.'
  },
  {
    type: 'thinking',
    content: JSON.stringify([
      "Analyzing the transcript for key points and structure...",
      "Looking for themes about data analytics and enterprise clients."
    ])
  },
  {
    type: 'text',
    content: '\n\n## Key Points\n\n1. First major point about the topic'
  },
  {
    type: 'citation',
    content: 'This is a citation from the transcript that supports the first point. It demonstrates how quoted content appears in the brief.'
  },
  {
    type: 'text',
    content: '\n2. Second important consideration\n3. Critical context for understanding'
  },
  {
    type: 'thinking',
    content: JSON.stringify([
      "Identifying key points about visualization capabilities and user experience.",
      "Need to elaborate on technical requirements from transcript."
    ])
  },
  {
    type: 'text',
    content: '\n\n## Analysis\n\nThe analysis section demonstrates how longer blocks of text are formatted. It includes multiple paragraphs to show spacing and flow.\n\nThis second paragraph contains more detailed information and helps visualize how content is structured within the brief.'
  },
  {
    type: 'citation',
    content: 'Here\'s another citation that provides evidence for the analysis. This one is a bit longer to show how longer citations are handled in the layout.'
  }
];

/**
 * StreamingBrief - Displays and manages streaming content from the API
 *
 * Handles real-time content streaming via server-sent events using the shared Citation component.
 * Captures and displays Claude's thinking process through a collapsible toggle interface.
 * Provides functionality to push completed briefs to Asana. After successful push to Asana,
 * navigates directly to the Ideas Board.
 *
 * Features:
 * - Real-time content streaming with citation support
 * - Claude thinking toggle with expandable view (new)
 * - Asana integration for creating tasks
 */
export function StreamingBrief({
  sessionId,
  isDevMode,
  title,
  projectId,
  isAlphaUser = false,
  transcriptId = '',
  onGenerated,
  onBack
}: StreamingBriefProps) {
  const [blocks, setBlocks] = useState<Block[]>(isDevMode ? mockBlocks : []);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isStreamingComplete, setIsStreamingComplete] = useState(false);
  const [taskCreated, setTaskCreated] = useState(false);
  const [saveAttempted, setSaveAttempted] = useState(false);

  useEffect(() => {
    if (isDevMode) {
      // In dev mode, simulate streaming completion
      setTimeout(() => {
        setIsStreaming(true);
        setIsStreamingComplete(true);
        // Use the predefined mockBlocks which already include thinking blocks
      }, 1500);
      return;
    }

    const es = new EventSource(`/api/generate-brief/stream/${sessionId}`);

    es.addEventListener('content_block_delta', (e) => {
      if (!isStreaming) setIsStreaming(true);

      const data = JSON.parse(e.data);

      if (data.delta.type === 'text_delta') {
        const text = data.delta.text || '';
        setBlocks(prev => {
          const lastBlock = prev[prev.length - 1];

          // If last block is text, append to it
          if (lastBlock?.type === 'text') {
            return [
              ...prev.slice(0, -1),
              { ...lastBlock, content: lastBlock.content + text }
            ];
          }

          // Otherwise create new text block
          return [...prev, { type: 'text', content: text }];
        });
      }
      else if (data.delta.type === 'citations_delta') {
        const citedText = data.delta.citation.cited_text || '';
        setBlocks(prev => [...prev, { type: 'citation', content: citedText }]);
      }
    });

    // Handle thinking events - add them inline as thinking blocks
    es.addEventListener('thinking', (e) => {
      const data = JSON.parse(e.data);
      if (data.type === 'thinking' && data.thinking) {
        // Process thinking content - split into sentences if it's a large chunk
        const processThinkingChunk = (chunk: string) => {
          const trimmed = chunk.trim();
          if (!trimmed) return [];

          // If it's a very long chunk, try to split it into sentences
          if (trimmed.length > 100) {
            const sentences = trimmed.split(/(?<=[.!?])\s+/);
            return sentences.filter(s => s.trim().length > 0);
          }

          return [trimmed];
        };

        // Get processed thinking chunks
        const newChunks = processThinkingChunk(data.thinking);

        // Filter out duplicates and empty chunks
        const uniqueNewChunks = newChunks.filter(chunk => chunk.length > 0);

        if (uniqueNewChunks.length > 0) {
          // Add as a thinking block in the main content stream
          setBlocks(prev => [...prev, { type: 'thinking', content: JSON.stringify(uniqueNewChunks) }]);
        }
      }
    });

    es.addEventListener('message_stop', () => {
      setIsStreamingComplete(true);
      es.close();
    });

    return () => {
      es.close();
    };
  }, [sessionId]);

  // Effect to SAVE brief when streaming completes (DO NOT mark generated here)
  useEffect(() => {
    if (isStreamingComplete && !isDevMode && !saveAttempted && transcriptId && transcriptId.trim().length > 0) {
      console.log('[StreamingBrief] Stream complete. Triggering save ONLY.');
      setSaveAttempted(true);
      saveBriefToDatabase(blocks);
    }
  }, [isStreamingComplete, isDevMode, blocks, saveAttempted, transcriptId]); // Removed callback from deps

  // Function to save the brief to MongoDB
  const saveBriefToDatabase = async (currentBlocks: Block[]) => {
    if (isDevMode) {
      return;
    }

    try {
      if (!currentBlocks || currentBlocks.length === 0) {
        return;
      }

      const payload = {
        brief_blocks: currentBlocks, // Send the array of Block objects
        idea_id: sessionId, // Using sessionId as idea_id
        idea_title: title,
        project_id: projectId,
        outline_title: 'Generated Brief', // Default title
        transcript_id: transcriptId // Include the transcript_id
      };

      const response = await fetch('/api/briefs/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to save brief: ${response.statusText}`);
      }

      const responseData = await response.json();
    } catch (error) {
    }
  };

  // Formatted content utilities (delegated to shared helpers)
  const getBriefContentForAsana = () => formatContentForAsana(blocks);

  // Handle successful Asana task creation
  const handleTaskSuccess = (_taskUrl: string) => {
    setTaskCreated(true);
    // Call the main completion handler (marks generated AND navigates)
    if (onGenerated) {
      console.log("[StreamingBrief] Asana success, calling onGenerated...");
      onGenerated();
    }
  };

  // Alpha user actions
  const handleCopyToClipboard = async () => {
    try {
      await copyBriefRich(blocks);
      toast.success("Successfully copied to clipboard (with formatting)");
      setTimeout(() => setTaskCreated(true), 0);
      if (onGenerated) onGenerated();
    } catch (err) {
      toast.error("Failed to copy to clipboard");
    }
  };

  const handleDownloadMarkdown = () => {
    // ... (download logic) ...
    setTaskCreated(true);
    // Call the main completion handler (marks generated AND navigates)
    if (onGenerated) {
      console.log("[StreamingBrief] Markdown download success, calling onGenerated...");
      onGenerated();
    }
  };

  const handleDownloadDocx = async () => {
    try {
      // ... (API call & toast) ...
      setTaskCreated(true);
      // Call the main completion handler (marks generated AND navigates)
      if (onGenerated) {
        console.log("[StreamingBrief] Docx download success, calling onGenerated...");
        onGenerated();
      }
    } catch (error) { /* ... */ }
  };

  return (
    <div className="flex flex-col h-full font-archivo overflow-hidden">
      {/* Loading indicator */}
      {!isStreaming && !isStreamingComplete && (
        <div>
          <LoadingSpinner
            size="md"
            primaryText="Generating your brief..."
            secondaryText="Connecting to stream..."
          />
        </div>
      )}

      {/* Main content display - Make this area grow and scroll */}
      <div className="flex-grow overflow-y-auto pr-2">
        {(isStreaming || isStreamingComplete) && (
          <>
            <StructuredBriefRenderer blocks={blocks} />
          </>
        )}
      </div>

      {/* Action buttons container - Keep fixed at bottom */}
      {isStreamingComplete && (
        <div className="space-y-3 flex-shrink-0 pt-4">
          {/* Asana Task Creator (Non-Alpha) */}
          {!taskCreated && !isAlphaUser && projectId && (
            <AsanaTaskCreator
              projectId={projectId}
              title={title}
              content={getBriefContentForAsana()}
              isDevMode={isDevMode}
              onSuccess={handleTaskSuccess}
            />
          )}

          {/* Action Buttons (Alpha Users) */}
          {!taskCreated && isAlphaUser && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 w-full">
              <Button
                onClick={handleCopyToClipboard}
                variant="primary"
                size="lg"
                icon={<span role="img" aria-label="clipboard" className="text-lg">📋</span>}
              >
                Copy
              </Button>

              <Button
                onClick={handleDownloadMarkdown}
                variant="primary"
                size="lg"
                icon={<span role="img" aria-label="download" className="text-lg">📥</span>}
              >
                Markdown
              </Button>

              <Button
                onClick={handleDownloadDocx}
                variant="primary"
                size="lg"
                icon={<span role="img" aria-label="word" className="text-lg">📄</span>}
              >
                Word
              </Button>
            </div>
          )}

          {/* Back Button (Common) - Moved inside the container, rendered when streaming is complete */}
          <Button
            onClick={onBack}
            variant="primary" // Changed to primary
            size="lg"
            fullWidth
          >
            Back to Outline Selection
          </Button>
        </div>
      )}
    </div>
  );
}
