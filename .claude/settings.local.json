{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_navigate", "WebFetch(domain:docs.anthropic.com)", "Bash(find:*)", "mcp__puppeteer__puppeteer_fill", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"brief\" /Users/<USER>/LinkedInsight/linkedinsight-viewer/src/components/ContentAgentPage.tsx)", "Bash(rg:*)", "WebFetch(domain:elevenlabs.io)", "WebFetch(domain:clerk.com)", "Bash(npm search:*)", "Bash(npm view:*)", "Bash(npm update:*)", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:www.adaline.ai)", "Bash(rm:*)", "mcp__ide__getDiagnostics", "Bash(npm run typecheck:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(python:*)", "mcp__gemini__think_deeper", "Bash(pip install:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["puppeteer"]}