# TanStack Query Migration Guide

## Overview

We've adopted TanStack Query (React Query) to standardize data fetching, caching, and async state management across the application. This migration eliminates ~500+ lines of boilerplate code and provides better performance through intelligent caching.

## Key Benefits

1. **Automatic caching** - Data is cached and reused across components
2. **Background refetching** - Stale data is updated seamlessly
3. **Request deduplication** - Multiple components requesting the same data share a single request
4. **Optimistic updates** - Mutations can update the UI before the server responds
5. **Built-in retry logic** - Failed requests retry automatically with exponential backoff
6. **DevTools** - Inspect cache state and debug queries easily

## Architecture

### 1. QueryClient Configuration
Located in `src/lib/query-client.ts`:
- 5-minute stale time (data considered fresh)
- 10-minute cache time (data kept in memory)
- Smart retry logic (no retries for 4xx errors)
- Disabled refetch on window focus (configurable)

### 2. Core Components

#### `useApiQuery` Hook
A wrapper around TanStack Query's `useQuery` that integrates with our `apiClient`:

```typescript
const { data, isLoading, isError, error, refetch } = useApiQuery(
  ['resource', id],                    // Query key for caching
  `/api/v1/resource/${id}`,           // API endpoint
  {
    mockData: mockResourceData,        // Optional mock data for dev
    staleTime: 5 * 60 * 1000,         // Override default stale time
    select: (data) => transform(data), // Transform response data
  }
);
```

#### `AsyncBoundary` Component
A declarative component for handling loading/error/empty states:

```typescript
<AsyncBoundary
  isLoading={isLoading}
  isError={isError}
  error={error}
  isEmpty={checkIsEmpty(data)}
  onRetry={refetch}
>
  {/* Your content here - only renders in success state */}
  <DataList items={data} />
</AsyncBoundary>
```

## Migration Steps

### Step 1: Replace Custom Hook

**Before:**
```typescript
export function useTranscriptIdeas(transcriptId: string | null) {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await apiClient.get(`/api/v1/transcript/${transcriptId}`);
        setData(response);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (transcriptId) {
      fetchData();
    }
  }, [transcriptId]);
  
  return { data, isLoading, error };
}
```

**After:**
```typescript
export function useTranscriptIdeasQuery(transcriptId: string | null) {
  const { data, isLoading, isError, error, refetch } = useApiQuery(
    ['transcript', transcriptId || 'none'],
    transcriptId ? `/api/v1/transcript/${transcriptId}` : null,
    {
      mockData: mockTranscriptData,
      select: (data) => ({
        ideas: data?.ideas?.map(transformBackendIdea) || [],
        generatedIdeas: data?.generated_ideas?.map(transformBackendIdea) || [],
      }),
    }
  );
  
  return {
    ideas: data?.ideas || [],
    generatedIdeas: data?.generatedIdeas || [],
    isLoading,
    isError,
    error,
    refetch,
  };
}
```

### Step 2: Update Component

**Before:**
```typescript
export function IdeasPage() {
  const { ideas, isLoading, error } = useTranscriptIdeas(transcriptId);
  
  if (isLoading) {
    return (
      <Container>
        <div className="text-center py-8">
          <p className="text-gray-600">Loading...</p>
        </div>
      </Container>
    );
  }
  
  if (error) {
    return (
      <Container>
        <div className="text-center py-8">
          <p className="text-red-600">{error.message}</p>
        </div>
      </Container>
    );
  }
  
  return (
    <Container>
      {ideas.map(idea => <IdeaCard key={idea.id} idea={idea} />)}
    </Container>
  );
}
```

**After:**
```typescript
export function IdeasPage() {
  const { ideas, isLoading, isError, error, refetch } = useTranscriptIdeasQuery(transcriptId);
  
  return (
    <Container>
      <AsyncBoundary
        isLoading={isLoading}
        isError={isError}
        error={error}
        isEmpty={ideas.length === 0}
        onRetry={refetch}
      >
        {ideas.map(idea => <IdeaCard key={idea.id} idea={idea} />)}
      </AsyncBoundary>
    </Container>
  );
}
```

## Migration Checklist

For each component:

- [ ] Identify data fetching hooks/logic
- [ ] Create or update hook to use `useApiQuery`
- [ ] Replace loading/error state renders with `AsyncBoundary`
- [ ] Remove manual state management code
- [ ] Test loading, error, and success states
- [ ] Verify mock mode still works

## Common Patterns

### 1. Conditional Fetching
```typescript
// Only fetch when userId is available
const { data } = useApiQuery(
  ['user', userId],
  userId ? `/api/v1/users/${userId}` : null
);
```

### 2. Dependent Queries
```typescript
// Fetch user first
const { data: user } = useApiQuery(['user'], '/api/v1/user');

// Then fetch preferences
const { data: prefs } = useApiQuery(
  ['preferences', user?.id],
  user ? `/api/v1/users/${user.id}/preferences` : null
);
```

### 3. Polling
```typescript
const { data } = useApiQuery(
  ['notifications'],
  '/api/v1/notifications',
  {
    refetchInterval: 30000, // Poll every 30 seconds
  }
);
```

### 4. Custom Loading/Error UI
```typescript
<AsyncBoundary
  isLoading={isLoading}
  isError={isError}
  error={error}
  loadingComponent={<CustomSpinner />}
  errorComponent={<CustomError error={error} />}
>
  {/* Content */}
</AsyncBoundary>
```

## Components to Migrate

Priority order based on complexity and code duplication:

1. **High Priority** (Most duplication):
   - [x] DraftLibraryPage ✓
   - [x] ContentPreferencesForm ✓
   - [x] ContentStrategyForm ✓
   - [x] TranscriptInputPage ✓

2. **Medium Priority**:
   - [ ] ContentAgentPage
   - [ ] NotesInputPage
   - [ ] UserPreferencesPage
   - [ ] InterviewingAgentView

3. **Low Priority** (Less async logic):
   - [ ] Home
   - [ ] QuickStartGuidePage
   - [ ] EditorAgentPage

## Mutations (Coming Soon)

For POST/PUT/DELETE operations, use `useMutation`:

```typescript
const { mutate: updateIdea } = useMutation({
  mutationFn: (data) => apiClient.put(`/api/v1/ideas/${id}`, data),
  onSuccess: () => {
    // Invalidate and refetch
    queryClient.invalidateQueries(['ideas']);
  },
});
```

## Tips

1. **Query Keys**: Keep them hierarchical and consistent
   - `['users']` - all users
   - `['users', userId]` - specific user
   - `['users', userId, 'posts']` - user's posts

2. **Error Handling**: Let TanStack Query handle retries before showing errors

3. **DevTools**: Use React Query DevTools in development to inspect cache state

4. **Performance**: Use `select` to transform data at the query level, not in components

## Next Steps

1. Start with high-priority components
2. Share learnings and patterns with the team
3. Consider adding mutations for write operations
4. Explore optimistic updates for better UX