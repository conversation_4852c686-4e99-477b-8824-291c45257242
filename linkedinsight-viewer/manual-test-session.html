<!DOCTYPE html>
<html>
<head>
    <title>Session Manager Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        .pass { background: #dfd; }
        .fail { background: #fdd; }
        button { margin: 5px; padding: 5px 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Session Manager Manual Test</h1>
    
    <div class="test">
        <h3>1. Create Session</h3>
        <button onclick="createSession()">Create New Session</button>
        <div id="session-id"></div>
    </div>

    <div class="test">
        <h3>2. Update Session</h3>
        <button onclick="updateSession()">Update with Test Data</button>
        <div id="update-result"></div>
    </div>

    <div class="test">
        <h3>3. Check URL</h3>
        <button onclick="checkUrl()">Check URL Parameter</button>
        <div id="url-result"></div>
    </div>

    <div class="test">
        <h3>4. Check LocalStorage</h3>
        <button onclick="checkStorage()">Check Storage</button>
        <pre id="storage-result"></pre>
    </div>

    <div class="test">
        <h3>5. Simulate Navigation</h3>
        <button onclick="simulateNav()">Navigate to /ideas</button>
        <button onclick="goBack()">Go Back</button>
        <div id="nav-result"></div>
    </div>

    <script type="module">
        // Import session manager (adjust path as needed)
        import sessionManager from './src/services/SimpleSessionManager.js';
        
        window.sessionManager = sessionManager;
        window.currentSessionId = null;

        window.createSession = async () => {
            try {
                const id = await sessionManager.getOrCreate();
                window.currentSessionId = id;
                document.getElementById('session-id').innerHTML = `✅ Created: ${id}`;
            } catch (error) {
                document.getElementById('session-id').innerHTML = `❌ Error: ${error.message}`;
            }
        };

        window.updateSession = async () => {
            if (!window.currentSessionId) {
                document.getElementById('update-result').innerHTML = '❌ No session created yet';
                return;
            }
            
            try {
                await sessionManager.update(window.currentSessionId, {
                    brief: 'Test brief content',
                    postLength: 'medium',
                    flow: 'transcript'
                });
                document.getElementById('update-result').innerHTML = '✅ Updated successfully';
            } catch (error) {
                document.getElementById('update-result').innerHTML = `❌ Error: ${error.message}`;
            }
        };

        window.checkUrl = () => {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session');
            document.getElementById('url-result').innerHTML = sessionId 
                ? `✅ Session in URL: ${sessionId}` 
                : '❌ No session in URL';
        };

        window.checkStorage = async () => {
            if (!window.currentSessionId) {
                document.getElementById('storage-result').textContent = 'No session created yet';
                return;
            }
            
            const session = await sessionManager.load(window.currentSessionId);
            document.getElementById('storage-result').textContent = JSON.stringify(session, null, 2);
        };

        window.simulateNav = () => {
            if (!window.currentSessionId) {
                document.getElementById('nav-result').innerHTML = '❌ No session created yet';
                return;
            }
            
            sessionManager.navigate(window.currentSessionId, '/ideas', { step: 'ideas' });
        };

        window.goBack = async () => {
            if (!window.currentSessionId) {
                document.getElementById('nav-result').innerHTML = '❌ No session created yet';
                return;
            }
            
            const session = await sessionManager.load(window.currentSessionId);
            const prevPath = sessionManager.getPreviousPath(session);
            if (prevPath) {
                sessionManager.navigate(window.currentSessionId, prevPath);
            } else {
                document.getElementById('nav-result').innerHTML = '❌ No previous path';
            }
        };
    </script>
</body>
</html>