{"name": "linkedinsight-viewer", "private": true, "version": "0.1.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --cache --report-unused-disable-directives", "lint:fix": "eslint . --cache --fix", "preview": "vite preview", "test": "vitest", "test:watch": "vitest watch", "coverage": "vitest run --coverage"}, "dependencies": {"@clerk/clerk-react": "^5.30.0", "@clerk/themes": "^2.2.46", "@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/react": "^7.101.1", "@sentry/tracing": "^7.101.1", "@sentry/vite-plugin": "^3.3.1", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@types/marked": "^5.0.2", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.4", "diff": "^8.0.2", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.288.0", "marked": "^15.0.7", "next-themes": "^0.4.4", "posthog-js": "^1.254.0", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-markdown": "^10.0.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.2", "recharts": "^2.15.1", "remark-directive": "^4.0.0", "remark-gfm": "^4.0.0", "sonner": "^2.0.3", "tailwind-merge": "^1.14.0", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@clerk/types": "^4.59.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/diff": "^7.0.2", "@types/node": "^20.6.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "@vitejs/plugin-react": "^4.0.4", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-define-config": "^2.1.0", "eslint-plugin-react": "^7.34.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^26.1.0", "postcss": "^8.4.30", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^6.3.3", "vitest": "^3.1.4"}}