/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['selector', '[data-theme="dark"]'], // Use only data-theme attribute
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // Font families using design system tokens
      fontFamily: {
        base: ["var(--font-family-base)"],
        heading: ["var(--font-family-heading)"],
        mono: ["var(--font-family-mono)"],
        // Keep the existing classes for backward compatibility
        archivo: ["Archivo", "sans-serif"],
        "ibm-plex-mono": ["IBM Plex Mono", "monospace"],
      },
      // Colors using design system tokens
      colors: {
        // Surface colors
        surface: {
          primary: "var(--surface-primary)",
          secondary: "var(--surface-secondary)",
          tertiary: "var(--surface-tertiary)",
          input: "var(--surface-input)",
          "input-hover": "var(--surface-input-hover)",
          "input-focus": "var(--surface-input-focus)",
        },
        // Text colors
        text: {
          primary: "var(--text-primary)",
          secondary: "var(--text-secondary)",
          tertiary: "var(--text-tertiary)",
          "on-primary": "var(--text-on-primary)",
          "on-secondary": "var(--text-on-secondary)",
        },
        // Border colors
        border: {
          DEFAULT: "var(--border-primary)", // For backward compatibility
          primary: "var(--border-primary)",
          subtle: "var(--border-subtle)",
          input: "var(--border-input)",
          "input-focus": "var(--border-input-focus)",
        },
        // Component-specific colors
        button: {
          "primary-bg": "var(--button-primary-bg)",
          "primary-text": "var(--button-primary-text)",
          "secondary-bg": "var(--button-secondary-bg)",
          "secondary-text": "var(--button-secondary-text)",
        },
        // Keep the existing colors for backward compatibility
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      // Border radius using design system tokens
      borderRadius: {
        // New classes using design system tokens
        "ds-sm": "var(--radius-sm)",
        "ds-md": "var(--radius-md)",
        "ds-lg": "var(--radius-lg)",
        // Keep the existing classes for backward compatibility
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      // Spacing using design system tokens
      spacing: {
        "space-1": "var(--space-1)",
        "space-2": "var(--space-2)",
        "space-3": "var(--space-3)",
        "space-4": "var(--space-4)",
        "space-5": "var(--space-5)",
        "space-6": "var(--space-6)",
        "space-8": "var(--space-8)",
        "space-10": "var(--space-10)",
        "space-12": "var(--space-12)",
      },
      // Animation tokens
      transitionProperty: {
        "ds-fast": "var(--transition-fast)",
        "ds-normal": "var(--transition-normal)",
        "ds-slow": "var(--transition-slow)",
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
      },
    },
  },
  plugins: [],
}