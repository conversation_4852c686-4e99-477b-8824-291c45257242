<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedInsight Creative Home Page Concepts</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600&family=Archivo:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* Design System Tokens */
        :root {
            --surface-window: rgb(237, 237, 237);
            --surface-primary: hsl(0, 0%, 100%);
            --surface-secondary: hsl(0, 0%, 96.1%);
            --text-primary: hsl(0, 0%, 3.9%);
            --text-secondary: hsl(0, 0%, 45.1%);
            --text-tertiary: hsl(0, 0%, 65%);
            --border-primary: hsl(0, 0%, 0%);
            --border-subtle: hsl(0, 0%, 89.8%);
            --font-family-mono: 'IBM Plex Mono', monospace;
            --font-family-heading: 'Archivo', sans-serif;
            --shadow-offset: 7px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: var(--surface-window);
            padding: 2rem;
            background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
            background-size: 20px 20px;
            overflow-x: hidden;
        }

        .demo-section {
            margin-bottom: 6rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .demo-title {
            font-family: var(--font-family-heading);
            font-size: 1.5rem;
            margin-bottom: 3rem;
            text-align: center;
            color: var(--text-tertiary);
            font-weight: 400;
        }

        /* Base components */
        .shadow-box {
            background-color: var(--surface-primary);
            border: 2px solid var(--border-primary);
            box-shadow: var(--shadow-offset) var(--shadow-offset) 0px 0px hsl(0, 0%, 30%);
            transition: all 150ms ease;
            cursor: pointer;
        }

        .shadow-box:hover {
            transform: translate(-2px, -2px);
            box-shadow: 9px 9px 0px 0px hsl(0, 0%, 30%);
        }

        .subtle-box {
            background-color: var(--surface-primary);
            border: 1px solid var(--border-subtle);
            transition: all 150ms ease;
            cursor: pointer;
        }

        .subtle-box:hover {
            border-color: var(--border-primary);
            transform: translateY(-2px);
        }

        .icon {
            font-size: 2rem;
            filter: grayscale(100%);
            opacity: 0.8;
        }

        .title {
            font-family: var(--font-family-heading);
            font-size: 2.5rem;
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.1;
        }

        .subtitle {
            font-family: var(--font-family-mono);
            font-size: 1rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        /* Design 1: Overlapping Cards */
        .design1 {
            position: relative;
            max-width: 700px;
            margin: 0 auto;
            height: 500px;
        }

        .design1 .main-card {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 600px;
            padding: 3rem;
            text-align: center;
            z-index: 10;
            background-color: var(--surface-primary);
        }

        .design1 .option-card {
            position: absolute;
            width: 280px;
            padding: 2rem;
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        .design1 .option-card:nth-child(2) {
            top: 180px;
            left: 0;
            transform: rotate(-3deg);
        }

        .design1 .option-card:nth-child(3) {
            top: 200px;
            right: 0;
            transform: rotate(2deg);
        }

        .design1 .option-card:nth-child(4) {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%) rotate(-1deg);
        }

        .design1 .option-card:hover {
            z-index: 20;
            transform: rotate(0deg) scale(1.05);
        }

        .design1 .option-card:nth-child(4):hover {
            transform: translateX(-50%) rotate(0deg) scale(1.05);
        }

        /* Design 2: Timeline Flow */
        .design2 {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            padding: 2rem 0;
        }

        .design2::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border-primary);
            transform: translateX(-50%);
        }

        .design2 .timeline-item {
            position: relative;
            margin-bottom: 3rem;
            display: flex;
            align-items: center;
        }

        .design2 .timeline-item:nth-child(odd) {
            justify-content: flex-start;
            padding-right: 50%;
        }

        .design2 .timeline-item:nth-child(even) {
            justify-content: flex-end;
            padding-left: 50%;
        }

        .design2 .timeline-content {
            padding: 2rem;
            position: relative;
            width: 100%;
            max-width: 350px;
        }

        .design2 .timeline-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: var(--surface-primary);
            border: 2px solid var(--border-primary);
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
        }

        .design2 .timeline-item:nth-child(odd) .timeline-dot {
            right: -10px;
        }

        .design2 .timeline-item:nth-child(even) .timeline-dot {
            left: -10px;
        }

        /* Design 3: Circular Navigation */
        .design3 {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
            height: 600px;
        }

        .design3 .center-hub {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            padding: 2rem;
            text-align: center;
            z-index: 10;
        }

        .design3 .orbit-item {
            position: absolute;
            width: 160px;
            padding: 1.5rem;
            text-align: center;
            transition: all 300ms ease;
        }

        .design3 .orbit-item:nth-child(2) {
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .design3 .orbit-item:nth-child(3) {
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
        }

        .design3 .orbit-item:nth-child(4) {
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .design3 .orbit-item:nth-child(5) {
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
        }

        .design3 .orbit-item:hover {
            transform: scale(1.1) translateX(-50%);
        }

        .design3 .orbit-item:nth-child(3):hover,
        .design3 .orbit-item:nth-child(5):hover {
            transform: scale(1.1) translateY(-50%);
        }

        /* Design 4: Accordion Stack */
        .design4 {
            max-width: 500px;
            margin: 0 auto;
        }

        .design4 .accordion-item {
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 300ms ease;
        }

        .design4 .accordion-header {
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
        }

        .design4 .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 300ms ease;
            padding: 0 2rem;
        }

        .design4 .accordion-item:hover .accordion-content {
            max-height: 100px;
            padding: 0 2rem 1.5rem;
        }

        .design4 .accordion-item:first-child {
            padding: 2.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .design4 .accordion-item:first-child:hover .accordion-content {
            max-height: none;
            padding: 0;
        }

        /* Design 5: Minimal Typography Focus */
        .design5 {
            max-width: 800px;
            margin: 0 auto;
            padding: 4rem 2rem;
        }

        .design5 .type-hero {
            margin-bottom: 4rem;
        }

        .design5 .type-hero h1 {
            font-family: var(--font-family-heading);
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: 500;
            line-height: 0.9;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .design5 .type-hero .subtitle {
            font-size: 1.125rem;
            max-width: 600px;
        }

        .design5 .options-minimal {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
            margin-top: 5rem;
        }

        .design5 .option-minimal {
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            transition: all 150ms ease;
            padding: 0;
        }

        .design5 .option-minimal:hover {
            transform: translateX(10px);
        }

        .design5 .option-number {
            font-family: var(--font-family-mono);
            font-size: 3rem;
            font-weight: 300;
            color: var(--text-tertiary);
            margin-bottom: 1rem;
        }

        .design5 .option-minimal h3 {
            font-family: var(--font-family-mono);
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .design5 .option-minimal p {
            font-size: 0.875rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        /* Design 6: Split Screen */
        .design6 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            height: 80vh;
            max-width: 1200px;
            margin: 0 auto;
            gap: 2rem;
        }

        .design6 .left-panel {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
        }

        .design6 .right-panel {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 1.5rem;
            padding: 2rem;
        }

        .design6 .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }

        .design6 .option-strip {
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            border-left: 4px solid transparent;
            transition: all 150ms ease;
        }

        .design6 .option-strip:hover {
            border-left-color: var(--border-primary);
            transform: translateX(10px);
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid black;
            padding: 1rem;
            box-shadow: 4px 4px 0 black;
            z-index: 100;
        }

        .nav a {
            display: block;
            padding: 0.5rem;
            color: var(--text-primary);
            text-decoration: none;
            font-family: var(--font-family-mono);
            font-size: 0.875rem;
        }

        .nav a:hover {
            background: var(--surface-secondary);
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <nav class="nav">
        <a href="#design1">Design 1: Overlapping</a>
        <a href="#design2">Design 2: Timeline</a>
        <a href="#design3">Design 3: Circular</a>
        <a href="#design4">Design 4: Accordion</a>
        <a href="#design5">Design 5: Typography</a>
        <a href="#design6">Design 6: Split Screen</a>
    </nav>

    <!-- Design 1: Overlapping Cards -->
    <section class="demo-section" id="design1">
        <h2 class="demo-title">Design 1: Overlapping Cards</h2>
        <div class="design1">
            <div class="shadow-box main-card">
                <h1 class="title">Why write when you've already said it?</h1>
                <p class="subtitle" style="margin-top: 1rem;">You share insights daily. We turn them into LinkedIn posts</p>
            </div>
            <div class="shadow-box option-card">
                <span class="icon">🎙️</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Have a recording?</h3>
                <p style="font-size: 0.875rem; color: var(--text-secondary);">Turn podcasts into posts</p>
            </div>
            <div class="shadow-box option-card">
                <span class="icon">💬</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Need to brainstorm?</h3>
                <p style="font-size: 0.875rem; color: var(--text-secondary);">Answer questions, get posts</p>
            </div>
            <div class="shadow-box option-card">
                <span class="icon">📝</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Have notes?</h3>
                <p style="font-size: 0.875rem; color: var(--text-secondary);">Transform into polished posts</p>
            </div>
        </div>
    </section>

    <!-- Design 2: Timeline Flow -->
    <section class="demo-section" id="design2">
        <h2 class="demo-title">Design 2: Timeline Flow</h2>
        <div class="design2">
            <div class="timeline-item">
                <div class="shadow-box timeline-content">
                    <h1 class="title" style="font-size: 2rem;">Why write when you've already said it?</h1>
                    <p class="subtitle" style="margin-top: 0.5rem;">You share insights daily. We turn them into LinkedIn posts</p>
                    <div class="timeline-dot"></div>
                </div>
            </div>
            <div class="timeline-item">
                <div class="subtle-box timeline-content">
                    <span class="icon">🎙️</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Have a recording?</h3>
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Turn podcasts & meetings into multiple posts</p>
                    <div class="timeline-dot"></div>
                </div>
            </div>
            <div class="timeline-item">
                <div class="subtle-box timeline-content">
                    <span class="icon">💬</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Need to brainstorm?</h3>
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Answer questions, get your post</p>
                    <div class="timeline-dot"></div>
                </div>
            </div>
            <div class="timeline-item">
                <div class="subtle-box timeline-content">
                    <span class="icon">📝</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0.5rem 0;">Have notes for a post?</h3>
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Watch as they transform into a polished post</p>
                    <div class="timeline-dot"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 3: Circular Navigation -->
    <section class="demo-section" id="design3">
        <h2 class="demo-title">Design 3: Circular Navigation</h2>
        <div class="design3">
            <div class="shadow-box center-hub">
                <h1 class="title" style="font-size: 2rem;">Why write when you've already said it?</h1>
                <p class="subtitle" style="margin-top: 1rem;">You share insights daily.<br>We turn them into LinkedIn posts</p>
            </div>
            <div class="subtle-box orbit-item">
                <span class="icon">🎙️</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 0.875rem; margin: 0.5rem 0;">Recording</h3>
            </div>
            <div class="subtle-box orbit-item">
                <span class="icon">💬</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 0.875rem; margin: 0.5rem 0;">Brainstorm</h3>
            </div>
            <div class="subtle-box orbit-item">
                <span class="icon">📝</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 0.875rem; margin: 0.5rem 0;">Notes</h3>
            </div>
            <div class="subtle-box orbit-item">
                <span class="icon" style="font-size: 1.5rem;">→</span>
                <h3 style="font-family: var(--font-family-mono); font-size: 0.875rem; margin: 0.5rem 0;">Quick Guide</h3>
            </div>
        </div>
    </section>

    <!-- Design 4: Accordion Stack -->
    <section class="demo-section" id="design4">
        <h2 class="demo-title">Design 4: Accordion Stack</h2>
        <div class="design4">
            <div class="shadow-box accordion-item">
                <h1 class="title" style="font-size: 2rem;">Why write when you've already said it?</h1>
                <p class="subtitle" style="margin-top: 1rem;">You share insights daily. We turn them into LinkedIn posts</p>
            </div>
            <div class="subtle-box accordion-item">
                <div class="accordion-header">
                    <span class="icon">🎙️</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Have a recording?</h3>
                </div>
                <div class="accordion-content">
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Turn podcasts & meetings into multiple posts</p>
                </div>
            </div>
            <div class="subtle-box accordion-item">
                <div class="accordion-header">
                    <span class="icon">💬</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Need to brainstorm?</h3>
                </div>
                <div class="accordion-content">
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Answer questions, get your post</p>
                </div>
            </div>
            <div class="subtle-box accordion-item">
                <div class="accordion-header">
                    <span class="icon">📝</span>
                    <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Have notes for a post?</h3>
                </div>
                <div class="accordion-content">
                    <p style="font-size: 0.875rem; color: var(--text-secondary);">Watch as they transform into a polished post</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 5: Minimal Typography Focus -->
    <section class="demo-section" id="design5">
        <h2 class="demo-title">Design 5: Typography-First Minimal</h2>
        <div class="design5">
            <div class="type-hero">
                <h1>Why write<br>when you've<br>already said it?</h1>
                <p class="subtitle">You share insights daily. We turn them into LinkedIn posts.</p>
            </div>
            <div class="options-minimal">
                <button class="option-minimal">
                    <div class="option-number">01</div>
                    <h3>Have a recording?</h3>
                    <p>Turn podcasts & meetings into multiple posts</p>
                </button>
                <button class="option-minimal">
                    <div class="option-number">02</div>
                    <h3>Need to brainstorm?</h3>
                    <p>Answer questions, get your post</p>
                </button>
                <button class="option-minimal">
                    <div class="option-number">03</div>
                    <h3>Have notes for a post?</h3>
                    <p>Watch as they transform into a polished post</p>
                </button>
            </div>
        </div>
    </section>

    <!-- Design 6: Split Screen -->
    <section class="demo-section" id="design6">
        <h2 class="demo-title">Design 6: Split Screen Layout</h2>
        <div class="design6">
            <div class="left-panel">
                <div class="hero-content">
                    <h1 class="title">Why write when you've already said it?</h1>
                    <p class="subtitle" style="margin-top: 1rem;">You share insights daily. We turn them into LinkedIn posts</p>
                </div>
            </div>
            <div class="right-panel">
                <div class="subtle-box option-strip">
                    <span class="icon">🎙️</span>
                    <div>
                        <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Have a recording?</h3>
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.25rem;">Turn podcasts & meetings into multiple posts</p>
                    </div>
                </div>
                <div class="subtle-box option-strip">
                    <span class="icon">💬</span>
                    <div>
                        <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Need to brainstorm?</h3>
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.25rem;">Answer questions, get your post</p>
                    </div>
                </div>
                <div class="subtle-box option-strip">
                    <span class="icon">📝</span>
                    <div>
                        <h3 style="font-family: var(--font-family-mono); font-size: 1rem; margin: 0;">Have notes for a post?</h3>
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.25rem;">Watch as they transform into a polished post</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>