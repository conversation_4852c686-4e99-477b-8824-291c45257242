# Session Management Testing Checklist

## Pre-Test Setup
- [ ] Clear browser localStorage (DevTools > Application > Storage > Clear site data)
- [ ] Close all browser tabs for the app
- [ ] Start fresh with `npm run dev`

## 1. Transcript Flow Testing

### Basic Flow
- [x] Navigate to home page - verify no session ID in URL
- [x] Click "Transcript" - verify session ID appears in URL
- [x] Paste transcript content and submit
- [x] Verify navigation to Ideas page with session ID preserved
- [ ] Select an idea and generate content
- [ ] Verify navigation to Content Agent with session ID preserved
- [ ] Wait for content generation
- [ ] Click Edit on a draft
- [ ] Verify navigation to Editor with session ID preserved

### Refresh Testing
- [ ] On Transcript Input page - refresh browser (Cmd+R)
  - [ ] Verify page loads correctly
  - [ ] Verify session ID remains in URL
- [ ] On Ideas page - refresh browser
  - [ ] Verify ideas are still loaded
  - [ ] Verify selected idea (if any) is preserved
- [ ] On Content Agent - refresh browser
  - [ ] Verify generated content is preserved
  - [ ] Verify no duplicate generation occurs
- [ ] On Editor - refresh browser
  - [ ] Verify edited content is preserved
  - [ ] Verify original content is preserved

### Back Button Testing
- [ ] From Editor, click Back button
  - [ ] Should return to Content Agent
  - [ ] Verify posts are still there
- [ ] From Content Agent, use browser back
  - [ ] Should return to Ideas page
  - [ ] Verify ideas are still loaded
- [ ] From Ideas, use browser back
  - [ ] Should return to Transcript Input
  - [ ] Verify transcript content (if any) preserved

## 2. Notes Flow Testing

### Basic Flow
- [ ] From home, click "Notes"
- [ ] Verify session ID in URL
- [ ] Enter notes and submit
- [ ] Verify navigation to Content Agent
- [ ] Verify content generation starts

### Refresh & Navigation
- [ ] Refresh on Notes page
- [ ] Refresh on Content Agent after notes submission
- [ ] Back navigation from Content Agent to Notes

## 3. Interview Flow Testing

### Basic Flow
- [ ] From home, click "Interactive Interview"
- [ ] Chat with the agent
- [ ] Generate post
- [ ] Edit the generated post

### State Preservation
- [ ] Refresh during interview - verify chat history preserved
- [ ] Refresh on Content Agent - verify interview brief preserved
- [ ] Back navigation through the flow

## 4. URL Sharing Testing

### Copy URL Tests
- [ ] On Ideas page, copy URL and paste in new tab
  - [ ] Verify same session loads
  - [ ] Verify ideas are loaded
- [ ] On Content Agent with generated posts, copy URL to new tab
  - [ ] Verify posts appear
  - [ ] Verify no new generation starts
- [ ] Share URL between different browsers (if possible)

## 5. Edge Cases

### Session Expiry
- [ ] Leave session idle for 24+ hours (or modify expiry in code to test)
- [ ] Verify graceful handling when session expires

### Storage Limits
- [ ] Generate many posts to fill localStorage
- [ ] Verify old sessions are cleaned up

### Invalid Sessions
- [ ] Manually edit session ID in URL to invalid value
- [ ] Verify new session is created gracefully

### Network Issues
- [ ] Disable network after session creation
- [ ] Verify app continues working with local storage
- [ ] Re-enable network and verify sync

## 6. Breadcrumb Testing

### Navigation Trail
- [ ] Navigate: Home → Transcript → Ideas → Content → Editor
- [ ] Check breadcrumbs show correct path at each step
- [ ] Click breadcrumb links to navigate back
- [ ] Verify session preserved when using breadcrumb navigation

## 7. Console & DevTools Checks

### No Errors
- [ ] Open browser console (no errors during any flow)
- [ ] Check network tab (no failed session requests)
- [ ] Check Application > Local Storage
  - [ ] Verify session data structure
  - [ ] Verify old sessions cleaned up

### Performance
- [ ] No duplicate API calls
- [ ] No infinite loops
- [ ] Session updates are debounced properly

## Sign-off
- [ ] All tests pass
- [ ] No console errors
- [ ] User experience feels smooth
- [ ] Ready to push to production