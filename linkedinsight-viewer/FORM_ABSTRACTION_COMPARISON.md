# Form Abstraction Comparison: Form<PERSON>ontainer vs FormWrapper

## FormContainer (Currently Used)

### Features:
- Title prop with consistent styling
- Error display with styled alert
- Loading state with custom message
- Submit button with customizable text
- Form submission handling
- Custom class names for form and submit button
- Children-based composition

### Props:
```typescript
interface FormContainerProps {
  title: string;
  error?: string | null;
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  submitText: string;
  isSubmitting?: boolean;
  submitButtonClass?: string;
  formClassName?: string;
}
```

### Usage Example:
```tsx
<FormContainer
  title="Set Your Content Preferences"
  error={error}
  isLoading={isFetching}
  loadingText="Loading content preferences..."
  onSubmit={handleSubmit}
  submitText={isSaving ? 'Saving...' : 'Save'}
  isSubmitting={isSaving}
>
  {/* Form fields */}
</FormContainer>
```

## FormWrapper (Unused)

### Features:
- No title (more flexible)
- No error handling (relies on parent)
- No loading state UI (only button state)
- Submit button with LoadingState component
- Async form submission support
- Optional submit button
- More flexible layout

### Props:
```typescript
interface FormWrapperProps {
  children: React.ReactNode;
  onSubmit: (e: FormEvent<HTMLFormElement>) => void | Promise<void>;
  isSubmitting?: boolean;
  submitText?: string;
  loadingText?: string;
  className?: string;
  showSubmitButton?: boolean;
  submitButtonClassName?: string;
  formClassName?: string;
}
```

### Usage Example:
```tsx
<FormWrapper
  onSubmit={handleSubmit}
  isSubmitting={isSubmitting}
  submitText="Save"
  loadingText="Saving..."
>
  {/* Form fields */}
</FormWrapper>
```

## Key Differences

| Feature | FormContainer | FormWrapper |
|---------|--------------|-------------|
| Title | Built-in with styling | Not included |
| Error Display | Built-in alert UI | Not included |
| Loading State | Full-page loading UI | Button-only loading |
| Flexibility | More opinionated | More flexible |
| Submit Button | Always shown | Optional |
| Async Support | Implicit | Explicit |
| Current Usage | Used in 1 form | Not used |

## Recommendation

**Keep FormContainer** and remove FormWrapper. Reasons:

1. **Already in use**: FormContainer is actively used and tested
2. **More complete**: Includes error handling and loading states
3. **Better UX**: Provides consistent title and error display
4. **Less work**: Requires migrating 2 forms vs all forms

However, FormContainer could benefit from some FormWrapper features:
- Optional submit button
- Better async support
- LoadingState component integration
