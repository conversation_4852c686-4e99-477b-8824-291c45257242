/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

/**
 * Vite configuration for the frontend application
 *
 * Key features:
 * - Development server with proxy to backend
 * - React plugin for JSX support
 * - Source maps for better debugging
 *
 * @see https://vitejs.dev/config/
 */
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5175,
    proxy: {
      // Proxy API requests to the backend server
      '/api': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        secure: false,
        cookieDomainRewrite: 'localhost',
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            // Log the request for debugging
            console.log('Proxying API request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            // Log the response for debugging
            console.log('API Proxy response:', proxyRes.statusCode, req.url);
            // Log cookies being set
            const cookies = proxyRes.headers['set-cookie'];
            if (cookies) {
              console.log('Cookies being set:', cookies);
            }
          });
          proxy.on('error', (err, req, res) => {
            console.error('Proxy error:', err);
          });
        }
      },
      // Proxy authentication requests to the backend server
      '/accounts': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        secure: false,
        cookieDomainRewrite: 'localhost',
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Proxying auth request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Auth Proxy response:', proxyRes.statusCode, req.url);
            // Log cookies being set
            const cookies = proxyRes.headers['set-cookie'];
            if (cookies) {
              console.log('Auth cookies being set:', cookies);
            }
          });
          proxy.on('error', (err, req, res) => {
            console.error('Auth proxy error:', err);
          });
        }
      }
    }
  },
  build: {
    // Build to the dist directory - this will be deployed separately
    outDir: path.resolve(__dirname, 'dist'),
    emptyOutDir: true,
    sourcemap: true,
    // Ensure SPA routing works by copying the _redirects file
    copyPublicDir: true,
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  },
  // Add history API fallback for SPA routing
  preview: {
    port: 5175,
    host: true,
    strictPort: true,
    historyApiFallback: true
  },
  test: {
    globals: true,
    environment: 'jsdom',
    define: {
      'import.meta.env.DEV': true,
    },
    setupFiles: ['./src/tests/setup.ts'],
  },
});
