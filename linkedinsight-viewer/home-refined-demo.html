<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedInsight | Refined Minimalism</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@300;400&family=Archivo:wght@300;400&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --ink: #0a0a0a;
            --paper: #fafafa;
            --whisper: #f5f5f5;
            --shadow: rgba(0, 0, 0, 0.04);
            --hover-shadow: rgba(0, 0, 0, 0.08);
            --text-primary: #0a0a0a;
            --text-secondary: #666;
            --text-tertiary: #999;
            --accent: #e5e5e5;
        }

        body {
            font-family: 'IBM Plex Mono', monospace;
            font-weight: 300;
            background: var(--paper);
            color: var(--text-primary);
            line-height: 1.6;
            cursor: default;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        /* Navigation - Subtle presence */
        .nav-refined {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 100;
            display: flex;
            gap: 0.75rem;
        }

        .nav-dot {
            width: 6px;
            height: 6px;
            background: var(--accent);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-dot:hover {
            background: var(--text-secondary);
            transform: scale(1.5);
        }

        .nav-dot.active {
            background: var(--ink);
            transform: scale(1.5);
        }

        /* Base slide styles */
        .slide {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.6s ease;
        }

        .slide.active {
            opacity: 1;
            pointer-events: all;
        }

        /* DESIGN 1: Paper Stack */
        .paper-stack {
            max-width: 600px;
            width: 90%;
            margin: 0 auto;
        }

        .paper {
            background: var(--paper);
            padding: 3rem;
            margin-bottom: -2rem;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .paper::before {
            content: '';
            position: absolute;
            inset: 0;
            background: var(--paper);
            box-shadow: 0 1px 3px var(--shadow);
            z-index: -1;
        }

        .paper:nth-child(1) {
            transform: rotate(-0.5deg);
        }

        .paper:nth-child(2) {
            transform: rotate(0.3deg);
            margin-bottom: 0;
        }

        .paper:hover {
            transform: translateY(-4px) rotate(0deg);
            box-shadow: 0 4px 12px var(--hover-shadow);
        }

        .paper h1 {
            font-family: 'Archivo', sans-serif;
            font-size: 2.5rem;
            font-weight: 300;
            line-height: 1.1;
            margin-bottom: 1rem;
        }

        .paper p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .paper-options {
            display: flex;
            gap: 2rem;
            margin-top: 3rem;
            padding: 0 3rem;
        }

        .paper-option {
            flex: 1;
            padding: 2rem 1rem;
            background: var(--whisper);
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .paper-option:hover {
            background: var(--paper);
            transform: translateY(-2px);
            box-shadow: 0 2px 8px var(--hover-shadow);
        }

        /* DESIGN 2: Swiss Grid */
        .swiss-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 1px;
            width: 100vw;
            height: 100vh;
            background: var(--accent);
        }

        .swiss-cell {
            background: var(--paper);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            text-align: center;
        }

        .swiss-hero {
            grid-column: 3 / 11;
            grid-row: 2 / 5;
            padding: 3rem;
        }

        .swiss-option-1 {
            grid-column: 3 / 6;
            grid-row: 6 / 8;
        }

        .swiss-option-2 {
            grid-column: 6 / 9;
            grid-row: 6 / 8;
        }

        .swiss-option-3 {
            grid-column: 9 / 12;
            grid-row: 6 / 8;
        }

        .swiss-cell:hover {
            background: var(--whisper);
            z-index: 10;
            box-shadow: 0 0 20px var(--hover-shadow);
        }

        /* DESIGN 3: Zen Garden */
        .zen-garden {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .zen-title {
            text-align: center;
            margin-bottom: 5rem;
        }

        .zen-title h1 {
            font-family: 'Archivo', sans-serif;
            font-size: 3rem;
            font-weight: 300;
            line-height: 1;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .zen-title p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            letter-spacing: 0.05em;
        }

        .zen-stones {
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            position: relative;
        }

        .zen-stone {
            flex: 1;
            aspect-ratio: 1;
            border-radius: 50%;
            background: var(--whisper);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .zen-stone::before {
            content: '';
            position: absolute;
            inset: -20px;
            border-radius: 50%;
            background: radial-gradient(circle, transparent 40%, var(--shadow) 70%, transparent 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .zen-stone:hover {
            transform: translateY(-8px);
            background: var(--paper);
            box-shadow: 0 20px 40px var(--hover-shadow);
        }

        .zen-stone:hover::before {
            opacity: 1;
        }

        .zen-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            filter: grayscale(100%);
            opacity: 0.7;
        }

        .zen-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: lowercase;
            letter-spacing: 0.1em;
        }

        /* DESIGN 4: Monochrome Layers */
        .mono-layers {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            perspective: 1000px;
        }

        .mono-stack {
            position: relative;
            width: 600px;
            height: 400px;
            transform-style: preserve-3d;
            transition: transform 0.6s ease;
        }

        .mono-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            background: var(--paper);
            border: 1px solid var(--accent);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mono-layer:nth-child(1) {
            transform: translateZ(0px);
            z-index: 4;
        }

        .mono-layer:nth-child(2) {
            transform: translateZ(-50px) translateX(20px);
            opacity: 0.8;
            z-index: 3;
        }

        .mono-layer:nth-child(3) {
            transform: translateZ(-100px) translateX(40px);
            opacity: 0.6;
            z-index: 2;
        }

        .mono-layer:nth-child(4) {
            transform: translateZ(-150px) translateX(60px);
            opacity: 0.4;
            z-index: 1;
        }

        .mono-layers:hover .mono-stack {
            transform: rotateY(-15deg);
        }

        .mono-layer:hover {
            transform: translateZ(20px) translateX(0);
            opacity: 1;
            box-shadow: 0 10px 40px var(--hover-shadow);
        }

        /* DESIGN 5: Breath */
        .breath-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: radial-gradient(circle at center, var(--paper) 0%, var(--whisper) 100%);
        }

        .breath-content {
            text-align: center;
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.02); opacity: 1; }
        }

        .breath-title {
            font-family: 'Archivo', sans-serif;
            font-size: 3.5rem;
            font-weight: 300;
            line-height: 1.1;
            margin-bottom: 1rem;
            letter-spacing: -0.03em;
        }

        .breath-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 4rem;
            letter-spacing: 0.05em;
        }

        .breath-options {
            display: flex;
            gap: 4rem;
            justify-content: center;
        }

        .breath-option {
            padding: 0;
            background: none;
            border: none;
            font-family: inherit;
            font-size: 0.9rem;
            color: var(--text-tertiary);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .breath-option::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 1px;
            background: var(--text-primary);
            transition: width 0.3s ease;
        }

        .breath-option:hover {
            color: var(--text-primary);
        }

        .breath-option:hover::after {
            width: 100%;
        }

        /* DESIGN 6: Horizon Line */
        .horizon-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .horizon-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--accent);
            top: 50%;
        }

        .horizon-content {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 10;
        }

        .horizon-title {
            font-family: 'Archivo', sans-serif;
            font-size: 4rem;
            font-weight: 300;
            line-height: 1;
            margin-bottom: 0.5rem;
            background: var(--paper);
            display: inline-block;
            padding-right: 1rem;
        }

        .horizon-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            background: var(--paper);
            display: inline-block;
            padding-right: 1rem;
            margin-bottom: 4rem;
        }

        .horizon-options {
            display: flex;
            gap: 3rem;
            background: var(--paper);
            padding: 2rem 0;
        }

        .horizon-option {
            opacity: 0.4;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .horizon-option:hover {
            opacity: 1;
            transform: translateX(10px);
        }

        /* DESIGN 7: Ink Wash */
        .ink-wash {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .ink-wash::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100%;
            height: 200%;
            background: radial-gradient(ellipse at center, transparent 30%, var(--shadow) 70%);
            transform: rotate(-30deg);
            opacity: 0.3;
        }

        .ink-content {
            max-width: 700px;
            padding: 2rem;
            position: relative;
            z-index: 10;
        }

        .ink-title {
            font-family: 'Archivo', sans-serif;
            font-size: 3rem;
            font-weight: 300;
            line-height: 1.2;
            margin-bottom: 2rem;
            position: relative;
        }

        .ink-title::after {
            content: '';
            position: absolute;
            bottom: -1rem;
            left: 0;
            width: 60px;
            height: 2px;
            background: var(--ink);
            opacity: 0.2;
        }

        .ink-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.8;
            margin-bottom: 3rem;
        }

        .ink-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .ink-option {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .ink-option:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-4px);
            box-shadow: 0 10px 30px var(--hover-shadow);
        }

        /* DESIGN 8: Silent Authority */
        .authority-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(to bottom, var(--paper) 0%, var(--whisper) 100%);
        }

        .authority-content {
            max-width: 500px;
            text-align: center;
        }

        .authority-mark {
            width: 60px;
            height: 60px;
            background: var(--ink);
            margin: 0 auto 3rem;
            position: relative;
            transition: all 0.4s ease;
        }

        .authority-mark::before,
        .authority-mark::after {
            content: '';
            position: absolute;
            background: var(--ink);
            transition: all 0.4s ease;
        }

        .authority-mark::before {
            width: 100%;
            height: 2px;
            top: 50%;
            left: -120%;
            transform: translateY(-50%);
        }

        .authority-mark::after {
            width: 100%;
            height: 2px;
            top: 50%;
            right: -120%;
            transform: translateY(-50%);
        }

        .authority-container:hover .authority-mark {
            transform: rotate(45deg);
        }

        .authority-container:hover .authority-mark::before {
            left: -100%;
        }

        .authority-container:hover .authority-mark::after {
            right: -100%;
        }

        .authority-title {
            font-family: 'Archivo', sans-serif;
            font-size: 2rem;
            font-weight: 400;
            line-height: 1.3;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .authority-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 3rem;
        }

        .authority-options {
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .authority-option {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .authority-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 3px solid transparent;
            border-right: 3px solid transparent;
            border-top: 4px solid var(--ink);
            transform: translateX(-50%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .authority-option:hover {
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .authority-option:hover::before {
            opacity: 1;
            top: -8px;
        }

        /* Subtle icon styling */
        .icon {
            font-size: 1.5rem;
            filter: grayscale(100%);
            opacity: 0.6;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .paper h1, .zen-title h1, .breath-title { font-size: 2rem; }
            .horizon-title { font-size: 2.5rem; }
            .swiss-grid { grid-template-columns: repeat(6, 1fr); }
            .swiss-hero { grid-column: 1 / 7; }
            .swiss-option-1 { grid-column: 1 / 3; }
            .swiss-option-2 { grid-column: 3 / 5; }
            .swiss-option-3 { grid-column: 5 / 7; }
            .zen-stones, .breath-options, .horizon-options { flex-direction: column; gap: 2rem; }
            .ink-options { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navigation -->
        <nav class="nav-refined">
            <div class="nav-dot active" data-slide="0"></div>
            <div class="nav-dot" data-slide="1"></div>
            <div class="nav-dot" data-slide="2"></div>
            <div class="nav-dot" data-slide="3"></div>
            <div class="nav-dot" data-slide="4"></div>
            <div class="nav-dot" data-slide="5"></div>
            <div class="nav-dot" data-slide="6"></div>
            <div class="nav-dot" data-slide="7"></div>
        </nav>

        <!-- Design 1: Paper Stack -->
        <div class="slide active" data-design="0">
            <div class="paper-stack">
                <div class="paper">
                    <h1>Why write when you've already said it?</h1>
                    <p>You share insights daily. We turn them into LinkedIn posts.</p>
                </div>
                <div class="paper">
                    <p style="color: var(--text-tertiary); font-size: 0.8rem;">Choose your preferred input method below</p>
                </div>
                <div class="paper-options">
                    <div class="paper-option">
                        <span class="icon">🎙️</span>
                        <p>recording</p>
                    </div>
                    <div class="paper-option">
                        <span class="icon">💬</span>
                        <p>interview</p>
                    </div>
                    <div class="paper-option">
                        <span class="icon">📝</span>
                        <p>notes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 2: Swiss Grid -->
        <div class="slide" data-design="1">
            <div class="swiss-grid">
                <div class="swiss-cell swiss-hero">
                    <div>
                        <h1 style="font-family: 'Archivo', sans-serif; font-size: 2.5rem; font-weight: 300; margin-bottom: 0.5rem;">
                            Why write when you've already said it?
                        </h1>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            You share insights daily. We turn them into LinkedIn posts.
                        </p>
                    </div>
                </div>
                <div class="swiss-cell swiss-option-1">
                    <div>
                        <span class="icon">🎙️</span>
                        <p style="font-size: 0.8rem; margin-top: 0.5rem;">recording</p>
                    </div>
                </div>
                <div class="swiss-cell swiss-option-2">
                    <div>
                        <span class="icon">💬</span>
                        <p style="font-size: 0.8rem; margin-top: 0.5rem;">interview</p>
                    </div>
                </div>
                <div class="swiss-cell swiss-option-3">
                    <div>
                        <span class="icon">📝</span>
                        <p style="font-size: 0.8rem; margin-top: 0.5rem;">notes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 3: Zen Garden -->
        <div class="slide" data-design="2">
            <div class="zen-garden">
                <div class="zen-title">
                    <h1>Why write when you've already said it?</h1>
                    <p>You share insights daily. We turn them into LinkedIn posts.</p>
                </div>
                <div class="zen-stones">
                    <div class="zen-stone">
                        <span class="zen-icon">🎙️</span>
                        <span class="zen-label">recording</span>
                    </div>
                    <div class="zen-stone">
                        <span class="zen-icon">💬</span>
                        <span class="zen-label">interview</span>
                    </div>
                    <div class="zen-stone">
                        <span class="zen-icon">📝</span>
                        <span class="zen-label">notes</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 4: Monochrome Layers -->
        <div class="slide" data-design="3">
            <div class="mono-layers">
                <div class="mono-stack">
                    <div class="mono-layer">
                        <h1 style="font-family: 'Archivo', sans-serif; font-size: 2.5rem; font-weight: 300; margin-bottom: 1rem;">
                            Why write when you've already said it?
                        </h1>
                        <p style="color: var(--text-secondary);">You share insights daily. We turn them into LinkedIn posts.</p>
                    </div>
                    <div class="mono-layer">
                        <span class="icon">🎙️</span>
                        <p>Have a recording?</p>
                    </div>
                    <div class="mono-layer">
                        <span class="icon">💬</span>
                        <p>Need to brainstorm?</p>
                    </div>
                    <div class="mono-layer">
                        <span class="icon">📝</span>
                        <p>Have notes?</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 5: Breath -->
        <div class="slide" data-design="4">
            <div class="breath-container">
                <div class="breath-content">
                    <h1 class="breath-title">Why write when<br>you've already said it?</h1>
                    <p class="breath-subtitle">You share insights daily. We turn them into LinkedIn posts.</p>
                    <div class="breath-options">
                        <button class="breath-option">recording</button>
                        <button class="breath-option">interview</button>
                        <button class="breath-option">notes</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 6: Horizon Line -->
        <div class="slide" data-design="5">
            <div class="horizon-container">
                <div class="horizon-line"></div>
                <div class="horizon-content">
                    <h1 class="horizon-title">Why write when you've already said it?</h1><br>
                    <p class="horizon-subtitle">You share insights daily. We turn them into LinkedIn posts.</p>
                    <div class="horizon-options">
                        <div class="horizon-option">
                            <span class="icon">🎙️</span>
                            <p style="margin-top: 0.5rem; font-size: 0.8rem;">recording</p>
                        </div>
                        <div class="horizon-option">
                            <span class="icon">💬</span>
                            <p style="margin-top: 0.5rem; font-size: 0.8rem;">interview</p>
                        </div>
                        <div class="horizon-option">
                            <span class="icon">📝</span>
                            <p style="margin-top: 0.5rem; font-size: 0.8rem;">notes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 7: Ink Wash -->
        <div class="slide" data-design="6">
            <div class="ink-wash">
                <div class="ink-content">
                    <h1 class="ink-title">Why write when you've already said it?</h1>
                    <p class="ink-subtitle">You share insights daily. We turn them into LinkedIn posts.</p>
                    <div class="ink-options">
                        <div class="ink-option">
                            <span class="icon">🎙️</span>
                            <p style="margin-top: 1rem; font-size: 0.85rem;">recording</p>
                        </div>
                        <div class="ink-option">
                            <span class="icon">💬</span>
                            <p style="margin-top: 1rem; font-size: 0.85rem;">interview</p>
                        </div>
                        <div class="ink-option">
                            <span class="icon">📝</span>
                            <p style="margin-top: 1rem; font-size: 0.85rem;">notes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 8: Silent Authority -->
        <div class="slide" data-design="7">
            <div class="authority-container">
                <div class="authority-content">
                    <div class="authority-mark"></div>
                    <h1 class="authority-title">Why write when you've already said it?</h1>
                    <p class="authority-subtitle">You share insights daily. We turn them into LinkedIn posts.</p>
                    <div class="authority-options">
                        <div class="authority-option">record</div>
                        <div class="authority-option">interview</div>
                        <div class="authority-option">notes</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        const dots = document.querySelectorAll('.nav-dot');
        const slides = document.querySelectorAll('.slide');
        
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                // Update active states
                dots.forEach(d => d.classList.remove('active'));
                dot.classList.add('active');
                
                slides.forEach(s => s.classList.remove('active'));
                slides[index].classList.add('active');
            });
        });

        // Keyboard navigation
        let currentSlide = 0;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                currentSlide = (currentSlide + 1) % slides.length;
                dots[currentSlide].click();
            } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                currentSlide = (currentSlide - 1 + slides.length) % slides.length;
                dots[currentSlide].click();
            }
        });

        // Add subtle interaction feedback
        document.querySelectorAll('[class*="option"], .paper, .swiss-cell, .zen-stone, .mono-layer, .ink-option').forEach(el => {
            el.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>