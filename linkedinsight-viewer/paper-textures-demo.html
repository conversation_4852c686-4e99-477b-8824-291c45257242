<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper Texture Backgrounds Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }

        .texture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .texture-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .texture-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .texture-content {
            position: absolute;
            inset: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .texture-info {
            padding: 20px;
            border-top: 1px solid #eee;
        }

        .texture-name {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
            color: #333;
        }

        .texture-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Current dots pattern */
        .bg-dots {
            background-color: #e5e5e5;
            background-image: radial-gradient(
                rgba(255, 255, 255, 0.15) 1.5px,
                transparent 1.5px
            );
            background-size: 8px 8px;
        }

        /* Paper texture alternatives */
        .bg-paper-grain {
            background-color: #e5e5e5;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='4'/%3E%3C/filter%3E%3Crect width='100' height='100' fill='%23fff' filter='url(%23noise)' opacity='0.03'/%3E%3C/svg%3E");
            background-size: 200px 200px;
        }

        .bg-linen {
            background-color: #e5e5e5;
            background-image: 
                repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,.05) 3px, rgba(255,255,255,.05) 6px),
                repeating-linear-gradient(-45deg, transparent, transparent 3px, rgba(255,255,255,.02) 3px, rgba(255,255,255,.02) 6px);
            background-size: 20px 20px;
        }

        .bg-watercolor {
            background-color: #e5e5e5;
            background-image: 
                radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(ellipse at bottom, rgba(255,255,255,0.05) 0%, transparent 50%);
            background-size: 100% 200%, 100% 200%;
            background-position: 0 0, 0 100%;
        }

        .bg-rice-paper {
            background-color: #e5e5e5;
            background-image: 
                linear-gradient(30deg, rgba(255,255,255,0.03) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.03) 87.5%, rgba(255,255,255,0.03)),
                linear-gradient(150deg, rgba(255,255,255,0.03) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.03) 87.5%, rgba(255,255,255,0.03));
            background-size: 15px 15px;
        }

        .bg-canvas {
            background-color: #e5e5e5;
            background-image: 
                repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(255,255,255,.5) 2px, rgba(255,255,255,.5) 4px),
                repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(255,255,255,.3) 2px, rgba(255,255,255,.3) 4px);
            background-size: 4px 4px;
        }

        .bg-fiber {
            background-color: #e5e5e5;
            background-image: 
                linear-gradient(90deg, transparent 50%, rgba(255,255,255,.05) 50%),
                linear-gradient(0deg, transparent 50%, rgba(255,255,255,.03) 50%);
            background-size: 3px 3px;
        }

        .bg-subtle-noise {
            background-color: #e5e5e5;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='turbulence' baseFrequency='0.9' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");
            background-size: 100px 100px;
        }

        /* Dark mode preview */
        .dark-mode-section {
            background: #1a1a1a;
            padding: 40px 0;
            margin: 0 -20px;
        }

        .dark-mode-section h2 {
            color: #fff;
            text-align: center;
            margin-bottom: 30px;
        }

        .dark-mode-section .texture-card {
            background: #2a2a2a;
        }

        .dark-mode-section .texture-preview {
            background: #333;
        }

        .dark-mode-section .bg-dots {
            background-color: #333;
            background-image: radial-gradient(
                rgba(255, 255, 255, 0.08) 1.5px,
                transparent 1.5px
            );
        }

        .dark-mode-section .bg-paper-grain {
            background-color: #333;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise2'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='4'/%3E%3C/filter%3E%3Crect width='100' height='100' fill='%23fff' filter='url(%23noise2)' opacity='0.02'/%3E%3C/svg%3E");
        }

        .dark-mode-section .bg-linen {
            background-color: #333;
            background-image: 
                repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,.03) 3px, rgba(255,255,255,.03) 6px),
                repeating-linear-gradient(-45deg, transparent, transparent 3px, rgba(255,255,255,.01) 3px, rgba(255,255,255,.01) 6px);
        }

        .dark-mode-section .bg-watercolor {
            background-color: #333;
            background-image: 
                radial-gradient(ellipse at top, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(ellipse at bottom, rgba(255,255,255,0.03) 0%, transparent 50%);
        }

        .dark-mode-section .bg-rice-paper {
            background-color: #333;
            background-image: 
                linear-gradient(30deg, rgba(255,255,255,0.02) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.02) 87.5%, rgba(255,255,255,0.02)),
                linear-gradient(150deg, rgba(255,255,255,0.02) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.02) 87.5%, rgba(255,255,255,0.02));
        }

        .dark-mode-section .bg-canvas {
            background-color: #333;
            background-image: 
                repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(255,255,255,.3) 2px, rgba(255,255,255,.3) 4px),
                repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(255,255,255,.2) 2px, rgba(255,255,255,.2) 4px);
        }

        .dark-mode-section .bg-fiber {
            background-color: #333;
            background-image: 
                linear-gradient(90deg, transparent 50%, rgba(255,255,255,.03) 50%),
                linear-gradient(0deg, transparent 50%, rgba(255,255,255,.02) 50%);
        }

        .dark-mode-section .bg-subtle-noise {
            background-color: #333;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter2'%3E%3CfeTurbulence type='turbulence' baseFrequency='0.9' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter2)' opacity='0.015'/%3E%3C/svg%3E");
        }

        .dark-mode-section .texture-content {
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
        }

        .dark-mode-section .texture-info {
            border-top-color: #444;
        }

        .dark-mode-section .texture-name {
            color: #fff;
        }

        .dark-mode-section .texture-description {
            color: #aaa;
        }

        .current-badge {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Paper Texture Background Options</h1>
        
        <!-- Light Mode Section -->
        <div class="texture-grid">
            <div class="texture-card">
                <div class="texture-preview bg-dots">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Dots Pattern <span class="current-badge">Current</span></div>
                    <div class="texture-description">The current dot pattern used in the application. Clean and geometric.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-paper-grain">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Paper Grain ⭐</div>
                    <div class="texture-description">Authentic paper texture using SVG noise. Most realistic paper feel, very subtle and elegant.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-linen">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Soft Linen</div>
                    <div class="texture-description">Cross-hatched pattern resembling linen fabric. Adds subtle depth without being distracting.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-watercolor">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Watercolor Paper</div>
                    <div class="texture-description">Gradient-based texture mimicking watercolor paper. Very subtle and organic.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-rice-paper">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Rice Paper</div>
                    <div class="texture-description">Diagonal pattern inspired by traditional rice paper. Extremely subtle.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-canvas">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Canvas Weave</div>
                    <div class="texture-description">Fine grid pattern resembling canvas texture. Clean and professional.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-fiber">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Paper Fiber</div>
                    <div class="texture-description">Minimal fiber-like texture. Almost imperceptible but adds warmth.</div>
                </div>
            </div>

            <div class="texture-card">
                <div class="texture-preview bg-subtle-noise">
                    <div class="texture-content">
                        <div>Sample Content</div>
                    </div>
                </div>
                <div class="texture-info">
                    <div class="texture-name">Subtle Noise</div>
                    <div class="texture-description">Very fine noise pattern. Slightly less organic than paper grain.</div>
                </div>
            </div>
        </div>

        <!-- Dark Mode Section -->
        <div class="dark-mode-section">
            <h2>Dark Mode Preview</h2>
            <div class="container">
                <div class="texture-grid">
                    <div class="texture-card">
                        <div class="texture-preview bg-dots">
                            <div class="texture-content">
                                <div>Sample Content</div>
                            </div>
                        </div>
                        <div class="texture-info">
                            <div class="texture-name">Dots Pattern <span class="current-badge">Current</span></div>
                            <div class="texture-description">The current dot pattern in dark mode.</div>
                        </div>
                    </div>

                    <div class="texture-card">
                        <div class="texture-preview bg-paper-grain">
                            <div class="texture-content">
                                <div>Sample Content</div>
                            </div>
                        </div>
                        <div class="texture-info">
                            <div class="texture-name">Paper Grain ⭐</div>
                            <div class="texture-description">Paper texture maintains subtlety in dark mode.</div>
                        </div>
                    </div>

                    <div class="texture-card">
                        <div class="texture-preview bg-linen">
                            <div class="texture-content">
                                <div>Sample Content</div>
                            </div>
                        </div>
                        <div class="texture-info">
                            <div class="texture-name">Soft Linen</div>
                            <div class="texture-description">Linen pattern with adjusted opacity for dark backgrounds.</div>
                        </div>
                    </div>

                    <div class="texture-card">
                        <div class="texture-preview bg-watercolor">
                            <div class="texture-content">
                                <div>Sample Content</div>
                            </div>
                        </div>
                        <div class="texture-info">
                            <div class="texture-name">Watercolor Paper</div>
                            <div class="texture-description">Gradient effects work well in dark mode.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>