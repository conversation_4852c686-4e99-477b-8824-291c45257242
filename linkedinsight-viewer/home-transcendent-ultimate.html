<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedInsight | The Interface Between Thought and Form</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@100;200;300;400&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            cursor: none;
        }

        :root {
            --consciousness: 0;
            --resonance: 0;
            --time-dilation: 1;
            --reality-coefficient: 1;
            --void: #000000;
            --essence: #ffffff;
            --whisper: rgba(0, 0, 0, 0.03);
            --breath: rgba(0, 0, 0, 0.1);
            --thought: rgba(0, 0, 0, 0.6);
        }

        html {
            font-size: calc(14px + 0.2vw);
            overflow: hidden;
        }

        body {
            font-family: 'IBM Plex Mono', monospace;
            font-weight: 200;
            background: var(--essence);
            color: var(--void);
            height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Custom Cursor - Living Entity */
        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 10000;
        }

        .cursor-dot {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: var(--void);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.1s ease;
            box-shadow: 0 0 0 2px var(--essence);
        }

        .cursor-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 24px;
            height: 24px;
            border: 2px solid var(--void);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.15s ease;
            opacity: 0.5;
        }

        .cursor.hovering .cursor-ring {
            width: 40px;
            height: 40px;
        }

        .cursor.clicking .cursor-dot {
            width: 4px;
            height: 4px;
            background: var(--breath);
        }

        /* Navigation - Breathing Organisms */
        .quantum-nav {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .nav-organism {
            width: 8px;
            height: 8px;
            background: var(--whisper);
            border-radius: 50%;
            position: relative;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
            cursor: pointer;
        }

        .nav-organism::before {
            content: attr(data-dimension);
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.7rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
            text-transform: lowercase;
            letter-spacing: 0.1em;
        }

        .nav-organism:hover::before {
            opacity: 0.6;
        }

        .nav-organism.active {
            background: var(--void);
            transform: scale(1.5);
        }

        .nav-organism.breathing {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* Container for all realities */
        .reality-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .dimension {
            position: absolute;
            inset: 0;
            opacity: 0;
            pointer-events: none;
            transition: opacity 1.5s cubic-bezier(0.23, 1, 0.320, 1);
        }

        .dimension.active {
            opacity: 1;
            pointer-events: all;
        }

        /* DIMENSION 1: QUANTUM SUPERPOSITION */
        .quantum-field {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantum-text {
            position: relative;
            font-size: 3rem;
            font-weight: 100;
            text-align: center;
            line-height: 1.2;
            letter-spacing: -0.02em;
        }

        .quantum-text span {
            display: inline-block;
            position: relative;
            transition: all 0.3s ease;
        }

        .quantum-text span::before,
        .quantum-text span::after {
            content: attr(data-char);
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .quantum-text span::before {
            transform: translateY(-2px);
            color: rgba(0, 0, 0, 0.2);
        }

        .quantum-text span::after {
            transform: translateY(2px);
            color: rgba(0, 0, 0, 0.1);
        }

        .quantum-field:hover .quantum-text span::before,
        .quantum-field:hover .quantum-text span::after {
            opacity: 1;
        }

        .quantum-field:hover .quantum-text span:nth-child(odd)::before {
            transform: translateY(-4px);
        }

        .quantum-field:hover .quantum-text span:nth-child(even)::after {
            transform: translateY(4px);
        }

        .quantum-options {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 4rem;
        }

        .quantum-option {
            font-size: 0.8rem;
            text-transform: lowercase;
            letter-spacing: 0.2em;
            opacity: 0.3;
            transition: all 0.5s ease;
            position: relative;
            cursor: pointer;
        }

        .quantum-option::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 0%;
            height: 1px;
            background: var(--void);
            transition: width 0.3s ease;
        }

        .quantum-option:hover {
            opacity: 1;
        }

        .quantum-option:hover::after {
            width: 100%;
        }

        /* DIMENSION 2: TEMPORAL ECHO */
        .temporal-space {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .temporal-echo {
            position: absolute;
            font-size: 2rem;
            font-weight: 100;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s ease;
        }

        .temporal-echo.visible {
            opacity: 1;
            animation: fadeAway 3s ease forwards;
        }

        @keyframes fadeAway {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.9); }
        }

        .temporal-present {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .temporal-present h1 {
            font-size: 2.5rem;
            font-weight: 100;
            margin-bottom: 1rem;
        }

        .temporal-options {
            display: flex;
            gap: 3rem;
            margin-top: 3rem;
        }

        .temporal-option {
            font-size: 0.9rem;
            opacity: 0.5;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .temporal-option:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        /* DIMENSION 3: EMOTIONAL RESONANCE FIELD */
        .resonance-field {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .resonance-wave {
            position: absolute;
            border-radius: 50%;
            border: 1px solid var(--breath);
            pointer-events: none;
            animation: ripple 2s ease-out;
        }

        @keyframes ripple {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 300px;
                height: 300px;
                opacity: 0;
            }
        }

        .resonance-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .resonance-text {
            font-size: 2rem;
            font-weight: 100;
            transition: all 0.3s ease;
        }

        .resonance-particles {
            position: absolute;
            inset: 0;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--breath);
            border-radius: 50%;
            pointer-events: none;
        }

        /* DIMENSION 4: LINGUISTIC DECAY */
        .linguistic-space {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .linguistic-evolution {
            max-width: 800px;
            text-align: center;
        }

        .evolving-text {
            font-size: 1.5rem;
            line-height: 1.8;
            font-weight: 200;
            transition: all 2s ease;
        }

        .evolving-text.stage-1 {
            font-weight: 300;
            letter-spacing: 0;
        }

        .evolving-text.stage-2 {
            font-weight: 200;
            letter-spacing: 0.05em;
            opacity: 0.8;
        }

        .evolving-text.stage-3 {
            font-weight: 100;
            letter-spacing: 0.1em;
            opacity: 0.6;
            font-style: italic;
        }

        .linguistic-triggers {
            margin-top: 4rem;
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .linguistic-trigger {
            font-size: 0.8rem;
            opacity: 0.4;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .linguistic-trigger:hover {
            opacity: 1;
        }

        /* DIMENSION 5: DIMENSIONAL FOLDING */
        .folding-space {
            position: relative;
            width: 100%;
            height: 100%;
            perspective: 1000px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fold-container {
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.6s ease;
        }

        .fold-plane {
            position: absolute;
            width: 400px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--breath);
            backface-visibility: hidden;
        }

        .fold-plane:nth-child(1) {
            transform: rotateY(0deg) translateZ(200px);
        }

        .fold-plane:nth-child(2) {
            transform: rotateY(90deg) translateZ(200px);
        }

        .fold-plane:nth-child(3) {
            transform: rotateY(180deg) translateZ(200px);
        }

        .fold-plane:nth-child(4) {
            transform: rotateY(270deg) translateZ(200px);
        }

        .folding-space:hover .fold-container {
            animation: dimensionalRotate 20s linear infinite;
        }

        @keyframes dimensionalRotate {
            0% { transform: rotateY(0deg) rotateX(0deg); }
            100% { transform: rotateY(360deg) rotateX(360deg); }
        }

        /* DIMENSION 6: CONSCIOUSNESS MIRROR */
        .consciousness-space {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .consciousness-reflection {
            position: relative;
            text-align: center;
        }

        .consciousness-question {
            font-size: 2rem;
            font-weight: 100;
            opacity: 0;
            animation: consciousnessAwaken 3s ease forwards;
        }

        @keyframes consciousnessAwaken {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .consciousness-thought {
            position: absolute;
            font-size: 0.8rem;
            opacity: 0;
            pointer-events: none;
            transition: all 0.5s ease;
        }

        .consciousness-thought.visible {
            opacity: 0.3;
        }

        /* DIMENSION 7: ENTROPIC DESIGN */
        .entropic-space {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .entropic-grid {
            position: absolute;
            inset: 0;
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            grid-template-rows: repeat(20, 1fr);
            gap: 1px;
        }

        .entropic-cell {
            background: var(--whisper);
            transition: all 0.5s ease;
        }

        .entropic-cell.decay-1 {
            opacity: 0.8;
            transform: scale(0.95);
        }

        .entropic-cell.decay-2 {
            opacity: 0.5;
            transform: scale(0.9) rotate(5deg);
        }

        .entropic-cell.decay-3 {
            opacity: 0.2;
            transform: scale(0.8) rotate(15deg);
        }

        .entropic-cell.decay-4 {
            opacity: 0;
            transform: scale(0.5) rotate(45deg);
        }

        .entropic-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        /* DIMENSION 8: SYNAPTIC NETWORK */
        .synaptic-space {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .synapse-node {
            position: absolute;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            text-align: center;
            background: var(--essence);
            border: 1px solid var(--breath);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .synapse-node:hover {
            transform: scale(1.1);
            border-color: var(--void);
        }

        .synapse-node.active {
            background: var(--void);
            color: var(--essence);
        }

        .synapse-connection {
            position: absolute;
            height: 1px;
            background: var(--breath);
            transform-origin: left center;
            pointer-events: none;
            transition: all 0.5s ease;
        }

        .synapse-connection.active {
            background: var(--void);
            height: 2px;
        }

        /* Subtle Background Pattern */
        .quantum-dust {
            position: fixed;
            inset: 0;
            pointer-events: none;
            opacity: 0.02;
            background-image: 
                radial-gradient(circle at 20% 50%, var(--void) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, var(--void) 0%, transparent 50%),
                radial-gradient(circle at 40% 20%, var(--void) 0%, transparent 50%);
            animation: dustDrift 30s ease-in-out infinite;
        }

        @keyframes dustDrift {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(-20px, -20px) scale(1.1); }
            66% { transform: translate(20px, -10px) scale(0.9); }
        }

        /* Loading/Transition State */
        .reality-shift {
            position: fixed;
            inset: 0;
            background: var(--essence);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 9999;
        }

        .reality-shift.active {
            opacity: 1;
        }

        /* Mobile Adjustments */
        @media (max-width: 768px) {
            .quantum-text { font-size: 2rem; }
            .temporal-present h1 { font-size: 1.8rem; }
            .fold-plane { width: 250px; height: 250px; }
            .synapse-node { width: 50px; height: 50px; font-size: 0.6rem; }
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="cursor">
        <div class="cursor-dot"></div>
        <div class="cursor-ring"></div>
    </div>

    <!-- Quantum Navigation -->
    <nav class="quantum-nav">
        <div class="nav-organism active" data-dimension="superposition"></div>
        <div class="nav-organism" data-dimension="temporal echo"></div>
        <div class="nav-organism" data-dimension="resonance"></div>
        <div class="nav-organism" data-dimension="linguistic"></div>
        <div class="nav-organism" data-dimension="folding"></div>
        <div class="nav-organism" data-dimension="consciousness"></div>
        <div class="nav-organism" data-dimension="entropy"></div>
        <div class="nav-organism" data-dimension="synaptic"></div>
    </nav>

    <!-- Reality Container -->
    <div class="reality-container">
        <!-- Background Pattern -->
        <div class="quantum-dust"></div>

        <!-- Dimension 1: Quantum Superposition -->
        <div class="dimension active" data-dimension="0">
            <div class="quantum-field">
                <h1 class="quantum-text" id="quantum-text">
                    <!-- Text will be split into spans by JS -->
                    why write when you've already said it?
                </h1>
                <div class="quantum-options">
                    <span class="quantum-option">record</span>
                    <span class="quantum-option">speak</span>
                    <span class="quantum-option">transcribe</span>
                </div>
            </div>
        </div>

        <!-- Dimension 2: Temporal Echo -->
        <div class="dimension" data-dimension="1">
            <div class="temporal-space" id="temporal-space">
                <div class="temporal-present">
                    <h1>your words echo through time</h1>
                    <p style="opacity: 0.6; margin-top: 1rem;">every interaction leaves a trace</p>
                    <div class="temporal-options">
                        <span class="temporal-option">past recordings</span>
                        <span class="temporal-option">present thoughts</span>
                        <span class="temporal-option">future posts</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dimension 3: Emotional Resonance -->
        <div class="dimension" data-dimension="2">
            <div class="resonance-field" id="resonance-field">
                <div class="resonance-particles" id="resonance-particles"></div>
                <div class="resonance-center">
                    <h1 class="resonance-text">your thoughts create ripples</h1>
                    <p style="opacity: 0.5; margin-top: 1rem;">move to feel the resonance</p>
                </div>
            </div>
        </div>

        <!-- Dimension 4: Linguistic Decay -->
        <div class="dimension" data-dimension="3">
            <div class="linguistic-space">
                <div class="linguistic-evolution">
                    <p class="evolving-text stage-1" id="evolving-text">
                        Leverage your domain expertise to create compelling LinkedIn content that drives engagement and builds your professional brand through our AI-powered content transformation platform.
                    </p>
                    <div class="linguistic-triggers">
                        <span class="linguistic-trigger" data-stage="1">corporate</span>
                        <span class="linguistic-trigger" data-stage="2">human</span>
                        <span class="linguistic-trigger" data-stage="3">poetry</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dimension 5: Dimensional Folding -->
        <div class="dimension" data-dimension="4">
            <div class="folding-space">
                <div class="fold-container">
                    <div class="fold-plane">
                        <div>
                            <h2 style="font-weight: 100;">recording</h2>
                            <p style="opacity: 0.6; margin-top: 1rem;">your voice holds wisdom</p>
                        </div>
                    </div>
                    <div class="fold-plane">
                        <div>
                            <h2 style="font-weight: 100;">conversation</h2>
                            <p style="opacity: 0.6; margin-top: 1rem;">dialogue births insight</p>
                        </div>
                    </div>
                    <div class="fold-plane">
                        <div>
                            <h2 style="font-weight: 100;">reflection</h2>
                            <p style="opacity: 0.6; margin-top: 1rem;">notes become narratives</p>
                        </div>
                    </div>
                    <div class="fold-plane">
                        <div>
                            <h2 style="font-weight: 100;">transformation</h2>
                            <p style="opacity: 0.6; margin-top: 1rem;">thought becomes form</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dimension 6: Consciousness Mirror -->
        <div class="dimension" data-dimension="5">
            <div class="consciousness-space" id="consciousness-space">
                <div class="consciousness-reflection">
                    <h1 class="consciousness-question">what do you really want to say?</h1>
                </div>
            </div>
        </div>

        <!-- Dimension 7: Entropic Design -->
        <div class="dimension" data-dimension="6">
            <div class="entropic-space">
                <div class="entropic-grid" id="entropic-grid"></div>
                <div class="entropic-message">
                    <h1 style="font-weight: 100;">order dissolves into beauty</h1>
                    <p style="opacity: 0.5; margin-top: 1rem;">watch structure become poetry</p>
                </div>
            </div>
        </div>

        <!-- Dimension 8: Synaptic Network -->
        <div class="dimension" data-dimension="7">
            <div class="synaptic-space" id="synaptic-space">
                <!-- Nodes will be created by JS -->
            </div>
        </div>
    </div>

    <!-- Reality Shift Overlay -->
    <div class="reality-shift" id="reality-shift"></div>

    <script>
        // Global consciousness state
        let consciousness = {
            currentDimension: 0,
            mouseX: 0,
            mouseY: 0,
            resonanceActive: false,
            temporalEchoes: [],
            synapticConnections: [],
            entropyLevel: 0
        };

        // Custom cursor
        const cursor = document.querySelector('.cursor');
        const cursorDot = document.querySelector('.cursor-dot');
        const cursorRing = document.querySelector('.cursor-ring');

        document.addEventListener('mousemove', (e) => {
            consciousness.mouseX = e.clientX;
            consciousness.mouseY = e.clientY;
            
            cursor.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
            
            // Update CSS variables for reactive elements
            document.documentElement.style.setProperty('--consciousness', 
                Math.abs(e.clientX - window.innerWidth/2) / (window.innerWidth/2));
            document.documentElement.style.setProperty('--resonance', 
                Math.abs(e.clientY - window.innerHeight/2) / (window.innerHeight/2));
        });

        // Cursor states
        document.addEventListener('mousedown', () => cursor.classList.add('clicking'));
        document.addEventListener('mouseup', () => cursor.classList.remove('clicking'));

        // Navigation
        const navOrganisms = document.querySelectorAll('.nav-organism');
        const dimensions = document.querySelectorAll('.dimension');
        const realityShift = document.getElementById('reality-shift');

        navOrganisms.forEach((organism, index) => {
            organism.addEventListener('click', () => {
                if (consciousness.currentDimension === index) return;
                
                // Reality shift effect
                realityShift.classList.add('active');
                
                setTimeout(() => {
                    // Update active states
                    navOrganisms.forEach(o => o.classList.remove('active'));
                    organism.classList.add('active');
                    
                    dimensions.forEach(d => d.classList.remove('active'));
                    dimensions[index].classList.add('active');
                    
                    consciousness.currentDimension = index;
                    initializeDimension(index);
                    
                    realityShift.classList.remove('active');
                }, 500);
            });
            
            // Breathing effect on hover
            organism.addEventListener('mouseenter', () => {
                organism.classList.add('breathing');
            });
            
            organism.addEventListener('mouseleave', () => {
                organism.classList.remove('breathing');
            });
        });

        // Initialize dimensions
        function initializeDimension(index) {
            switch(index) {
                case 0: initQuantumSuperposition(); break;
                case 1: initTemporalEcho(); break;
                case 2: initResonanceField(); break;
                case 3: initLinguisticDecay(); break;
                case 4: initDimensionalFolding(); break;
                case 5: initConsciousnessMirror(); break;
                case 6: initEntropicDesign(); break;
                case 7: initSynapticNetwork(); break;
            }
        }

        // QUANTUM SUPERPOSITION
        function initQuantumSuperposition() {
            const text = document.getElementById('quantum-text');
            const words = text.textContent.trim().split(' ');
            text.innerHTML = words.map(word => 
                word.split('').map(char => 
                    `<span data-char="${char}">${char}</span>`
                ).join('')
            ).join(' ');
        }

        // TEMPORAL ECHO
        function initTemporalEcho() {
            const space = document.getElementById('temporal-space');
            
            space.addEventListener('click', (e) => {
                const echo = document.createElement('div');
                echo.className = 'temporal-echo visible';
                echo.textContent = new Date().toLocaleTimeString();
                echo.style.left = e.clientX + 'px';
                echo.style.top = e.clientY + 'px';
                
                space.appendChild(echo);
                
                setTimeout(() => echo.remove(), 3000);
            });
        }

        // RESONANCE FIELD
        function initResonanceField() {
            const field = document.getElementById('resonance-field');
            const particles = document.getElementById('resonance-particles');
            
            // Create particles
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particles.appendChild(particle);
            }
            
            // Mouse movement creates waves
            field.addEventListener('mousemove', (e) => {
                if (!consciousness.resonanceActive) return;
                
                const wave = document.createElement('div');
                wave.className = 'resonance-wave';
                wave.style.left = e.clientX - 150 + 'px';
                wave.style.top = e.clientY - 150 + 'px';
                
                field.appendChild(wave);
                setTimeout(() => wave.remove(), 2000);
                
                // Move particles away from mouse
                const particleElements = particles.querySelectorAll('.particle');
                particleElements.forEach(p => {
                    const rect = p.getBoundingClientRect();
                    const dx = rect.left - e.clientX;
                    const dy = rect.top - e.clientY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 100) {
                        const angle = Math.atan2(dy, dx);
                        const force = (100 - distance) / 100;
                        p.style.transform = `translate(${Math.cos(angle) * force * 20}px, ${Math.sin(angle) * force * 20}px)`;
                        
                        setTimeout(() => {
                            p.style.transform = 'translate(0, 0)';
                        }, 500);
                    }
                });
            });
            
            field.addEventListener('mouseenter', () => consciousness.resonanceActive = true);
            field.addEventListener('mouseleave', () => consciousness.resonanceActive = false);
        }

        // LINGUISTIC DECAY
        function initLinguisticDecay() {
            const text = document.getElementById('evolving-text');
            const triggers = document.querySelectorAll('.linguistic-trigger');
            
            const stages = {
                1: "Leverage your domain expertise to create compelling LinkedIn content that drives engagement and builds your professional brand through our AI-powered content transformation platform.",
                2: "Share what you know. We'll help you write it. Your voice, your insights, crafted into posts that connect with your network.",
                3: "speak and we listen / your wisdom takes wing / thoughts become poetry"
            };
            
            triggers.forEach(trigger => {
                trigger.addEventListener('click', () => {
                    const stage = trigger.dataset.stage;
                    text.className = `evolving-text stage-${stage}`;
                    text.textContent = stages[stage];
                });
            });
        }

        // DIMENSIONAL FOLDING
        function initDimensionalFolding() {
            // Auto-rotation is handled by CSS
        }

        // CONSCIOUSNESS MIRROR
        function initConsciousnessMirror() {
            const space = document.getElementById('consciousness-space');
            const thoughts = [
                "are you creating or consuming?",
                "what would you say if no one was listening?",
                "does your content reflect your truth?",
                "when did you last surprise yourself?",
                "what wisdom lives in your silence?",
                "are these your words or your mask?",
                "what story are you not telling?"
            ];
            
            setInterval(() => {
                const thought = document.createElement('div');
                thought.className = 'consciousness-thought';
                thought.textContent = thoughts[Math.floor(Math.random() * thoughts.length)];
                thought.style.left = Math.random() * 80 + 10 + '%';
                thought.style.top = Math.random() * 80 + 10 + '%';
                
                space.appendChild(thought);
                
                setTimeout(() => thought.classList.add('visible'), 100);
                setTimeout(() => thought.remove(), 5000);
            }, 3000);
        }

        // ENTROPIC DESIGN
        function initEntropicDesign() {
            const grid = document.getElementById('entropic-grid');
            
            // Create grid cells
            for (let i = 0; i < 400; i++) {
                const cell = document.createElement('div');
                cell.className = 'entropic-cell';
                grid.appendChild(cell);
            }
            
            // Entropy progression
            const cells = grid.querySelectorAll('.entropic-cell');
            let entropyInterval = setInterval(() => {
                if (consciousness.currentDimension !== 6) {
                    clearInterval(entropyInterval);
                    return;
                }
                
                const randomCell = cells[Math.floor(Math.random() * cells.length)];
                const currentDecay = parseInt(randomCell.className.match(/decay-(\d)/) ? randomCell.className.match(/decay-(\d)/)[1] : 0);
                
                if (currentDecay < 4) {
                    randomCell.className = `entropic-cell decay-${currentDecay + 1}`;
                }
                
                consciousness.entropyLevel++;
                
                // Reset after full decay
                if (consciousness.entropyLevel > 1000) {
                    cells.forEach(cell => cell.className = 'entropic-cell');
                    consciousness.entropyLevel = 0;
                }
            }, 50);
        }

        // SYNAPTIC NETWORK
        function initSynapticNetwork() {
            const space = document.getElementById('synaptic-space');
            space.innerHTML = ''; // Clear previous
            
            const nodes = [
                { x: 50, y: 25, text: 'speak' },
                { x: 25, y: 50, text: 'record' },
                { x: 75, y: 50, text: 'write' },
                { x: 50, y: 75, text: 'share' },
                { x: 50, y: 50, text: 'transform' }
            ];
            
            const nodeElements = [];
            
            // Create nodes
            nodes.forEach((node, i) => {
                const el = document.createElement('div');
                el.className = 'synapse-node';
                el.textContent = node.text;
                el.style.left = node.x + '%';
                el.style.top = node.y + '%';
                el.dataset.index = i;
                
                space.appendChild(el);
                nodeElements.push(el);
                
                el.addEventListener('click', () => {
                    el.classList.toggle('active');
                    updateSynapticConnections();
                });
            });
            
            // Create connections
            function updateSynapticConnections() {
                // Remove old connections
                space.querySelectorAll('.synapse-connection').forEach(c => c.remove());
                
                const activeNodes = space.querySelectorAll('.synapse-node.active');
                
                // Connect all active nodes
                for (let i = 0; i < activeNodes.length; i++) {
                    for (let j = i + 1; j < activeNodes.length; j++) {
                        const node1 = activeNodes[i];
                        const node2 = activeNodes[j];
                        
                        const rect1 = node1.getBoundingClientRect();
                        const rect2 = node2.getBoundingClientRect();
                        
                        const x1 = rect1.left + rect1.width / 2;
                        const y1 = rect1.top + rect1.height / 2;
                        const x2 = rect2.left + rect2.width / 2;
                        const y2 = rect2.top + rect2.height / 2;
                        
                        const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
                        
                        const connection = document.createElement('div');
                        connection.className = 'synapse-connection active';
                        connection.style.width = length + 'px';
                        connection.style.left = x1 + 'px';
                        connection.style.top = y1 + 'px';
                        connection.style.transform = `rotate(${angle}deg)`;
                        
                        space.appendChild(connection);
                    }
                }
            }
        }

        // Initialize first dimension
        initQuantumSuperposition();

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                const next = (consciousness.currentDimension + 1) % dimensions.length;
                navOrganisms[next].click();
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                const prev = (consciousness.currentDimension - 1 + dimensions.length) % dimensions.length;
                navOrganisms[prev].click();
            }
        });

        // Track interaction states
        const interactiveElements = document.querySelectorAll('.quantum-option, .temporal-option, .linguistic-trigger, .synapse-node');
        interactiveElements.forEach(el => {
            el.addEventListener('mouseenter', () => cursor.classList.add('hovering'));
            el.addEventListener('mouseleave', () => cursor.classList.remove('hovering'));
        });
    </script>
</body>
</html>