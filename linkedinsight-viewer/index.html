<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#ffffff" id="theme-color-meta" />
    <!-- Favicon -->
    <link rel="icon" href="/favicon_small.png" type="image/png" />
    <title>LinkedInsight Viewer</title>
    <!-- Google Fonts - IBM Plex Mono and Archivo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Archivo:wght@400;500;600;700&family=IBM+Plex+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- 
      CRITICAL: Dark mode flash prevention
      These inline styles MUST remain in the <head> to prevent white flash on page load.
      They set the background color immediately before any external CSS loads.
      DO NOT move to external CSS files or remove!
    -->
    <style>
      /* Minimal critical styles for preventing white flash */
      :root {
        --surface-primary: #ffffff;
      }
      [data-theme="dark"] {
        --surface-primary: #0a0a0a;
      }
      html, body {
        background-color: var(--surface-primary);
        margin: 0;
      }
    </style>
    
    <!-- 
      CRITICAL: Dark mode detection script
      This script MUST run before page render to prevent FOUC (Flash of Unstyled Content).
      It detects the user's theme preference and applies it immediately.
      DO NOT move to external JS files or defer/async this script!
    -->
    <script>
      // Immediately detect and apply theme to prevent white flash in dark mode
      (function() {
        const isDark = localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);
        
        if (isDark) {
          // Set dark theme - inline style and data-theme attribute
          document.documentElement.style.backgroundColor = '#0a0a0a';
          document.documentElement.setAttribute('data-theme', 'dark');
          document.getElementById('theme-color-meta').setAttribute('content', '#0a0a0a');
        } else {
          // Set light theme
          document.documentElement.style.backgroundColor = '#ffffff';
          document.documentElement.setAttribute('data-theme', 'light');
          document.getElementById('theme-color-meta').setAttribute('content', '#ffffff');
        }
      })();
    </script>

    <!-- SPA redirect script -->
    <script>
      // Single Page Apps for GitHub Pages or Vercel
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct URL and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new URL.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>

    <!-- Auth enforcement script -->
    <script>
      // Ensure auth tokens are properly handled on page load
      (function() {
        // Skip auth check on callback pages
        if (window.location.pathname.includes('/callback')) return;

        // Clear URL tokens if no auth data exists in storage
        // This prevents token bypass attempts
        if (window.location.search.includes('token')) {
          const hasAuthData = Object.keys(localStorage).some(key =>
            key.startsWith('authkit') || key.includes('token')
          );
          
          if (!hasAuthData) {
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>