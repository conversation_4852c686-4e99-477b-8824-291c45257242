{"permissions": {"allow": ["mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_navigate", "Bash(grep:*)", "mcp__puppeteer__puppeteer_click", "Bash(ls:*)", "mcp__puppeteer__puppeteer_fill", "Bash(rg:*)", "WebFetch(domain:clerk.com)", "Bash(find:*)", "mcp__gemini__think_deeper", "Bash(npx tsc:*)", "Bash(npm run lint:*)", "Bash(npx eslint:*)"], "deny": []}, "enableAllProjectMcpServers": false}