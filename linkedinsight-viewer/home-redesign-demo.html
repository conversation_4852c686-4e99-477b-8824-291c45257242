<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedInsight Home Page Redesign Ideas</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600&family=Archivo:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* Design System Tokens */
        :root {
            --surface-window: rgb(237, 237, 237);
            --surface-primary: hsl(0, 0%, 100%);
            --surface-secondary: hsl(0, 0%, 96.1%);
            --text-primary: hsl(0, 0%, 3.9%);
            --text-secondary: hsl(0, 0%, 45.1%);
            --border-primary: hsl(0, 0%, 0%);
            --font-family-mono: 'IBM Plex Mono', monospace;
            --font-family-heading: 'Archivo', sans-serif;
            --shadow-offset: 7px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: var(--surface-window);
            padding: 2rem;
            background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .demo-section {
            margin-bottom: 4rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .demo-title {
            font-family: var(--font-family-heading);
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--text-primary);
        }

        /* Shadow Container Base */
        .shadow-box {
            background-color: var(--surface-primary);
            border: 2px solid var(--border-primary);
            box-shadow: var(--shadow-offset) var(--shadow-offset) 0px 0px hsl(0, 0%, 30%);
            transition: all 150ms ease;
            cursor: pointer;
            padding: 2rem;
        }

        .shadow-box:hover {
            transform: translate(-2px, -2px);
            box-shadow: 9px 9px 0px 0px hsl(0, 0%, 30%);
        }

        .shadow-box:active {
            transform: translate(2px, 2px);
            box-shadow: 5px 5px 0px 0px hsl(0, 0%, 30%);
        }

        .icon {
            font-size: 2rem;
            filter: grayscale(100%);
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }

        .title {
            font-family: var(--font-family-heading);
            font-size: 2.5rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-family: var(--font-family-mono);
            font-size: 1rem;
            color: var(--text-secondary);
        }

        .item-title {
            font-family: var(--font-family-mono);
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .item-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        /* Design 1: Stacked Vertical Layout */
        .design1 {
            max-width: 600px;
            margin: 0 auto;
        }

        .design1 .header-box {
            margin-bottom: 2rem;
            text-align: center;
        }

        .design1 .options-stack {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .design1 .option-box {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding: 1.5rem 2rem;
        }

        .design1 .option-box .icon {
            font-size: 2.5rem;
            margin-bottom: 0;
        }

        /* Design 2: Asymmetric Grid */
        .design2 {
            max-width: 900px;
            margin: 0 auto;
        }

        .design2 .grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 1.5rem;
            height: 500px;
        }

        .design2 .header-box {
            grid-column: 1 / -1;
            text-align: center;
            padding: 2.5rem;
        }

        .design2 .large-box {
            grid-row: 2 / 4;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .design2 .small-box {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 1.5rem;
        }

        /* Design 3: Masonry-Style Layout */
        .design3 {
            max-width: 800px;
            margin: 0 auto;
        }

        .design3 .header-box {
            margin-bottom: 2rem;
            text-align: center;
            padding: 3rem;
        }

        .design3 .masonry {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .design3 .box-tall {
            grid-row: span 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 2.5rem 2rem;
        }

        .design3 .box-regular {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 2rem;
        }

        /* Design 4: Bento Box Layout */
        .design4 {
            max-width: 800px;
            margin: 0 auto;
        }

        .design4 .bento-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 140px);
            gap: 1.5rem;
        }

        .design4 .bento-header {
            grid-column: 1 / 3;
            grid-row: 1 / 3;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2rem;
        }

        .design4 .bento-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 1.5rem;
        }

        .design4 .bento-item-wide {
            grid-column: span 2;
        }

        .design4 .bento-header .title {
            font-size: 2rem;
            text-align: left;
        }

        .design4 .bento-header .subtitle {
            text-align: left;
        }

        /* Design 5: Centered Focus Layout */
        .design5 {
            max-width: 700px;
            margin: 0 auto;
            height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .design5 .hero-box {
            text-align: center;
            padding: 3rem;
            margin-bottom: 2rem;
        }

        .design5 .options-row {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .design5 .option-card {
            flex: 1;
            max-width: 200px;
            padding: 1.5rem 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .design5 .option-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid black;
            padding: 1rem;
            box-shadow: 4px 4px 0 black;
        }

        .nav a {
            display: block;
            padding: 0.5rem;
            color: var(--text-primary);
            text-decoration: none;
            font-family: var(--font-family-mono);
            font-size: 0.875rem;
        }

        .nav a:hover {
            background: var(--surface-secondary);
        }
    </style>
</head>
<body>
    <nav class="nav">
        <a href="#design1">Design 1: Vertical Stack</a>
        <a href="#design2">Design 2: Asymmetric</a>
        <a href="#design3">Design 3: Masonry</a>
        <a href="#design4">Design 4: Bento Box</a>
        <a href="#design5">Design 5: Centered Focus</a>
    </nav>

    <!-- Design 1: Stacked Vertical Layout -->
    <section class="demo-section" id="design1">
        <h2 class="demo-title">Design 1: Vertical Stack (Mobile-First)</h2>
        <div class="design1">
            <div class="shadow-box header-box">
                <h1 class="title">Why write when you've already said it?</h1>
                <p class="subtitle">You share insights daily. We turn them into LinkedIn posts<br>New here? Quick guide →</p>
            </div>
            <div class="options-stack">
                <div class="shadow-box option-box">
                    <span class="icon">🎙️</span>
                    <div>
                        <h3 class="item-title">Have a recording?</h3>
                        <p class="item-desc">Turn podcasts & meetings into multiple posts</p>
                    </div>
                </div>
                <div class="shadow-box option-box">
                    <span class="icon">💬</span>
                    <div>
                        <h3 class="item-title">Need to brainstorm?</h3>
                        <p class="item-desc">Answer questions, get your post</p>
                    </div>
                </div>
                <div class="shadow-box option-box">
                    <span class="icon">📝</span>
                    <div>
                        <h3 class="item-title">Have notes for a post?</h3>
                        <p class="item-desc">Watch as they transform into a polished post</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 2: Asymmetric Grid -->
    <section class="demo-section" id="design2">
        <h2 class="demo-title">Design 2: Asymmetric Grid</h2>
        <div class="design2">
            <div class="grid">
                <div class="shadow-box header-box">
                    <h1 class="title">Why write when you've already said it?</h1>
                    <p class="subtitle">You share insights daily. We turn them into LinkedIn posts</p>
                </div>
                <div class="shadow-box large-box">
                    <span class="icon" style="font-size: 4rem;">🎙️</span>
                    <h3 class="item-title" style="font-size: 1.25rem;">Have a recording?</h3>
                    <p class="item-desc">Turn podcasts & meetings into multiple posts</p>
                </div>
                <div class="shadow-box small-box">
                    <span class="icon">💬</span>
                    <h3 class="item-title">Need to brainstorm?</h3>
                    <p class="item-desc">Answer questions, get your post</p>
                </div>
                <div class="shadow-box small-box">
                    <span class="icon">📝</span>
                    <h3 class="item-title">Have notes?</h3>
                    <p class="item-desc">Transform into polished posts</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 3: Masonry Style -->
    <section class="demo-section" id="design3">
        <h2 class="demo-title">Design 3: Masonry Style</h2>
        <div class="design3">
            <div class="shadow-box header-box">
                <h1 class="title">Why write when you've already said it?</h1>
                <p class="subtitle">You share insights daily. We turn them into LinkedIn posts<br>New here? Quick guide →</p>
            </div>
            <div class="masonry">
                <div class="shadow-box box-tall">
                    <span class="icon" style="font-size: 3rem;">🎙️</span>
                    <h3 class="item-title">Have a recording?</h3>
                    <p class="item-desc">Turn podcasts & meetings into multiple posts</p>
                </div>
                <div class="shadow-box box-regular">
                    <span class="icon">💬</span>
                    <h3 class="item-title">Need to brainstorm?</h3>
                    <p class="item-desc">Answer questions, get your post</p>
                </div>
                <div class="shadow-box box-regular">
                    <span class="icon">📝</span>
                    <h3 class="item-title">Have notes for a post?</h3>
                    <p class="item-desc">Watch as they transform into a polished post</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 4: Bento Box Layout -->
    <section class="demo-section" id="design4">
        <h2 class="demo-title">Design 4: Bento Box Layout</h2>
        <div class="design4">
            <div class="bento-grid">
                <div class="shadow-box bento-header">
                    <h1 class="title">Why write when<br>you've already<br>said it?</h1>
                    <p class="subtitle">You share insights daily.<br>We turn them into LinkedIn posts</p>
                </div>
                <div class="shadow-box bento-item">
                    <span class="icon">🎙️</span>
                    <h3 class="item-title">Recording</h3>
                </div>
                <div class="shadow-box bento-item">
                    <span class="icon">💬</span>
                    <h3 class="item-title">Brainstorm</h3>
                </div>
                <div class="shadow-box bento-item bento-item-wide">
                    <span class="icon">📝</span>
                    <h3 class="item-title">Have notes for a post?</h3>
                    <p class="item-desc">Transform into polished posts</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Design 5: Centered Focus -->
    <section class="demo-section" id="design5">
        <h2 class="demo-title">Design 5: Centered Focus</h2>
        <div class="design5">
            <div class="shadow-box hero-box">
                <h1 class="title" style="font-size: 3rem; margin-bottom: 1rem;">Why write when you've already said it?</h1>
                <p class="subtitle" style="font-size: 1.125rem;">You share insights daily. We turn them into LinkedIn posts</p>
            </div>
            <div class="options-row">
                <div class="shadow-box option-card">
                    <span class="icon">🎙️</span>
                    <h3 class="item-title">Recording</h3>
                    <p class="item-desc">Podcasts & meetings</p>
                </div>
                <div class="shadow-box option-card">
                    <span class="icon">💬</span>
                    <h3 class="item-title">Brainstorm</h3>
                    <p class="item-desc">Answer questions</p>
                </div>
                <div class="shadow-box option-card">
                    <span class="icon">📝</span>
                    <h3 class="item-title">Notes</h3>
                    <p class="item-desc">Transform to posts</p>
                </div>
            </div>
        </div>
    </section>
</body>
</html>