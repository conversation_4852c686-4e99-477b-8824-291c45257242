# CSS Refactor Testing Checklist

## Pre-Test Setup
- [ ] Delete old `src/styles/design-system/components.css` file
- [ ] Run `npm run dev` and ensure no build errors
- [ ] Test in both Chrome and Safari (if on macOS)
- [ ] Test in both light and dark modes

## Component Visual Testing

### 1. Buttons
- [x] **Primary Button**: Black bg/white text (light), White bg/black text (dark)
- [x] **Secondary Button**: Gray bg with border
- [x] **Outline Button**: Transparent bg with border
- [x] **Button States**: Hover, active, disabled states work correctly
- [x] **Loading State**: GenerateButton shows spinner when loading
- [x] **Size Variants**: sm, md, lg sizes display correctly

### 2. Forms & Inputs
- [x] **Text Inputs**: Border, focus state, placeholder text
- [x] **Textareas**: Proper padding, focus states, no resize handle
- [x] **Draft Textarea**: Fixed size, proper styling in agent panes
- [x] **Brief Textarea**: 600px min-height maintained
- [x] **Radio Buttons**: Inline display, proper spacing
- [x] **Length Selector**: Buttons highlight when active

### 3. Containers
- [x] **Shadow Containers**: Box shadows visible in both themes
- [x] **Form Containers**: Proper spacing and alignment
- [x] **Draft Containers**: Hover states, click interactions
- [x] **Agent Panes**: Background consistency in dark mode

### 4. Chat Components
- [ ] **Chat Messages**: User/assistant message styling
- [ ] **Chat Input**: Multi-line input with proper border
- [ ] **Chat History**: Scrolling, message spacing
- [ ] **Streaming Messages**: Loading animation displays

### 5. Tabs
- [ ] **Tab Navigation**: Active/inactive states
- [ ] **Tab Emojis**: Grayscale filter applied
- [ ] **Fixed Tabs**: Sticky header, scrollable content
- [ ] **Tab Transitions**: Smooth switching between tabs

### 6. Loading States
- [ ] **Loading Spinner**: Size variants (sm, md, lg)
- [ ] **Loading Messages**: Random message display
- [ ] **Progress Indicators**: Proper animations

### 7. Typography & Prose
- [ ] **Headings**: h1, h2, h3 with proper font family
- [ ] **Paragraphs**: Line height and spacing
- [ ] **Lists**: Bullets, numbers, proper indentation
- [ ] **Code Blocks**: Background, border, font family
- [ ] **Inline Code**: Background color, padding

### 8. Markdown Rendering
- [ ] **Markdown Output**: Proper styling in chat messages
- [ ] **Code Syntax**: Highlighting works correctly
- [ ] **Blockquotes**: Border and background styling
- [ ] **Links**: Hover states, underlines

## Page-Specific Testing

### Home Page
- [ ] Creator cards: Black/white header inversion
- [ ] Grid layout with stagger animation
- [ ] Responsive design at different widths

### Brief Input Page
- [ ] Form layout and spacing
- [ ] Generate button functionality
- [ ] Error state display

### Transcript Input Page
- [ ] File upload area styling
- [ ] Transcript display formatting
- [ ] Process button (cool-button) animation

### Ideas Page
- [ ] Idea cards layout and shadows
- [ ] Star ratings display
- [ ] Citation highlighting
- [ ] Generate variations buttons

### Content Agent Page
- [ ] Dual pane layout
- [ ] Agent status indicators
- [ ] Draft switching functionality

### Editor Agent Page
- [ ] Diff editor styling
- [ ] Side-by-side comparison view
- [ ] Syntax highlighting

## Dark Mode Specific
- [ ] Background color consistency
- [ ] Text contrast meets accessibility standards
- [ ] Border colors visible but subtle
- [ ] Surface colors properly differentiated
- [ ] No pure black/white except for buttons

## Responsive Design
- [ ] Mobile viewport (< 640px)
- [ ] Tablet viewport (640px - 1024px)
- [ ] Desktop viewport (> 1024px)
- [ ] Container max-widths respected

## Performance Checks
- [ ] CSS file size comparison (before/after)
- [ ] Page load time comparison
- [ ] No CSS conflicts in console
- [ ] No missing CSS classes warnings

## Accessibility
- [ ] Focus indicators visible on all interactive elements
- [ ] Color contrast ratios meet WCAG standards
- [ ] Keyboard navigation works throughout

## Browser Compatibility
- [ ] Chrome/Edge (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Mobile Safari (iOS)
- [ ] Chrome Mobile (Android)

## Final Verification
- [ ] All design system tokens properly applied
- [ ] No inline styles that should be classes
- [ ] No duplicate CSS rules
- [ ] CSS classes follow naming conventions
- [ ] All old CSS references removed from components