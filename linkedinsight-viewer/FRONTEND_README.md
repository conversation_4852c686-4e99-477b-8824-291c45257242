# LinkedInsight Viewer

A React-based viewer for LinkedIn content analysis reports and AI-driven content generation.

## Features

- Creator Library for browsing analyzed LinkedIn creators
- Comprehensive content analysis with tabbed interface
- AI-powered content generation with creator style matching
- Unified design system with consistent styling
- Dark/light mode with seamless transitions
- Responsive design for all device sizes
- Authentication via Clerk

## Architecture

### Design System

LinkedInsight implements a token-based design system providing a single source of truth for styling:

- **Design Tokens**: CSS variables for colors, spacing, typography in `src/styles/design-system/tokens.css`
- **Component Classes**: Standardized styling in `src/styles/design-system/components.css`
- **Tailwind Integration**: Custom theme extending design tokens
- **Dark Mode**: Implemented via data-theme attribute

### Component Structure

- **Layout**: Core layout components in `src/components/layout/`
- **UI Components**: Reusable UI elements in `src/components/ui/`
- **Feature Components**: Feature-specific components in dedicated directories
- **Pages**: Top-level page components in `src/components/`

### API Integration

- **Centralized API Client**: `src/services/apiClient.ts` handles all backend communication
- **Service Modules**: Feature-specific API wrappers in `src/services/`
- **React Hooks**: Custom hooks for service integration in `src/hooks/`

### File Structure

```
src/
├── components/           # UI components
│   ├── agent/            # Agent-related components
│   ├── ai-assistant/     # AI assistant interface
│   ├── auth/             # Authentication components
│   ├── brief/            # Brief input and processing
│   ├── content-strategy/ # Content strategy tools
│   ├── interview/        # Interview agent components
│   ├── layout/           # Core layout components
│   ├── ui/               # Reusable UI components
│   │   └── icons/        # SVG icons
├── contexts/             # React context providers
├── data/                 # Static data and constants
├── hooks/                # Custom React hooks
├── lib/                  # Third-party library integrations
├── services/             # API services and data fetching
├── styles/               # Global styles
│   └── design-system/    # Design system files
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
```

## Application Structure

### Key Pages

- **Home** (`/`): Entry point with brief input form and creator agent
- **Creator Library** (`/creators`): Browse and select analyzed creators
- **Creator Analysis** (`/:creatorName`): View analysis for a specific creator
- **Agent Page** (`/agent`): Dedicated agent interface for content generation
- **Design System** (`/design-system`): Design system documentation and examples

### Content Analysis Sections

- **Overview**: Key metrics and findings summary
- **Themes**: Content theme analysis
- **Hooks**: Opening lines and attention-grabbing patterns
- **Body Structure**: Content organization frameworks
- **Endings & CTAs**: Conclusions and calls-to-action
- **Linguistic Patterns**: Language choices and patterns

### Core Components

- **Layout**: Main layout wrapper with header and content area
- **ShadowContainer**: Signature container component used throughout the app
- **AgentPanes**: Two-pane interface for agent interaction
- **BriefInputForm**: Form for submitting content briefs
- **Tabs/TabPanel**: Tabbed interface for content navigation
- **MarkdownRenderer**: Renders analysis content from markdown

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Backend Integration

The frontend connects to the LinkedInsight FastAPI backend:

1. **Backend Server**: Ensure the FastAPI server is running via `python run_api_server.py`
2. **API Configuration**: Default API URL is `http://localhost:8001` (configurable in `.env`)
3. **API Routes**:
   - **Analysis Results**: `/api/results/{creator_name}/{analysis_type}`
   - **Agent Router**: `/api/agent/` (Handles agent operations via paths like `/api/agent/generate`, `/api/agent/status/{job_id}`, etc.)
   - **Brief Classifier**: `/api/classifier/brief`

## Development

### Design System

The design system implementation follows a token-based approach that provides a single source of truth for all styling. Key components include:

1. **Design Tokens**: Core variables for colors, spacing, typography, and other visual properties
   - Light/dark mode variants via `data-theme` attribute
   - Semantic naming (e.g., `--surface-primary` instead of `--white`)

2. **Component Classes**: Standardized CSS classes for common UI elements
   - Consistent styling for textareas, buttons, containers
   - Uses design tokens for all values
   - Reduces duplication and ensures consistency

3. **Tailwind Integration**: Custom Tailwind theme that maps to design tokens
   - Access tokens via utility classes (e.g., `bg-surface-primary`)
   - Custom spacing, color, and typography scales

4. **Key Components**:
   - `ShadowContainer`: Signature container with consistent shadow styling
   - `Button`: Standardized button with variants
   - `Layout`: Core layout structure with header and content area

### Design System Usage

```jsx
// Using design tokens with Tailwind
<div className="bg-surface-primary text-text-primary p-space-4">
  <h2 className="font-heading text-xl">Content</h2>
</div>

// Using component classes
<textarea className="textarea-input" />
<div className="shadow-container">
  <div className="shadow-container-content">Content</div>
</div>
```

### Best Practices

- Use design tokens instead of hardcoded values
- Prefer component classes for common UI elements
- Combine with Tailwind using the `cn` utility for flexibility
- Use semantic class names that describe purpose, not appearance

## License

MIT
