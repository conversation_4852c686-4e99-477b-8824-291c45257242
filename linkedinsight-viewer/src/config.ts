// Configuration for different environments

// Default to development environment
const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

// API configuration
export const API_CONFIG = {
  // Use environment variable if available, otherwise use default URLs
  BASE_URL: import.meta.env.VITE_API_BASE_URL ||
    (isDevelopment ? 'http://localhost:8001' : 'https://linkedinsight-api-bff25eb4f27e.herokuapp.com'),
};

// Frontend configuration
export const APP_CONFIG = {
  APP_NAME: 'LinkedInsight',
  APP_VERSION: '1.0.0',
};
