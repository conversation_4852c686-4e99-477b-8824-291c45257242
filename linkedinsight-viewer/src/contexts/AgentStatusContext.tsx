import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// Define step keys and user-friendly messages
const STEP_MESSAGES: Record<string, string> = {
  initializing: 'Initializing generation...',
  processing_theme: 'Identifying theme...',
  creating_hook: 'Crafting hook...',
  developing_body: 'Drafting body...',
  crafting_ending: 'Finalizing ending...',
  completed: 'Content generated successfully!',
  failed: 'Error generating content.',
  pending: 'Job queued, waiting to start...',
  idle: '' // Added for idle state
};

// 1. Define the state structure
interface AgentStatusState {
  overallStatus: string; // e.g., IDLE, PENDING, RUNNING, COMPLETED, FAILED
  currentStepKey: string | null; // e.g., initializing, processing_theme
  isGenerating: boolean;
  userFriendlyMessage: string;
}

// 2. Define action types
type AgentStatusAction = 
  | { type: 'START_GENERATION'; payload?: { initialStepKey?: string } }
  | { type: 'UPDATE_PROGRESS'; payload: { overallStatus: string; currentStepKey?: string | null } }
  | { type: 'END_GENERATION'; payload: { finalStatus: string; finalStepKey?: string | null } };

// 3. Initial state
const initialState: AgentStatusState = {
  overallStatus: 'IDLE',
  currentStepKey: null,
  isGenerating: false,
  userFriendlyMessage: STEP_MESSAGES.idle
};

// 4. Reducer function
const agentStatusReducer = (state: AgentStatusState, action: AgentStatusAction): AgentStatusState => {
  console.log('[AgentStatusContext] Action received:', JSON.stringify(action));
  console.log('[AgentStatusContext] State BEFORE:', JSON.stringify(state));
  let newState = state; // Default to current state if action not handled

  switch (action.type) {
    case 'START_GENERATION': {
      const initialStep = action.payload?.initialStepKey || 'initializing';
      newState = {
        ...state,
        overallStatus: 'PENDING', 
        currentStepKey: initialStep,
        isGenerating: true,
        userFriendlyMessage: STEP_MESSAGES[initialStep] || 'Starting generation...'
      };
      break;
    }
    case 'UPDATE_PROGRESS': {
      // Use distinct names for payload values to avoid any potential (though unlikely) scope/closure confusion
      const payloadOverallStatus = action.payload.overallStatus;
      const payloadCurrentStepKey = action.payload.currentStepKey;

      const stepKeyForMsg = payloadCurrentStepKey === undefined ? null : payloadCurrentStepKey;
      const messageKey = stepKeyForMsg || payloadOverallStatus.toLowerCase();
      
      newState = {
        ...state, // Spread previous state first
        overallStatus: payloadOverallStatus,
        currentStepKey: stepKeyForMsg,
        userFriendlyMessage: STEP_MESSAGES[messageKey] || STEP_MESSAGES[payloadOverallStatus.toLowerCase()] || 'Processing...'
      };
      // Explicitly set isGenerating on the newState object AFTER initial construction
      const statusLower = payloadOverallStatus.toLowerCase();
      newState.isGenerating = statusLower === 'pending' || statusLower === 'running';
      break;
    }
    case 'END_GENERATION': {
      const { finalStatus, finalStepKey } = action.payload;
      const resolvedFinalStepKey = finalStepKey === undefined ? null : finalStepKey;
      const finalMessageKey = resolvedFinalStepKey || finalStatus.toLowerCase();
      newState = {
        ...state,
        overallStatus: finalStatus,
        currentStepKey: resolvedFinalStepKey, 
        isGenerating: false,
        userFriendlyMessage: STEP_MESSAGES[finalMessageKey] || (finalStatus === 'COMPLETED' ? STEP_MESSAGES.completed : STEP_MESSAGES.failed)
      };
      break;
    }
    default:
      // Explicitly return current state if action type is unrecognized
      // Or, you could throw an error for unhandled actions if preferred
      newState = state; 
  }
  console.log('[AgentStatusContext] State AFTER:', JSON.stringify(newState));
  return newState;
};

// 5. Create context
const AgentStatusContext = createContext<{
  state: AgentStatusState;
  dispatch: React.Dispatch<AgentStatusAction>;
} | undefined>(undefined);

// 6. Create provider component
interface AgentStatusProviderProps {
  children: ReactNode;
}

export const AgentStatusProvider: React.FC<AgentStatusProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(agentStatusReducer, initialState);
  return (
    <AgentStatusContext.Provider value={{ state, dispatch }}>
      {children}
    </AgentStatusContext.Provider>
  );
};

// 7. Custom hook for easy consumption
export const useAgentStatus = () => {
  const context = useContext(AgentStatusContext);
  if (context === undefined) {
    throw new Error('useAgentStatus must be used within an AgentStatusProvider');
  }
  return context;
}; 