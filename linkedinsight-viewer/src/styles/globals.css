@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Archivo:wght@400;500;700&display=swap');

/* Import design system */
@import './design-system/index.css';

/* Import component styles */
@import './components/home.css';
@import './components/markdown.css';
@import './components/pricing-cards.css';

/* Import transition styles */
@import './grid-stagger-transition.css';
@import './page-transitions.css';

@tailwind base;
@tailwind components;
@tailwind utilities;


/* 
 * Select dropdown animations
 * 
 * IMPORTANT: This is the ONLY place where Select animations should be defined.
 * Do NOT add animation utility classes to the Select component itself.
 * 
 * Previously, the Select component tried to use undefined Tailwind animation classes
 * (animate-in, fade-out-0, etc.) which caused severe performance issues with multiple
 * selects on one page. Keep animations simple and defined here.
 */
[data-radix-popper-content-wrapper] {
  animation: fadeIn 150ms ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* DiffEditor styles */
.draft-textarea[contenteditable] {
  @apply bg-background text-foreground;
}

.draft-textarea[contenteditable]:focus {
  @apply outline-none;
}

.draft-textarea[contenteditable]:empty:before {
  content: attr(data-placeholder);
  @apply text-muted-foreground;
}

/* Icon button styles */
.icon-button {
  @apply w-9 h-9 p-0;
}

.icon-button svg {
  @apply w-4 h-4;
}

/* Basic markdown styles using design system tokens */
@layer base {
  h1 {
    @apply text-2xl font-bold mb-6 mt-8;
    font-family: var(--font-family-heading);
    color: var(--text-primary);
  }
  h2 {
    @apply text-xl font-bold mb-4 mt-6;
    font-family: var(--font-family-heading);
    color: var(--text-primary);
  }
  h3 {
    @apply text-lg font-bold mb-3 mt-5;
    font-family: var(--font-family-heading);
    color: var(--text-primary);
  }
  p {
    @apply mb-4 leading-relaxed;
    color: var(--text-primary);
  }
  ul, ol {
    @apply mb-6 pl-0;
    color: var(--text-primary);
  }
  ol {
    @apply list-decimal list-outside ml-5;
  }
  ol > li {
    @apply relative pl-4 mb-3;
  }
  ul {
    @apply list-disc list-outside ml-5;
  }
  ul > li {
    @apply relative pl-4 mb-3;
  }
  blockquote {
    @apply border-l-4 p-4 italic my-4 rounded-r;
    border-color: var(--border-subtle);
    background-color: var(--surface-tertiary);
  }
  code {
    @apply rounded px-1.5 py-0.5 text-[0.9em];
    font-family: var(--font-family-mono);
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
  }
  pre {
    @apply p-4 rounded-lg mb-4 overflow-x-auto;
    background-color: var(--surface-tertiary);
    border: 1px solid var(--border-subtle);
  }
  pre code {
    background-color: transparent;
    color: var(--text-primary);
    padding: 0;
  }
}

body {
  font-family: var(--font-family-base);
  color: var(--text-primary);
  /* Paper texture background for light mode */
  background: 
    url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.75' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' opacity='0.12'/%3E%3C/svg%3E"),
    linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
}

/* Dark mode: Clean gradient without texture */
[data-theme="dark"] body {
  background: linear-gradient(135deg, #0a0a0a 0%, #141414 50%, #0f0f0f 100%);
}

/* Global button styling using design system tokens */
button {
  font-family: var(--font-family-mono);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Button styling for Process Transcript button - using design system tokens */
.cool-button {
  height: 50px;
  position: relative;
  cursor: pointer;
  border: none;
  padding: 0;
  width: 100%;
  z-index: 1;
  transform-style: preserve-3d;
  -webkit-tap-highlight-color: transparent;
}

.cool-button:active {
  transform: scale(0.95);
}

.button-inner {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  font-weight: 500;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.button-inner:hover {
  opacity: 0.9;
}

/* 
 * Tailwind/shadcn compatibility layer
 * These variables are required for Tailwind utilities and shadcn/ui components.
 * They map to our design system tokens defined in design-system/tokens.css
 * DO NOT remove these even if they seem redundant - components depend on them!
 */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 3.9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
  }

  [data-theme="dark"] {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 30%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

/* Base element styles */
@layer base {
  * {
    border-color: var(--border-subtle);
  }
  /* Note: body styles are already defined above using design system tokens */
}

/*
 * Draft Textarea Styles are now defined in components.css
 * The .draft-textarea class in components.css has been updated to be non-resizable
 */

/*
 * Agent Pane Styles are now using shadow-container classes from components.css
 * The .agent-pane-container and .agent-pane-content classes should be replaced with
 * .shadow-container and .shadow-container-content classes in the components
 */

/* Force same background for draft container areas as main pane */
[data-theme="dark"] .flex.flex-col.h-full.pt-2 {
  background-color: var(--surface-primary);
}

/* Character count styling - moved from duplicate in globals.css */
.character-count {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  text-align: right;
  margin-top: var(--space-2);
}

/* Dark mode text visibility for agent panes - using design system tokens */
[data-theme="dark"] .agent-pane-content .dark\:text-neutral-400 {
  color: var(--text-secondary);
}

[data-theme="dark"] .agent-pane-content .dark\:text-neutral-100 {
  color: var(--text-primary);
}

/* Transition styles */
.transform-style-3d {
  transform-style: preserve-3d;
}

.rotateX-90 {
  transform: rotateX(90deg);
}

.transition-all {
  transition-property: all;
}

/* Tab Emoji Styles */
.tab-emoji {
  filter: grayscale(100%);
  margin-right: 8px;
  font-size: 0.9em;
  opacity: 0.8;
}

/* Adjust opacity for active/inactive states */
.tab:not(.active) .tab-emoji {
  opacity: 0.7;
}

.tab.active .tab-emoji {
  opacity: 1;
}

/* Optional hover effect */
.tab:hover .tab-emoji {
  opacity: 0.9;
}

/* Remove list styling from breadcrumbs */
nav[aria-label="breadcrumb"] ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

nav[aria-label="breadcrumb"] li {
  list-style: none;
}

/* Fixed Tabs Styles - using design system tokens */
/* Note: The base styles are now in components.css */

/* Prevent outer scrollbar */
body.has-fixed-tabs {
  overflow: hidden;
  background-color: var(--surface-primary);
}

.tabs-container-fixed {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto; /* Fill available space but allow shrinking */
  max-height: calc(100vh - 4.5rem - 1rem); /* Limit to viewport height minus header & bottom padding */
  min-height: 0; /* Allow flex children to shrink */
}

.tabs-header-fixed {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: inherit;
}

.tabs-content-scrollable {
  overflow-y: auto;
  flex: 1 1 auto; /* Take remaining space */
  min-height: 0;
  scrollbar-width: thin;
  scrollbar-color: var(--text-tertiary) transparent;
  padding-bottom: 0; /* Add bottom padding to ensure content doesn't touch the edge */
  display: flex;
  flex-direction: column;
}

/* Global scrollbar styling - using design system tokens */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--text-tertiary);
  border-radius: var(--radius-sm);
}

*::-webkit-scrollbar-track {
  background-color: var(--surface-primary);
  border-radius: var(--radius-sm);
}

/* Fix scrollbar corner rendering issue with rounded containers
   Ensures corners match the track background and have rounded edges */
*::-webkit-scrollbar-corner {
  background-color: var(--surface-primary);
  border-radius: var(--radius-sm);
}

/* Special handling for shadow containers with rounded borders
   Makes scrollbar background transparent so container background shows through
   This prevents the ugly gap between scrollbar and container border radius */
.shadow-container-content::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: var(--radius-lg);
}

.shadow-container-content::-webkit-scrollbar-corner {
  background-color: transparent;
}


/* Clerk Modal Centering */
.cl-modalBackdrop {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.cl-modalContent {
  position: relative !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  margin: auto !important;
}

/* Force center the Clerk modal container */
.cl-modalContainer {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
}

/* Center Clerk sign-in and sign-up pages */
.cl-signIn-root,
.cl-signUp-root {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: calc(100vh - 3rem) !important; /* Account for header height */
  padding-top: 3rem; /* Add padding for fixed header */
}

/* Ensure the card itself is centered */
.cl-card,
.cl-formContainer {
  margin: auto !important;
}

/* Remove double container appearance from Clerk sign-in */
.cl-cardBox {
  box-shadow: none !important;
  background-color: transparent !important;
}

/* Note: Clerk card styles are applied via JavaScript in clerkStyles.ts
   due to Clerk dynamically loading styles that override CSS */

/* Fix for dual pane layouts to prevent content clipping */
.grid.grid-cols-2 .shadow-container {
  margin-bottom: 0;
  margin-right: 0;
  height: 100%;
}

/* Ensure action buttons area remains visible */
.grid.grid-cols-2 .action-buttons {
  padding-right: var(--space-2);
}

/* Prevent content overflow in grid layouts */
.grid.grid-cols-2 .shadow-container-content {
  overflow-y: auto;
  overflow-x: hidden;
}

/* Fix Clerk button text visibility in dark mode */
.cl-formButtonPrimary {
  background-color: var(--button-primary-bg) !important;
  color: var(--button-primary-text) !important;
}

/* Ensure button text is always visible */
.cl-formButtonPrimary:hover {
  opacity: 0.9;
}

/* Grayscale emojis in Quick Start Guide */
.quick-start-content {
  /* Target all text elements that might contain emojis */
}

.quick-start-content h1,
.quick-start-content h2,
.quick-start-content h3,
.quick-start-content h4,
.quick-start-content h5,
.quick-start-content h6,
.quick-start-content p,
.quick-start-content li,
.quick-start-content strong,
.quick-start-content em,
.quick-start-content span {
  /* Apply grayscale filter to emoji unicode ranges */
  filter: grayscale(100%);
}
