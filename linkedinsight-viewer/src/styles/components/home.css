/* Home Entry Point Styles - using design system tokens */
.home-entry-container {
  max-width: 480px;
  margin: 0 auto;
  padding: var(--space-12) var(--space-6);
}

.home-entry-title {
  font-family: var(--font-family-heading);
  font-size: 2rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--space-8);
}

.home-entry-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.home-entry-option {
  background-color: var(--surface-primary);
  border: 2px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: inherit;
  display: block;
}

.home-entry-option:hover {
  border-color: var(--text-primary);
  background-color: var(--surface-secondary);
  transform: translateY(-2px);
}

.home-entry-option:active {
  transform: translateY(0);
}

.home-entry-option-title {
  font-family: var(--font-family-mono);
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.home-entry-option-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Home Grid Layout - Minimalist design system */
.home-grid-container {
  max-width: 900px; /* Increased for 3 columns */
  margin: 0 auto;
  padding: var(--space-4);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-template-rows: 160px 180px; /* Header taller, options row bigger for better readability */
  gap: var(--space-5); /* Same gap for rows and columns */
  width: 100%;
  max-height: calc(100vh - 8rem); /* Ensure it fits in viewport */
}

/* Grid header - additional styling for the header card */
.home-grid-header {
  cursor: pointer;
}

.home-grid-title {
  font-family: var(--font-family-heading);
  font-size: 2.5rem;
  font-weight: 500;
  margin-bottom: var(--space-2);
  margin-top: 0;
  color: var(--text-primary);
}

.home-grid-subtitle {
  font-family: var(--font-family-mono);
  font-size: 1rem;
  color: var(--text-secondary);
}

/* Grid items */
.home-grid-item {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: none;
  width: 100%;
  height: 100%;
  transition: all var(--transition-fast);
  display: block; /* Ensure button fills grid cell */
}

/* Ensure shadow container fills the grid item */
.home-grid-item .shadow-container {
  height: 100%;
  width: 100%;
}

.home-grid-item .shadow-container-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100%;
  overflow: hidden; /* Hide scrollbars */
}

.home-grid-item:hover .shadow-container {
  transform: translate(-1px, -1px);
}

.home-grid-item:active .shadow-container {
  transform: translate(1px, 1px);
}

/* Full width item (e.g., Quick Start Guide) */
.home-grid-item-wide {
  grid-column: 1 / -1;
}

.home-grid-icon {
  font-size: 2rem;
  margin-bottom: var(--space-1); /* Minimal spacing */
  display: block;
  filter: grayscale(100%);
  opacity: 0.8;
}

.home-grid-item-title {
  font-family: var(--font-family-mono);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0; /* Remove bottom margin */
  margin-top: 0; /* Also remove any top margin */
  color: var(--text-primary);
}

.home-grid-item-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Masonry Layout Styles */
.home-masonry-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-4);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Removed paper texture background - now using global paper texture from globals.css */

.home-masonry-header {
  margin-bottom: var(--space-6);
  text-align: center;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: none;
  width: 100%;
  height: 180px; /* Match the height of regular grid items */
}

/* Let the design system's center-content do its job without overrides
   Header needs explicit height for vertical centering to work properly */
.home-masonry-header .shadow-container {
  height: 100%;
}

.home-masonry-header .shadow-container-content {
  height: 100%;
}

.home-masonry-header:hover .shadow-container {
  transform: translate(-1px, -1px);
}

.home-masonry-header:active .shadow-container {
  transform: translate(1px, 1px);
}

/* Override center-content to use flexbox for true vertical centering */
.home-masonry-header .shadow-container .shadow-container-content.center-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6); /* More top padding to lower text */
}

.home-masonry-title {
  font-family: var(--font-family-mono);
  font-size: 2.2rem;
  font-weight: 500;
  margin-bottom: var(--space-2);
  margin-top: 0;
  color: var(--text-primary);
  line-height: 1.2;
}

.home-masonry-subtitle {
  font-family: var(--font-family-mono);
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.home-masonry-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-auto-rows: 180px;
  gap: var(--space-5);
  width: 100%;
}

.home-masonry-item {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: none;
  width: 100%;
  transition: all var(--transition-fast);
  display: block;
}

/* Tall item spans 2 rows */
.home-masonry-tall {
  grid-row: span 2;
  height: 100%;
}

/* Regular items take normal height */
.home-masonry-regular {
  height: 100%;
}

.home-masonry-item .shadow-container {
  height: 100%;
  width: 100%;
}

.home-masonry-item .shadow-container-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100%;
  padding: var(--space-6);
  overflow: hidden;
}

.home-masonry-item:hover .shadow-container {
  transform: translate(-1px, -1px);
}

.home-masonry-item:active .shadow-container {
  transform: translate(1px, 1px);
}

.home-masonry-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-3);
  display: block;
  filter: grayscale(100%);
  opacity: 0.8;
}

/* Make the icon bigger for tall items */
.home-masonry-tall .home-masonry-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.home-masonry-item-title {
  font-family: var(--font-family-mono);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--space-2);
  margin-top: 0;
  color: var(--text-primary);
}

.home-masonry-item-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Dark mode adjustments */
/* Removed dark mode shadow overrides - using transform only */

/* Image icon styling to match emoji icons */
.home-masonry-icon-img {
  width: 5rem;
  height: 5rem;
  margin-bottom: var(--space-3);
  display: block;
  filter: grayscale(100%);
  opacity: 0.8;
  object-fit: contain;
  margin-left: auto;
  margin-right: auto;
}

/* Make the icon bigger for tall items */
.home-masonry-tall .home-masonry-icon-img {
  width: 9.75rem;
  height: 9.75rem;
  margin-bottom: var(--space-4);
}

/* Make speech bubble and notes icons slightly larger with no spacing */
.home-masonry-regular .home-masonry-icon-img {
  width: 6rem;
  height: 6rem;
  margin-bottom: 0; /* No spacing */
}

/* Pull title closer to icon for regular items */
.home-masonry-regular .home-masonry-item-title {
  margin-top: -0.5rem; /* Negative margin to reduce gap */
}

/* Make speech bubble icon slightly larger to match notes icon visual size */
.home-masonry-regular img[src="/speech-bubble-icon.png"] {
  width: 7rem;
  height: 7rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .home-grid-container {
    padding: var(--space-4);
    height: auto;
    min-height: calc(100vh - 4.5rem - 2rem);
  }
  
  .home-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto repeat(3, 140px); /* 3 options with better height on mobile */
  }
  
  .home-grid-title {
    font-size: 2rem;
  }
  
  /* Masonry layout responsive */
  .home-masonry-container {
    padding: var(--space-4);
    height: auto;
    min-height: calc(100vh - 4.5rem - 2rem);
  }
  
  .home-masonry-grid {
    grid-template-columns: 1fr;
  }
  
  .home-masonry-tall {
    grid-row: span 1; /* No spanning on mobile */
    height: 200px;
  }
  
  .home-masonry-tall .shadow-container {
    min-height: unset;
  }
  
  .home-masonry-regular {
    height: 180px;
  }
  
  .home-masonry-title {
    font-size: 2rem;
  }
  
  .home-masonry-icon {
    font-size: 2rem;
  }
  
  .home-masonry-tall .home-masonry-icon {
    font-size: 2rem; /* Same size on mobile */
  }
}