/* Pricing Cards Styles */

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 0 auto;
  max-width: 800px;
}

.pricing-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem 0.75rem;
  text-align: center;
}

.pricing-card__header {
  margin-bottom: 0.5rem;
}

.pricing-card__title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-family: var(--font-family-heading);
}

.pricing-card__subtitle {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.pricing-card__price-container {
  margin-bottom: 0.125rem;
}

.pricing-card__price {
  font-size: 2.25rem;
  font-weight: 700;
}

.pricing-card__price-period {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.pricing-card__billing {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.pricing-card__features {
  flex: 1;
  text-align: left;
  margin-bottom: 0.75rem;
}

.pricing-card__features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.pricing-card__feature {
  display: flex;
  align-items: flex-start;
  font-size: 0.8125rem;
  line-height: 1.4;
}

.pricing-card__feature-icon {
  color: var(--color-primary);
  margin-right: 0.375rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.pricing-card__cta {
  margin-top: auto;
}

.pricing-card__badge {
  display: inline-block;
  background: #000;
  color: #fff;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  text-transform: uppercase;
  margin-bottom: 0.125rem;
}

/* Dark mode badge styling */
[data-theme="dark"] .pricing-card__badge {
  background: #fff;
  color: #000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pricing-grid {
    grid-template-columns: 1fr;
  }
}