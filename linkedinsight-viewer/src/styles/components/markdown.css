/* Markdown Content Styles - using design system tokens */
.markdown-content {
  background-color: var(--surface-secondary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-subtle);
  margin-bottom: var(--space-8);
}

.markdown-content h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: var(--space-4);
  margin-top: 0;
  border-bottom: 1px solid var(--border-subtle);
  padding-bottom: var(--space-2);
  font-family: var(--font-family-heading);
  color: var(--text-primary);
}

.markdown-content h2 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: var(--space-3);
  margin-top: var(--space-6);
  border-bottom: 1px solid var(--border-subtle);
  padding-bottom: var(--space-2);
  font-family: var(--font-family-heading);
  color: var(--text-primary);
}

.markdown-content h3 {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: var(--space-2);
  margin-top: var(--space-4);
  font-family: var(--font-family-heading);
  color: var(--text-primary);
}

.markdown-content p {
  margin-bottom: var(--space-3);
  line-height: 1.5;
  color: var(--text-primary);
}

.markdown-content ul {
  list-style-type: disc;
  list-style-position: outside;
  margin-left: var(--space-5);
  margin-bottom: var(--space-4);
}

.markdown-content ol {
  list-style-type: decimal;
  list-style-position: outside;
  margin-left: var(--space-5);
  margin-bottom: var(--space-4);
}

.markdown-content li {
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.markdown-content blockquote {
  border-left: 4px solid var(--border-primary);
  background-color: var(--surface-tertiary);
  padding: var(--space-4);
  font-style: italic;
  margin: var(--space-4) 0;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.markdown-content code {
  background-color: var(--surface-tertiary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  padding: 0 var(--space-1) 0 var(--space-1);
  font-size: 0.9em;
  font-family: var(--font-family-mono);
}

.markdown-content pre {
  background-color: var(--surface-tertiary);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  overflow-x: auto;
  border: 1px solid var(--border-subtle);
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  color: var(--text-primary);
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-4);
}

.markdown-content th {
  background-color: var(--surface-tertiary);
  padding: var(--space-2);
  text-align: left;
  border: 1px solid var(--border-subtle);
}

.markdown-content td {
  padding: var(--space-2);
  border: 1px solid var(--border-subtle);
}