/* Grid Stagger Transition - Single source of truth */

/* Base transition class for all grid items */
.grid-stagger-item {
  transition: all var(--transition-normal);
  transform-origin: center;
}

/* Transitioning state - applied to the container */
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item {
  animation-duration: var(--transition-normal);
  animation-timing-function: var(--ease-out);
  animation-fill-mode: forwards;
}

/* Clicked item scales up slightly before fading */
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-clicked="true"] {
  animation-name: staggerClickedItem;
}

/* Non-clicked items fade and scale down with stagger */
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-clicked="false"] {
  animation-name: staggerOutItem;
}

/* Calculate stagger delays using CSS custom properties */
/* Header has index -1 and animates first */
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="-1"] {
  animation-delay: 0ms;
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="0"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 1);
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="1"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 2);
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="2"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 3);
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="3"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 4);
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="4"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 5);
}
.grid-stagger-container[data-transitioning="true"] .grid-stagger-item[data-index="5"] {
  animation-delay: calc(var(--stagger-delay, 50ms) * 6);
}

/* Keyframes for clicked item */
@keyframes staggerClickedItem {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

/* Keyframes for non-clicked items */
@keyframes staggerOutItem {
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

/* Optional: Add a subtle fade for the entire page during transition */
.page-transition-overlay {
  position: fixed;
  inset: 0;
  background: var(--background);
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-slow);
  z-index: 100;
}

.page-transition-overlay.active {
  opacity: 1;
  pointer-events: all;
}