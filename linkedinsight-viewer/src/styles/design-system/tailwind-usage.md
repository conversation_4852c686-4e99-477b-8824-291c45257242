# Using the Design System with Tailwind

This document explains how to use the LinkedInsight design system tokens with Tailwind CSS.

## Overview

The design system has been integrated with Tailwind, allowing you to use design tokens directly in your Tailwind classes. This ensures consistency across the application and makes it easier to maintain the design system.

## Colors

### Surface Colors

```jsx
// Using surface colors
<div className="bg-surface-primary">Primary background</div>
<div className="bg-surface-secondary">Secondary background</div>
<div className="bg-surface-tertiary">Tertiary background</div>
<div className="bg-surface-input">Input background</div>
```

### Text Colors

```jsx
// Using text colors
<p className="text-text-primary">Primary text</p>
<p className="text-text-secondary">Secondary text</p>
<p className="text-text-tertiary">Tertiary text</p>
<p className="text-text-on-primary">Text on primary</p>
<p className="text-text-on-secondary">Text on secondary</p>
```

### Border Colors

```jsx
// Using border colors
<div className="border border-primary">Primary border</div>
<div className="border border-subtle">Subtle border</div>
<div className="border border-input">Input border</div>
<div className="border border-input-focus">Input focus border</div>
```

### Button Colors

```jsx
// Using button colors
<button className="bg-button-primary-bg text-button-primary-text">
  Primary Button
</button>
<button className="bg-button-secondary-bg text-button-secondary-text">
  Secondary Button
</button>
```

## Typography

```jsx
// Using font families
<h1 className="font-heading">Heading Text</h1>
<p className="font-base">Base Text</p>
<code className="font-mono">Monospace Text</code>
```

## Spacing

```jsx
// Using spacing tokens
<div className="p-space-4">Padding using space-4</div>
<div className="m-space-2">Margin using space-2</div>
<div className="gap-space-6">Gap using space-6</div>
```

## Border Radius

```jsx
// Using border radius tokens
<div className="rounded-ds-sm">Small radius</div>
<div className="rounded-ds-md">Medium radius</div>
<div className="rounded-ds-lg">Large radius</div>
```

## Transitions

```jsx
// Using transition tokens
<div className="transition-ds-fast">Fast transition</div>
<div className="transition-ds-normal">Normal transition</div>
<div className="transition-ds-slow">Slow transition</div>
```

## Backward Compatibility

For backward compatibility, the original Tailwind classes still work:

```jsx
// Original Tailwind classes still work
<div className="bg-background text-foreground">
  Using original Tailwind classes
</div>
```

## Best Practices

1. **Prefer design system tokens**: Use the design system tokens whenever possible to ensure consistency.
2. **Use component classes**: For common components, use the CSS classes defined in `components.css` instead of building styles with Tailwind.
3. **Avoid hardcoded values**: Don't use hardcoded color values, spacing, or other design properties.
4. **Combine with component classes**: You can combine Tailwind classes with component classes using the `cn` utility.

```jsx
import { cn } from "../../utils/utils";

// Combining component classes with Tailwind
<div className={cn(
  "shadow-container-content",
  "bg-surface-primary",
  "p-space-4"
)}>
  Content
</div>
```

## Dark Mode Support

The design system automatically handles dark mode through CSS variables. When using design system tokens with Tailwind, dark mode will work automatically.

The Tailwind configuration supports both the `dark` class and the `data-theme="dark"` attribute for dark mode.
