/**
 * Design System Tokens
 *
 * This file defines the core design tokens (CSS custom properties) that serve as
 * the single source of truth for the LinkedInsight design system.
 *
 * Tokens are organized into semantic categories:
 * - Surface: Background colors for different UI layers
 * - Text: Text colors for different contexts
 * - Border: Border colors and styles
 * - Component-specific: Tokens for specific components
 */

:root {
  /* ===== SURFACE TOKENS ===== */
  --surface-window: rgb(237, 237, 237);         /* Window/application background */
  --surface-primary: hsl(0, 0%, 100%);          /* Main background */
  --surface-secondary: hsl(0, 0%, 96.1%);       /* Secondary backgrounds, cards */
  --surface-tertiary: hsl(0, 0%, 93%);          /* Tertiary backgrounds */
  --surface-input: hsl(0, 0%, 96.1%);           /* Form input backgrounds */
  --surface-input-hover: hsl(0, 0%, 94%);       /* Form input hover state */
  --surface-input-focus: hsl(0, 0%, 100%);      /* Form input focus state */
  --surface-disabled: hsl(0, 0%, 92%);          /* Disabled element backgrounds */
  --surface-tab-inactive: hsl(0, 0%, 85%);      /* Inactive tab backgrounds */
  --surface-input-light: hsl(0, 0%, 96%);       /* Light input variant */

  /* ===== TEXT TOKENS ===== */
  --text-primary: hsl(0, 0%, 3.9%);             /* Main text color */
  --text-secondary: hsl(0, 0%, 45.1%);          /* Secondary text, labels */
  --text-tertiary: hsl(0, 0%, 65%);             /* Tertiary text, placeholders */
  --text-on-primary: hsl(0, 0%, 100%);          /* Text on primary buttons */
  --text-on-secondary: hsl(0, 0%, 3.9%);        /* Text on secondary buttons */

  /* ===== BORDER TOKENS ===== */
  --border-subtle: hsl(0, 0%, 89.8%);           /* Subtle borders */
  --border-input: hsl(0, 0%, 89.8%);            /* Form input borders */
  --border-input-focus: hsl(0, 0%, 3.9%);       /* Form input focus borders */
  --border-primary: hsl(0, 0%, 0%);             /* Primary borders */

  /* ===== COMPONENT-SPECIFIC TOKENS ===== */
  /* Textarea */
  --textarea-bg: var(--surface-input);
  --textarea-bg-hover: var(--surface-input-hover);
  --textarea-bg-focus: var(--surface-input-focus);
  --textarea-border: var(--border-primary);
  --textarea-border-focus: var(--border-input-focus);
  --textarea-text: var(--text-primary);
  --textarea-placeholder: var(--text-tertiary);

  /* Shadow Container */
  --shadow-container-bg: var(--surface-primary);
  --shadow-container-border: var(--border-primary);
  --shadow-container-shadow: 4px 4px 0px 0px hsl(0, 0%, 30%);
  --shadow-offset: 4px; /* Used for margin calculations */

  /* Draft Container */
  --draft-bg: var(--surface-secondary);
  --draft-border: var(--border-subtle);

  /* Button */
  --button-primary-bg: hsl(0, 0%, 0%);
  --button-primary-text: hsl(0, 0%, 100%);
  --button-secondary-bg: hsl(0, 0%, 96.1%);
  --button-secondary-text: hsl(0, 0%, 3.9%);

  /* Error states */
  --surface-error: hsl(0, 100%, 95%);
  --text-error: hsl(0, 100%, 30%);
  --border-error: hsl(0, 100%, 85%);

  /* ===== SPACING TOKENS ===== */
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-11: 2.75rem; /* 44px */
  --space-12: 3rem;    /* 48px */

  /* ===== TYPOGRAPHY TOKENS ===== */
  --font-family-base: Arial, Helvetica, sans-serif;
  --font-family-mono: 'IBM Plex Mono', monospace;
  --font-family-heading: 'Archivo', sans-serif;

  /* ===== ANIMATION TOKENS ===== */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Easing functions */
  --ease-out: cubic-bezier(0.25, 0.8, 0.25, 1);
  --ease-in: cubic-bezier(0.55, 0, 1, 0.45);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ===== RADIUS TOKENS ===== */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */

  /* ===== BORDER WIDTH TOKENS ===== */
  --border-width-default: 1px;  /* Default thinner border for app pages */
  --border-width-thick: 2px;    /* Thicker border for home/landing pages */

  /* ===== SHADOW TOKENS ===== */
  --shadow-focus: 0 0 0 2px rgba(0, 0, 0, 0.1);
  --shadow-dropdown: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* ===== HOVER STATE TOKENS ===== */
  --surface-hover-light: rgba(0, 0, 0, 0.05);
  --surface-hover-dark: rgba(255, 255, 255, 0.1);
}

/* Dark theme tokens */
[data-theme="dark"] {
  /* ===== SURFACE TOKENS ===== */
  --surface-window: hsl(0, 0%, 3.9%);           /* Window/application background */
  --surface-primary: hsl(0, 0%, 3.9%);          /* Main background */
  --surface-secondary: hsl(0, 0%, 14.9%);       /* Secondary backgrounds, cards */
  --surface-tertiary: hsl(0, 0%, 10%);          /* Tertiary backgrounds */
  --surface-input: hsl(0, 0%, 14.9%);           /* Form input backgrounds */
  --surface-input-hover: hsl(0, 0%, 18%);       /* Form input hover state */
  --surface-input-focus: hsl(0, 0%, 20%);       /* Form input focus state */
  --surface-disabled: hsl(0, 0%, 16.5%);        /* Disabled element backgrounds */
  --surface-tab-inactive: hsl(0, 0%, 25%);      /* Inactive tab backgrounds */
  --surface-input-light: hsl(0, 0%, 20%);       /* Light input variant */

  /* ===== TEXT TOKENS ===== */
  --text-primary: hsl(0, 0%, 98%);              /* Main text color */
  --text-secondary: hsl(0, 0%, 63.9%);          /* Secondary text, labels */
  --text-tertiary: hsl(0, 0%, 45%);             /* Tertiary text, placeholders */
  --text-on-primary: hsl(0, 0%, 3.9%);          /* Text on primary buttons */
  --text-on-secondary: hsl(0, 0%, 98%);         /* Text on secondary buttons */

  /* ===== BORDER TOKENS ===== */
  --border-subtle: hsl(0, 0%, 14.9%);           /* Subtle borders */
  --border-input: hsl(0, 0%, 14.9%);            /* Form input borders */
  --border-input-focus: hsl(0, 0%, 83.1%);      /* Form input focus borders */
  --border-primary: hsl(0, 0%, 100%);           /* Primary borders */

  /* ===== COMPONENT-SPECIFIC TOKENS ===== */
  /* Shadow Container */
  --shadow-container-shadow: 4px 4px 0px 0px hsl(0, 0%, 27%);
  --shadow-offset: 4px; /* Used for margin calculations */

  /* Button */
  --button-primary-bg: hsl(0, 0%, 100%);
  --button-primary-text: hsl(0, 0%, 0%);
  --button-secondary-bg: hsl(0, 0%, 30%);  /* Dark gray instead of blue */
  --button-secondary-text: hsl(0, 0%, 98%);

  /* Error states */
  --surface-error: hsl(0, 100%, 15%);
  --text-error: hsl(0, 100%, 70%);
  --border-error: hsl(0, 100%, 30%);

  /* ===== SHADOW TOKENS ===== */
  --shadow-focus: 0 0 0 2px rgba(255, 255, 255, 0.2);
  --shadow-dropdown: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}
