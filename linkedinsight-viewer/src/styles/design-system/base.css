/**
 * Base Styles and Resets
 * 
 * Foundational styles that establish consistent defaults across the application
 */

/* ===== DRAFT COMPONENTS ===== */
.draft-container {
  display: block;
  padding: var(--space-5);
  background-color: var(--draft-bg);
  border: 1px solid var(--draft-border);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  font-family: var(--font-family-mono);
  transition: all var(--transition-fast);
  cursor: pointer;
  text-align: left;
  width: 100%;
}

.draft-container:hover {
  background-color: var(--surface-tertiary);
  border-color: var(--text-secondary);
}

.draft-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.draft-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* Dark mode adjustments */
[data-theme="dark"] .draft-container:hover {
  background-color: var(--surface-tertiary);
}

/* 
 * Draft textarea styling
 * IMPORTANT: This class is used in DiffEditor for both textarea and diff viewer containers
 * to ensure consistent layout behavior and prevent overflow issues
 */
.draft-textarea {
  width: 100%;
  height: auto; /* Let flexbox decide appropriate height */
  max-height: 100%; /* But never exceed parent container */
  resize: none; /* Fixed size, not resizable as per user request */
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  background-color: var(--textarea-bg);
  color: var(--text-primary);
  padding: var(--space-4);
  font-family: var(--font-family-base);
  transition: all var(--transition-fast);
  flex: 1; /* Allow it to grow within flex containers */
  min-height: 0; /* Allow it to shrink within flex containers */
  white-space: pre-wrap; /* Enable text wrapping */
  word-wrap: break-word; /* Break long words if needed */
  overflow-wrap: break-word; /* Modern property for word breaking */
  overflow-y: auto; /* Ensure scrolling when content overflows */
  display: flex;
  flex-direction: column;
}

.draft-textarea:focus {
  outline: none;
  border-color: var(--textarea-border-focus);
  background-color: var(--textarea-bg-focus);
}

/* Notes textarea for the notes input form */
.notes-textarea {
  min-height: 600px;
  height: 100%;
}

/* When draft-textarea is a textarea element, ensure it fills the container */
textarea.draft-textarea {
  height: 100%;   /* Fill remaining space inside flex container */
  min-height: 0;  /* Allow the textarea to shrink as needed */
}

/* When draft-textarea is a div (for explanations), match textarea behavior */
div.draft-textarea {
  height: 100%;   /* Fill remaining space inside flex container */
  min-height: 0;  /* Allow shrinkage and internal scrolling */
}