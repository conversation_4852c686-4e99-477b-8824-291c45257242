/**
 * Form Component Styles
 * 
 * Styles for form elements including inputs, textareas, labels, and form containers
 */

/* ===== TEXTAREA COMPONENTS ===== */
.textarea-input {
  width: 100%;
  padding: var(--space-4);
  background-color: var(--textarea-bg);
  color: var(--text-primary);
  border: 1px solid var(--textarea-border);
  border-radius: var(--radius-md);
  font-family: var(--font-family-base);
  resize: none;
  transition: all var(--transition-fast);
  min-height: 120px;
  flex: 1 1 auto; /* Allow it to grow and shrink */
  display: block; /* Ensure it fills the container */
}

.textarea-input:hover {
  background-color: var(--textarea-bg-hover);
}

.textarea-input:focus {
  outline: none;
  background-color: var(--textarea-bg-focus);
  border-color: var(--textarea-border-focus);
  box-shadow: var(--shadow-focus);
}

.textarea-input::placeholder {
  color: var(--text-tertiary);
}

/* Dark mode support */
[data-theme="dark"] .textarea-input::placeholder {
  color: var(--text-tertiary);
}

/* ===== FORM CONTAINERS ===== */
.form-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  padding: var(--space-4) var(--space-8) var(--space-4);
  /* CSS containment to prevent layout thrashing */
  contain: layout style;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  flex-grow: 1;
}

.form-submit-container {
  text-align: center;
  padding-top: var(--space-4);
  margin-top: auto;
}

/* Form container variants */
.form-equal-height {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  flex-grow: 1;
}

.form-equal-height > .shadow-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-equal-height .shadow-container-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-equal-height .form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-equal-height .form-container > *:last-child {
  margin-top: auto;
}

/* Button width consistency */
.form-equal-height .button-lg {
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
}

/* Balanced form spacing */
.form-container-balanced {
  gap: var(--space-4);
}

/* Reduced padding for form submit container in dual pane */
.form-equal-height .form-submit-container {
  padding-top: 0;
  padding-bottom: 0;
}

/* ===== RADIO BUTTON COMPONENTS ===== */
.radio-inline {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  cursor: pointer;
}

.radio-option input[type="radio"] {
  margin: 0;
  accent-color: var(--text-primary);
}

.radio-option label {
  font-size: 0.875rem;
  font-family: var(--font-family-mono);
  cursor: pointer;
  margin: 0;
  color: var(--text-primary);
}

.inline-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

/* ===== LENGTH SELECTOR COMPONENTS ===== */
.length-selector {
  display: inline-flex;
  align-items: center;
  gap: var(--space-4);
}

.length-selector-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.length-selector-options {
  display: flex;
  gap: var(--space-2);
  position: relative;
  z-index: 1;
}

.length-selector-button {
  padding: var(--space-1) var(--space-3);
  background-color: var(--surface-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

.length-selector-button:hover {
  border-color: var(--text-secondary);
  background-color: var(--surface-secondary);
}

.length-selector-button.active {
  background-color: var(--border-primary);
  color: var(--text-on-primary);
  border-color: var(--border-primary);
}

[data-theme="dark"] .length-selector-button.active {
  background-color: var(--border-primary);
  color: var(--text-on-primary);
}

.length-selector-emoji {
  font-size: 1rem;
  line-height: 1;
  filter: grayscale(100%);
  opacity: 0.8;
}

/* ===== FORM LOADING STATES ===== */
.form-loading-container {
  max-width: 32rem;
  margin: 0 auto;
  padding: var(--space-6);
  text-align: center;
}

.form-error-strong {
  font-weight: bold;
}

/* ===== FORM ELEMENTS ===== */
.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--space-2);
  text-align: center;
  color: var(--text-primary);
  font-family: var(--font-family-heading);
}

.form-container-compact .form-title {
  margin-top: var(--space-2);
}

.form-container-compact {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.form-error {
  background-color: var(--surface-error);
  border: 1px solid var(--border-error);
  color: var(--text-error);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-4);
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.form-input {
  width: 100%;
  padding: var(--space-4);
  background-color: var(--textarea-bg);
  color: var(--text-primary);
  border: 1px solid var(--textarea-border);
  border-radius: var(--radius-md);
  font-family: var(--font-family-base);
  transition: all var(--transition-fast);
  height: 40px;
  box-sizing: border-box;
}

.form-input:hover {
  background-color: var(--textarea-bg-hover);
}

.form-input:focus {
  outline: none;
  background-color: var(--textarea-bg-focus);
  border-color: var(--textarea-border-focus);
  box-shadow: var(--shadow-focus);
}

.form-input::placeholder {
  color: var(--text-tertiary);
}

