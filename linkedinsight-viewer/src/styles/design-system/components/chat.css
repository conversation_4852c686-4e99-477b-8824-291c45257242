/**
 * Chat Component Styles
 * 
 * Styles for chat interfaces, messages, and related components
 */

/* ===== MESSAGE COMPONENTS ===== */
.message-bubble-user {
  background-color: var(--surface-tertiary);
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  margin-left: auto;
}

.message-bubble-agent {
  /* Agent messages use prose styling, no background needed */
}

/* ===== CHAT INPUT ===== */
.chat-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-input);
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-primary);
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
  min-height: 2.5rem;
  max-height: 10rem;
  transition: height 0.1s ease;
}

.chat-input:focus {
  outline: none;
  border-color: var(--text-primary);
}

.chat-input::placeholder {
  color: var(--text-tertiary);
}

.chat-input-base {
  resize: none;
  overflow: hidden;
}

/* ===== CHAT STATES ===== */
.loading-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--text-secondary);
  border-radius: 50%;
}

.error-message {
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  background-color: var(--surface-error);
  color: var(--text-error);
  border: 1px solid var(--border-error);
}