/**
 * Container Component Styles
 * 
 * Styles for container elements including shadow containers, content sections, and layouts
 */

/* ===== SHADOW CONTAINER COMPONENTS ===== */
.shadow-container {
  position: relative;
  margin-bottom: var(--shadow-offset);
  margin-right: var(--shadow-offset);
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure it takes full height of parent */
}

/* Remove margins when inside grid layouts to prevent clipping */
.grid .shadow-container {
  margin-bottom: 0;
  margin-right: 0;
}

.shadow-container-content {
  position: relative;
  background-color: var(--shadow-container-bg);
  border-radius: var(--radius-lg);
  border: var(--border-width, var(--border-width-default)) solid var(--border-primary);
  overflow: hidden;
  padding: var(--space-6);
  box-shadow: var(--shadow-container-shadow);
  z-index: 1;
  flex: 1; /* Allow it to grow and fill available space */
  display: flex;
  flex-direction: column; /* Enable flex layout for children */
}

/* Fix scrollbar corners when overflow-y-auto is applied via Tailwind
   The Box component adds overflow-y-auto which conflicts with overflow: hidden
   This ensures scrollbars render properly within rounded containers */
.shadow-container-content.overflow-y-auto {
  /* Override the base overflow: hidden to allow vertical scrolling only */
  overflow: hidden auto;
  /* Reserve space for scrollbar to prevent content shift */
  scrollbar-gutter: stable;
}

/* Adjust overflow for pane containers to prevent clipping */
.grid .shadow-container .shadow-container-content {
  overflow: visible;
}

/* CRITICAL EXCEPTION: Editor panes need proper overflow for scrolling
   Without this rule, the editor content will overflow beyond container bounds
   DO NOT remove the editor-pane class from EditorAgentPage components */
.grid .shadow-container.editor-pane .shadow-container-content {
  overflow: hidden; /* Reset to hidden to allow child flex containers to handle overflow */
}

/* Agent panes also need proper overflow handling to prevent content from extending beyond viewport */
.grid .shadow-container.agent-pane .shadow-container-content {
  overflow: hidden; /* Allow child flex containers to handle overflow */
}

/* No padding variant */
.shadow-container-content.no-padding {
  padding: 0;
}

/* No shadow variant */
.shadow-container-content.shadow-none {
  box-shadow: none;
}

/* No border variant */
.shadow-container-content.border-0 {
  border: none;
}

/* Tabs variant */
.shadow-container-content.has-tabs {
  overflow: hidden; /* Disable overflow for tab containers */
  display: flex;
  flex-direction: column;
}

/* Allow tabs content to manage its own scrolling */
.shadow-container-content.has-tabs > * {
  min-height: 0; /* Critical for flex children to respect overflow */
}

/* Fixed tabs variant */
.shadow-container-fixed {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Center content variant - needs higher specificity to override flex */
.shadow-container .shadow-container-content.center-content {
  display: block; /* Override flex for padding-based centering */
  padding: 2.5rem var(--space-6); /* Equal vertical padding for optical centering */
  text-align: center;
}

/* Allow padding override via CSS variable */
.shadow-container[style*="--container-padding"] .shadow-container-content.center-content {
  padding: var(--container-padding);
}

/* Landing page containers - use consistent vertical padding */
[style*="--landing-container-padding"] .shadow-container-content {
  padding-top: var(--landing-container-padding);
  padding-bottom: var(--landing-container-padding);
}


/* ===== DRAFT CONTAINER COMPONENTS ===== */
.draft-container {
  display: block;
  padding: var(--space-5);
  background-color: var(--draft-bg);
  border: 1px solid var(--draft-border);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  font-family: var(--font-family-mono);
  transition: all var(--transition-fast);
  cursor: pointer;
  text-align: left;
  width: 100%;
}

.draft-container:hover {
  background-color: var(--surface-tertiary);
  border-color: var(--text-secondary);
}

.draft-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.draft-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* Dark mode adjustments */
[data-theme="dark"] .draft-container:hover {
  background-color: var(--surface-tertiary);
}

/* ===== CONTENT SECTIONS ===== */
/* Handles prose to action section transitions */
.content-section {
  position: relative;
}

.content-section .prose:last-child p:last-child,
.content-section .prose:last-child ul:last-child,
.content-section .prose:last-child ol:last-child,
.content-section .prose:last-child blockquote:last-child {
  margin-bottom: 0;
}

/* ===== ACTION SECTIONS ===== */
.action-section {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-2);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.action-section-subtle {
  border-top: 1px solid var(--border-subtle);
  padding-top: var(--space-2);
}

/* Compact variant for tight spacing after prose content */
.action-section-compact {
  margin-top: -3.5rem; /* Compensate for prose bottom margins */
  position: relative;
  z-index: 10; /* Ensure it's above the content section */
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
  position: relative;
  z-index: 10; /* Ensure buttons are above other content */
}

/* Position fixed panel at bottom of container */
.bottom-action-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--space-4);
  background-color: var(--surface-primary);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}