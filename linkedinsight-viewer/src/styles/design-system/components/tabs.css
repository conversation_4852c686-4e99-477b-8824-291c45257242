/**
 * Tab Component Styles
 * 
 * Styles for tab navigation and tab panels
 */

/* ===== TAB COMPONENTS ===== */
.tab {
  padding: var(--space-2) var(--space-6);
  font-family: var(--font-family-mono);
  font-weight: 600;
  white-space: nowrap;
  flex: 1;
  transition: all var(--transition-normal);
  border: none;
  cursor: pointer;
  background-color: var(--surface-tab-inactive);
  color: var(--text-primary);
}

.tab.active {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.tab:disabled {
  background-color: var(--surface-disabled);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

.tab-emoji {
  margin-right: var(--space-1);
  filter: grayscale(100%);
  opacity: 0.8;
}