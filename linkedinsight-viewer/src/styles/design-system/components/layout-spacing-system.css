/**
 * Layout Spacing System
 * 
 * Centralized spacing definitions for consistent layouts across the application.
 * 
 * NAMING CONVENTION:
 * .{component}-{element}-{property}-{direction}-{value}
 * 
 * Examples:
 * .chat-interface-layout-margin-y-32px
 * .form-container-wrapper-padding-all-24px
 * 
 * USAGE:
 * Apply these classes to maintain consistent spacing instead of using
 * ad-hoc Tailwind utilities. This enables global spacing changes from
 * a single source of truth.
 */

/* ===== CHAT/INTERVIEW INTERFACE SPACING ===== */
/* Used in: InteractiveInterviewAgent, EditorAgentPage, InterviewingAgentView */

/* The main layout wrapper for chat interfaces */
.chat-interface-layout-margin-y-32px {
  margin-top: var(--space-8);    /* 32px */
  margin-bottom: var(--space-8); /* 32px */
}

/* Individual conversation/content boxes within chat interfaces */
.chat-conversation-box-margin-bottom-16px {
  margin-bottom: var(--space-4); /* 16px */
}

/* Bottom input panel in chat interfaces */
.chat-input-panel-padding-all-16px {
  padding: var(--space-4); /* 16px all sides */
}

/* ===== FORM CONTAINER SPACING ===== */
/* Used in: ContentStrategyForm, ContentPreferencesForm, and other form containers */

/* The outer form container wrapper */
.form-container-wrapper-padding-all-24px {
  padding: var(--space-6); /* 24px all sides */
}

/* Spacing between form field groups */
.form-field-group-gap-16px {
  gap: var(--space-4); /* 16px */
}

/* Submit button area at bottom of forms */
.form-submit-area-padding-top-16px {
  padding-top: var(--space-4); /* 16px */
}

/* ===== CONTENT DISPLAY SPACING ===== */
/* Used in: DraftLibraryPage, content listings */

/* Container for content lists and grids */
.content-list-container-padding-all-16px {
  padding: var(--space-4); /* 16px all sides */
}

/* Individual content cards or items */
.content-card-item-margin-bottom-16px {
  margin-bottom: var(--space-4); /* 16px */
}

/* Page headers in content pages */
.content-header-section-padding-y-24px {
  padding-top: var(--space-6);    /* 24px */
  padding-bottom: var(--space-6); /* 24px */
}

/* ===== DUAL PANE LAYOUT SPACING ===== */
/* Used in: ReusableDualPaneLayout implementations */

/* The wrapper containing dual pane layouts */
.dualpane-layout-wrapper-margin-y-32px {
  margin-top: var(--space-8);    /* 32px */
  margin-bottom: var(--space-8); /* 32px */
}

/* Horizontal gap between the two panes */
.dualpane-column-gap-horizontal-32px {
  gap: var(--space-8); /* 32px */
}

/* Mobile responsive gap */
.dualpane-column-gap-horizontal-16px-mobile {
  gap: var(--space-4); /* 16px on mobile */
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
/* Mobile-specific spacing overrides */
@media (max-width: 768px) {
  .chat-interface-layout-margin-y-32px {
    margin-top: var(--space-4);    /* 16px on mobile */
    margin-bottom: var(--space-4); /* 16px on mobile */
  }
  
  .dualpane-layout-wrapper-margin-y-32px {
    margin-top: var(--space-4);    /* 16px on mobile */
    margin-bottom: var(--space-4); /* 16px on mobile */
  }
  
  .dualpane-column-gap-horizontal-32px {
    gap: var(--space-4); /* 16px on mobile */
  }
}