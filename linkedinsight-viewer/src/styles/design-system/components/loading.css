/**
 * Loading Component Styles
 * 
 * Styles for loading states, spinners, and progress indicators
 */

/* ===== LOADING SPINNER COMPONENTS ===== */
.loading-spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  color: var(--text-secondary);
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid;
  border-color: transparent;
  border-top-color: var(--border-primary);
  border-radius: 50%;
  animation: spin 0.75s linear infinite;
}

.loading-spinner-large {
  width: 3rem;
  height: 3rem;
  border-width: 2px;
  border-bottom-color: var(--border-primary);
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  margin: 0 auto var(--space-4);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== LOADING PAGE STATES ===== */
.loading-page {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) 0;
}

.loading-page-content {
  text-align: center;
}

.loading-page-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.loading-page-subtitle {
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}