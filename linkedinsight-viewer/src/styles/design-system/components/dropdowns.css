/**
 * Dropdown Component Styles
 * 
 * Styles for dropdown menus, select elements, and related components
 */

/* ===== DROPDOWN MENU COMPONENTS ===== */
.dropdown-menu-content {
  min-width: 220px;
  background-color: var(--surface-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-dropdown);
  padding: var(--space-1);
  z-index: 50;
}

.dropdown-menu-item {
  display: flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  font-size: 0.875rem;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.dropdown-menu-item:hover {
  background-color: var(--surface-secondary);
}

.dropdown-menu-item-icon {
  margin-right: var(--space-2);
  width: 16px;
  height: 16px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.dropdown-menu-separator {
  height: 1px;
  background-color: var(--border-subtle);
  margin: var(--space-1) 0;
}

.dropdown-menu-user-info {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-subtle);
}

.dropdown-menu-user-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.dropdown-menu-user-email {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}