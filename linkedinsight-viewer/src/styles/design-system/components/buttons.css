/**
 * <PERSON><PERSON> Component Styles
 * 
 * Styles for all button variants and states
 */

/* ===== BASE BUTTON STYLES ===== */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== BUTTON VARIANTS ===== */
.button-primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
}

.button-primary:hover:not(:disabled) {
  opacity: 0.9;
}

.button-secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--border-primary);
}

.button-secondary:hover:not(:disabled) {
  opacity: 0.9;
}

.button-outline {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.button-outline:hover:not(:disabled) {
  background-color: var(--surface-hover-light);
}

[data-theme="dark"] .button-outline:hover:not(:disabled) {
  background-color: var(--surface-hover-dark);
}

.button-dark {
  background-color: hsl(0, 0%, 75%);
  color: hsl(0, 0%, 0%);
  border: none;
}

.button-dark:hover:not(:disabled) {
  background-color: hsl(0, 0%, 70%);
}

[data-theme="dark"] .button-dark {
  background-color: hsl(0, 0%, 25%);
  color: hsl(0, 0%, 100%);
}

[data-theme="dark"] .button-dark:hover:not(:disabled) {
  background-color: hsl(0, 0%, 30%);
}

/* ===== BUTTON SIZES ===== */
.button-sm {
  height: 28px;
  padding: 0 var(--space-3);
  font-size: 0.875rem;
}

.button-md {
  height: 36px;
  padding: 0 var(--space-4);
  font-size: 0.875rem;
}

.button-lg {
  height: 40px;
  padding: 0 var(--space-5);
  font-size: 1rem;
}

/* Icon-only button */
.icon-button {
  padding: var(--space-2);
  width: 2.5rem;
  height: 2.5rem;
}

.icon-button svg {
  width: 1.25rem;
  height: 1.25rem;
}