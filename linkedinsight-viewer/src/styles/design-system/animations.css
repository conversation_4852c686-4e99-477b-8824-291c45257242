/* Animation Keyframes */
@keyframes pulse-propagation {
  0% {
    transform: translateX(-100%);
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: translateX(100%);
    opacity: 0.8;
  }
}

/* Animation Classes */
.animate-pulse-propagation::before,
.animate-pulse-propagation::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
}

.animate-pulse-propagation::before {
  animation: pulse-propagation 3s linear infinite;
}

.animate-pulse-propagation::after {
  animation: pulse-propagation 3s linear infinite;
  animation-delay: 1.5s;
}

/* Background Pattern Animations */
.bg-dots {
  background-image: radial-gradient(
    rgba(255, 255, 255, 0.15) 1.5px,
    transparent 1.5px
  );
  background-size: 8px 8px;
}

/* Streaming Status Indicators */
.streaming-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #000000;
  font-size: 0.875rem;
}

.streaming-status-indicator .status-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top-color: #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode adjustments */
[data-theme="dark"] .streaming-status-indicator {
  color: #ffffff;
}

[data-theme="dark"] .streaming-status-indicator .status-spinner {
  border-color: #374151;
  border-top-color: #ffffff;
}