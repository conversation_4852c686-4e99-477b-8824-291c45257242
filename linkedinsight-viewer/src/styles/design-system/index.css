/**
 * Design System Entry Point
 *
 * This file imports all design system files in the correct order.
 */

/* First import tokens (variables) */
@import './tokens.css';

/* Import base styles and resets */
@import './base.css';

/* Import component classes that use those tokens */
@import './components/buttons.css';
@import './components/forms.css';
@import './components/containers.css';
@import './components/chat.css';
@import './components/loading.css';
@import './components/tabs.css';
@import './components/dropdowns.css';
@import './components/prose.css';
@import './components/layout-spacing-system.css';

/* Import animations */
@import './animations.css';
