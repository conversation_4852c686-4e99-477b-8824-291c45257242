/* Global Page Transitions */

/* Apply transitions when body has transitioning class */
body.page-transitioning .shadow-container,
body.page-transitioning .shadowed-tabs-container,
body.page-transitioning main,
body.page-transitioning .page-content {
  animation: pageTransitionOut 250ms var(--ease-out) forwards;
}

/* Entry animation for new pages - only when transitioning */
body.page-entering .shadow-container,
body.page-entering .shadowed-tabs-container,
body.page-entering main,
body.page-entering .page-content {
  animation: pageTransitionIn 300ms var(--ease-out);
}

@keyframes pageTransitionIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Also fade any modals or overlays */
body.page-transitioning .modal,
body.page-transitioning .drawer {
  animation: pageFadeOut 250ms var(--ease-out) forwards;
}

/* Slide + Fade transition (global default) */
@keyframes pageTransitionOut {
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

