import { describe, it, expect, vi } from 'vitest';
import { ErrorService, getErrorMessage, formatError } from './errorHandling';
import type { ApiError } from '../types/api';

describe('ErrorService', () => {
  describe('getMessage', () => {
    it('should handle standard Error objects', () => {
      const error = new Error('Test error message');
      expect(ErrorService.getMessage(error)).toBe('Test error message');
    });

    it('should handle string errors', () => {
      expect(ErrorService.getMessage('String error')).toBe('String error');
    });

    it('should handle unknown error types', () => {
      expect(ErrorService.getMessage(null)).toBe('An unexpected error occurred');
      expect(ErrorService.getMessage(undefined)).toBe('An unexpected error occurred');
      expect(ErrorService.getMessage(123)).toBe('An unexpected error occurred');
      expect(ErrorService.getMessage({})).toBe('An unexpected error occurred');
    });

    it('should handle network errors with user-friendly message', () => {
      const error = new Error('Failed to fetch');
      expect(ErrorService.getMessage(error)).toBe('Unable to connect to server. Please check your connection and try again.');
    });

    it('should handle timeout errors', () => {
      const error = new Error('Request timeout');
      expect(ErrorService.getMessage(error)).toBe('Request timed out. Please try again.');
    });

    it('should handle abort errors', () => {
      const error = new Error('Request aborted');
      error.name = 'AbortError';
      expect(ErrorService.getMessage(error)).toBe('Request was cancelled.');
    });
  });

  describe('API error handling', () => {
    it('should handle 401 errors', () => {
      const error = new Error('API Error: 401 Unauthorized') as ApiError;
      error.apiError = { status: 401, statusText: 'Unauthorized', url: '/api/test' };
      expect(ErrorService.getMessage(error)).toBe('Authentication required. Please sign in and try again.');
    });

    it('should handle 403 errors', () => {
      const error = new Error('API Error: 403 Forbidden') as ApiError;
      error.apiError = { status: 403, statusText: 'Forbidden', url: '/api/test' };
      expect(ErrorService.getMessage(error)).toBe('You do not have permission to perform this action.');
    });

    it('should handle 404 errors with context', () => {
      const error = new Error('API Error: 404 Not Found') as ApiError;
      error.apiError = { status: 404, statusText: 'Not Found', url: '/api/test' };
      expect(ErrorService.getMessage(error, 'User profile')).toBe('User profile not found.');
    });

    it('should handle 429 errors', () => {
      const error = new Error('API Error: 429 Too Many Requests') as ApiError;
      error.apiError = { status: 429, statusText: 'Too Many Requests', url: '/api/test' };
      expect(ErrorService.getMessage(error)).toBe('Too many requests. Please wait a moment and try again.');
    });

    it('should handle 500 errors', () => {
      const error = new Error('API Error: 500 Internal Server Error') as ApiError;
      error.apiError = { status: 500, statusText: 'Internal Server Error', url: '/api/test' };
      expect(ErrorService.getMessage(error)).toBe('Server error. Please try again later.');
    });

    it('should handle 503 errors', () => {
      const error = new Error('API Error: 503 Service Unavailable') as ApiError;
      error.apiError = { status: 503, statusText: 'Service Unavailable', url: '/api/test' };
      expect(ErrorService.getMessage(error)).toBe('Service temporarily unavailable. Please try again later.');
    });

    it('should extract message from API error body', () => {
      const error = new Error('API Error') as ApiError;
      error.apiError = { 
        status: 400, 
        statusText: 'Bad Request',
        url: '/api/test',
        body: { message: 'Invalid input data' }
      };
      expect(ErrorService.getMessage(error)).toBe('Invalid input data');
    });

    it('should extract error from API error body', () => {
      const error = new Error('API Error') as ApiError;
      error.apiError = { 
        status: 400, 
        statusText: 'Bad Request',
        url: '/api/test',
        body: { error: 'Custom error message' }
      };
      expect(ErrorService.getMessage(error)).toBe('Custom error message');
    });
  });

  describe('formatError', () => {
    it('should format error without prefix', () => {
      const error = new Error('Test error');
      expect(formatError(error)).toBe('Test error');
    });

    it('should format error with prefix', () => {
      const error = new Error('Test error');
      expect(formatError(error, 'Failed to save')).toBe('Failed to save: Test error');
    });
  });

  describe('createAsyncHandler', () => {
    it('should handle successful operations', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      const result = await ErrorService.createAsyncHandler(operation);
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
    });

    it('should handle errors with fallback value', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      const result = await ErrorService.createAsyncHandler(operation, {
        fallbackValue: 'fallback'
      });
      expect(result).toBe('fallback');
    });

    it('should call custom error handler', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      const onError = vi.fn();
      
      await ErrorService.createAsyncHandler(operation, { onError });
      
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error',
          originalError: expect.any(Error)
        })
      );
    });

    it('should rethrow errors when requested', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorService.createAsyncHandler(operation, { rethrow: true })
      ).rejects.toThrow('Test error');
    });

    it('should include context in error details', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      const onError = vi.fn();
      
      await ErrorService.createAsyncHandler(operation, {
        context: 'Loading user data',
        onError
      });
      
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Loading user data: Test error',
          context: 'Loading user data'
        })
      );
    });
  });

  describe('logError', () => {
    it('should log error details to console', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = new Error('Test error');
      
      ErrorService.logError(error, 'Test context');
      
      expect(consoleSpy).toHaveBeenCalledWith(
        '[Error - Test context]',
        expect.objectContaining({
          message: 'Test context: Test error',
          originalError: error
        })
      );
      
      consoleSpy.mockRestore();
    });
  });
});