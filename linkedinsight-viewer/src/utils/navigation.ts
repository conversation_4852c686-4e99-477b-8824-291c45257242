import { NavigateFunction, To } from 'react-router-dom';

export function navigateWithTransition(
  navigate: NavigateFunction,
  to: To | number,
  options?: any,
  duration: number = 300
) {
  // Check if we're on the home page - if so, skip exit animation
  const isLeavingHome = window.location.pathname === '/';
  
  if (isLeavingHome) {
    // Navigate immediately without exit animation
    navigate(to as To, options);
    // Add entering class for the new page
    document.body.classList.add('page-entering');
    
    // Remove entering class after animation completes
    setTimeout(() => {
      document.body.classList.remove('page-entering');
    }, 300);
  } else {
    // Normal transition with exit animation
    document.body.classList.add('page-transitioning');
    
    // Navigate after transition
    setTimeout(() => {
      navigate(to as To, options);
      // Remove transitioning class and add entering class
      document.body.classList.remove('page-transitioning');
      document.body.classList.add('page-entering');
      
      // Remove entering class after animation completes
      setTimeout(() => {
        document.body.classList.remove('page-entering');
      }, 300);
    }, duration);
  }
}