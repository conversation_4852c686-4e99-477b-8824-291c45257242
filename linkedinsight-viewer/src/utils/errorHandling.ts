import { ApiError } from '../types/api';

export interface ErrorDetails {
  message: string;
  code?: string;
  statusCode?: number;
  context?: string;
  originalError?: unknown;
}

export type ErrorHandler = (error: unknown, context?: string) => ErrorDetails;

/**
 * Standardized error handling utility
 * Provides consistent error messages and handling across the application
 */
export class ErrorService {
  /**
   * Extract a user-friendly error message from any error type
   */
  static getMessage(error: unknown, context?: string): string {
    const details = this.getErrorDetails(error, context);
    return details.message;
  }

  /**
   * Get detailed error information for logging or debugging
   */
  static getErrorDetails(error: unknown, context?: string): ErrorDetails {
    // Handle API errors with specific error structure
    if (this.isApiError(error)) {
      return this.handleApiError(error, context);
    }

    // Handle standard Error objects
    if (error instanceof Error) {
      return this.handleStandardError(error, context);
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        message: error,
        context,
        originalError: error
      };
    }

    // Handle unknown error types
    return {
      message: 'An unexpected error occurred',
      context,
      originalError: error
    };
  }

  /**
   * Check if error is an API error with additional metadata
   */
  private static isApiError(error: unknown): error is ApiError {
    return (
      error instanceof Error &&
      'apiError' in error &&
      typeof (error as ApiError).apiError === 'object'
    );
  }

  /**
   * Handle API-specific errors with enhanced messaging
   */
  private static handleApiError(error: ApiError, context?: string): ErrorDetails {
    const apiErrorData = error.apiError;
    
    // Extract message from API error response
    let message = error.message;
    if (apiErrorData?.body) {
      if (typeof apiErrorData.body === 'object' && 'message' in apiErrorData.body) {
        message = apiErrorData.body.message as string;
      } else if (typeof apiErrorData.body === 'object' && 'error' in apiErrorData.body) {
        message = apiErrorData.body.error as string;
      }
    }

    // Handle common HTTP status codes with user-friendly messages
    const statusCode = apiErrorData?.status;
    if (statusCode) {
      switch (statusCode) {
        case 401:
          message = 'Authentication required. Please sign in and try again.';
          break;
        case 403:
          message = 'You do not have permission to perform this action.';
          break;
        case 404:
          message = context ? `${context} not found.` : 'Resource not found.';
          break;
        case 429:
          message = 'Too many requests. Please wait a moment and try again.';
          break;
        case 500:
          message = 'Server error. Please try again later.';
          break;
        case 503:
          message = 'Service temporarily unavailable. Please try again later.';
          break;
      }
    }

    return {
      message,
      statusCode,
      context,
      originalError: error
    };
  }

  /**
   * Handle standard Error objects
   */
  private static handleStandardError(error: Error, context?: string): ErrorDetails {
    // Handle network errors
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return {
        message: 'Unable to connect to server. Please check your connection and try again.',
        context,
        originalError: error
      };
    }

    // Handle timeout errors
    if (error.message.includes('timeout') || error.message.includes('Timeout')) {
      return {
        message: 'Request timed out. Please try again.',
        context,
        originalError: error
      };
    }

    // Handle abort errors
    if (error.name === 'AbortError') {
      return {
        message: 'Request was cancelled.',
        context,
        originalError: error
      };
    }

    // Default error handling
    return {
      message: context ? `${context}: ${error.message}` : error.message,
      context,
      originalError: error
    };
  }

  /**
   * Format error for display with optional prefix
   */
  static formatError(error: unknown, prefix?: string): string {
    const message = this.getMessage(error);
    return prefix ? `${prefix}: ${message}` : message;
  }

  /**
   * Log error with structured details
   */
  static logError(error: unknown, context?: string): void {
    const details = this.getErrorDetails(error, context);
    console.error(`[Error${context ? ` - ${context}` : ''}]`, {
      message: details.message,
      code: details.code,
      statusCode: details.statusCode,
      originalError: details.originalError
    });
  }

  /**
   * Create a standard error handler for async operations
   */
  static createAsyncHandler<T = void>(
    operation: () => Promise<T>,
    options: {
      context?: string;
      onError?: (error: ErrorDetails) => void;
      fallbackValue?: T;
      rethrow?: boolean;
    } = {}
  ): Promise<T | undefined> {
    return operation().catch((error) => {
      const errorDetails = this.getErrorDetails(error, options.context);
      
      // Log the error
      this.logError(error, options.context);
      
      // Call custom error handler if provided
      if (options.onError) {
        options.onError(errorDetails);
      }
      
      // Rethrow if requested
      if (options.rethrow) {
        throw error;
      }
      
      // Return fallback value
      return options.fallbackValue;
    });
  }
}

// Convenience functions for common use cases
export const getErrorMessage = ErrorService.getMessage.bind(ErrorService);
export const formatError = ErrorService.formatError.bind(ErrorService);
export const logError = ErrorService.logError.bind(ErrorService);
export const createAsyncHandler = ErrorService.createAsyncHandler.bind(ErrorService);