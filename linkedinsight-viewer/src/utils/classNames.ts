/**
 * Common className patterns used throughout the application
 */

export const classNames = {
  // Container patterns
  container: {
    centered: 'max-w-7xl mx-auto w-full',
    centeredWithPadding: 'max-w-7xl mx-auto w-full px-4 md:px-8',
    fullHeight: 'h-full flex flex-col',
    flexCenter: 'flex items-center justify-center'
  },
  
  // Button patterns
  button: {
    primary: 'px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'px-4 py-2 rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'px-4 py-2 rounded-md hover:bg-accent hover:text-accent-foreground',
    disabled: 'opacity-50 cursor-not-allowed'
  },
  
  // Form patterns
  form: {
    wrapper: 'space-y-6',
    field: 'space-y-2',
    label: 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
    input: 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
  },
  
  // Layout patterns
  layout: {
    header: 'h-12 border-b flex items-center px-4 md:px-8',
    main: 'flex-1 overflow-y-auto',
    footer: 'h-12 border-t flex items-center justify-center'
  },
  
  // Loading patterns
  loading: {
    spinner: 'animate-spin text-primary',
    overlay: 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center'
  }
} as const;

/**
 * Helper function to combine class names from the patterns
 */
export function getClassName(...paths: string[]): string {
  return paths.map(path => {
    const keys = path.split('.');
    let current: any = classNames;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return '';
      }
    }
    
    return typeof current === 'string' ? current : '';
  }).filter(Boolean).join(' ');
}