/**
 * Utility functions for formatting data in the frontend
 */

/**
 * Format a number to a readable string with K/M suffixes
 * Example: 1200 -> "1.2K", 1500000 -> "1.5M"
 * 
 * @param value The number to format
 * @param decimals Number of decimal places (defaults to 1)
 * @returns Formatted string
 */
export function formatDisplayNumber(value: number, decimals: number = 1): string {
  if (value === null || value === undefined || isNaN(value)) {
    return "0";
  }
  
  // Convert to absolute value for formatting
  const absValue = Math.abs(value);
  
  // Format with suffix based on magnitude
  if (absValue >= 1000000) {
    return `${(value / 1000000).toFixed(decimals)}M`;
  } else if (absValue >= 1000) {
    return `${(value / 1000).toFixed(decimals)}K`;
  } else {
    return value.toString();
  }
}