import { describe, it, expect } from 'vitest';
import { transformBackendIdea, transformBackendIdeas } from './ideaTransformers';
import type { BackendIdea } from './ideaTransformers';

describe('ideaTransformers', () => {
  describe('transformBackendIdea', () => {
    it('should transform backend idea to frontend format', () => {
      const backendIdea: BackendIdea = {
        id: '1',
        idea: 'Test idea text',
        category: 'Technology',
        summary: 'Test summary',
        engagement: 4,
        citations: [
          { text: 'Citation 1', content: 'Citation content 1' },
          { text: 'Citation 2' }
        ],
        generated_brief: 'Test brief'
      };

      const result = transformBackendIdea(backendIdea);

      expect(result).toEqual({
        id: '1',
        idea_text: 'Test idea text', // Should map idea to idea_text
        category: 'Technology',
        summary: 'Test summary',
        engagement: 4,
        citations: [
          { text: 'Citation 1', content: 'Citation content 1' },
          { text: 'Citation 2' }
        ],
        generated_brief: 'Test brief'
      });
    });

    it('should handle missing optional fields', () => {
      const backendIdea: BackendIdea = {
        id: '2',
        idea: 'Minimal idea',
        category: 'General',
        engagement: 3,
        // No summary, citations, or generated_brief
      };

      const result = transformBackendIdea(backendIdea);

      expect(result).toEqual({
        id: '2',
        idea_text: 'Minimal idea',
        category: 'General',
        summary: '', // Should default to empty string
        engagement: 3,
        citations: [], // Should default to empty array
        generated_brief: undefined
      });
    });
  });

  describe('transformBackendIdeas', () => {
    it('should transform an array of backend ideas', () => {
      const backendIdeas: BackendIdea[] = [
        {
          id: '1',
          idea: 'First idea',
          category: 'Tech',
          engagement: 5,
          summary: 'Summary 1'
        },
        {
          id: '2',
          idea: 'Second idea',
          category: 'Business',
          engagement: 3,
          citations: [{ text: 'Quote' }]
        }
      ];

      const results = transformBackendIdeas(backendIdeas);

      expect(results).toHaveLength(2);
      expect(results[0].idea_text).toBe('First idea');
      expect(results[0].summary).toBe('Summary 1');
      expect(results[1].idea_text).toBe('Second idea');
      expect(results[1].citations).toEqual([{ text: 'Quote' }]);
    });

    it('should handle empty array', () => {
      const results = transformBackendIdeas([]);
      expect(results).toEqual([]);
    });
  });
});