/**
 * Utility functions for managing strategy mode state
 */

/**
 * Check if strategy mode is enabled
 * @returns {boolean} True if strategy mode is enabled, false otherwise
 */
export function isStrategyModeEnabled(): boolean {
  const strategyMode = localStorage.getItem('strategyMode');
  // Default to true if not set
  return strategyMode !== 'false';
}

/**
 * Set strategy mode state
 * @param {boolean} enabled - Whether strategy mode should be enabled
 */
export function setStrategyMode(enabled: boolean): void {
  localStorage.setItem('strategyMode', enabled.toString());
}