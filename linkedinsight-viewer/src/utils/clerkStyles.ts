/**
 * Apply custom styles to Clerk components after they load
 * This handles the race condition where Clerk's styles override ours
 */
export function applyClerkStyles() {
  const applyStyles = () => {
    const cards = document.querySelectorAll('.cl-card');
    cards.forEach((card) => {
      if (card instanceof HTMLElement) {
        // Apply our design system styles directly
        card.style.boxShadow = '7px 7px 0px 0px hsl(0, 0%, 30%)';
        card.style.border = '2px solid hsl(0, 0%, 0%)';
        card.style.borderRadius = '0.75rem';
        card.style.backgroundColor = 'var(--shadow-container-bg)';
      }
    });
  };

  // Apply immediately
  applyStyles();

  // Watch for DOM changes and reapply
  const observer = new MutationObserver(() => {
    applyStyles();
  });

  // Start observing the document for added nodes
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // Clean up observer after a reasonable time
  setTimeout(() => {
    observer.disconnect();
  }, 5000);
}