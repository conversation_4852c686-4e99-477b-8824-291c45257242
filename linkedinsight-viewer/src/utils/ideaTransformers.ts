// Backend idea format
interface BackendIdea {
  id: string;
  idea: string;
  category: string;
  summary?: string;
  engagement: number;
  citations?: Array<{
    content?: string;
    text?: string;
  }>;
  generated_brief?: string;
}

// Import the frontend Idea type
import { Idea } from '../types/idea';

/**
 * Transforms a backend idea format to the frontend Idea format
 * This is the single source of truth for idea data transformation
 */
export function transformBackendIdea(backendIdea: BackendIdea): Idea {
  return {
    id: backendIdea.id,
    idea_text: backendIdea.idea, // Map 'idea' to 'idea_text'
    summary: backendIdea.summary || '',
    category: backendIdea.category,
    engagement: backendIdea.engagement,
    citations: backendIdea.citations || [],
    generated_brief: backendIdea.generated_brief,
  };
}

/**
 * Transforms an array of backend ideas to frontend format
 */
export function transformBackendIdeas(backendIdeas: BackendIdea[]): Idea[] {
  return backendIdeas.map(transformBackendIdea);
}

// Re-export the BackendIdea type for use in other files
export type { BackendIdea };