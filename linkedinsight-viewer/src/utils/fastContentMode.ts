/**
 * Fast Content Mode utilities
 * 
 * Fast Content Mode allows developers to test content generation flows quickly
 * without waiting for the full AI pipeline. When enabled, content is generated
 * in ~3 seconds instead of 60+ seconds.
 */

/**
 * Check if fast content mode is enabled
 * @returns true if fast content mode is enabled in localStorage
 */
export function isFastContentModeEnabled(): boolean {
  return localStorage.getItem('fastContentMode') === 'true';
}