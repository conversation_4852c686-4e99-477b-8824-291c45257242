// Idea types for transcript processing

export interface Idea {
  id: string;
  idea_text: string;
  summary?: string;
  category?: string;
  engagement?: number;
  engagement_rationale?: string;
  citations?: Citation[];
  generated_brief?: string;
}

export interface Citation {
  content?: string;
  text?: string;
  speaker?: string;
  timestamp?: string;
  engagement?: string;
  start_index?: number;
  end_index?: number;
}