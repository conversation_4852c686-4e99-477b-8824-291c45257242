/**
 * Type definitions for Content Agent Page
 */

// Navigation State Types
export interface IdeaNavigationState {
  fromIdea: boolean;
  idea: {
    id: string;
    idea_text: string;
    summary: string;
    category: string;
    citations?: Array<{
      text?: string;
      content?: string;
    }>;
  };
  postLength?: string;
  transcriptId?: string;
  ideaId?: string;
}

export interface BriefNavigationState {
  brief: string;
  postLength?: string;
  ideaId?: string;
  transcriptId?: string;
}

export interface PreservedEditorState {
  preservedFromEditor: boolean;
  sourceState: {
    brief?: string;
    postLength?: string;
    ideaId?: string;
    transcriptId?: string;
    sessionId?: string;
    generatedPosts?: GeneratedPostsMap;
    explanations?: ExplanationsMap;
  };
}

export type ContentAgentNavigationState = IdeaNavigationState | BriefNavigationState | PreservedEditorState;

// Generation Status Types
export interface GenerationStatus {
  overallStatus: string;
  currentStepKey?: string | null;
  userFriendlyMessage?: string;
}

export type GenerationStatusMap = Record<string, GenerationStatus | undefined>;

// Post Generation Types
export type GeneratedPostsMap = Record<string, string | null>;
export type ExplanationsMap = Record<string, string | null>;

// Source State for AgentPanes
export interface AgentPanesSourceState {
  brief: string;
  postLength: string;
  sessionId: string;
  generatedPosts?: GeneratedPostsMap;
  explanations?: ExplanationsMap;
  [key: string]: unknown; // For additional navigation state properties
}

// Type guards
export function isIdeaNavigationState(state: unknown): state is IdeaNavigationState {
  return (
    typeof state === 'object' &&
    state !== null &&
    'fromIdea' in state &&
    'idea' in state &&
    (state as IdeaNavigationState).fromIdea === true
  );
}

export function isBriefNavigationState(state: unknown): state is BriefNavigationState {
  return (
    typeof state === 'object' &&
    state !== null &&
    'brief' in state &&
    !('fromIdea' in state) &&
    !('preservedFromEditor' in state)
  );
}

export function isPreservedEditorState(state: unknown): state is PreservedEditorState {
  return (
    typeof state === 'object' &&
    state !== null &&
    'preservedFromEditor' in state &&
    'sourceState' in state &&
    (state as PreservedEditorState).preservedFromEditor === true
  );
}

// Citation type for better type safety
export interface Citation {
  text?: string;
  content?: string;
}

// Helper type for session fields that might have different naming conventions
export interface SessionFieldMapping {
  sessionId?: string;
  session_id?: string;
  generatedPosts?: GeneratedPostsMap;
  generated_posts?: GeneratedPostsMap;
}