// Shared types for agent components

export interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  text: string;
  timestamp: Date;
  isStreaming?: boolean;
  streamUrl?: string;
}

export interface AgentSession {
  sessionId: string;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
}

export interface InterviewSession extends AgentSession {
  briefContent: string;
}

export interface EditorSession extends AgentSession {
  currentContent: string;
  originalContent: string;
  lastAIContent?: string;
  lastUserContent?: string | null;
  metadata?: {
    postLength?: string;
  };
}