// linkedinsight-viewer/src/types/clerk.d.ts

// Extend the global Window interface
declare global {
  interface Window {
    Clerk?: { // Make Clerk optional as it might not be loaded immediately
      session?: {
        getToken: (options?: { template?: string }) => Promise<string | null>;
        // Add other session properties/methods if needed
      };
      // Add other Clerk instance properties/methods if needed
      // For example: user, signOut, openUserProfile, etc.
      user?: any; // Replace 'any' with a more specific type if available
      signOut: () => Promise<void>;
      openUserProfile: () => void;
      // ... other Clerk top-level methods or properties
    };
  }
}

// Export {} to make this a module file
export {}; 