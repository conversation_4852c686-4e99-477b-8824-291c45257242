/**
 * Type definitions for service layer
 */

// Agent Service Types
export interface Agent {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  // Add other agent properties as needed
}

export interface AgentData {
  name: string;
  description?: string;
  // Add other agent data properties as needed
}

// Session Service Types
export interface SessionListResponse {
  sessions: ContentGenerationSession[];
  total: number;
  limit: number;
  skip: number;
}

export interface ContentGenerationSession {
  sessionId: string;
  userId: string;
  brief: string;
  postLength: string;
  generatedPosts: { [draftId: string]: string };
  explanations?: { [draftId: string]: string | null };
  status: 'draft' | 'generating' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  // Support both naming conventions for compatibility
  session_id?: string;
  user_id?: string;
  post_length?: string;
  generated_posts?: { [draftId: string]: string };
  created_at?: string;
  updated_at?: string;
}

// API Response wrappers
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface SessionApiResponse {
  success: boolean;
  session?: ContentGenerationSession;
  message?: string;
}

export interface AgentListResponse {
  agents: Agent[];
  total?: number;
}