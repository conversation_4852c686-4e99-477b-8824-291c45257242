/**
 * Unified session types for single source of truth state management
 */

export interface Breadcrumb {
  path: string;
  label: string;
  timestamp: number;
  params?: Record<string, string>;
}

export interface IdeaItem {
  id: string;
  idea_text: string;
  summary: string;
  category: string;
  citations?: Array<{ text?: string; content?: string }>;
  selected?: boolean;
}

export interface InterviewMessage {
  id: string;
  type: 'user' | 'agent';
  text: string;
  timestamp: Date;
}

export interface AppSession {
  // Core identification
  id: string;
  version: 1; // For future migrations
  
  // Flow tracking
  flow: 'transcript' | 'notes' | 'interview' | 'direct';
  step: 'input' | 'ideas' | 'content' | 'editor';
  
  // Input data
  transcript?: string;
  transcriptId?: string;
  ideas?: IdeaItem[];
  selectedIdeaId?: string;
  notes?: string;
  interview?: {
    messages: InterviewMessage[];
    brief: string;
    sessionId?: string;
  };
  
  // Content generation
  brief?: string;
  postLength?: 'short' | 'medium' | 'long';
  posts?: Record<string, string>;
  explanations?: Record<string, string>;
  hasGenerated?: boolean;
  generationStatus?: Record<string, {
    status: 'pending' | 'generating' | 'completed' | 'failed';
    message?: string;
  }>;
  
  // Editor state
  originalContent?: string;
  editedContent?: string;
  editorSource?: string;
  lastAIContent?: string;
  lastUserContent?: string | null;
  
  // Navigation history
  breadcrumbs: Breadcrumb[];
  previousPath?: string;
  
  // Metadata
  created: number;
  updated: number;
  userId?: string;
  
  // Backend sync
  backendId?: string; // Maps to ContentSessionService sessionId
  lastSyncedAt?: number;
  syncStatus?: 'synced' | 'pending' | 'failed';
}

export interface SessionUpdate extends Partial<Omit<AppSession, 'id' | 'created' | 'version'>> {
  // Helper to ensure updates don't override core fields
}

export interface SessionManagerConfig {
  storagePrefix?: string;
  syncInterval?: number;
  cacheExpiry?: number;
}