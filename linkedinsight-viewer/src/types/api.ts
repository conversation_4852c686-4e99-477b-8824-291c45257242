// API Error types
export interface ApiErrorData {
  status: number;
  statusText: string;
  url: string;
  body?: unknown;
}

export interface ApiError extends Error {
  apiError?: ApiErrorData;
}

// Request body types
export type RequestBody = 
  | FormData 
  | string 
  | URLSearchParams 
  | Blob 
  | ArrayBuffer 
  | Record<string, any>  // Changed from unknown to any for better flexibility
  | object              // Added to accept any object type
  | null
  | undefined;

// Generic API response type
export type ApiResponse<T = unknown> = T | null;

// Request logging types
export interface RequestLogData {
  data?: unknown;
  optionsPassed?: Omit<RequestInit, 'headers'>;
}

// Blob information type
export interface BlobInfo {
  size: number;
  type: string;
}