import { useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '../services/apiClient';
import { useToast } from '../components/ui/toast-provider';
import { queryKeys } from '../lib/queryKeys';
import { ErrorService } from '../utils/errorHandling';

/**
 * Hook for deleting a draft
 * Automatically invalidates the drafts query on success
 */
export function useDeleteDraftMutation() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (draftId: string) => {
      await apiClient.delete(`/api/v1/draft-library/drafts/${draftId}`);
    },
    onSuccess: () => {
      // Invalidate only the drafts list query
      queryClient.invalidateQueries({ queryKey: queryKeys.drafts.list() });
      toast.success('Draft deleted');
    },
    onError: (error) => {
      ErrorService.logError(error, 'Failed to delete draft');
      toast.error('Failed to delete draft');
    },
  });
}

/**
 * Hook for copying draft content to clipboard
 * This doesn't need to invalidate queries since it doesn't modify server state
 */
export function useCopyDraftMutation() {
  const toast = useToast();

  return useMutation({
    mutationFn: async (content: string) => {
      await navigator.clipboard.writeText(content);
    },
    onSuccess: () => {
      toast.success('Draft copied to clipboard!');
    },
    onError: (error) => {
      ErrorService.logError(error, 'Failed to copy draft');
      toast.error('Failed to copy draft');
    },
  });
}