import { useMemo } from 'react';
import { useApiQuery } from './useApiQuery';
import { BackendIdea, transformBackendIdea } from '../utils/ideaTransformers';
import { Idea } from '../types/idea';
import { queryKeys } from '../lib/queryKeys';
import { mockService } from '../services/MockService';


interface TranscriptData {
  id: string;
  createdAt?: Date;
  transcript_content: string;
  ideas: BackendIdea[];
  generated_ideas: BackendIdea[];
  user_email: string;
  project_id?: string;
}

interface UseTranscriptIdeasQueryReturn {
  ideas: Idea[];
  generatedIdeas: Idea[];
  transcript: string;
  isLoading: boolean;
  isFetching: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export function useTranscriptIdeasQuery(transcriptId: string | null): UseTranscriptIdeasQueryReturn {
  const { 
    data, 
    isLoading, 
    isFetching,
    isError, 
    error, 
    refetch 
  } = useApiQuery<TranscriptData>(
    queryKeys.transcripts.detail(transcriptId || 'none'),
    transcriptId ? `/api/transcripts/${transcriptId}` : null,
    {
      mockFn: async () => {
        if (!transcriptId) {
          throw new Error('No transcript ID provided');
        }
        const mockResponse = await mockService.getTranscript(transcriptId);
        // Transform mock ideas to backend format for consistency
        const backendIdeas: BackendIdea[] = mockResponse.ideas.map(idea => ({
          id: idea.id,
          idea: idea.idea_text,
          summary: idea.summary || '',
          category: idea.category || '',
          citations: idea.citations || [],
          engagement: idea.engagement || 0,
          generated_brief: idea.generated_brief
        }));
        
        return {
          id: mockResponse.id,
          transcript_content: mockResponse.transcript_content,
          ideas: backendIdeas,
          generated_ideas: [],
          user_email: '<EMAIL>'
        };
      },
      staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    }
  );

  // Memoize transformed data to prevent unnecessary re-renders
  const ideas = useMemo(() => 
    data?.ideas?.map(transformBackendIdea) || [], 
    [data?.ideas]
  );
  
  const generatedIdeas = useMemo(() => 
    data?.generated_ideas?.map(transformBackendIdea) || [], 
    [data?.generated_ideas]
  );
  
  const transcript = data?.transcript_content || '';

  return {
    ideas,
    generatedIdeas,
    transcript,
    isLoading,
    isFetching,
    isError,
    error: error as Error | null,
    refetch,
  };
}