import { useState } from 'react';

/**
 * Hook for managing generate button state and async actions
 * Provides consistent loading state management across all generate buttons
 */
export function useGenerateAction() {
  const [isGenerating, setIsGenerating] = useState(false);
  
  const executeGenerate = async (action: () => Promise<any>) => {
    if (isGenerating) return; // Prevent double-clicks
    
    setIsGenerating(true);
    try {
      const result = await action();
      return result;
    } finally {
      setIsGenerating(false);
    }
  };
  
  return { isGenerating, executeGenerate };
}