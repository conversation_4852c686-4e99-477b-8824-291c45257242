import { useApiQuery } from './useApiQuery';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '../services/apiClient';
import { useToast } from '../components/ui/toast-provider';
import { queryKeys } from '../lib/queryKeys';
import { isValidResponse, createMutationErrorHandler, queryPresets } from '../lib/queryUtils';

interface ContentStrategyFormData {
  industry: string;
  offering: string;
  targetAudience: string;
  differentiators: string;
  contentGoal: string;
}

// API response type matching backend's ContentStrategy model with camelCase fields
interface ContentStrategyApiResponse {
  clerkUserId?: string;
  industry: string;
  offering: string;
  targetAudience: string;
  differentiators: string;
  contentGoal: string;
}

// Mock data for development
const mockContentStrategy: ContentStrategyApiResponse = {
  industry: 'SaaS',
  offering: 'AI-powered content generation tools',
  targetAudience: 'Marketing professionals and content creators',
  differentiators: 'Real-time collaboration, advanced AI models, seamless integrations',
  contentGoal: 'Thought leadership and brand awareness',
};

/**
 * Hook to fetch user's content strategy
 * Handles 404 gracefully when user has no strategy yet
 */
export function useContentStrategy() {
  return useApiQuery<ContentStrategyApiResponse>(
    queryKeys.contentStrategy.current(),
    'api/v1/content-strategy/',
    {
      mockData: mockContentStrategy,
      ...queryPresets.userContent,
    }
  );
}

/**
 * Hook to update/create content strategy
 * Handles validation errors and updates cache on success
 */
export function useUpdateContentStrategy() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (data: ContentStrategyFormData) => {
      const response = await apiClient.post<ContentStrategyApiResponse>(
        'api/v1/content-strategy/',
        data
      );
      
      if (!isValidResponse<ContentStrategyApiResponse>(response)) {
        throw new Error('Invalid response from server');
      }
      
      return response;
    },
    onSuccess: (data) => {
      // Update the cache with the new data
      queryClient.setQueryData(queryKeys.contentStrategy.current(), data);
      toast.success('Strategy saved successfully!');
    },
    onError: createMutationErrorHandler(
      toast,
      'Failed to save strategy',
      'Error saving strategy'
    ),
  });
}