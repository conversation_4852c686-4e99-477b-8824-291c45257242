import { useTranscriptNavigationContext } from './useTranscriptNavigationContext';

/**
 * Hook that guarantees a non-null transcriptId.
 * Throws an error if used when no transcript is selected.
 * Use this in pages/components that require a transcript to function.
 */
export function useTranscriptId(): string {
  const { transcriptId } = useTranscriptNavigationContext();
  if (!transcriptId) {
    throw new Error('useTranscriptId: transcriptId not available. Ensure this page is rendered only after a transcript is selected.');
  }
  return transcriptId;
} 