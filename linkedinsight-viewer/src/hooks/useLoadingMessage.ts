import { useState, useEffect } from 'react';
import { loadingMessages } from '../data/loadingMessages';

export function useLoadingMessage(intervalMs: number = 2500) {
  const [message, setMessage] = useState(loadingMessages[0].text);

  useEffect(() => {
    const interval = setInterval(() => {
      const randomMessage = loadingMessages[
        Math.floor(Math.random() * loadingMessages.length)
      ];
      setMessage(randomMessage.text);
    }, intervalMs);

    return () => clearInterval(interval);
  }, [intervalMs]);

  return message;
} 