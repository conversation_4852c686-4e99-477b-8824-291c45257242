import { useApiQuery } from './useApiQuery';
import { queryKeys } from '../lib/queryKeys';

interface DraftMetadata {
  hookPattern: string;
  bodyFramework: string;
  endingPattern: string;
  linguisticPattern?: string;
  wordCount: number;
  charCount: number;
}

interface Draft {
  draftId: string;
  userId: string;
  content: string;
  brief: string;
  metadata: DraftMetadata;
  postLength: string;
  draftType?: 'draft_a' | 'draft_b';
  sessionId?: string;
  jobId?: string;
  createdAt: string;
  updatedAt?: string;
}

interface DraftLibraryResponse {
  drafts: Draft[];
  total: number;
}

interface UseDraftsQueryOptions {
  page: number;
  limit: number;
  search?: string;
  enabled?: boolean;
}

// Mock data for development
const mockDrafts: Draft[] = [
  {
    draftId: 'mock-1',
    userId: 'user-123',
    content: 'This is a mock draft about AI in healthcare. It contains interesting insights about how artificial intelligence is transforming patient care and medical diagnostics.',
    brief: 'AI Healthcare Transformation',
    metadata: {
      hookPattern: 'question',
      bodyFramework: 'problem-solution',
      endingPattern: 'call-to-action',
      wordCount: 150,
      charCount: 750
    },
    postLength: 'medium',
    draftType: 'draft_a',
    createdAt: new Date().toISOString(),
  },
  {
    draftId: 'mock-2',
    userId: 'user-123',
    content: 'Remote work productivity tips: 1) Create a dedicated workspace, 2) Set clear boundaries, 3) Use time-blocking techniques, 4) Take regular breaks.',
    brief: 'Remote Work Best Practices',
    metadata: {
      hookPattern: 'statistic',
      bodyFramework: 'listicle',
      endingPattern: 'question',
      wordCount: 200,
      charCount: 1000
    },
    postLength: 'short',
    draftType: 'draft_b',
    createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
  }
];

const getMockDraftsResponse = (options: UseDraftsQueryOptions): DraftLibraryResponse => {
  const { page, limit, search } = options;
  
  // Filter by search term if provided
  let filteredDrafts = mockDrafts;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredDrafts = mockDrafts.filter(draft => 
      draft.content.toLowerCase().includes(searchLower) ||
      draft.brief.toLowerCase().includes(searchLower)
    );
  }
  
  // Paginate
  const startIndex = (page - 1) * limit;
  const paginatedDrafts = filteredDrafts.slice(startIndex, startIndex + limit);
  
  return {
    drafts: paginatedDrafts,
    total: filteredDrafts.length
  };
};

export function useDraftsQuery(options: UseDraftsQueryOptions) {
  const { page, limit, search, enabled = true } = options;
  
  // Build query parameters
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('limit', limit.toString());
  if (search) {
    params.append('search', search);
  }
  
  const { data, ...queryResult } = useApiQuery<DraftLibraryResponse>(
    [...queryKeys.drafts.list(), page.toString(), limit.toString(), search || ''], // Query key includes all params for proper caching
    `/api/v1/draft-library/drafts?${params.toString()}`,
    {
      mockData: getMockDraftsResponse(options),
      // Keep data fresh for 2 minutes since drafts change frequently
      staleTime: 2 * 60 * 1000,
      enabled,
    }
  );
  
  return {
    drafts: data?.drafts || [],
    totalDrafts: data?.total || 0,
    totalPages: Math.ceil((data?.total || 0) / limit),
    ...queryResult,
  };
}