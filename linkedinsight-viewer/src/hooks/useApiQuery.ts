import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import apiClient from '../services/apiClient';
import { isMockModeEnabled } from '../utils/mockMode';
import { mockDelay } from '../lib/queryUtils';

interface UseApiQueryOptions<TData = unknown, TError = Error> 
  extends Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> {
  mockData?: TData;
  mockFn?: () => Promise<TData>;
}

/**
 * Custom hook that wraps useQuery with our apiClient
 * Handles mock mode and provides consistent error handling
 */
export function useApiQuery<TData = unknown>(
  queryKey: readonly unknown[],
  url: string | null,
  options?: UseApiQueryOptions<TData>
) {
  const { mockData, mockFn, ...queryOptions } = options || {};

  return useQuery<TData>({
    queryKey,
    queryFn: async () => {
      // If mock mode is enabled
      if (isMockModeEnabled()) {
        // Use mockFn if provided
        if (mockFn) {
          await mockDelay();
          return mockFn();
        }
        
        // Otherwise use static mockData if provided
        if (mockData !== undefined) {
          await mockDelay();
          return mockData;
        }
      }

      // If no URL provided, throw error (React Query will handle it properly)
      if (!url) {
        throw new Error('No URL provided for query');
      }

      // Make the API call - apiClient.get returns ApiResponse<T> which is T | null
      const response = await apiClient.get<TData>(url);
      if (response === null) {
        throw new Error('No data received from server');
      }
      return response;
    },
    enabled: !!url || (isMockModeEnabled() && (mockData !== undefined || mockFn !== undefined)),
    ...queryOptions,
  });
}