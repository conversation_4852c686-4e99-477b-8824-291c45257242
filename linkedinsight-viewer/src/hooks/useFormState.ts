import { useState, useCallback } from 'react';
import { useToast } from '../components/ui/toast-provider';
import { ErrorService } from '../utils/errorHandling';

interface UseFormStateOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  successMessage?: string;
  errorMessage?: string;
}

/**
 * Custom hook for managing form state with consistent error handling
 * and loading states.
 */
export function useFormState(options: UseFormStateOptions = {}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const toast = useToast();

  const handleSubmit = useCallback(async (
    submitFn: () => Promise<void>
  ) => {
    setIsSubmitting(true);
    setError(null);

    try {
      await submitFn();
      
      if (options.successMessage) {
        toast.success(options.successMessage);
      }
      
      if (options.onSuccess) {
        options.onSuccess();
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      
      const message = options.errorMessage || ErrorService.getMessage(error, 'Form submission');
      toast.error(message);
      
      if (options.onError) {
        options.onError(error);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [toast, options]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isSubmitting,
    error,
    handleSubmit,
    resetError
  };
}