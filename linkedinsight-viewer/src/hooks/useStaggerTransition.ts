import { useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

interface StaggerTransitionOptions {
  staggerDelay?: number;
  transitionDuration?: number;
  onTransitionComplete?: () => void;
}

export function useStaggerTransition(options: StaggerTransitionOptions = {}) {
  const {
    staggerDelay = 50,
    transitionDuration = 300,
    onTransitionComplete
  } = options;

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [clickedIndex, setClickedIndex] = useState<number | null>(null);
  const navigate = useNavigate();
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const handleStaggerNavigation = useCallback((path: string, index: number, totalItems: number) => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setClickedIndex(index);

    // Calculate the longest animation time
    const totalAnimationTime = transitionDuration + (staggerDelay * (totalItems - 1));

    // Navigate after animations complete
    timeoutRef.current = setTimeout(() => {
      navigate(path);
      setIsTransitioning(false);
      setClickedIndex(null);
      onTransitionComplete?.();
    }, totalAnimationTime);
  }, [isTransitioning, navigate, staggerDelay, transitionDuration, onTransitionComplete]);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    isTransitioning,
    clickedIndex,
    handleStaggerNavigation,
    cleanup
  };
}