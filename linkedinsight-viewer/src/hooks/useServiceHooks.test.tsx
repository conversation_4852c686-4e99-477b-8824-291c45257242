/// <reference types="vitest/globals" />
import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useAgentServiceHook } from './useServiceHooks';

// Mock services
import agentServiceInstance from '../services/AgentService';

// Mock Clerk's useAuth hook
import { useAuth } from '@clerk/clerk-react';

vi.mock('@clerk/clerk-react', () => ({
  useAuth: vi.fn(),
}));

vi.mock('../services/AgentService', () => ({
  default: {
    getAgents: vi.fn(),
    getAgentById: vi.fn(),
    createAgent: vi.fn(),
    updateAgent: vi.fn(),
    deleteAgent: vi.fn(),
    generateContent: vi.fn(), // Mock the instance method
  },
}));

describe('Service Hooks', () => {
  const mockGetToken = vi.fn();
  const mockToken = 'mock-auth-token';

  beforeEach(() => {
    vi.clearAllMocks();
    (useAuth as any).mockReturnValue({ getToken: mockGetToken });
    mockGetToken.mockResolvedValue(mockToken);
  });

  describe('useAgentServiceHook', () => {
    it('getAgents should call service without token parameter', async () => {
      const { result } = renderHook(() => useAgentServiceHook());
      (agentServiceInstance.getAgents as any).mockResolvedValue({ data: 'agents' });
      await act(async () => { await result.current.getAgents(); });
      expect(mockGetToken).toHaveBeenCalledTimes(1);
      expect(agentServiceInstance.getAgents).toHaveBeenCalledWith();
    });

    it('getAgentById should call service with id only', async () => {
      const { result } = renderHook(() => useAgentServiceHook());
      (agentServiceInstance.getAgentById as any).mockResolvedValue({ data: 'agent' });
      await act(async () => { await result.current.getAgentById('id123'); });
      expect(mockGetToken).toHaveBeenCalledTimes(1);
      expect(agentServiceInstance.getAgentById).toHaveBeenCalledWith('id123');
    });
    
    it('createAgent should call service with data only', async () => {
        const { result } = renderHook(() => useAgentServiceHook());
        const agentData = { name: 'New Agent' };
        (agentServiceInstance.createAgent as any).mockResolvedValue({ id: 'newId' });
        await act(async () => { await result.current.createAgent(agentData); });
        expect(mockGetToken).toHaveBeenCalledTimes(1);
        expect(agentServiceInstance.createAgent).toHaveBeenCalledWith(agentData);
      });
  
      it('updateAgent should call service with id and data only', async () => {
        const { result } = renderHook(() => useAgentServiceHook());
        const agentData = { name: 'Updated Agent' };
        (agentServiceInstance.updateAgent as any).mockResolvedValue({ id: 'id123' });
        await act(async () => { await result.current.updateAgent('id123', agentData); });
        expect(mockGetToken).toHaveBeenCalledTimes(1);
        expect(agentServiceInstance.updateAgent).toHaveBeenCalledWith('id123', agentData);
      });
  
      it('deleteAgent should call service with id only', async () => {
        const { result } = renderHook(() => useAgentServiceHook());
        (agentServiceInstance.deleteAgent as any).mockResolvedValue({});
        await act(async () => { await result.current.deleteAgent('id123'); });
        expect(mockGetToken).toHaveBeenCalledTimes(1);
        expect(agentServiceInstance.deleteAgent).toHaveBeenCalledWith('id123');
      });

    it('generateAgentContent should call service method with correct params', async () => {
      const { result } = renderHook(() => useAgentServiceHook());
      const input = 'test input';
      const onStatusUpdate = vi.fn();
      const postLength = 'medium';
      const draftId = 'draft123';
      (agentServiceInstance.generateContent as any).mockResolvedValue({ generated_post: 'post' });
      
      await act(async () => { 
        await result.current.generateAgentContent(input, onStatusUpdate, postLength, draftId);
      });
      
      expect(mockGetToken).toHaveBeenCalledTimes(0); // getToken is not called directly in this method
      expect(agentServiceInstance.generateContent).toHaveBeenCalledWith(input, onStatusUpdate, postLength, draftId);
    });
  });
}); 