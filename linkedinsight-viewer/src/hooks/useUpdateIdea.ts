import { useState } from 'react';
import apiClient from '../services/apiClient';

interface IdeaUpdate {
  status?: string;
  generated_brief?: string;
}

interface UseUpdateIdeaReturn {
  updateIdea: (transcriptId: string, ideaId: string, updates: IdeaUpdate) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useUpdateIdea(): UseUpdateIdeaReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateIdea = async (transcriptId: string, ideaId: string, updates: IdeaUpdate) => {
    setIsLoading(true);
    setError(null);

    try {
      await apiClient.patch(`/api/transcripts/${transcriptId}/ideas/${ideaId}`, updates);
    } catch (err: any) {
      setError(err.message || 'Failed to update idea');
      console.error('Error updating idea:', err);
      throw err; // Re-throw to allow handling in components
    } finally {
      setIsLoading(false);
    }
  };

  return {
    updateIdea,
    isLoading,
    error
  };
}