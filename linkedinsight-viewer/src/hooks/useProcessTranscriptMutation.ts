import { useNavigate } from 'react-router-dom';
import { transcriptApi } from '../services/TranscriptService';
import { navigateWithTransition } from '../utils/navigation';
import { useTranscriptNavigationContext } from '../hooks/useTranscriptNavigationContext';
import { Idea } from '../types/idea';
import { mockService } from '../services/MockService';
import { useApiMutation } from './useApiMutation';

interface ProcessTranscriptResult {
  ideas: Idea[];
  transcript: string;
  transcript_id?: string;
}

interface ProcessTranscriptOptions {
  onSuccess?: (data: ProcessTranscriptResult) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook to process a transcript and extract ideas
 * Handles both real API calls and mock mode
 * Includes navigation to ideas page on success
 */
export function useProcessTranscriptMutation(options?: ProcessTranscriptOptions) {
  const navigate = useNavigate();
  const { persistTranscriptId } = useTranscriptNavigationContext();

  return useApiMutation<ProcessTranscriptResult, Error, string>(
    async (content: string): Promise<ProcessTranscriptResult> => {
      // Real API call
      return transcriptApi.extractIdeas(content);
    },
    {
      mockFn: async (content: string) => {
        // Use mock service for consistent behavior with 2 second delay
        const formData = new FormData();
        formData.append('audio_file', new Blob([content], { type: 'text/plain' }), 'transcript.txt');
        
        const result = await mockService.processTranscript(formData, { delay: 2000 });
        const mockTranscript = await mockService.getTranscript(result.transcript_id);
        
        return {
          ideas: mockTranscript.ideas,
          transcript: mockTranscript.transcript_content,
          transcript_id: result.transcript_id
        };
      },
      mockDelay: 0, // Delay is already handled by mockService
      onSuccess: (data) => {
      // Persist the transcript ID to session storage
      if (data.transcript_id) {
        persistTranscriptId(data.transcript_id);
      }

      // Call custom onSuccess if provided
      options?.onSuccess?.(data);

      // Navigate to ideas page
      navigateWithTransition(navigate, '/ideas', {
        state: {
          extractedIdeas: data.ideas,
          transcriptId: data.transcript_id
        }
      });
      },
      onError: (error: Error) => {
        console.error('Failed to process transcript:', error);
        options?.onError?.(error);
      }
    }
  );
}