import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { isMockModeEnabled } from '../utils/mockMode';
import { mockDelay } from '../lib/queryUtils';

interface UseApiMutationOptions<TData = unknown, TError = Error, TVariables = void>
  extends Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'> {
  mockFn?: (variables: TVariables) => Promise<TData>;
  mockDelay?: number;
}

/**
 * Custom hook that wraps useMutation with our API patterns
 * Handles mock mode and provides consistent error handling
 * 
 * @param mutationFn - The function that performs the actual mutation
 * @param options - Extended mutation options including mock mode support
 */
export function useApiMutation<TData = unknown, TError = Error, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseApiMutationOptions<TData, TError, TVariables>
) {
  const queryClient = useQueryClient();
  const { mockFn, mockDelay: delayMs = 300, ...mutationOptions } = options || {};

  return useMutation<TData, TError, TVariables>({
    mutationFn: async (variables: TVariables) => {
      // Handle mock mode
      if (isMockModeEnabled()) {
        console.log('[useApiMutation] Mock mode: Simulating mutation', { variables });
        
        if (mockFn) {
          // Use provided mock function
          await mockDelay(delayMs);
          return mockFn(variables);
        }
        
        // Default mock behavior - just delay and return undefined
        await mockDelay(delayMs);
        return undefined as TData;
      }
      
      // Real API call
      return mutationFn(variables);
    },
    ...mutationOptions,
    // Ensure onSuccess runs after our internal logic
    onSuccess: (data, variables, context) => {
      // Allow component-level onSuccess to run
      mutationOptions?.onSuccess?.(data, variables, context);
    },
  });
}