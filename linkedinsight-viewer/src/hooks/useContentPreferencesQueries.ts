import { useApiQuery } from './useApiQuery';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '../services/apiClient';
import { useToast } from '../components/ui/toast-provider';
import { queryKeys } from '../lib/queryKeys';
import { isValidResponse, createMutationErrorHandler, queryPresets } from '../lib/queryUtils';
import { ErrorService } from '../utils/errorHandling';

interface ContentPreferencesFormData {
  transcriptName: string;
  opinionStyle: string;
  emojiUsage: string;
  voiceDistinctiveness: string;
  controversyApproach: string;
  humorStyle: string;
  bragLevel: string;
  readabilityStyle: string;
}

// API response type matching backend's ContentPreferences model with camelCase fields
interface ContentPreferencesApiResponse {
  clerkUserId?: string;
  transcriptName: string;
  opinionStyle: string;
  emojiUsage: string;
  voiceDistinctiveness: string;
  controversyApproach: string;
  humorStyle: string;
  bragLevel: string;
  readabilityStyle: string;
}

interface PreferenceOption {
  value: string;
  label: string;
}

interface PreferenceOptions {
  opinion_style?: PreferenceOption[];
  emoji_usage?: PreferenceOption[];
  voice_distinctiveness?: PreferenceOption[];
  controversy_approach?: PreferenceOption[];
  humor_style?: PreferenceOption[];
  brag_level?: PreferenceOption[];
  readability_style?: PreferenceOption[];
}

// Mock data for development
const mockPreferenceOptions: PreferenceOptions = {
  opinion_style: [
    { value: 'balanced', label: 'Balanced' },
    { value: 'strong', label: 'Strong Opinions' },
    { value: 'neutral', label: 'Neutral' },
  ],
  emoji_usage: [
    { value: 'none', label: 'No Emojis' },
    { value: 'minimal', label: 'Minimal' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'frequent', label: 'Frequent' },
  ],
  voice_distinctiveness: [
    { value: 'professional', label: 'Professional' },
    { value: 'conversational', label: 'Conversational' },
    { value: 'unique', label: 'Unique' },
  ],
  controversy_approach: [
    { value: 'avoid', label: 'Avoid' },
    { value: 'acknowledge', label: 'Acknowledge' },
    { value: 'embrace', label: 'Embrace' },
  ],
  humor_style: [
    { value: 'none', label: 'No Humor' },
    { value: 'subtle', label: 'Subtle' },
    { value: 'witty', label: 'Witty' },
    { value: 'playful', label: 'Playful' },
  ],
  brag_level: [
    { value: 'humble', label: 'Humble' },
    { value: 'modest', label: 'Modest' },
    { value: 'confident', label: 'Confident' },
  ],
  readability_style: [
    { value: 'simple', label: 'Simple' },
    { value: 'balanced', label: 'Balanced' },
    { value: 'sophisticated', label: 'Sophisticated' },
  ],
};

const mockPreferences: ContentPreferencesApiResponse = {
  transcriptName: 'Mock User',
  opinionStyle: 'balanced',
  emojiUsage: 'minimal',
  voiceDistinctiveness: 'conversational',
  controversyApproach: 'acknowledge',
  humorStyle: 'witty',
  bragLevel: 'modest',
  readabilityStyle: 'balanced',
};

/**
 * Hook to fetch content preference options (dropdown values)
 * These options rarely change, so we cache them for a long time
 */
export function useContentPreferencesOptions() {
  return useApiQuery<PreferenceOptions>(
    queryKeys.contentPreferences.options(),
    'api/v1/content-preferences/options',
    {
      mockData: mockPreferenceOptions,
      staleTime: Infinity, // Options rarely change
      gcTime: 1000 * 60 * 60 * 24, // Keep in cache for 24 hours
      retry: false, // Don't retry if options fail to load
    }
  );
}

/**
 * Hook to fetch user's content preferences
 * Handles 404 gracefully when user has no preferences yet
 */
export function useContentPreferences() {
  return useApiQuery<ContentPreferencesApiResponse>(
    queryKeys.contentPreferences.current(),
    'api/v1/content-preferences/',
    {
      mockData: mockPreferences,
      ...queryPresets.userContent,
    }
  );
}

/**
 * Hook to update/create content preferences
 * Optimistically updates the cache and handles validation errors
 */
export function useUpdateContentPreferences() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (data: ContentPreferencesFormData) => {
      const response = await apiClient.post<ContentPreferencesApiResponse>(
        'api/v1/content-preferences/',
        data
      );
      
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response from server');
      }
      
      return response;
    },
    onSuccess: (data) => {
      // Update the cache with the new data
      queryClient.setQueryData(queryKeys.contentPreferences.current(), data);
      toast.success('Content preferences saved successfully!');
    },
    onError: (err: any) => {
      const errorMessage = ErrorService.getMessage(err, 'Saving preferences');
      toast.error(errorMessage);
      ErrorService.logError(err, 'Content preferences save failed');
      
      // Re-throw with formatted message for the component to handle
      throw new Error(errorMessage);
    },
  });
}