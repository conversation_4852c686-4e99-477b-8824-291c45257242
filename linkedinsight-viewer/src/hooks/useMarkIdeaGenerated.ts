import { useQueryClient } from '@tanstack/react-query';
import { useApiMutation } from './useApiMutation';
import apiClient from '../services/apiClient';
import { mockService } from '../services/MockService';
import { ErrorService } from '../utils/errorHandling';
import { queryKeys } from '../lib/queryKeys';

interface MarkGeneratedVariables {
  ideaId: string;
  transcriptId: string;
}

interface MarkGeneratedResponse {
  status: string;
}

// API function for marking idea as generated
const markIdeaAsGeneratedAPI = async ({ ideaId, transcriptId }: MarkGeneratedVariables): Promise<MarkGeneratedResponse> => {
  // The backend expects query parameters, not a JSON body
  const params = new URLSearchParams({
    idea_id: ideaId,
    transcript_id: transcriptId
  });
  
  const response = await apiClient.post<MarkGeneratedResponse>(`/api/ideas/mark-generated?${params.toString()}`, null);
  
  if (!response) {
    throw new Error('No response from mark generated API');
  }
  
  // Status is already_generated is not an error, just informational
  if (response.status === 'already_generated') {
    // Already marked, no action needed
  }
  
  return response;
};

export function useMarkIdeaGenerated() {
  const queryClient = useQueryClient();

  const mutation = useApiMutation(markIdeaAsGeneratedAPI, {
    mockFn: async ({ ideaId, transcriptId }) => {
      await mockService.markIdeaGenerated(ideaId, transcriptId);
      return { status: 'success' };
    },
    onSuccess: (_, variables) => {
      // Only invalidate the specific transcript's data
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.transcripts.detail(variables.transcriptId) 
      });
      
      // Invalidate the specific transcript's ideas query
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.transcripts.ideas(variables.transcriptId) 
      });
      
      // Invalidate generated ideas for this transcript
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.transcripts.generatedIdeas(variables.transcriptId) 
      });
    },
    onError: (error: unknown) => {
      console.error('Error marking idea as generated:', error);
    }
  });

  return {
    markAsGenerated: async (ideaId: string, transcriptId: string): Promise<void> => {
      // mutateAsync returns a promise
      await mutation.mutateAsync({ ideaId, transcriptId });
    },
    isLoading: mutation.isPending,
    error: mutation.error ? ErrorService.getMessage(mutation.error) : null
  };
}