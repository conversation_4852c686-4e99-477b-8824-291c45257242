import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { isMockModeEnabled } from '../utils/mockMode';

interface TranscriptNavigationContext {
  transcriptId: string | null;
  ideaId: string | null;
  // Breadcrumb data for transcript flow
  breadcrumbPath: BreadcrumbSegment[];
  // Helper to persist transcript ID
  persistTranscriptId: (id: string) => void;
  // Helper to build navigation state
  getNavigationState: () => NavigationState;
}

interface BreadcrumbSegment {
  label: string;
  path: string;
  state?: any;
}

interface NavigationState {
  transcriptId?: string;
  ideaId?: string;
}

/**
 * Custom hook to manage transcript navigation context
 * Centralizes transcript flow state management and breadcrumb generation
 */
export function useTranscriptNavigationContext(): TranscriptNavigationContext {
  const location = useLocation();
  
  // Get transcript ID from location state or sessionStorage
  const [transcriptId, setTranscriptId] = useState<string | null>(() => {
    return location.state?.transcriptId || sessionStorage.getItem('currentTranscriptId');
  });
  
  // Get idea ID from location state
  const ideaId = location.state?.ideaId || null;
  
  // Update transcript ID when location state changes
  useEffect(() => {
    if (location.state?.transcriptId && location.state.transcriptId !== transcriptId) {
      setTranscriptId(location.state.transcriptId);
      sessionStorage.setItem('currentTranscriptId', location.state.transcriptId);
    }
  }, [location.state?.transcriptId]);
  
  // Persist transcript ID to sessionStorage
  const persistTranscriptId = (id: string) => {
    setTranscriptId(id);
    sessionStorage.setItem('currentTranscriptId', id);
  };
  
  // Build breadcrumb path based on current route and transcript context
  const breadcrumbPath: BreadcrumbSegment[] = [];
  
  // Always include transcript if we have a transcript ID
  if (transcriptId) {
    breadcrumbPath.push({
      label: 'Transcript',
      path: '/transcript-input'
    });
    
    // Add Ideas segment
    breadcrumbPath.push({
      label: 'Ideas',
      path: '/ideas',
      state: { transcriptId }
    });
    
    // Add Content Agent segment if we're on that page
    if (location.pathname === '/content-agent') {
      breadcrumbPath.push({
        label: 'Content Agent',
        path: '/content-agent',
        state: { transcriptId, ideaId }
      });
    }
  }
  
  // Helper to get navigation state for passing to other routes
  const getNavigationState = (): NavigationState => {
    const state: NavigationState = {};
    if (transcriptId) state.transcriptId = transcriptId;
    if (ideaId) state.ideaId = ideaId;
    return state;
  };
  
  return {
    transcriptId,
    ideaId,
    breadcrumbPath,
    persistTranscriptId,
    getNavigationState
  };
}