import { useAuth } from '@clerk/clerk-react';
import { useCallback } from 'react';

// Import singleton service instances
import agentServiceInstance from '../services/AgentService';

// Types from AgentService - consider re-exporting or importing from a central types file
import { AgentRequest, AgentResponse, AgentJobResponse, AgentJobStatusResponse } from '../services/AgentService';

export function useAgentServiceHook() {
  const { getToken } = useAuth();
  
  const getAgents = useCallback(async () => {
    const token = await getToken();
    return agentServiceInstance.getAgents();
  }, [getToken]);
  
  const getAgentById = useCallback(async (agentId: string) => {
    const token = await getToken();
    return agentServiceInstance.getAgentById(agentId);
  }, [getToken]);

  const createAgent = useCallback(async (agentData: any) => { // Consider more specific type for agentData
      const token = await getToken();
      return agentServiceInstance.createAgent(agentData);
  }, [getToken]);

  const updateAgent = useCallback(async (agentId: string, agentData: any) => {
      const token = await getToken();
      return agentServiceInstance.updateAgent(agentId, agentData);
  }, [getToken]);

  const deleteAgent = useCallback(async (agentId: string) => {
      const token = await getToken();
      return agentServiceInstance.deleteAgent(agentId);
  }, [getToken]);

  // generateContent still takes getToken directly, so components can call it on the instance if needed,
  // or we could wrap it here too if there's a common pattern of use from hooks.
  // For now, hook focuses on methods that primarily just need a token for one-shot calls.
  const generateAgentContent = useCallback(async (
    input: string, 
    onStatusUpdate?: (statusDetails: { overallStatus: string; currentStepKey?: string | null }) => void,
    postLength?: string,
    draftId?: string
  ) => {
    // getToken is passed directly to the service method here, as it handles its own token needs internally for polling
    return agentServiceInstance.generateContent(input, onStatusUpdate, postLength, draftId);
  }, [getToken]); // getToken from useAuth is stable

  return {
    getAgents,
    getAgentById,
    createAgent,
    updateAgent,
    deleteAgent,
    generateAgentContent, // Exposing the wrapped generateContent
  };
}

 