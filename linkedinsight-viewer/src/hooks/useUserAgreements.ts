import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@clerk/clerk-react';
import apiClient from '../services/apiClient';

interface TermsStatus {
  has_accepted: boolean;
  accepted_at: string | null;
  terms_version: string | null;
}

interface AcceptTermsPayload {
  terms_version?: string;
  accepted?: boolean;
}

interface AcceptTermsResponse {
  success: boolean;
  accepted_at: string;
  terms_version: string;
}

export function useTermsStatus() {
  const { isLoaded, isSignedIn } = useAuth();
  
  return useQuery<TermsStatus>({
    queryKey: ['termsStatus'],
    queryFn: async () => {
      const response = await apiClient.get<{ data: TermsStatus }>('/api/user/terms-status');
      console.log('Raw API Response:', response);
      console.log('Response type:', typeof response);
      console.log('Response has data property:', response && typeof response === 'object' && 'data' in response);
      
      // The API returns { data: TermsStatus }, so extract the data
      if (response && typeof response === 'object' && 'data' in response) {
        console.log('Returning response.data:', (response as any).data);
        return (response as any).data;
      }
      
      // Fallback, but this shouldn't happen based on the API
      console.warn('Unexpected response format, returning as is:', response);
      return response as any;
    },
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache
    retry: 1, // Reduce retries for faster failure
    enabled: isLoaded && isSignedIn, // Only run when Clerk is loaded and user is signed in
    refetchOnWindowFocus: true, // Refetch on window focus to ensure fresh data
  });
}

export function useAcceptTerms() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (payload: AcceptTermsPayload = {}) => {
      const response = await apiClient.post<{ data: AcceptTermsResponse }>('/api/user/accept-terms', {
        terms_version: payload.terms_version || '1.0',
        accepted: payload.accepted !== false
      });
      return response?.data;
    },
    onSuccess: (data) => {
      // Immediately update the cache with the new terms status
      queryClient.setQueryData(['termsStatus'], {
        has_accepted: true,
        accepted_at: data?.accepted_at,
        terms_version: data?.terms_version
      });
    },
  });
}