import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import sessionManager from '../services/SimpleSessionManager';
import { AppSession, SessionUpdate } from '../types/session';

interface UseSessionReturn {
  // Core state
  session: AppSession | null;
  sessionId: string;
  loading: boolean;
  error: Error | null;
  
  // Computed properties
  isTranscriptFlow: boolean;
  isNotesFlow: boolean;
  isInterviewFlow: boolean;
  currentStep: AppSession['step'] | null;
  posts: Record<string, string>;
  hasGeneratedContent: boolean;
  
  // Actions
  updateSession: (updates: SessionUpdate) => Promise<void>;
  navigate: (to: string, updates?: SessionUpdate) => void;
  goBack: () => void;
  refreshSession: () => Promise<void>;
}

/**
 * Hook for accessing and managing the current session
 * Provides a simple interface to the SimpleSessionManager
 */
export function useSession(): UseSessionReturn {
  const [session, setSession] = useState<AppSession | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const navigate = useNavigate();
  const initRef = useRef(false);

  // Initialize session on mount
  useEffect(() => {
    if (initRef.current) return;
    initRef.current = true;

    const initSession = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get or create session
        const id = await sessionManager.getOrCreate();
        setSessionId(id);
        
        // Load full session data
        const sessionData = await sessionManager.load(id);
        if (sessionData) {
          setSession(sessionData);
        } else {
          throw new Error('Failed to load session');
        }
      } catch (err) {
        console.error('Session initialization error:', err);
        setError(err instanceof Error ? err : new Error('Failed to initialize session'));
      } finally {
        setLoading(false);
      }
    };

    initSession();
  }, []);

  // Update session data
  const updateSession = useCallback(async (updates: SessionUpdate) => {
    if (!sessionId) {
      console.warn('Cannot update session: no session ID');
      return;
    }

    try {
      const updated = await sessionManager.update(sessionId, updates);
      if (updated) {
        setSession(updated);
      }
    } catch (err) {
      console.error('Failed to update session:', err);
      setError(err instanceof Error ? err : new Error('Failed to update session'));
    }
  }, [sessionId]);

  // Navigate with session preservation
  const navigateWithSession = useCallback((to: string, updates?: SessionUpdate) => {
    if (!sessionId) {
      console.warn('Cannot navigate: no session ID');
      return;
    }

    // Use the session manager's navigate method
    sessionManager.navigate(sessionId, to, updates);
  }, [sessionId]);

  // Go back to previous page in session history
  const goBack = useCallback(() => {
    if (!session) return;
    
    const previousPath = sessionManager.getPreviousPath(session);
    if (previousPath) {
      navigateWithSession(previousPath);
    } else {
      // Fallback to browser back
      navigate(-1);
    }
  }, [session, navigateWithSession, navigate]);

  // Refresh session from storage
  const refreshSession = useCallback(async () => {
    if (!sessionId) return;
    
    try {
      const fresh = await sessionManager.load(sessionId);
      if (fresh) {
        setSession(fresh);
      }
    } catch (err) {
      console.error('Failed to refresh session:', err);
    }
  }, [sessionId]);

  // Computed properties
  const isTranscriptFlow = session?.flow === 'transcript';
  const isNotesFlow = session?.flow === 'notes';
  const isInterviewFlow = session?.flow === 'interview';
  const currentStep = session?.step || null;
  const posts = session?.posts || {};
  const hasGeneratedContent = !!(session?.hasGenerated || Object.keys(posts).length > 0);

  return {
    // Core state
    session,
    sessionId,
    loading,
    error,
    
    // Computed properties
    isTranscriptFlow,
    isNotesFlow,
    isInterviewFlow,
    currentStep,
    posts,
    hasGeneratedContent,
    
    // Actions
    updateSession,
    navigate: navigateWithSession,
    goBack,
    refreshSession
  };
}

/**
 * Hook for accessing session data without management capabilities
 * Useful for components that only need to read session data
 */
export function useSessionData() {
  const { session, loading, error } = useSession();
  
  return {
    session,
    loading,
    error,
    // Convenience getters
    brief: session?.brief,
    posts: session?.posts || {},
    explanations: session?.explanations || {},
    breadcrumbs: session?.breadcrumbs || [],
    flow: session?.flow,
    step: session?.step
  };
}