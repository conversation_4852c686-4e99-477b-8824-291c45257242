/**
 * Centralized query keys factory for TanStack Query
 * Prevents key conflicts and enables hierarchical cache invalidation
 */

export const queryKeys = {
  all: ['linkedinsight'] as const,
  
  // Transcript related queries
  transcripts: {
    all: () => [...queryKeys.all, 'transcripts'] as const,
    list: () => [...queryKeys.transcripts.all(), 'list'] as const,
    detail: (id: string) => [...queryKeys.transcripts.all(), 'detail', id] as const,
    ideas: (id: string) => [...queryKeys.transcripts.detail(id), 'ideas'] as const,
    generatedIdeas: (id: string) => [...queryKeys.transcripts.detail(id), 'generated-ideas'] as const,
  },
  
  // Content strategy queries
  contentStrategy: {
    all: () => [...queryKeys.all, 'content-strategy'] as const,
    current: () => [...queryKeys.contentStrategy.all(), 'current'] as const,
  },
  
  // Content preferences queries
  contentPreferences: {
    all: () => [...queryKeys.all, 'content-preferences'] as const,
    current: () => [...queryKeys.contentPreferences.all(), 'current'] as const,
    options: () => [...queryKeys.contentPreferences.all(), 'options'] as const,
  },
  
  // Drafts queries
  drafts: {
    all: () => [...queryKeys.all, 'drafts'] as const,
    list: () => [...queryKeys.drafts.all(), 'list'] as const,
    detail: (id: string) => [...queryKeys.drafts.all(), 'detail', id] as const,
  },
  
  // Content sessions queries
  contentSessions: {
    all: () => [...queryKeys.all, 'content-sessions'] as const,
    detail: (id: string) => [...queryKeys.contentSessions.all(), id] as const,
  },
  
  // Chat history queries
  chatHistory: {
    all: () => [...queryKeys.all, 'chat-history'] as const,
    byDraft: (draftId: string) => [...queryKeys.chatHistory.all(), 'draft', draftId] as const,
  },
} as const;

// Type helper to extract query key types
export type QueryKeys = typeof queryKeys;