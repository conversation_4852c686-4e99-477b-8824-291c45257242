/**
 * Shared utilities for React Query hooks
 * These utilities extract common patterns to reduce code duplication
 * while maintaining the exact same behavior
 */

import { ErrorService } from '../utils/errorHandling';
import { useToast } from '../components/ui/toast-provider';

type ToastHook = ReturnType<typeof useToast>;

/**
 * Standard mock delay used across all mock operations
 */
export const mockDelay = (ms: number = 300): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Validate API response is a valid object
 */
export const isValidResponse = <T>(response: unknown): response is T => {
  return response !== null && response !== undefined && typeof response === 'object';
};

/**
 * Format validation errors from API 422 responses
 */
export const formatValidationError = (detail: any): string => {
  if (Array.isArray(detail)) {
    return "Validation error: " + 
      detail
        .map((d: { loc: string[]; msg: string }) => 
          `Field '${d.loc.join('.')}' - ${d.msg}`
        )
        .join('; ');
  }
  return `Validation error: ${JSON.stringify(detail)}`;
};

/**
 * Extract error message from API error response
 */
export const extractApiErrorMessage = (apiError: any, fallbackPrefix: string): string => {
  if (apiError.status === 422 && apiError.body?.detail) {
    return formatValidationError(apiError.body.detail);
  }
  
  // Handle different error message locations in API response
  if (apiError.body?.detail && typeof apiError.body.detail === 'string') {
    return apiError.body.detail;
  }
  
  if (apiError.body?.message) {
    return apiError.body.message;
  }
  
  return `${fallbackPrefix}: ${apiError.statusText || apiError.message || 'Unknown error'}`;
};

/**
 * Create a standardized mutation error handler
 * Maintains the exact same error handling behavior as original implementations
 */
export const createMutationErrorHandler = (
  toast: ToastHook,
  context: string,
  consolePrefix: string
) => (err: any) => {
  let errorMessage = 'An unknown error occurred';
  
  if (err.apiError) {
    errorMessage = extractApiErrorMessage(err.apiError, context);
  } else if (err.message) {
    errorMessage = `${context}: ${err.message}`;
  }
  
  toast.error(errorMessage);
  console.error(`${consolePrefix}:`, err.apiError || err);
  
  // Re-throw with formatted message for the component to handle
  throw new Error(errorMessage);
};

/**
 * Common query configuration presets
 */
export const queryPresets = {
  // For user-specific data that changes occasionally
  userContent: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount: number, error: any) => {
      // Don't retry on 404s (resource doesn't exist)
      if (error?.apiError?.status === 404) return false;
      return failureCount < 2;
    }
  },
  
  // For static options that rarely change
  staticOptions: {
    staleTime: Infinity,
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    retry: false
  },
  
  // For frequently changing data
  dynamic: {
    staleTime: 0, // Always refetch
    gcTime: 5 * 60 * 1000, // 5 minutes
  }
};