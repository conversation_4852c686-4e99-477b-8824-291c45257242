/**
 * Centralized route configuration for the application
 * Single source of truth for routes and their breadcrumb metadata
 */

// Route paths as constants
export const routes = {
  home: '/home',
  landing: '/',
  draftLibrary: '/draft-library',
  contentAgent: '/content-agent',
  contentStrategy: '/content-strategy-form',
  contentPreferences: '/content-preferences',
  userPreferences: '/user-preferences',
  interview: '/interview',
  interactiveInterview: '/interactive-interview',
  designSystem: '/design-system',
  transcriptInput: '/transcript-input',
  ideas: '/ideas',
  notesInput: '/notes-input',
  quickStart: '/quick-start',
  editor: '/editor',
  onboardingDemo: '/onboarding-demo',
  // auth routes
  signIn: '/sign-in',
  signUp: '/sign-up',
} as const;

export interface RouteConfig {
  path: string;
  breadcrumbLabel: string;
  showBreadcrumb?: boolean;
  isExactMatch?: boolean; // Use exact match instead of startsWith
}

export const routeConfigs: RouteConfig[] = [
  {
    path: '/home',
    breadcrumbLabel: 'Home',
    showBreadcrumb: false,
    isExactMatch: true,
  },
  {
    path: '/content-agent',
    breadcrumbLabel: 'Content Agent',
    showBreadcrumb: true,
  },
  {
    path: '/draft-library',
    breadcrumbLabel: 'Draft Library',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/content-strategy',
    breadcrumbLabel: 'Content Strategy',
    showBreadcrumb: true,
  },
  {
    path: '/content-strategy-form',
    breadcrumbLabel: 'Content Strategy',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/interactive-interview',
    breadcrumbLabel: 'Interactive Interview',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/transcript-input',
    breadcrumbLabel: 'Transcript',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/ideas',
    breadcrumbLabel: 'Ideas',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/notes-input',
    breadcrumbLabel: 'Notes Input',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/user-preferences',
    breadcrumbLabel: 'Your Style',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/content-preferences',
    breadcrumbLabel: 'Content Preferences',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/quick-start',
    breadcrumbLabel: 'Quick Start Guide',
    showBreadcrumb: true,
    isExactMatch: true,
  },
  {
    path: '/editor',
    breadcrumbLabel: 'Post Editor',
    showBreadcrumb: true,
    isExactMatch: true,
  },
];

/**
 * Helper function to find a route configuration for a given path
 */
export function findRouteConfig(pathname: string): RouteConfig | undefined {
  // Sort routes by path length in descending order to match more specific paths first
  const sortedConfigs = [...routeConfigs].sort((a, b) => b.path.length - a.path.length);
  
  return sortedConfigs.find(config => {
    if (config.isExactMatch) {
      return pathname === config.path;
    }
    return pathname.startsWith(config.path);
  });
}

/**
 * Helper function to determine if breadcrumbs should be shown for a path
 */
export function shouldShowBreadcrumbs(pathname: string, activeTab?: string): boolean {
  // Show breadcrumbs if we have an activeTab
  if (activeTab) return true;
  
  // Otherwise check route configuration
  const routeConfig = findRouteConfig(pathname);
  return routeConfig?.showBreadcrumb ?? false;
}