import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockService } from './MockService';
import { MOCK_IDEAS, MOCK_LINKEDIN_POSTS, MOCK_POST_EXPLANATION } from './mockData';

describe('MockService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getTranscript', () => {
    it('should return mock transcript with correct ID', async () => {
      const result = await mockService.getTranscript('test-123');
      
      expect(result.id).toBe('test-123');
      expect(result.transcript_content).toBeTruthy();
      expect(result.ideas).toEqual(MOCK_IDEAS);
      expect(result.status).toBe('completed');
    });

    it('should respect custom delay', async () => {
      const start = Date.now();
      await mockService.getTranscript('test-123', { delay: 100 });
      const duration = Date.now() - start;
      
      expect(duration).toBeGreaterThanOrEqual(100);
    });

    it('should throw error when requested', async () => {
      await expect(
        mockService.getTranscript('test-123', { throwError: true, errorType: 'notFound' })
      ).rejects.toThrow('Resource not found');
    });
  });

  describe('generateContent', () => {
    it('should generate content with status updates', async () => {
      const statusUpdates: any[] = [];
      const onStatusUpdate = vi.fn((status) => statusUpdates.push(status));
      
      const result = await mockService.generateContent(
        'Test brief',
        onStatusUpdate,
        { customData: { draftId: 'draft_a' } }
      );
      
      // Check status updates were called (4 processing + 1 completed)
      expect(onStatusUpdate).toHaveBeenCalledTimes(5);
      expect(statusUpdates.some(s => s.overallStatus === 'processing')).toBe(true);
      expect(statusUpdates.some(s => s.overallStatus === 'completed')).toBe(true);
      
      // Check result
      expect(result.conversation_id).toMatch(/^mock-\d+$/);
      expect(result.generated_post).toContain(MOCK_LINKEDIN_POSTS.draft_a);
      expect(result.explanation).toBe(MOCK_POST_EXPLANATION);
    });

    it('should return draft_b when specified', async () => {
      const result = await mockService.generateContent(
        'Test brief',
        undefined,
        { customData: { draftId: 'draft_b' } }
      );
      
      expect(result.generated_post).toContain(MOCK_LINKEDIN_POSTS.draft_b);
    });

    it('should format post based on length', async () => {
      const shortResult = await mockService.generateContent(
        'Test brief',
        undefined,
        { customData: { postLength: 'short' } }
      );
      
      const longResult = await mockService.generateContent(
        'Test brief',
        undefined,
        { customData: { postLength: 'long' } }
      );
      
      // Short should be shorter than long
      expect(shortResult.generated_post.length).toBeLessThan(longResult.generated_post.length);
      expect(longResult.generated_post).toContain('P.S.');
    });

    it('should skip step simulation when simulateSteps is false', async () => {
      const start = Date.now();
      const result = await mockService.generateContent(
        'Test brief',
        undefined,
        { simulateSteps: false }
      );
      const duration = Date.now() - start;
      
      // Should be much faster than 1000ms when steps are skipped
      expect(duration).toBeLessThan(200);
      expect(result.conversation_id).toMatch(/^mock-\d+$/);
    });
  });

  describe('markIdeaGenerated', () => {
    it('should complete without errors', async () => {
      // Just verify the function runs without throwing
      await expect(
        mockService.markIdeaGenerated('idea-1', 'transcript-1')
      ).resolves.not.toThrow();
    });
  });

  describe('processTranscript', () => {
    it('should return mock transcript ID', async () => {
      const formData = new FormData();
      formData.append('audio_file', new Blob(['test']), 'test.txt');
      
      const result = await mockService.processTranscript(formData);
      
      expect(result.transcript_id).toBe('mock-transcript-1');
    });
  });

  describe('createContentSession', () => {
    it('should create mock session with unique ID', async () => {
      const sessionData = {
        brief: 'Test brief',
        postLength: 'medium',
        ideaId: 'idea-1',
        transcriptId: 'transcript-1'
      };
      
      const result = await mockService.createContentSession(sessionData);
      
      expect(result.sessionId).toMatch(/^mock-session-\d+$/);
      expect(result.brief).toBe('Test brief');
      expect(result.postLength).toBe('medium');
      expect(result.status).toBe('draft');
    });

    it('should use default brief when empty', async () => {
      const result = await mockService.createContentSession({
        brief: '',
        postLength: 'medium'
      });
      
      expect(result.brief).toContain('Strategic Content Brief');
    });
  });

  describe('error handling', () => {
    it('should throw appropriate error types', async () => {
      await expect(
        mockService.getTranscript('test', { throwError: true, errorType: 'network' })
      ).rejects.toThrow('Network error');
      
      await expect(
        mockService.getTranscript('test', { throwError: true, errorType: 'timeout' })
      ).rejects.toThrow('Request timed out');
      
      await expect(
        mockService.getTranscript('test', { throwError: true, errorType: 'unauthorized' })
      ).rejects.toThrow('Authentication required');
    });
  });
});