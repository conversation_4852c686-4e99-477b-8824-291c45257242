import { ApiClient } from './apiClient';
import { Idea, Citation } from '../types/idea';
import { ErrorService } from '../utils/errorHandling';

export class ApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

// Always use relative paths with Vercel proxy
const API_BASE_URL = '/api';

/**
 * Common options for all API requests
 */
const defaultHeaders = {
    'Content-Type': 'application/json',
};


// Use existing API client
const apiClient = ApiClient.getInstance();

interface ProcessedIdeas {
    ideas: Idea[];
    transcript: string;
    transcript_id?: string;
}

// Updated interface to match API response and TranscriptLibrary expectations
interface TranscriptListItem {
    id: string;
    createdAt: string; // ISO date string
    snippet: string;
    explicitCount: number;
    implicitCount: number;
    project_id?: string; // Optional project ID for filtering
}

// Represents the full data for a single transcript
export interface TranscriptDetail {
    id: string;
    createdAt: string;
    transcript_content: string;
    ideas: Idea[];
    generated_ideas?: Idea[];
    user_email?: string;
}

/**
 * API service for transcript processing
 */
// TODO: Hash validation temporarily disabled for MVP
// This function is no longer used since backend validation is disabled
// async function calculateHash(message: string): Promise<string> { ... }

export const transcriptApi = {

    /**
     * Parse an uploaded file and extract its text content
     * Supports .docx, .txt, and other text files
     */
    parseFile: async (file: File): Promise<string> => {
        // Create a FormData object to send the file
        const formData = new FormData();
        formData.append('file', file);

        // Make a POST request to the file parsing endpoint
        const response = await fetch(`${API_BASE_URL}/parse-file`, {
            method: 'POST',
            credentials: 'include',
            body: formData,
        });

        // Handle errors
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const errorMessage = errorData.error || `Failed to parse file: ${response.statusText}`;
            throw new ApiError(errorMessage, response.status);
        }

        // Return the extracted text
        const data = await response.json();
        return data.text;
    },

    /**
     * Convert markdown content to a .docx file and trigger a download
     * @param markdown The markdown content to convert
     * @param filename The name for the downloaded file (without extension)
     */
    convertToDocx: async (markdown: string, filename: string = 'document'): Promise<void> => {
        try {
            // Ensure filename doesn't have .docx extension (we'll add it on the server)
            const cleanFilename = filename.endsWith('.docx')
                ? filename.substring(0, filename.length - 5)
                : filename;

            // Create the request payload
            const payload = {
                markdown,
                filename: cleanFilename
            };

            // Make a POST request to the conversion endpoint
            // Use fetch directly to handle the blob response
            const response = await fetch(`${API_BASE_URL}/convert-to-docx`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });

            // Handle errors
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.error || `Failed to convert to DOCX: ${response.statusText}`;
                throw new ApiError(errorMessage, response.status);
            }

            // Get the blob from the response
            const blob = await response.blob();

            // Create a download link and trigger the download
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${cleanFilename}.docx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the URL object
            window.URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            ErrorService.logError(error, 'Convert to DOCX');
            throw error;
        }
    },

    /**
     * Extract ideas from a transcript - using non-blocking pattern to avoid timeouts
     * This will keep the user in the loading state until results are ready
     * Includes automatic retry logic for transcript mismatch errors
     */
    extractIdeas: async (transcript: string, retryAttempt: number = 0): Promise<ProcessedIdeas> => {
        // This function supports automatic retries for transcript mismatch errors
        // The retryAttempt parameter tracks the current retry count
        // Proceed with normal processing

        // Start the extraction process
        const requestPayload = { transcript };
        const response = await apiClient.post(
            '/api/ideas/extract',
            requestPayload
        ) as ProcessedIdeas & {job_id?: string, polling_url?: string, status?: string};

        // Log that we received the initial response
        console.log('Received initial response from ideas extraction:', response);

        // If we don't have a job ID, just return what we got
        if (!response.job_id) {
            console.log('No job_id in response, returning as is');
            return {
                ...response,
                ideas: response.ideas || [],
                transcript: transcript
            } as unknown as ProcessedIdeas;
        }

        // We have a job ID, wait for the results
        console.log(`Ideas extraction started with job ID: ${response.job_id}, polling for results... (retry attempt: ${retryAttempt})`);

        // If this is a retry, log it for monitoring
        if (retryAttempt > 0) {
            console.warn(`This is retry attempt ${retryAttempt} for transcript processing`);
            try {
                // @ts-ignore - Sentry might be available globally
                if (window.Sentry) {
                    // @ts-ignore - Access Sentry methods
                    window.Sentry.addBreadcrumb({
                        category: 'transcript',
                        message: `Starting retry attempt ${retryAttempt} for transcript processing`,
                        level: 'warning'
                    });
                }
            } catch (e) {
                // Ignore Sentry errors
            }
        }

        // Poll for results and block until we get them
        // This will keep the progress bar showing while we wait
        const finalResult = await transcriptApi.pollForIdeas(response.job_id, transcript, retryAttempt);

        if (!finalResult) {
            // If polling failed, return the initial empty arrays
            console.warn('Polling failed, returning initial empty response');
            return {
                ideas: [],
                transcript: transcript
            };
        }

        // Return the final result
        console.log('Returning final extraction result:', finalResult);
        return finalResult;
    },

    /**
     * Poll for ideas extraction results
     * Includes handling for transcript mismatch errors with automatic retry
     */
    pollForIdeas: async (jobId: string, transcript: string, retryAttempt: number = 0): Promise<ProcessedIdeas | null> => {
        try {
            const MAX_ATTEMPTS = 150;  // 300 seconds (5 minutes) total (2s intervals)
            const MAX_RETRIES = 2;    // Maximum number of retries for transcript mismatch errors

            for (let attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
                // Wait 2 seconds between attempts
                await new Promise(resolve => setTimeout(resolve, 2000));

                try {
                    // TODO: Hash validation temporarily disabled for MVP - no longer passing transcript_hash
                    // Check status - use a relative path for consistent proxy handling
                    // The API client already prepends the API_BASE_URL
                    const pollingPath = `/api/ideas/status/${jobId}`;
                    console.log(`Polling ${pollingPath} (attempt ${attempt+1}/${MAX_ATTEMPTS})`);

                    const response = await apiClient.get<ProcessedIdeas | { status: string; error?: string }>(pollingPath);

                    if (!response) {
                        console.error('No response from API');
                        return null;
                    }

                    // If we have completed results, return them
                    if ('ideas' in response && Array.isArray(response.ideas)) {
                        // Make sure we also include the transcript in the response
                        return {
                            ...response,
                            transcript: transcript,
                            transcript_id: (response as ProcessedIdeas).transcript_id // Ensure transcript_id is passed through
                        } as ProcessedIdeas;
                    }

                    // If job failed, log error and stop polling
                    if ('status' in response && response.status === 'failed') {
                        console.error('Ideas extraction failed:', response.error);
                        return null;
                    }

                    console.log(`Status: ${'status' in response ? response.status : 'unknown'} - waiting for completion...`);
                } catch (pollError: any) {
                    // Check if this is a transcript mismatch error
                    if (pollError?.response?.data?.error?.includes('Transcript mismatch') && retryAttempt < MAX_RETRIES) {
                        console.warn(`Transcript mismatch detected (attempt ${retryAttempt+1}/${MAX_RETRIES+1}), retrying with a new job...`);

                        // Send to Sentry if available
                        try {
                            // @ts-ignore - Sentry might be available globally
                            if (window.Sentry) {
                                // @ts-ignore - Access Sentry methods
                                window.Sentry.captureMessage(`Transcript mismatch retry ${retryAttempt+1}/${MAX_RETRIES+1}`, 'warning');
                            }
                        } catch (e) {
                            // Ignore errors with Sentry
                            console.warn('Failed to log to Sentry:', e);
                        }

                        // Start a new extraction job with the same transcript
                        return transcriptApi.extractIdeas(transcript, retryAttempt + 1);
                    }

                    console.warn(`Polling attempt ${attempt+1} failed:`, pollError);
                    // Continue polling despite other request failures
                }
            }

            console.warn(`Ideas extraction polling timed out after ${MAX_ATTEMPTS * 2} seconds (${MAX_ATTEMPTS} attempts)`);
            return null;
        } catch (error) {
            console.error('Error in polling process:', error);
            return null;
        }
    },

    /**
     * Get status or results for an extraction job
     */
    getIdeasExtractionStatus: async (jobId: string): Promise<ProcessedIdeas | {status: string, message?: string, error?: string}> => {
        const response = await apiClient.get<ProcessedIdeas | {status: string, message?: string, error?: string}>(`/api/ideas/status/${jobId}`);
        if (!response) {
            throw new Error('Failed to get ideas extraction status');
        }
        return response;
    },

    /**
     * Update a specific idea
     */
    updateIdea: async (idea: Idea) => {
        return apiClient.put('/update-idea', idea);
    },

    /**
     * Update a specific idea within a transcript
     */
    updateTranscriptIdea: async (transcriptId: string, ideaId: string, updateData: Partial<Idea>) => {
        if (!transcriptId || !ideaId) {
            throw new Error("Transcript ID and Idea ID are required.");
        }
        return apiClient.put(`/api/transcripts/${transcriptId}/ideas/${ideaId}/`, updateData);
    },

    /**
     * Generate a brief for an idea with streaming updates
     */
    generateBrief: (params: {
        transcript: string;
        idea_id: string;
        idea: string;
        outline: {
            title: string;
            sections: string[];
        };
    }, onEvent: (event: {
        type: 'content_block_delta';
        delta: {
            type: 'text_delta';
            text: string;
        };
    }) => void, onError?: (error: Error) => void) => {
        console.log('Starting brief generation with params:', params);

        let abortController = new AbortController();

        // Make a single streaming POST request
        fetch(`${API_BASE_URL}/generate-brief`, {
            method: 'POST',
            headers: defaultHeaders,
            body: JSON.stringify(params),
            signal: abortController.signal
        }).then(async response => {
            if (!response.ok) {
                const errorBody = await response.json().catch(() => ({}));
                const errorMessage = errorBody.error || `API Error: ${response.statusText}`;
                const error = new ApiError(errorMessage, response.status);

                console.error('Brief generation API error:', error);
                if (onError) onError(error);
                throw error;
            }

            const reader = response.body!.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            try {
                let done = false;
                while (!done) {
                    const result = await reader.read();
                    done = result.done || false;
                    if (done) break;
                    const value = result.value;

                    // Decode the chunk and add to buffer
                    buffer += decoder.decode(value, { stream: true });

                    // Process complete SSE messages
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || ''; // Keep incomplete line in buffer

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const event = JSON.parse(line.slice(6));
                                console.log('Received SSE event:', event);
                                onEvent(event);
                            } catch (error) {
                                console.error('Failed to parse SSE message:', error);
                                const parseError = new Error('Failed to parse server response');
                                if (onError) onError(parseError);
                                throw parseError;
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Stream reading error:', error);
                if (onError && error instanceof Error) onError(error);
                throw error;
            }
        }).catch(error => {
            console.error('Brief generation request failed:', error);
            if (onError && error instanceof Error) onError(error);
            throw error;
        });

        // Return cleanup function
        return () => {
            console.log('Aborting brief generation request');
            abortController.abort();
        };
    },

    /**
     * Fetch the list of transcripts for the logged-in user.
     * @param project_id Optional project ID to filter transcripts by project
     */
    listTranscripts: async (project_id?: string): Promise<TranscriptListItem[]> => {
        // Use URLSearchParams for proper query parameter handling
        const url = new URL(`${window.location.origin}/api/transcripts`);

        // Add project_id as a query parameter if provided
        if (project_id) {
            url.searchParams.append('project_id', project_id);
            console.log(`Fetching transcripts with project_id: ${project_id}`);
            console.log(`Full URL: ${url.toString()}`);
        } else {
            console.log('Fetching all transcripts (no project_id filter)');
        }

        // Use the relative path for the API client
        const relativePath = url.pathname.replace('/api', '') + url.search;
        return apiClient.get(`/api${relativePath}`) as Promise<TranscriptListItem[]>;
    },

    /**
     * Fetch the full details for a specific transcript.
     */
    getTranscriptDetail: async (transcriptId: string): Promise<TranscriptDetail> => {
        if (!transcriptId) {
            console.error("[DEBUG] getTranscriptDetail called with empty transcriptId");
            throw new Error("Transcript ID is required.");
        }
        return apiClient.get(`/api/transcripts/${transcriptId}/`) as Promise<TranscriptDetail>;
    },

    /**
     * Fetch all generated briefs for the current user
     */
    getGeneratedBriefs: async () => {
        return apiClient.get('/briefs/');
    },

    /**
     * Fetch a specific generated brief by ID
     */
    getGeneratedBrief: async (briefId: string) => {
        if (!briefId) {
            throw new Error("Brief ID is required.");
        }
        return apiClient.get(`/briefs/${briefId}/`);
    },

    // TODO: Hash validation temporarily disabled for MVP
    // calculateTranscriptHash: async (transcript: string): Promise<string> => {
    //     return calculateHash(transcript);
    // },
};