import { API_CONFIG } from '../config';
import type { Clerk } from '@clerk/types'; // Import Clerk type from @clerk/types
import type { ApiError, ApiErrorData, RequestBody, ApiResponse, RequestLogData, BlobInfo } from '../types/api';

declare global {
  interface Window {
    Clerk?: Clerk;
  }
}

interface ApiClientOptions {
  baseUrl?: string;
  defaultHeaders?: Record<string, string>;
}

export class ApiClient {
  private static instance: ApiClient;
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  /**
   * Private constructor to enforce singleton pattern.
   * @param {ApiClientOptions} [options] - Optional configuration for the client.
   */
  private constructor(options?: ApiClientOptions) {
    this.baseUrl = options?.baseUrl || (API_CONFIG && API_CONFIG.BASE_URL) || 'http://localhost:3001'; // Corrected fallback
    this.defaultHeaders = options?.defaultHeaders || {}; // MODIFIED: No default Content-Type
  }

  /**
   * Gets the singleton instance of the ApiClient.
   * @param {ApiClientOptions} [options] - Optional configuration, applied only on first instantiation.
   * @returns {ApiClient} The singleton ApiClient instance.
   */
  public static getInstance(options?: ApiClientOptions): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient(options);
    }
    return ApiClient.instance;
  }

  /**
   * Constructs the full URL for an API request.
   * @param {string} path - The API endpoint path (e.g., '/users', 'data/items').
   * @returns {string} The fully qualified URL.
   */
  private buildUrl(path: string): string {
    // Remove leading slash from path if it exists
    const normalizedPath = path.startsWith('/') ? path.substring(1) : path;
    
    // Ensure baseUrl ends with a slash
    const normalizedBaseUrl = this.baseUrl.endsWith('/') 
      ? this.baseUrl 
      : `${this.baseUrl}/`;
    
    return `${normalizedBaseUrl}${normalizedPath}`;
  }

  /**
   * Retrieves the Clerk authentication token if available.
   * Logs warnings if Clerk is not available or session is missing.
   * @private
   * @returns {Promise<string | null>} The token or null if not available/error occurs.
   */
  private async _getClerkToken(): Promise<string | null> {
    if (typeof window !== 'undefined' && window.Clerk && window.Clerk.session) {
      try {
        // Request token with email template to include email in JWT claims
        const token = await window.Clerk.session.getToken({ template: 'api_email' });
        return token;
      } catch (error) {
        console.error("Error retrieving Clerk token:", error);
        // Log more details if helpful, e.g., if session exists but getToken fails
        if (window.Clerk && !window.Clerk.session) {
          console.warn("Clerk session not available at token retrieval time.");
        }
      }
    } else {
        if (typeof window !== 'undefined') {
            if (!window.Clerk) {
                console.warn("Clerk instance (window.Clerk) not available.");
            } else if (!window.Clerk.loaded) {
                console.warn("Clerk not loaded yet (window.Clerk.loaded is false).");
            } else if (!window.Clerk.session) {
                console.warn("Clerk session not available. User might be signed out or Clerk not fully initialized.");
            }
        }
    }
    return null;
  }

  /**
   * Builds the Headers object for a request, including default headers,
   * custom headers from RequestInit, and the Authorization token.
   * @private
   * @param {string} [explicitToken] - An explicit token to use, bypassing Clerk token retrieval.
   * @param {HeadersInit} [customHeadersInit] - Custom headers from RequestInit.
   * @returns {Promise<Headers>} The constructed Headers object.
   */
  private async buildHeaders(
    explicitToken?: string,
    customHeadersInit?: HeadersInit
  ): Promise<Headers> {
    const headers = new Headers(this.defaultHeaders); // Start with class defaults

    // Merge custom headers from RequestInit if provided
    if (customHeadersInit) {
      new Headers(customHeadersInit).forEach((value, key) => {
        headers.set(key, value);
      });
    }

    const tokenToUse = explicitToken ?? await this._getClerkToken();
    if (tokenToUse) {
      headers.set('Authorization', `Bearer ${tokenToUse}`);
    }
    return headers;
  }

  /**
   * Logs API request details during development.
   * @private
   * @param {string} method - The HTTP method (e.g., 'GET', 'POST').
   * @param {string} url - The request URL.
   * @param {unknown} [data] - Optional request data/payload.
   * @param {Headers} [headers] - Optional headers for POST/PUT logging.
   */
  private logRequest(method: string, url: string, data?: unknown, headers?: Headers): void {
    if (import.meta.env.DEV) {
      console.groupCollapsed(`API Request: ${method} ${url}`);
      if (data) {
        console.log('Request Data/Options:', data);
      }
      if (headers) {
        const plainHeaders: Record<string, string> = {};
        headers.forEach((value, key) => { plainHeaders[key] = value; });
        console.log('Request Headers:', plainHeaders);
      }
      console.groupEnd();
    }
  }

  /**
   * Logs API response details during development.
   * @private
   * @param {string} method - The HTTP method.
   * @param {string} url - The request URL.
   * @param {unknown} responseData - The response data.
   */
  private logResponse(method: string, url: string, responseData: unknown): void {
    if (import.meta.env.DEV) {
      console.groupCollapsed(`API Response: ${method} ${url}`);
      console.log('Response Data:', responseData);
      console.groupEnd();
    }
  }

  /**
   * Logs API error details during development.
   * @private
   * @param {string} method - The HTTP method.
   * @param {string} url - The request URL.
   * @param {unknown} error - The error object.
   */
  private logError(method: string, url: string, error: unknown): void {
    // In a real app, consider sending this to a logging service (e.g., Sentry)
    // especially for production errors, but also for dev if a centralized log is preferred.
    if (import.meta.env.DEV) {
      console.groupCollapsed(`API Error: ${method} ${url}`);
      if (error && typeof error === 'object' && 'apiError' in error) {
        console.error('API Error Details:', (error as ApiError).apiError);
      } else {
        console.error('Error:', error);
      }
      if (error instanceof Error && error.stack) {
        console.log('Stack Trace:', error.stack);
      }
      console.groupEnd();
    } else {
      // Basic logging for production if not sending to a service
      // console.error(`API Error: ${method} ${url}`, error.message);
    }
  }

  /**
   * Handles the raw Response object from a fetch call.
   * Parses response based on Content-Type, handles errors, and empty responses.
   * @private
   * @param {Response} response - The Response object from fetch.
   * @returns {Promise<ApiResponse<T>>} The parsed response data or null for empty responses.
   * @throws Will throw an error for non-ok HTTP statuses, with error details in `error.apiError`.
   */
  private async handleResponse<T = unknown>(response: Response): Promise<ApiResponse<T>> {
    if (!response.ok) {
      const errorData: ApiErrorData = {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
      };
      try {
        errorData.body = await response.clone().json();
      } catch (e) {
        try {
          errorData.body = await response.clone().text();
        } catch (e2) { /* body remains undefined */ }
      }
      const error = new Error(`API Error: ${response.status} ${response.statusText}`) as ApiError;
      error.apiError = errorData;
      throw error;
    }

    const contentType = response.headers.get('Content-Type') || '';
    const contentLength = response.headers.get('Content-Length');

    if (response.status === 204 || contentLength === '0') {
      return null;
    }

    if (contentType.includes('application/json')) {
      return response.json() as Promise<T>;
    } else if (contentType.includes('text/')) {
      return response.text() as Promise<T>;
    } else if (response.body) { 
      // Fallback for other types with a body, could be blob(), arrayBuffer() depending on needs
      // Defaulting to text() if not JSON, as a general measure.
      return response.text() as Promise<T>; 
    }
    return null; // Should ideally not be reached if status is ok and not 204/empty
  }

  /**
   * Prepares the request body and sets appropriate 'Content-Type' header if not already set for objects.
   * @private
   * @param {RequestBody} data - The data to be sent in the request body.
   * @param {Headers} headers - The Headers object for the request (will be mutated for Content-Type).
   * @returns {BodyInit | null} The prepared body, or null if data is undefined.
   */
  private _prepareRequestBodyAndHeaders(data: RequestBody, headers: Headers): BodyInit | null {
    let body: BodyInit | null = null;
    if (data !== undefined) {
      if (data instanceof FormData || 
          typeof data === 'string' || 
          data instanceof URLSearchParams || 
          data instanceof Blob || 
          data instanceof ArrayBuffer
      ) {
        body = data;
        console.log('[apiClient._prepareRequestBodyAndHeaders] Data type: FormData/string/URLSearchParams/Blob/ArrayBuffer. Body set directly.', body);
      } else if (typeof data === 'object' && data !== null) {
        if (!headers.has('Content-Type')) {
          headers.set('Content-Type', 'application/json');
          console.log('[apiClient._prepareRequestBodyAndHeaders] Data type: object. Content-Type not set, defaulting to application/json.');
        } else {
          console.log(`[apiClient._prepareRequestBodyAndHeaders] Data type: object. Content-Type already set to: ${headers.get('Content-Type')}`);
        }
        
        if (headers.get('Content-Type')?.includes('application/json')) {
          body = JSON.stringify(data);
          console.log('[apiClient._prepareRequestBodyAndHeaders] Body stringified for application/json.', body);
        } else {
          body = String(data); 
          console.log('[apiClient._prepareRequestBodyAndHeaders] Body converted to string for non-JSON object content type.', body);
        }
      } else {
        body = String(data);
        console.log('[apiClient._prepareRequestBodyAndHeaders] Data type: primitive/other. Body converted to string.', body);
      }
    }
    return body;
  }

  /**
   * Performs a GET request.
   * @param {string} path - The API endpoint path.
   * @param {RequestInit} [options] - Optional fetch RequestInit options (headers here will be merged).
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<ApiResponse<T>>} The response data.
   */
  async get<T = unknown>(path: string, options?: RequestInit, explicitToken?: string): Promise<ApiResponse<T>> {
    const url = this.buildUrl(path);
    this.logRequest('GET', url, options); // Log with options as potential data for GET
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const finalHeaders = await this.buildHeaders(explicitToken, customHeadersInit);
      const response = await fetch(url, {
        method: 'GET',
        headers: finalHeaders,
        ...restOfOptions,
      });
      const data = await this.handleResponse<T>(response);
      this.logResponse('GET', url, data);
      return data;
    } catch (error) {
      this.logError('GET', url, error);
      throw error;
    }
  }

  /**
   * Performs a GET request and returns the response as a Blob.
   * Useful for downloading files.
   * @param {string} path - The API endpoint path.
   * @param {RequestInit} [options] - Optional fetch RequestInit options.
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<Blob>} A promise that resolves with the Blob data.
   * @throws Will throw an error for non-ok HTTP statuses.
   */
  async getBlob(path: string, options?: RequestInit, explicitToken?: string): Promise<Blob> {
    const url = this.buildUrl(path);
    this.logRequest('GET_BLOB', url, options); // Log with options
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const finalHeaders = await this.buildHeaders(explicitToken, customHeadersInit);
      const response = await fetch(url, {
        method: 'GET',
        headers: finalHeaders,
        ...restOfOptions,
      });

      if (!response.ok) {
        const errorData: ApiErrorData = {
          status: response.status,
          statusText: response.statusText,
          url: response.url,
        };
        try {
          errorData.body = await response.clone().json(); // Try to get JSON error body
        } catch (e) {
          try {
            errorData.body = await response.clone().text(); // Fallback to text error body
          } catch (e2) { /* body remains undefined */ }
        }
        const error = new Error(`API Error: ${response.status} ${response.statusText}`) as ApiError;
        error.apiError = errorData;
        this.logError('GET_BLOB', url, error);
        throw error;
      }
      
      const blob = await response.blob();
      const blobInfo: BlobInfo = { size: blob.size, type: blob.type };
      this.logResponse('GET_BLOB', url, { blobInfo });
      return blob;
    } catch (error) {
      this.logError('GET_BLOB', url, error);
      throw error;
    }
  }

  /**
   * Performs a POST request.
   * Handles various data types for the body and sets 'Content-Type' to 'application/json' for objects by default.
   * @param {string} path - The API endpoint path.
   * @param {RequestBody} data - The data to send in the request body.
   * @param {RequestInit} [options] - Optional fetch RequestInit options.
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<ApiResponse<T>>} The response data.
   */
  async post<T = unknown>(path: string, data: RequestBody, options?: RequestInit, explicitToken?: string): Promise<ApiResponse<T>> {
    const url = this.buildUrl(path);
    // this.logRequest('POST', url, data); // Logging will be done after headers are finalized
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const finalHeaders = await this.buildHeaders(explicitToken, customHeadersInit);
      const body = this._prepareRequestBodyAndHeaders(data, finalHeaders);

      const logData: RequestLogData = { data, optionsPassed: restOfOptions };
      this.logRequest('POST', url, logData, finalHeaders); // Log with final headers and body info

      const response = await fetch(url, {
        method: 'POST',
        headers: finalHeaders,
        body,
        ...restOfOptions,
      });
      const responseData = await this.handleResponse<T>(response);
      this.logResponse('POST', url, responseData);
      return responseData;
    } catch (error) {
      this.logError('POST', url, error);
      throw error;
    }
  }

  /**
   * Performs a PUT request.
   * Handles various data types for the body and sets 'Content-Type' to 'application/json' for objects by default.
   * @param {string} path - The API endpoint path.
   * @param {RequestBody} data - The data to send in the request body.
   * @param {RequestInit} [options] - Optional fetch RequestInit options.
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<ApiResponse<T>>} The response data.
   */
  async put<T = unknown>(path: string, data: RequestBody, options?: RequestInit, explicitToken?: string): Promise<ApiResponse<T>> {
    const url = this.buildUrl(path);
    // this.logRequest('PUT', url, data); // Logging will be done after headers are finalized
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const finalHeaders = await this.buildHeaders(explicitToken, customHeadersInit);
      const body = this._prepareRequestBodyAndHeaders(data, finalHeaders);

      const logData: RequestLogData = { data, optionsPassed: restOfOptions };
      this.logRequest('PUT', url, logData, finalHeaders); // Log with final headers and body info

      const response = await fetch(url, {
        method: 'PUT',
        headers: finalHeaders,
        body,
        ...restOfOptions,
      });
      const responseData = await this.handleResponse<T>(response);
      this.logResponse('PUT', url, responseData);
      return responseData;
    } catch (error) {
      this.logError('PUT', url, error);
      throw error;
    }
  }

  /**
   * Performs a PATCH request with a JSON payload.
   * @param {string} path - The API endpoint path.
   * @param {RequestBody} data - The data to send as JSON.
   * @param {RequestInit} [options] - Optional fetch RequestInit options.
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<ApiResponse<T>>} The response data.
   */
  async patch<T = unknown>(path: string, data: RequestBody, options?: RequestInit, explicitToken?: string): Promise<ApiResponse<T>> {
    const url = this.buildUrl(path);
    this.logRequest('PATCH', url, data);
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const customHeaders = new Headers(customHeadersInit);
      // Ensure JSON content type
      if (!customHeaders.has('Content-Type')) {
        customHeaders.set('Content-Type', 'application/json');
      }
      const finalHeaders = await this.buildHeaders(explicitToken, customHeaders);
      const response = await fetch(url, {
        method: 'PATCH',
        headers: finalHeaders,
        body: JSON.stringify(data),
        ...restOfOptions,
      });
      const responseData = await this.handleResponse<T>(response);
      this.logResponse('PATCH', url, responseData);
      return responseData;
    } catch (error) {
      this.logError('PATCH', url, error);
      throw error;
    }
  }

  /**
   * Performs a DELETE request.
   * @param {string} path - The API endpoint path.
   * @param {RequestInit} [options] - Optional fetch RequestInit options.
   * @param {string} [explicitToken] - Optional token to use instead of the automatically fetched Clerk token.
   * @returns {Promise<ApiResponse<T>>} The response data.
   */
  async delete<T = unknown>(path: string, options?: RequestInit, explicitToken?: string): Promise<ApiResponse<T>> {
    const url = this.buildUrl(path);
    this.logRequest('DELETE', url, options); // Log with options
    try {
      const { headers: customHeadersInit, ...restOfOptions } = options || {};
      const finalHeaders = await this.buildHeaders(explicitToken, customHeadersInit);
      const response = await fetch(url, {
        method: 'DELETE',
        headers: finalHeaders,
        ...restOfOptions,
      });
      const data = await this.handleResponse<T>(response);
      this.logResponse('DELETE', url, data);
      return data;
    } catch (error) {
      this.logError('DELETE', url, error);
      throw error;
    }
  }

  /**
   * Gets the base URL of the API.
   * Useful for constructing SSE/EventSource URLs that need the full URL.
   * @returns {string} The base API URL.
   */
  public getBaseUrl(): string {
    return this.baseUrl;
  }
}

// Export a singleton instance
const apiClient = ApiClient.getInstance();
export default apiClient; 