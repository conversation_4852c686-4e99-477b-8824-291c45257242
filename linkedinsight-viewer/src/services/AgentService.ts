/**
 * Agent Service for LinkedInsight
 *
 * This service handles communication with the backend agent API endpoint.
 * It provides a clean interface for components to generate content
 * using the agent functionality.
 */

import apiClient from './apiClient'; // ADDED
import { isMockModeEnabled } from '../utils/mockMode';
import { mockService } from './MockService';
import type { Agent, AgentData, AgentListResponse } from '../types/services';

// Define interfaces for the agent request and response
export interface AgentRequest {
  input_text: string;
  post_length?: string;
  draft_id?: string;
}

export interface AgentJobResponse {
  job_id: string;
  status: string;
  created_at: string;
  polling_url: string;
}

export interface AgentJobStatusResponse {
  job_id: string;
  status: string;
  created_at: string;
  updated_at?: string;
  completed_at?: string;
  duration?: number;
  result?: AgentResponse;
  error?: string;
  current_generation_step?: string;
}

export interface AgentResponse {
  conversation_id: string; // Backend still returns this, but we won't use it for MVP
  generated_post?: string;
  explanation?: string;
  questions?: string[];
  error?: string;
}

// Default polling configuration
const DEFAULT_POLLING_INTERVAL = 2000; // 2 seconds
const DEFAULT_MAX_POLLING_TIME = 300000; // 5 minutes

/**
 * Agent Service class for handling content generation
 */
export class AgentService {
  private activeJobs: Set<string> = new Set();
  private abortControllers: Map<string, AbortController> = new Map();
  private cleanupPromises: Map<string, Promise<void>> = new Map();

  /**
   * Cancel all active generation jobs
   */
  async cancelAllJobs(): Promise<void> {
    
    // Cancel jobs on server
    const cancelPromises = Array.from(this.activeJobs).map(async (jobId) => {
      try {
        await apiClient.delete(`/api/agent/cancel/${jobId}`);
      } catch (error) {
        // Failed to cancel job on server
      }
    });

    // Cancel client-side polling
    for (const [, controller] of this.abortControllers.entries()) {
      controller.abort();
    }

    // Wait for all server cancellations to complete
    await Promise.allSettled(cancelPromises);

    // Wait for any cleanup promises
    const cleanupPromises = Array.from(this.cleanupPromises.values());
    await Promise.allSettled(cleanupPromises);

    this.activeJobs.clear();
    this.abortControllers.clear();
    this.cleanupPromises.clear();
  }

  /**
   * Cancel a specific job
   */
  async cancelJob(jobId: string): Promise<void> {
    // Cancel on server first
    try {
      await apiClient.delete(`/api/agent/cancel/${jobId}`);
    } catch (error) {
      // Failed to cancel job on server
    }

    // Cancel client-side polling
    const controller = this.abortControllers.get(jobId);
    if (controller) {
      controller.abort();
      this.activeJobs.delete(jobId);
      this.abortControllers.delete(jobId);
    }
  }

  /**
   * Get active job count
   */
  getActiveJobCount(): number {
    return this.activeJobs.size;
  }

  /**
   * Generate content using the agent with job-based approach
   *
   * @param input User input text
   * @param onStatusUpdate Optional callback for status updates during polling
   * @param postLength Optional post length
   * @returns Promise resolving to the agent response
   */
  async generateContent(
    input: string,
    onStatusUpdate?: (statusDetails: { overallStatus: string; currentStepKey?: string | null }) => void,
    postLength?: string,
    draftId?: string
  ): Promise<AgentResponse> {
    // Mock mode: use centralized mock service
    if (isMockModeEnabled()) {
      return mockService.generateContent(input, onStatusUpdate, {
        customData: { draftId, postLength }
      });
    }

    try {
      // Step 1: Submit the job
      const jobResponse = await this.submitJob(input, postLength, draftId);

      // Track the active job
      this.activeJobs.add(jobResponse.job_id);
      const abortController = new AbortController();
      this.abortControllers.set(jobResponse.job_id, abortController);

      try {
        // Step 2: Poll for results
        const result = await this.pollForResults(
          jobResponse.job_id,
          onStatusUpdate,
          abortController.signal
        );
        return result;
      } finally {
        // Clean up tracking - ensure cleanup is complete
        const cleanupPromise = this.cleanupJob(jobResponse.job_id);
        this.cleanupPromises.set(jobResponse.job_id, cleanupPromise);
        await cleanupPromise;
      }
    } catch (error: unknown) {
      const message = (error as { apiError?: { body?: { message?: string } } })?.apiError?.body?.message || (error as Error).message || 'Unknown error occurred';
      return {
        conversation_id: 'error',
        error: message
      };
    }
  }

  /**
   * Submit a new agent job
   *
   * @param input User input text
   * @param postLength Optional post length
   * @returns Promise resolving to the job response
   */
  private async submitJob(
    input: string,
    postLength?: string,
    draftId?: string
  ): Promise<AgentJobResponse> {
    const payload: AgentRequest = {
      input_text: input,
      ...(postLength && { post_length: postLength }),
      ...(draftId && { draft_id: draftId })
    };
    
    // Use apiClient.post - path assumes /api prefix is handled by apiClient or added here
    // Based on previous fixes, path should include /api
    try {
      const data = await apiClient.post('api/agent/generate', payload);
      return data as AgentJobResponse; // apiClient.post returns Promise<any> from handleResponse
    } catch (error: unknown) {
        const message = (error as { apiError?: { body?: { message?: string } } })?.apiError?.body?.message || (error as Error).message || 'Failed to submit job';
        throw new Error(message);
    }
  }

  /**
   * Poll for job results
   *
   * @param jobId Job ID to poll for
   * @param onStatusUpdate Optional callback for status updates
   * @param pollingInterval Milliseconds between polling attempts
   * @param maxPollingTime Maximum time to poll in milliseconds
   * @returns Promise resolving to the agent response
   */
  private async pollForResults(
    jobId: string,
    onStatusUpdate?: (statusDetails: { overallStatus: string; currentStepKey?: string | null }) => void,
    abortSignal?: AbortSignal,
    pollingInterval: number = DEFAULT_POLLING_INTERVAL,
    maxPollingTime: number = DEFAULT_MAX_POLLING_TIME
  ): Promise<AgentResponse> {
    const startTime = Date.now();
    let lastStatus = '';
    let lastCurrentStep: string | null = '';
    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 3;

    while (Date.now() - startTime < maxPollingTime) {
      // Check if polling was aborted
      if (abortSignal?.aborted) {
        throw new Error(`Job ${jobId} was cancelled`);
      }

      let jobStatus: AgentJobStatusResponse;
      try {
        const response = await apiClient.get<AgentJobStatusResponse>(`api/agent/status/${jobId}`);
        if (!response) {
          throw new Error(`No response received for job status ${jobId}`);
        }
        jobStatus = response;
        consecutiveErrors = 0; // Reset on successful API call
      } catch (apiCallError: unknown) {
        const message = (apiCallError as { apiError?: { body?: { message?: string } } })?.apiError?.body?.message || (apiCallError as Error).message;
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          throw new Error(`Encountered ${MAX_CONSECUTIVE_ERRORS} consecutive API call errors while polling job ${jobId}: ${message}`);
        }
        await new Promise(resolve => setTimeout(resolve, pollingInterval * consecutiveErrors));
        continue; // Try next poll iteration
      }

      // Process successfully fetched jobStatus
      if (lastStatus !== jobStatus.status || lastCurrentStep !== (jobStatus.current_generation_step || null)) {
        lastStatus = jobStatus.status;
        lastCurrentStep = jobStatus.current_generation_step || null;
      }

      if (onStatusUpdate) {
        const statusDetails = { 
          overallStatus: jobStatus.status,
          currentStepKey: jobStatus.current_generation_step || null
        };
        onStatusUpdate(statusDetails);
      }

      if (jobStatus.status === 'completed' && jobStatus.result) {
        return jobStatus.result; // Successfully exit
      }

      if (jobStatus.status === 'failed') {
        throw new Error(jobStatus.error || `Job ${jobId} failed without specific error message`);
      }
      
      // If not completed or failed, wait for the next poll interval
      await new Promise(resolve => setTimeout(resolve, pollingInterval)); 
    }

    throw new Error(`Job ${jobId} timed out after ${maxPollingTime / 1000} seconds`);
  }


  // NEW CRUD-like methods
  async getAgents(): Promise<AgentListResponse> {
    const response = await apiClient.get<AgentListResponse>('api/agents');
    if (!response) {
      throw new Error('Failed to fetch agents');
    }
    return response;
  }
  
  async getAgentById(agentId: string): Promise<Agent> {
    const response = await apiClient.get<Agent>(`api/agents/${agentId}`);
    if (!response) {
      throw new Error(`Agent ${agentId} not found`);
    }
    return response;
  }
  
  async createAgent(agentData: AgentData): Promise<Agent> {
    const response = await apiClient.post<Agent>('api/agents', agentData);
    if (!response) {
      throw new Error('Failed to create agent');
    }
    return response;
  }
  
  async updateAgent(agentId: string, agentData: Partial<AgentData>): Promise<Agent> {
    const response = await apiClient.put<Agent>(`api/agents/${agentId}`, agentData);
    if (!response) {
      throw new Error(`Failed to update agent ${agentId}`);
    }
    return response;
  }
  
  async deleteAgent(agentId: string): Promise<{ success: boolean; message?: string }> {
    const response = await apiClient.delete<{ success: boolean; message?: string }>(`api/agents/${agentId}`);
    if (!response) {
      throw new Error(`Failed to delete agent ${agentId}`);
    }
    return response;
  }

  /**
   * Cleanup a specific job
   */
  private async cleanupJob(jobId: string): Promise<void> {
    try {
      // Remove from tracking collections
      this.activeJobs.delete(jobId);
      const controller = this.abortControllers.get(jobId);
      if (controller) {
        controller.abort();
        this.abortControllers.delete(jobId);
      }
      this.cleanupPromises.delete(jobId);
    } catch (error) {
      // Error during cleanup
    }
  }
}

// Create singleton instance
const agentServiceInstance = new AgentService();

// Ensure cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    // Best effort cleanup - may not complete
    agentServiceInstance.cancelAllJobs().catch(() => {});
  });
}

export default agentServiceInstance; // Export instance
