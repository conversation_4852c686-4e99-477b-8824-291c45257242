import { AppSession, SessionUpdate, SessionManagerConfig, Breadcrumb } from '../types/session';
// ContentSessionService has been removed - migration handled internally

/**
 * Simple, robust session manager with multiple persistence layers
 * Priority: URL -> Memory -> LocalStorage -> Backend
 */
export class SimpleSessionManager {
  private cache = new Map<string, AppSession>();
  private config: Required<SessionManagerConfig>;
  private syncTimer?: NodeJS.Timeout;

  constructor(config: SessionManagerConfig = {}) {
    this.config = {
      storagePrefix: config.storagePrefix || 'linkedinsight_session_',
      syncInterval: config.syncInterval || 5000, // 5 seconds
      cacheExpiry: config.cacheExpiry || 1800000, // 30 minutes
    };
  }

  /**
   * Get or create a session based on current context
   */
  async getOrCreate(params?: {
    source?: AppSession['flow'];
    data?: Partial<AppSession>;
  }): Promise<string> {
    // 1. Check URL for existing session
    const urlParams = new URLSearchParams(window.location.search);
    const urlSessionId = urlParams.get('session');
    
    if (urlSessionId) {
      const session = await this.load(urlSessionId);
      if (session) {
        return urlSessionId;
      }
    }

    // 2. Check for migration from old system
    const migratedSession = await this.migrateOldSession();
    if (migratedSession) {
      await this.save(migratedSession.id, migratedSession);
      return migratedSession.id;
    }

    // 3. Create new session
    const sessionId = this.generateId();
    const session: AppSession = {
      id: sessionId,
      version: 1,
      flow: params?.source || 'direct',
      step: 'input',
      breadcrumbs: [{
        path: window.location.pathname,
        label: this.getPageLabel(window.location.pathname),
        timestamp: Date.now()
      }],
      created: Date.now(),
      updated: Date.now(),
      ...params?.data
    };

    await this.save(sessionId, session);
    return sessionId;
  }

  /**
   * Load session from any available source
   */
  async load(sessionId: string): Promise<AppSession | null> {
    // 1. Check memory cache
    if (this.cache.has(sessionId)) {
      return this.cache.get(sessionId)!;
    }

    // 2. Check localStorage
    const localData = this.loadFromLocalStorage(sessionId);
    if (localData && !this.isExpired(localData)) {
      this.cache.set(sessionId, localData);
      return localData;
    }

    // 3. Try backend (if we have a backend ID)
    if (localData?.backendId) {
      try {
        const backendData = await contentSessionService.getSession(localData.backendId);
        if (backendData) {
          const merged = this.mergeBackendData(localData, backendData);
          await this.save(sessionId, merged);
          return merged;
        }
      } catch (error) {
        console.warn('Failed to load from backend, using local data:', error);
      }
    }

    return localData; // Return even if expired, better than nothing
  }

  /**
   * Save session to all persistence layers
   */
  async save(sessionId: string, session: AppSession): Promise<void> {
    const updated = {
      ...session,
      updated: Date.now()
    };

    // 1. Save to memory
    this.cache.set(sessionId, updated);

    // 2. Save to localStorage
    this.saveToLocalStorage(sessionId, updated);

    // 3. Schedule backend sync
    this.scheduleBackendSync(sessionId, updated);

    // 4. Update URL if needed
    this.updateUrl(sessionId);
  }

  /**
   * Update session with partial data
   */
  async update(sessionId: string, updates: SessionUpdate): Promise<AppSession | null> {
    const current = await this.load(sessionId);
    if (!current) return null;

    // Add breadcrumb if navigating
    let breadcrumbs = current.breadcrumbs;
    if (window.location.pathname !== current.breadcrumbs[current.breadcrumbs.length - 1]?.path) {
      breadcrumbs = [...breadcrumbs, {
        path: window.location.pathname,
        label: this.getPageLabel(window.location.pathname),
        timestamp: Date.now()
      }];
    }

    const updated: AppSession = {
      ...current,
      ...updates,
      breadcrumbs,
      updated: Date.now()
    };

    await this.save(sessionId, updated);
    return updated;
  }

  /**
   * Navigate while preserving session
   */
  navigate(sessionId: string, to: string, updates?: SessionUpdate): void {
    // Update session first if needed
    if (updates) {
      this.update(sessionId, updates);
    }

    // Navigate with session in URL
    const url = new URL(to, window.location.origin);
    url.searchParams.set('session', sessionId);
    window.location.href = url.toString();
  }

  /**
   * Get previous navigation path
   */
  getPreviousPath(session: AppSession): string | null {
    if (session.breadcrumbs.length < 2) return null;
    return session.breadcrumbs[session.breadcrumbs.length - 2].path;
  }

  // Private helper methods

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getPageLabel(path: string): string {
    const labels: Record<string, string> = {
      '/': 'Home',
      '/home': 'Home',
      '/transcript-input': 'Transcript',
      '/notes-input': 'Notes',
      '/interview': 'Interview',
      '/ideas': 'Ideas',
      '/content-agent': 'Content Agent',
      '/editor': 'Editor',
    };
    return labels[path] || path;
  }

  private loadFromLocalStorage(sessionId: string): AppSession | null {
    try {
      const key = `${this.config.storagePrefix}${sessionId}`;
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
      return null;
    }
  }

  private saveToLocalStorage(sessionId: string, session: AppSession): void {
    try {
      const key = `${this.config.storagePrefix}${sessionId}`;
      localStorage.setItem(key, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
      // Clean up old sessions if we're out of space
      this.cleanupOldSessions();
    }
  }

  private isExpired(session: AppSession): boolean {
    return Date.now() - session.updated > this.config.cacheExpiry;
  }

  private updateUrl(sessionId: string): void {
    const url = new URL(window.location.href);
    if (url.searchParams.get('session') !== sessionId) {
      url.searchParams.set('session', sessionId);
      window.history.replaceState(null, '', url.toString());
    }
  }

  private scheduleBackendSync(sessionId: string, session: AppSession): void {
    // Clear existing timer
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }

    // Schedule new sync
    this.syncTimer = setTimeout(async () => {
      try {
        // Only sync if we have meaningful data
        if (session.brief || session.posts || session.transcript) {
          await this.syncToBackend(sessionId, session);
        }
      } catch (error) {
        console.error('Backend sync failed:', error);
        // Mark as pending for retry
        await this.update(sessionId, { syncStatus: 'pending' });
      }
    }, this.config.syncInterval);
  }

  private async syncToBackend(sessionId: string, session: AppSession): Promise<void> {
    try {
      if (session.backendId) {
        // Update existing backend session
        await contentSessionService.updateSession(session.backendId, {
          generatedPosts: session.posts || {},
          explanations: session.explanations || {},
          brief: session.brief,
          metadata: {
            flow: session.flow,
            step: session.step,
            breadcrumbs: session.breadcrumbs
          }
        });
      } else if (session.brief) {
        // Create new backend session
        const backendSession = await contentSessionService.createSession({
          brief: session.brief,
          postLength: session.postLength || 'medium',
          ideaId: session.selectedIdeaId,
          transcriptId: session.transcriptId
        });
        
        // Update local session with backend ID
        await this.update(sessionId, {
          backendId: backendSession.sessionId,
          syncStatus: 'synced',
          lastSyncedAt: Date.now()
        });
      }
    } catch (error) {
      console.error('Backend sync error:', error);
      throw error;
    }
  }

  private mergeBackendData(local: AppSession, backend: any): AppSession {
    return {
      ...local,
      // Prefer backend data for these fields
      posts: backend.generatedPosts || local.posts,
      explanations: backend.explanations || local.explanations,
      brief: backend.brief || local.brief,
      // Keep local navigation state
      breadcrumbs: local.breadcrumbs,
      flow: local.flow,
      step: local.step,
      // Update sync metadata
      lastSyncedAt: Date.now(),
      syncStatus: 'synced'
    };
  }

  private async migrateOldSession(): Promise<AppSession | null> {
    // Check for old ContentSessionService session
    const urlParams = new URLSearchParams(window.location.search);
    const oldSessionId = urlParams.get('session');
    
    if (oldSessionId && oldSessionId.includes('-')) {
      try {
        const oldData = await contentSessionService.getSession(oldSessionId);
        if (oldData) {
          // Convert to new format
          return {
            id: this.generateId(),
            version: 1,
            flow: 'direct',
            step: 'content',
            brief: oldData.brief,
            postLength: oldData.postLength,
            posts: oldData.generatedPosts || {},
            explanations: oldData.explanations || {},
            hasGenerated: true,
            breadcrumbs: [{
              path: window.location.pathname,
              label: 'Content Agent',
              timestamp: Date.now()
            }],
            created: new Date(oldData.createdAt).getTime(),
            updated: Date.now(),
            backendId: oldSessionId,
            syncStatus: 'synced'
          };
        }
      } catch (error) {
        console.warn('Failed to migrate old session:', error);
      }
    }

    // Clean up old localStorage items
    this.cleanupOldStorage();
    
    return null;
  }

  private cleanupOldStorage(): void {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('contentSession_') ||
        key.startsWith('hasGenerated_') ||
        key.startsWith('strategyMode') ||
        key.startsWith('fastContentMode')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  private cleanupOldSessions(): void {
    const sessions: Array<{ key: string; updated: number }> = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(this.config.storagePrefix)) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const session = JSON.parse(data);
            sessions.push({ key, updated: session.updated || 0 });
          }
        } catch (error) {
          // Invalid session, remove it
          localStorage.removeItem(key!);
        }
      }
    }

    // Remove oldest sessions if we have too many
    if (sessions.length > 10) {
      sessions
        .sort((a, b) => a.updated - b.updated)
        .slice(0, sessions.length - 10)
        .forEach(s => localStorage.removeItem(s.key));
    }
  }

  /**
   * Clear all data for current session
   */
  async clear(sessionId: string): Promise<void> {
    this.cache.delete(sessionId);
    localStorage.removeItem(`${this.config.storagePrefix}${sessionId}`);
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }
  }
}

// Export singleton instance
export default new SimpleSessionManager();