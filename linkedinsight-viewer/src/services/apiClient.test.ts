import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ApiClient } from './apiClient'; // Import the class
import defaultApiClientInstance from './apiClient'; // Import the default instance

// Mock the config module
vi.mock('../config', () => ({
  API_CONFIG: {
    BASE_URL: 'http://mocked-api-base-url.com/api',
  },
}));

describe('ApiClient', () => {
  beforeEach(() => {
    // Attempt to reset the singleton instance before each test for isolation.
    // This is a common pattern but requires the ApiClient.instance to be modifiable.
    // A more robust solution would be a static reset method on ApiClient.
    (ApiClient as any).instance = undefined;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('getInstance should return a singleton instance', () => {
    const instance1 = ApiClient.getInstance();
    const instance2 = ApiClient.getInstance();
    expect(instance1).toBeInstanceOf(ApiClient);
    expect(instance2).toBeInstanceOf(ApiClient);
    expect(instance1).toBe(instance2);
  });

  it('defaultApiClientInstance should be an instance of ApiClient after reset', () => {
    // Since we reset instance in beforeEach, the defaultApiClientInstance from module import
    // might be the one created *before* reset if modules are cached. 
    // This specific test might be tricky depending on module execution order in tests.
    // Let's ensure we get a fresh default instance for this test logic.
    (ApiClient as any).instance = undefined; // ensure it's reset
    const newDefaultInstance = ApiClient.getInstance(); // This will be the first after reset
    expect(newDefaultInstance).toBeInstanceOf(ApiClient);
  });
  
  it('getInstance should return the same instance as a newly created default if called again', () => {
    const firstInstance = ApiClient.getInstance(); // Creates the instance for this test scope
    const secondInstance = ApiClient.getInstance();
    expect(secondInstance).toBe(firstInstance);
  });

  it('should initialize with the correct base URL from mocked API_CONFIG when getInstance is called', () => {
    const instance = ApiClient.getInstance();
    expect((instance as any).baseUrl).toBe('http://mocked-api-base-url.com/api');
  });

  it('should initialize with provided baseUrl if options are given to getInstance', () => {
    const customInstance = ApiClient.getInstance({ baseUrl: 'http://custom-base.com' });
    expect((customInstance as any).baseUrl).toBe('http://custom-base.com');

    (ApiClient as any).instance = defaultApiClientInstance;
  });

  it('should have default headers (empty by default) if no options are given', () => {
    const instance = ApiClient.getInstance();
    expect((instance as any).defaultHeaders).toEqual({});
  });
  
  it('should use provided defaultHeaders if options are given (first call)', () => {
    const customHeaders = { 'X-Custom-Header': 'TestValue' };
    const customInstance = ApiClient.getInstance({ defaultHeaders: customHeaders });
    expect((customInstance as any).defaultHeaders).toEqual(customHeaders);
  });

  // Test stubs for methods
  it('get method should be defined', () => {
    const instance = ApiClient.getInstance();
    expect(instance.get).toBeInstanceOf(Function);
  });

  it('post method should be defined', () => {
    const instance = ApiClient.getInstance();
    expect(instance.post).toBeInstanceOf(Function);
  });

  it('put method should be defined', () => {
    const instance = ApiClient.getInstance();
    expect(instance.put).toBeInstanceOf(Function);
  });

  it('delete method should be defined', () => {
    const instance = ApiClient.getInstance();
    expect(instance.delete).toBeInstanceOf(Function);
  });

  describe('buildUrl', () => {
    const testCases = [
      { base: 'http://localhost/api/', path: 'users', expected: 'http://localhost/api/users' },
      { base: 'http://localhost/api/', path: '/users', expected: 'http://localhost/api/users' },
      { base: 'http://localhost/api', path: 'users', expected: 'http://localhost/api/users' },
      { base: 'http://localhost/api', path: '/users', expected: 'http://localhost/api/users' },
      { base: 'http://localhost/api/', path: 'users/1', expected: 'http://localhost/api/users/1' },
      { base: 'http://localhost/api/', path: '/users/1', expected: 'http://localhost/api/users/1' },
      { base: 'http://localhost/api', path: 'users/1', expected: 'http://localhost/api/users/1' },
      { base: 'http://localhost/api', path: '/users/1', expected: 'http://localhost/api/users/1' },
      { base: 'http://localhost/api', path: '', expected: 'http://localhost/api/' },
      { base: 'http://localhost/api/', path: '', expected: 'http://localhost/api/' },
    ];

    testCases.forEach(({ base, path, expected }) => {
      it(`should correctly join base: "${base}" and path: "${path}" to "${expected}"`, () => {
        (ApiClient as any).instance = undefined;
        const client = ApiClient.getInstance({ baseUrl: base });
        expect((client as any).buildUrl(path)).toBe(expected);
      });
    });

    it('should use the default baseUrl from mocked config if no baseUrl option is provided', () => {
      (ApiClient as any).instance = undefined;
      const client = ApiClient.getInstance();
      expect((client as any).buildUrl('test')).toBe('http://mocked-api-base-url.com/api/test');
    });
  });

  describe('_getClerkToken', () => {
    let mockClerk: any;

    beforeEach(() => {
      (ApiClient as any).instance = undefined;
      mockClerk = {
        session: {
          getToken: vi.fn(),
        },
      };
      (window as any).Clerk = mockClerk;
    });

    afterEach(() => {
      delete (window as any).Clerk;
      vi.restoreAllMocks();
    });

    it('_getClerkToken should return token if Clerk.session.getToken resolves', async () => {
      const client = ApiClient.getInstance();
      const mockToken = 'mock-jwt-token';
      mockClerk.session.getToken.mockResolvedValue(mockToken);

      const token = await (client as any)._getClerkToken();
      expect(token).toBe(mockToken);
      expect(mockClerk.session.getToken).toHaveBeenCalledTimes(1);
    });

    it('_getClerkToken should return null if Clerk.session.getToken rejects', async () => {
      const client = ApiClient.getInstance();
      mockClerk.session.getToken.mockRejectedValue(new Error('Token retrieval failed'));

      const token = await (client as any)._getClerkToken();
      expect(token).toBeNull();
      expect(mockClerk.session.getToken).toHaveBeenCalledTimes(1);
    });

    it('_getClerkToken should return null if window.Clerk or session is not available', async () => {
      const client = ApiClient.getInstance();
      delete (window as any).Clerk;
      
      let token = await (client as any)._getClerkToken();
      expect(token).toBeNull();

      (window as any).Clerk = { session: undefined }; 
      token = await (client as any)._getClerkToken();
      expect(token).toBeNull();
    });
  });

  // New describe block for HTTP method tests
  describe('HTTP Methods', () => {
    let client: ApiClient;
    const mockFetch = vi.fn();
    let mockGetClerkToken: ReturnType<typeof vi.fn>;

    beforeEach(() => {
      (ApiClient as any).instance = undefined; // Reset singleton
      client = ApiClient.getInstance();
      
      // Mock _getClerkToken directly on the instance for these tests
      // Ensure it's a new mock for each test to control its behavior
      mockGetClerkToken = vi.fn();
      (client as any)._getClerkToken = mockGetClerkToken;

      vi.stubGlobal('fetch', mockFetch);
      mockFetch.mockResolvedValue(new Response(null, { status: 200 })); // Default mock response
    });

    afterEach(() => {
      vi.restoreAllMocks(); // This will also un-stub fetch
    });

    describe('GET', () => {
      it('should call fetch with correct URL, method, and headers', async () => {
        mockGetClerkToken.mockResolvedValue('test-token');
        await client.get('/test-path');
        expect(mockFetch).toHaveBeenCalledWith('http://mocked-api-base-url.com/api/test-path', {
          method: 'GET',
          headers: expect.any(Headers),
        });
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Authorization')).toBe('Bearer test-token');
      });

      it('should use explicit token if provided', async () => {
        await client.get('/test-path', {}, 'explicit-token');
        expect(mockGetClerkToken).not.toHaveBeenCalled();
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Authorization')).toBe('Bearer explicit-token');
      });

      it('should pass through other fetch options', async () => {
        const options = { cache: 'no-cache' as RequestCache };
        await client.get('/test-path', options);
        expect(mockFetch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining(options));
      });
    });

    describe('POST', () => {
      const postData = { key: 'value' };
      it('should call fetch with correct URL, method, headers, and stringified body for JSON', async () => {
        mockGetClerkToken.mockResolvedValue('test-token');
        await client.post('/test-path', postData);
        expect(mockFetch).toHaveBeenCalledWith('http://mocked-api-base-url.com/api/test-path', {
          method: 'POST',
          headers: expect.any(Headers),
          body: JSON.stringify(postData),
        });
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Authorization')).toBe('Bearer test-token');
        expect(headers.get('Content-Type')).toBe('application/json');
      });

      it('should send FormData as is', async () => {
        const formData = new FormData();
        formData.append('field', 'value');
        await client.post('/test-path', formData);
        expect(mockFetch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({ body: formData }));
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        // Content-Type for FormData is usually set by the browser/fetch automatically
        expect(headers.get('Content-Type')).toBeNull(); // Or check for specific multipart if desired
      });

      it('should respect custom Content-Type from options', async () => {
        await client.post('/test-path', postData, { headers: { 'Content-Type': 'application/xml' } });
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Content-Type')).toBe('application/xml');
        // Body would be stringified by default in our current implementation if not FormData and Content-Type is not JSON
        expect(mockFetch.mock.calls[0][1].body).toBe(String(postData)); 
      });
    });

    describe('PUT', () => {
      const putData = { key: 'update' };
      it('should call fetch with correct URL, method, headers, and stringified body for JSON', async () => {
        mockGetClerkToken.mockResolvedValue('test-token');
        await client.put('/test-path', putData);
        expect(mockFetch).toHaveBeenCalledWith('http://mocked-api-base-url.com/api/test-path', {
          method: 'PUT',
          headers: expect.any(Headers),
          body: JSON.stringify(putData),
        });
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Authorization')).toBe('Bearer test-token');
        expect(headers.get('Content-Type')).toBe('application/json');
      });
    });

    describe('DELETE', () => {
      it('should call fetch with correct URL, method, and headers', async () => {
        mockGetClerkToken.mockResolvedValue('test-token');
        await client.delete('/test-path');
        expect(mockFetch).toHaveBeenCalledWith('http://mocked-api-base-url.com/api/test-path', {
          method: 'DELETE',
          headers: expect.any(Headers),
        });
        const headers = mockFetch.mock.calls[0][1].headers as Headers;
        expect(headers.get('Authorization')).toBe('Bearer test-token');
      });
    });
  });

  describe('handleResponse', () => {
    let client: ApiClient;

    beforeEach(() => {
      (ApiClient as any).instance = undefined; // Reset singleton
      client = ApiClient.getInstance();
    });

    it('should parse JSON response for successful request with JSON content type', async () => {
      const mockData = { key: 'value' };
      const response = new Response(JSON.stringify(mockData), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
      const result = await (client as any).handleResponse(response);
      expect(result).toEqual(mockData);
    });

    it('should return text for successful request with text content type', async () => {
      const mockText = 'Hello World';
      const response = new Response(mockText, {
        status: 200,
        headers: { 'Content-Type': 'text/plain' },
      });
      const result = await (client as any).handleResponse(response);
      expect(result).toBe(mockText);
    });

    it('should return null for 204 No Content response', async () => {
      const response = new Response(null, { status: 204 });
      const result = await (client as any).handleResponse(response);
      expect(result).toBeNull();
    });

    it('should return null for response with Content-Length: 0', async () => {
      const response = new Response(null, {
        status: 200,
        headers: { 'Content-Length': '0' },
      });
      const result = await (client as any).handleResponse(response);
      expect(result).toBeNull();
    });

    it('should throw error for non-ok response and parse JSON error body', async () => {
      const errorBody = { error: 'Not found', message: 'Resource does not exist' };
      const response = new Response(JSON.stringify(errorBody), {
        status: 404,
        statusText: 'Not Found',
        headers: { 'Content-Type': 'application/json' },
      });
      // response.url = 'http://test.com/api/resource'; // Cannot assign to read-only property

      try {
        await (client as any).handleResponse(response);
      } catch (error: any) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('API Error: 404 Not Found');
        expect(error.apiError).toBeDefined();
        expect(error.apiError.status).toBe(404);
        expect(error.apiError.statusText).toBe('Not Found');
        expect(error.apiError.url).toBe(response.url); // Check against actual response.url
        expect(error.apiError.body).toEqual(errorBody);
      }
    });

    it('should throw error for non-ok response and parse text error body', async () => {
      const errorText = 'An unexpected error occurred';
      const response = new Response(errorText, {
        status: 500,
        statusText: 'Server Error',
        headers: { 'Content-Type': 'text/plain' },
      });
      // response.url = 'http://test.com/api/error'; // Cannot assign

      try {
        await (client as any).handleResponse(response);
      } catch (error: any) {
        expect(error.message).toBe('API Error: 500 Server Error');
        expect(error.apiError.url).toBe(response.url); // Check against actual response.url
        expect(error.apiError.body).toBe(errorText);
      }
    });

    it('should throw error and have undefined body if error response is not parsable', async () => {
      // Create a Response where .json() and .text() will fail
      const unparsableBody = new ReadableStream({
        start(controller) {
          controller.error(new Error("Simulated stream read error"));
        }
      });
      const response = new Response(unparsableBody, { 
        status: 503, 
        statusText: 'Service Unavailable' 
      });
      // response.url = 'http://test.com/api/broken'; // Cannot assign

      try {
        await (client as any).handleResponse(response);
      } catch (error: any) {
        expect(error.message).toBe('API Error: 503 Service Unavailable');
        expect(error.apiError).toBeDefined();
        expect(error.apiError.url).toBe(response.url); // Check against actual response.url
        expect(error.apiError.body).toBeUndefined();
      }
    });
    
    it('should return text for successful non-JSON/non-text response with a body (fallback)', async () => {
      const mockData = 'some other data';
      const response = new Response(mockData, {
        status: 200,
        headers: { 'Content-Type': 'application/octet-stream' }, // Example non-JSON/non-text
      });
      const result = await (client as any).handleResponse(response);
      expect(result).toBe(mockData);
    });

    it('should return null for successful response with no Content-Type and empty body', async () => {
        const response = new Response(null, { status: 200 }); // No body, no content-type, not 204
        const result = await (client as any).handleResponse(response);
        expect(result).toBeNull();
    });
  });

  // New describe block for Logging tests
  describe('Logging (assuming DEV mode default for tests)', () => {
    let client: ApiClient;
    const mockFetch = vi.fn();
    let mockGetClerkToken: ReturnType<typeof vi.fn>;

    // Spies on console methods
    let consoleSpies: any; // Defined here, assigned in beforeEach

    beforeEach(() => {
      (ApiClient as any).instance = undefined; // Reset singleton
      client = ApiClient.getInstance();
      
      mockGetClerkToken = vi.fn().mockResolvedValue('test-token');
      (client as any)._getClerkToken = mockGetClerkToken;

      vi.stubGlobal('fetch', mockFetch);
      
      // Re-initialize spies for this describe block
      consoleSpies = {
        groupCollapsed: vi.spyOn(console, 'groupCollapsed').mockClear(), // also clear previous calls
        log: vi.spyOn(console, 'log').mockClear(),
        error: vi.spyOn(console, 'error').mockClear(),
        groupEnd: vi.spyOn(console, 'groupEnd').mockClear(),
      };
    });

    afterEach(() => {
      vi.restoreAllMocks();
      vi.unstubAllGlobals(); // Ensure any globals like fetch are unstubbed
    });

    it('GET success: should call logRequest and logResponse', async () => {
      mockFetch.mockResolvedValue(new Response(JSON.stringify({ data: 'success' }), { status: 200, headers: { 'Content-Type': 'application/json'} }));
      await client.get('/log-test');
      
      expect(consoleSpies.groupCollapsed).toHaveBeenCalledWith(expect.stringContaining('API Request: GET http://mocked-api-base-url.com/api/log-test'));
      // No direct console.log for request data in this specific client.get call
      
      expect(consoleSpies.groupCollapsed).toHaveBeenCalledWith(expect.stringContaining('API Response: GET http://mocked-api-base-url.com/api/log-test'));
      expect(consoleSpies.log).toHaveBeenCalledWith('Response Data:', { data: 'success' });
      // Avoid checking toHaveBeenCalledTimes for console.log if groupCollapsed might also trigger it via the spy.
      // We've confirmed the specific call we care about.
      expect(consoleSpies.groupEnd).toHaveBeenCalledTimes(2); 
    });

    it('GET error: should call logRequest and logError', async () => {
      const error = new Error('Network Failure');
      (error as any).apiError = { status: 500, body: 'Server down' };
      mockFetch.mockRejectedValue(error);
      
      try {
        await client.get('/log-test');
      } catch (e) {
        // Error is expected
      }
      expect(consoleSpies.groupCollapsed).toHaveBeenCalledWith(expect.stringContaining('API Request: GET http://mocked-api-base-url.com/api/log-test'));
      expect(consoleSpies.groupCollapsed).toHaveBeenCalledWith(expect.stringContaining('API Error: GET http://mocked-api-base-url.com/api/log-test'));
      expect(consoleSpies.error).toHaveBeenCalledWith('API Error Details:', (error as any).apiError );
      expect(consoleSpies.log).toHaveBeenCalledWith('Stack Trace:', expect.any(String)); // Check for stack trace log
      expect(consoleSpies.groupEnd).toHaveBeenCalledTimes(2); 
    });
    
    it('logError should log apiError details correctly if present', async () => {
        // Assuming import.meta.env.DEV is true
        const apiErrorDetails = { status: 404, statusText: 'Not Found', url: '', body: { message: 'Resource missing' } };
        // Construct an error object similar to how handleResponse would create it
        const errorWithApiDetails = new Error('API Error: 404 Not Found');
        (errorWithApiDetails as any).apiError = apiErrorDetails;

        // Simulate a scenario where this error is passed to logError
        // This means we need to trigger the catch block in one of the HTTP methods
        mockFetch.mockResolvedValue(new Response(JSON.stringify(apiErrorDetails.body), { 
            status: 404, 
            statusText: 'Not Found', 
            headers: { 'Content-Type': 'application/json' }
        }));

        try {
            await client.get('/log-test-api-error');
        } catch (e) {
            // Error is caught and logError (private) would have been called by the get method.
            // We are testing if the spies on console methods were called correctly by logError.
        }
        expect(consoleSpies.error).toHaveBeenCalledWith('API Error Details:', apiErrorDetails);
    });

  });
}); 