/**
 * Service for managing content generation sessions via API
 */

import apiClient from './apiClient';
import type { SessionApiResponse, ContentGenerationSession as IContentGenerationSession } from '../types/services';

// Re-export from types for backward compatibility
export type { ContentGenerationSession } from '../types/services';

export interface ContentGenerationSessionLegacy {
  sessionId: string;
  userId: string;
  brief: string;
  postLength: string;
  generatedPosts: { [draftId: string]: string };
  status: 'draft' | 'generating' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  // Support both naming conventions for compatibility
  session_id?: string;
  user_id?: string;
  post_length?: string;
  generated_posts?: { [draftId: string]: string };
  created_at?: string;
  updated_at?: string;
}

export interface CreateSessionRequest {
  brief: string;
  postLength?: string;
  ideaId?: string;
  transcriptId?: string | null;
}

export interface UpdateSessionRequest {
  brief?: string;
  postLength?: string;
  generatedPosts?: { [draftId: string]: string };
  explanations?: { [draftId: string]: string | null };
  status?: 'draft' | 'generating' | 'completed' | 'failed';
}

export interface SessionResponse {
  success: boolean;
  session?: IContentGenerationSession;
  message?: string;
}

class ContentSessionService {
  private baseUrl = '/api/v1/content-sessions/';
  private cachePrefix = 'contentSession_';
  private cacheExpiry = 30 * 60 * 1000; // 30 minutes

  /**
   * Cache session data in localStorage
   */
  private cacheSession(session: IContentGenerationSession): void {
    try {
      const cacheKey = `${this.cachePrefix}${session.sessionId}`;
      const cacheData = {
        session,
        timestamp: Date.now()
      };
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache session:', error);
    }
  }

  /**
   * Get cached session data
   */
  private getCachedSession(sessionId: string): IContentGenerationSession | null {
    try {
      const cacheKey = `${this.cachePrefix}${sessionId}`;
      const cached = localStorage.getItem(cacheKey);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const age = Date.now() - cacheData.timestamp;
      
      // Check if cache is expired
      if (age > this.cacheExpiry) {
        localStorage.removeItem(cacheKey);
        return null;
      }

      return cacheData.session;
    } catch (error) {
      console.warn('Failed to read cached session:', error);
      return null;
    }
  }

  /**
   * Clear all cached sessions
   */
  clearCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.cachePrefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear session cache:', error);
    }
  }

  /**
   * Create a new content generation session
   */
  async createSession(data: CreateSessionRequest): Promise<IContentGenerationSession> {
    const response = await apiClient.post<SessionApiResponse>(this.baseUrl, data);
    
    if (!response || !response.success || !response.session) {
      throw new Error(response?.message || 'Failed to create session');
    }

    // Normalize field names
    const normalizedSession = this.normalizeSession(response.session);
    
    // Cache the session
    this.cacheSession(normalizedSession);
    
    return normalizedSession;
  }

  /**
   * Get a content generation session by ID with retry logic
   */
  async getSession(sessionId: string, maxRetries: number = 3): Promise<IContentGenerationSession | null> {
    let lastError: unknown = null;
    
    // Implement exponential backoff retry
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // Add delay for retries (exponential backoff)
        if (attempt > 0) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Try to get from API
        const response = await apiClient.get<SessionApiResponse>(`${this.baseUrl}${sessionId}`);
        
        if (response && response.success && response.session) {
          const normalizedSession = this.normalizeSession(response.session);
          
          // Update cache with fresh data
          this.cacheSession(normalizedSession);
          
          return normalizedSession;
        }
        
        // If we get here, the API returned but session wasn't found
        break; // No point retrying if session doesn't exist
        
      } catch (error) {
        lastError = error;
        console.warn(`Session fetch attempt ${attempt + 1} failed:`, error);
        
        // Don't retry on 404 errors
        if ((error as { response?: { status?: number } })?.response?.status === 404) {
          break;
        }
      }
    }
    
    // All retries failed or session not found, try cache as fallback
    const cachedSession = this.getCachedSession(sessionId);
    if (cachedSession) {
      console.info('Using cached session data as fallback');
      return cachedSession;
    }
    
    // Log the final error if we had one
    if (lastError) {
      console.error('Failed to fetch session after all retries:', lastError);
    }
    
    return null;
  }

  /**
   * Update a content generation session
   */
  async updateSession(sessionId: string, data: UpdateSessionRequest): Promise<IContentGenerationSession> {
    const response = await apiClient.put<SessionApiResponse>(`${this.baseUrl}${sessionId}`, data);
    
    if (!response || !response.success || !response.session) {
      throw new Error(response?.message || 'Failed to update session');
    }

    const normalizedSession = this.normalizeSession(response.session);
    
    // Update cache with latest data
    this.cacheSession(normalizedSession);
    
    return normalizedSession;
  }

  /**
   * List user's content generation sessions
   */
  async listSessions(limit = 10, skip = 0): Promise<IContentGenerationSession[]> {
    try {
      const response = await apiClient.get<IContentGenerationSession[]>(
        `${this.baseUrl}?limit=${limit}&skip=${skip}`
      );
      
      // Normalize each session in the list
      const sessions = response || [];
      return sessions.map((session: IContentGenerationSession) => this.normalizeSession(session));
    } catch (error) {
      return [];
    }
  }

  /**
   * Delete a content generation session
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`${this.baseUrl}${sessionId}`);
      return response?.success || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Save generated posts to session
   */
  async saveGeneratedPosts(sessionId: string, generatedPosts: { [draftId: string]: string }): Promise<void> {
    await this.updateSession(sessionId, {
      generatedPosts,
      status: 'completed'
    });
  }

  /**
   * Update session status
   */
  async updateStatus(sessionId: string, status: 'draft' | 'generating' | 'completed' | 'failed'): Promise<void> {
    await this.updateSession(sessionId, { status });
  }

  /**
   * Normalize session object to use camelCase consistently
   */
  private normalizeSession(session: Partial<IContentGenerationSession> & { 
    sessionId?: string;
    session_id?: string;
    userId?: string;
    user_id?: string;
    postLength?: string;
    post_length?: string;
    generatedPosts?: { [draftId: string]: string };
    generated_posts?: { [draftId: string]: string };
    createdAt?: string;
    created_at?: string;
    updatedAt?: string;
    updated_at?: string;
  }): IContentGenerationSession {
    return {
      sessionId: session.sessionId || session.session_id || '',
      userId: session.userId || session.user_id || '',
      brief: session.brief || '',
      postLength: session.postLength || session.post_length || '',
      generatedPosts: session.generatedPosts || session.generated_posts || {},
      status: session.status || 'draft',
      createdAt: session.createdAt || session.created_at || '',
      updatedAt: session.updatedAt || session.updated_at || '',
      // Keep snake_case versions for compatibility
      session_id: session.sessionId || session.session_id,
      user_id: session.userId || session.user_id,
      post_length: session.postLength || session.post_length,
      generated_posts: session.generatedPosts || session.generated_posts || {},
      created_at: session.createdAt || session.created_at,
      updated_at: session.updatedAt || session.updated_at
    };
  }
}

// Export singleton instance
const contentSessionService = new ContentSessionService();
export default contentSessionService;