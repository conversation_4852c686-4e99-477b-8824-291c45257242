/**
 * Service for fetching and caching strategy configuration from the backend.
 * Provides a single source of truth for funnel stage mappings and other config.
 */

import apiClient from './apiClient';

interface FunnelStageConfig {
  displayName: string;
  shortName: string;
  tooltip: string;
}

interface StrategyConfig {
  funnel_stages: {
    [key: string]: FunnelStageConfig;
  };
}

class ConfigService {
  private static instance: ConfigService;
  private configCache: StrategyConfig | null = null;
  private configPromise: Promise<StrategyConfig> | null = null;

  private constructor() {}

  static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * Fetch strategy configuration from the backend.
   * Results are cached for the lifetime of the application.
   */
  async getStrategyConfig(): Promise<StrategyConfig> {
    // Return cached config if available
    if (this.configCache) {
      return this.configCache;
    }

    // If a request is already in progress, return the same promise
    if (this.configPromise) {
      return this.configPromise;
    }

    // Fetch configuration from backend
    this.configPromise = apiClient
      .get('api/config/strategy')
      .then((data) => {
        this.configCache = data as StrategyConfig;
        this.configPromise = null;
        return this.configCache;
      })
      .catch((error) => {
        console.error('Failed to fetch strategy configuration:', error);
        this.configPromise = null;
        
        // Return default configuration on error
        const defaultConfig: StrategyConfig = {
          funnel_stages: {
            unclassified: {
              displayName: 'Not classified',
              shortName: 'N/A',
              tooltip: 'Funnel stage not determined'
            }
          }
        };
        this.configCache = defaultConfig;
        return defaultConfig;
      });

    return this.configPromise;
  }

  /**
   * Get funnel stage configuration by key.
   * Handles various input formats (top, top_of_funnel, TOFU, etc.)
   */
  async getFunnelStageConfig(stage?: string): Promise<FunnelStageConfig> {
    const config = await this.getStrategyConfig();
    
    if (!stage) {
      return config.funnel_stages.unclassified || {
        displayName: 'Not classified',
        shortName: 'N/A',
        tooltip: 'Funnel stage not determined'
      };
    }

    // Normalize the stage key
    const stageLower = stage.toLowerCase();
    
    // Try direct lookup first
    if (config.funnel_stages[stageLower]) {
      return config.funnel_stages[stageLower];
    }

    // Try common variations
    const stageMap: { [key: string]: string } = {
      'top': 'top_of_funnel',
      'tofu': 'top_of_funnel',
      'top of funnel': 'top_of_funnel',
      'middle': 'middle_funnel',
      'mofu': 'middle_funnel',
      'middle funnel': 'middle_funnel',
      'bottom': 'bottom_funnel',
      'bofu': 'bottom_funnel',
      'bottom funnel': 'bottom_funnel'
    };

    const normalizedKey = stageMap[stageLower];
    if (normalizedKey && config.funnel_stages[normalizedKey]) {
      return config.funnel_stages[normalizedKey];
    }

    // Default to unclassified
    return config.funnel_stages.unclassified || {
      displayName: 'Not classified',
      shortName: 'N/A',
      tooltip: 'Funnel stage not determined'
    };
  }

  /**
   * Clear the cached configuration.
   * Useful for testing or when config might have changed.
   */
  clearCache(): void {
    this.configCache = null;
    this.configPromise = null;
  }
}

export default ConfigService.getInstance();