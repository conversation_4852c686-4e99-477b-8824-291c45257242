/**
 * Centralized mock data for testing and development
 * All mock data should be defined here to maintain a single source of truth
 */

import { Idea } from '../types/idea';
import { ContentGenerationSession } from '../types/services';

// Define TranscriptResponse interface
export interface TranscriptResponse {
  id: string;
  transcript_content: string;
  ideas: Idea[];
  status: string;
}

// Mock user information
export const MOCK_USER_NAME = 'Ben';

// Mock ideas for transcript display
export const MOCK_IDEAS: Idea[] = [
  {
    id: "1",
    idea_text: "Create engaging LinkedIn posts about AI trends",
    summary: "Leverage current AI developments to create thought leadership content that resonates with your professional network.",
    category: "Technology & Innovation",
    engagement: 4,
    citations: [{ 
      text: "AI is transforming how we work and communicate",
      content: "AI is transforming how we work and communicate"
    }],
    generated_brief: 'false'
  },
  {
    id: "2", 
    idea_text: "Share personal stories about professional growth",
    summary: "Use authentic personal experiences to connect with your audience and demonstrate leadership principles.", 
    category: "Leadership & Growth",
    engagement: 5,
    citations: [{ 
      text: "Vulnerability in leadership builds trust",
      content: "Vulnerability in leadership builds trust"
    }],
    generated_brief: 'false'
  },
  {
    id: "3",
    idea_text: "Discuss industry trends and market insights",
    summary: "Position yourself as a thought leader by sharing data-driven insights about your industry.",
    category: "Industry Analysis", 
    engagement: 3,
    citations: [{
      text: "Market dynamics are shifting rapidly in 2024",
      content: "Market dynamics are shifting rapidly in 2024"
    }],
    generated_brief: 'false'
  },
  {
    id: "4",
    idea_text: "Building resilient teams in remote environments",
    summary: "Explore strategies for fostering team cohesion and productivity in distributed work settings.",
    category: "Team Management",
    engagement: 4,
    citations: [{
      text: "Remote work requires intentional connection strategies",
      content: "Remote work requires intentional connection strategies"
    }],
    generated_brief: 'false'
  },
  {
    id: "5",
    idea_text: "The future of sustainable business practices",
    summary: "Examine how companies are integrating environmental responsibility into their core business models.",
    category: "Sustainability",
    engagement: 5,
    citations: [{
      text: "Sustainability is becoming a competitive advantage",
      content: "Sustainability is becoming a competitive advantage"
    }],
    generated_brief: 'false'
  },
  {
    id: "6",
    idea_text: "Navigating career transitions with confidence",
    summary: "Share insights on successfully managing career pivots and embracing new opportunities.",
    category: "Career Development",
    engagement: 4,
    citations: [{
      text: "Career transitions are opportunities for growth",
      content: "Career transitions are opportunities for growth"
    }],
    generated_brief: 'false'
  },
  {
    id: "7",
    idea_text: "Data-driven decision making in uncertain times",
    summary: "Discuss how analytics and metrics can guide strategic choices during market volatility.",
    category: "Business Strategy",
    engagement: 3,
    citations: [{
      text: "Data provides clarity in uncertain environments",
      content: "Data provides clarity in uncertain environments"
    }],
    generated_brief: 'false'
  },
  {
    id: "8",
    idea_text: "The power of mentorship in professional development",
    summary: "Highlight the mutual benefits of mentoring relationships and how to cultivate them.",
    category: "Professional Growth",
    engagement: 5,
    citations: [{
      text: "Mentorship accelerates career advancement",
      content: "Mentorship accelerates career advancement"
    }],
    generated_brief: 'false'
  }
];

// Mock transcript response
export const MOCK_TRANSCRIPT_RESPONSE: TranscriptResponse = {
  id: "mock-transcript-1",
  transcript_content: "This is a mock transcript about AI trends, leadership, and professional growth. It contains insights about how technology is changing the workplace and the importance of continuous learning.",
  ideas: MOCK_IDEAS,
  status: "completed"
};

// Mock LinkedIn post variations
export const MOCK_LINKEDIN_POSTS = {
  draft_a: `🎯 The Hidden Cost of Always Being "On"

Ever notice how a quick Slack check turns into 30 minutes of lost focus?

Here's what I've discovered about deep work in our hyper-connected world:

• Batch communication into 2-3 daily windows
• Turn off all notifications during focus blocks
• Use "office hours" for availability

The result? 3x productivity and actually finishing work by 5 PM.

What's your #1 strategy for protecting focus time?

#DeepWork #Productivity #ProfessionalDevelopment`,

  draft_b: `Your phone buzzes. "Just a quick check," you tell yourself.

45 minutes later, you're still scrolling through messages, having completely forgotten what you were working on.

Sound familiar?

After tracking my work patterns for 30 days, I discovered something shocking:

→ Average time to regain deep focus: 23 minutes
→ Daily interruptions: 47
→ Actual deep work time: Less than 2 hours

The solution wasn't working harder. It was working differently.

Now I batch all communications into specific windows and guard my focus time like a treasure.

Result: I accomplish more by noon than I used to in a full day.

What's the biggest threat to your focus?`
};

// Mock brief content for content generation
export const MOCK_BRIEF_CONTENT = `# Strategic Content Brief for ${MOCK_USER_NAME}

## Core Theme: The Compound Effect of Small Professional Habits

**Target Insight**: Micro-habits in professional communication create exponential trust and relationship value over time.

## Hook Strategy
Open with a relatable moment of frustration about delayed responses, then pivot to contrast with someone who always responds quickly.

## Content Development Structure
1. **Personal Story**: Share experience with mentor who maintained 2-hour response rule
2. **The Three-Touch Rule Framework**: Before committing, ask "Can I deliver this 3 times consecutively?"
3. **Business Impact**: This reliability led to 40% more referrals over 18 months

## Call to Action
Challenge readers to identify one micro-commitment they can make to a key relationship this week.`;

// Mock content session
export const MOCK_CONTENT_SESSION: ContentGenerationSession = {
  sessionId: "mock-session-1",
  userId: "mock-user-1",
  brief: MOCK_BRIEF_CONTENT,
  postLength: "medium",
  generatedPosts: {},
  status: "draft",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Mock agent generation steps - total 1 second
export const MOCK_GENERATION_STEPS = [
  { key: 'processing_theme', duration: 200, message: 'Identifying theme...' },
  { key: 'creating_hook', duration: 250, message: 'Crafting hook...' },
  { key: 'developing_body', duration: 350, message: 'Drafting body...' },
  { key: 'crafting_ending', duration: 200, message: 'Finalizing ending...' }
];

// Mock explanation template
export const MOCK_POST_EXPLANATION = `# How This Post Was Created

This post was created through a step-by-step process where each component builds on the previous ones:

**Hook Creation:** Started with an engaging emoji and contrarian statement to grab attention and create intrigue about the topic being discussed.

**Body Structure & Style:** Applied a structured insight-sharing pattern with bullet points and concrete examples to build credibility and provide actionable value to the reader.

**Ending:** Completed with a community engagement pattern using a direct question to drive comments and discussion, encouraging audience participation.

This coherent approach ensures that each element of the post works together to deliver a compelling message with a distinctive style.`;

// Mock error messages for testing error scenarios
export const MOCK_ERROR_MESSAGES = {
  network: "Network error: Unable to connect to server",
  timeout: "Request timed out after 30 seconds",
  notFound: "Resource not found",
  unauthorized: "Authentication required",
  serverError: "Internal server error"
};