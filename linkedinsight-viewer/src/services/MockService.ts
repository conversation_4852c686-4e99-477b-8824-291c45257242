/**
 * Centralized mock service for consistent mock behavior across the application
 * Implemented as a functional module for simplicity
 */

import { 
  MOCK_IDEAS, 
  MOCK_TRANSCRIPT_RESPONSE, 
  MOCK_LINKEDIN_POSTS,
  MOCK_BRIEF_CONTENT,
  MOCK_GENERATION_STEPS,
  MOCK_POST_EXPLANATION,
  MOCK_ERROR_MESSAGES,
  MOCK_CONTENT_SESSION
} from './mockData';
import { Idea } from '../types/idea';
import { TranscriptResponse } from './mockData';
import { ContentGenerationSession } from './ContentSessionService';

export interface MockOptions {
  delay?: number;
  throwError?: boolean;
  errorType?: keyof typeof MOCK_ERROR_MESSAGES;
  customData?: Record<string, unknown>;
  simulateSteps?: boolean; // New flag for dev mode
}

export interface GenerationStatusCallback {
  (statusDetails: { 
    overallStatus: string; 
    currentStepKey?: string | null;
    userFriendlyMessage?: string;
  }): void;
}

// Constants
const DEFAULT_DELAY = 100; // Reduced from 500ms for better dev experience

/**
 * Simulate async delay
 */
async function simulateDelay(ms?: number): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, ms ?? DEFAULT_DELAY));
}

/**
 * Get appropriate error message
 */
function getErrorMessage(errorType?: keyof typeof MOCK_ERROR_MESSAGES): string {
  return MOCK_ERROR_MESSAGES[errorType || 'serverError'];
}

/**
 * Higher-Order Function to wrap mock endpoints with common behavior
 */
function createMockEndpoint<TArgs extends any[], TResult>(
  implementation: (...args: TArgs) => TResult | Promise<TResult>,
  defaultDelay?: number
) {
  return async function(...args: [...TArgs, MockOptions?]): Promise<TResult> {
    // Extract options from the last argument if it matches MockOptions shape
    const lastArg = args[args.length - 1];
    const hasOptions = lastArg && typeof lastArg === 'object' && 
      ('delay' in lastArg || 'throwError' in lastArg || 'errorType' in lastArg || 
       'customData' in lastArg || 'simulateSteps' in lastArg);
    
    const options: MockOptions = hasOptions ? lastArg : {};
    const implArgs = hasOptions ? args.slice(0, -1) as unknown as TArgs : args as unknown as TArgs;
    
    // Apply delay
    await simulateDelay(options.delay ?? defaultDelay);
    
    // Handle errors
    if (options.throwError) {
      throw new Error(getErrorMessage(options.errorType));
    }
    
    // Call implementation
    return implementation(...implArgs);
  };
}

/**
 * Get mock transcript with ideas
 */
export const getTranscript = createMockEndpoint(
  (transcriptId: string): TranscriptResponse => ({
    ...MOCK_TRANSCRIPT_RESPONSE,
    id: transcriptId
  })
);

/**
 * Get mock ideas for a transcript
 */
export const getIdeas = createMockEndpoint(
  (_transcriptId: string): Idea[] => MOCK_IDEAS
);

/**
 * Mark an idea as generated (mock implementation)
 */
export const markIdeaGenerated = createMockEndpoint(
  (_ideaId: string, _transcriptId: string): void => {
    // Mock implementation - in real usage, this would update backend state
  },
  100 // Quick operation
);

/**
 * Process a transcript (mock implementation)
 */
export const processTranscript = createMockEndpoint(
  (_formData: FormData): { transcript_id: string } => {
    // Mock implementation - returns a fixed transcript ID
    return { transcript_id: 'mock-transcript-1' };
  },
  1000 // Longer delay for file processing
);

/**
 * Create a content session (mock implementation)
 */
export const createContentSession = createMockEndpoint(
  (sessionData: { 
    brief: string; 
    postLength: string; 
    ideaId?: string; 
    transcriptId?: string 
  }): ContentGenerationSession => {
    const sessionId = `mock-session-${Date.now()}`;
    
    return {
      ...MOCK_CONTENT_SESSION,
      sessionId,
      brief: sessionData.brief || MOCK_BRIEF_CONTENT,
      postLength: sessionData.postLength,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },
  300
);

/**
 * Get a content session (mock implementation)
 */
export const getContentSession = createMockEndpoint(
  (sessionId: string): ContentGenerationSession | null => {
    // Return null for non-mock session IDs to simulate not found
    if (!sessionId.startsWith('mock-')) {
      return null;
    }

    return {
      ...MOCK_CONTENT_SESSION,
      sessionId,
      updatedAt: new Date().toISOString()
    };
  },
  200
);

/**
 * Update a content session (mock implementation)
 */
export const updateContentSession = createMockEndpoint(
  (sessionId: string, updateData: Partial<ContentGenerationSession>): ContentGenerationSession => {
    return {
      ...MOCK_CONTENT_SESSION,
      sessionId,
      ...updateData,
      updatedAt: new Date().toISOString()
    };
  },
  200
);

/**
 * Format post based on length preference
 */
function formatPost(post: string, length: string): string {
  if (length === 'short') {
    // Return first paragraph only
    const paragraphs = post.split('\n\n');
    return paragraphs.slice(0, 2).join('\n\n') + '\n\n' + paragraphs[paragraphs.length - 1];
  } else if (length === 'long') {
    // Add extra content
    return post + `\n\nP.S. This is additional content for the long format post. In a real implementation, this would include more detailed insights and examples.`;
  }
  
  // Default to medium (return as is)
  return post;
}

/**
 * Generate mock content with status updates
 * Note: This function has special handling for options due to status callback
 */
export async function generateContent(
  brief: string, 
  onStatusUpdate?: GenerationStatusCallback,
  options?: MockOptions
): Promise<{
  conversation_id: string;
  generated_post: string;
  explanation: string;
}> {
  if (options?.throwError) {
    throw new Error(getErrorMessage(options.errorType));
  }

  const simulateSteps = options?.simulateSteps ?? true;

  if (simulateSteps) {
    // Simulate step-by-step generation
    for (const step of MOCK_GENERATION_STEPS) {
      if (onStatusUpdate) {
        onStatusUpdate({
          overallStatus: 'processing',
          currentStepKey: step.key,
          userFriendlyMessage: step.message
        });
      }
      await simulateDelay(step.duration);
    }
  }

  // Final completion callback
  if (onStatusUpdate) {
    onStatusUpdate({
      overallStatus: 'completed',
      currentStepKey: 'completed',
      userFriendlyMessage: 'Post ready!'
    });
  }

  // Return appropriate draft based on options
  const draftId = options?.customData?.draftId as string;
  const draftKey = draftId === 'draft_b' ? 'draft_b' : 'draft_a';
  const postLength = (options?.customData?.postLength as string) || 'medium';
  
  return {
    conversation_id: `mock-${Date.now()}`,
    generated_post: formatPost(MOCK_LINKEDIN_POSTS[draftKey], postLength),
    explanation: MOCK_POST_EXPLANATION
  };
}

// Export a mock service object for backward compatibility
// This allows existing code to continue using mockService.methodName()
export const mockService = {
  getTranscript,
  getIdeas,
  generateContent,
  markIdeaGenerated,
  processTranscript,
  createContentSession,
  getContentSession,
  updateContentSession
};