/// <reference types="vitest/globals" />
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { act } from '@testing-library/react';
import apiClient from './apiClient'; // Import the actual instance to mock its methods
import agentServiceInstance from './AgentService'; // Import the default instance
import { AgentService, AgentJobResponse, AgentJobStatusResponse, AgentResponse } from './AgentService';

// Mock the apiClient methods used by AgentService
vi.mock('./apiClient', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
    // Ensure other methods are also mocked if AgentService were to use them, or use a partial mock
  },
}));

// Helper to create a promise that resolves after a timeout
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

describe('AgentService', () => {
  beforeEach(() => {
    vi.clearAllMocks(); // Clear all mocks before each test
  });

  describe('generateContent', () => {
    const input = 'Test input';

    it('should submit job and poll for results successfully', async () => {
      const mockJobResponse: AgentJobResponse = {
        job_id: 'job123',
        status: 'pending',
        created_at: new Date().toISOString(),
        polling_url: '/api/agent/status/job123', // Not directly used by our refactored service
      };
      const mockFinalResult: AgentResponse = { conversation_id: 'conv1', generated_post: 'Generated post' };
      const mockPendingStatus: AgentJobStatusResponse = { job_id: 'job123', status: 'processing', created_at: new Date().toISOString() };
      const mockCompletedStatus: AgentJobStatusResponse = { job_id: 'job123', status: 'completed', created_at: new Date().toISOString(), result: mockFinalResult };

      (apiClient.post as any).mockResolvedValueOnce(mockJobResponse);
      (apiClient.get as any)
        .mockResolvedValueOnce(mockPendingStatus) // First poll
        .mockResolvedValueOnce(mockCompletedStatus); // Second poll - completed

      const onStatusUpdate = vi.fn();
      const result = await agentServiceInstance.generateContent(input, onStatusUpdate);

      expect(apiClient.post).toHaveBeenCalledWith(
        'api/agent/generate',
        { input_text: input }
      );
      expect(apiClient.get).toHaveBeenCalledWith('api/agent/status/job123');
      expect(apiClient.get).toHaveBeenCalledTimes(2); // Pending, then Completed
      expect(onStatusUpdate).toHaveBeenCalledWith({ overallStatus: 'processing', currentStepKey: null });
      expect(onStatusUpdate).toHaveBeenCalledWith({ overallStatus: 'completed', currentStepKey: null });
      expect(result).toEqual(mockFinalResult);
    });

    it('should handle error during job submission', async () => {
      const submitError = new Error('Submission failed');
      (apiClient.post as any).mockRejectedValue(submitError);

      const result = await agentServiceInstance.generateContent(input);

      expect(result.error).toBe('Submission failed');
      expect(apiClient.get).not.toHaveBeenCalled();
    });

    it('should handle job failure during polling', async () => {
      const mockJobResponse: AgentJobResponse = { job_id: 'job123', status: 'pending', created_at: 'time', polling_url: 'url' };
      const mockFailedStatus: AgentJobStatusResponse = { job_id: 'job123', status: 'failed', created_at: 'time', error: 'Job processing error' };

      (apiClient.post as any).mockResolvedValueOnce(mockJobResponse);
      (apiClient.get as any).mockResolvedValueOnce(mockFailedStatus);
      
      const onStatusUpdate = vi.fn();
      const result = await agentServiceInstance.generateContent(input, onStatusUpdate);

      expect(apiClient.get).toHaveBeenCalledTimes(1);
      expect(onStatusUpdate).toHaveBeenCalledWith({ overallStatus: 'failed', currentStepKey: mockFailedStatus.current_generation_step || null });
      expect(result.error).toBe('Job processing error');
    }, 20000);

    it('should handle polling timeout', async () => {
      vi.useFakeTimers();
      let currentTime = Date.now();
      const dateSpy = vi.spyOn(Date, 'now').mockImplementation(() => currentTime);

      const mockJobResponse: AgentJobResponse = { job_id: 'job123', status: 'pending', created_at: 'time', polling_url: 'url' };
      const mockPendingStatus: AgentJobStatusResponse = { job_id: 'job123', status: 'processing', created_at: 'time' };

      (apiClient.post as any).mockResolvedValueOnce(mockJobResponse);
      (apiClient.get as any).mockResolvedValue(mockPendingStatus); // Always pending

      const onStatusUpdate = vi.fn(); // Added for consistency
      const generatePromise = agentServiceInstance.generateContent(input, onStatusUpdate);
      
      // Advance time in steps
      for (let i = 0; i < (300000 / 2000) + 5; i++) { 
          await act(async () => {
            currentTime += 2000; 
            vi.advanceTimersByTime(2000);
          });
          await Promise.resolve(); 
      }

      const result = await generatePromise;
      expect(result.error).toBe('Job job123 timed out after 300 seconds');
      
      dateSpy.mockRestore();
      vi.useRealTimers();
    }, 20000);

    it('should handle MAX_CONSECUTIVE_ERRORS during polling', async () => {
      vi.useFakeTimers();
      let currentTime = Date.now();
      const dateSpy = vi.spyOn(Date, 'now').mockImplementation(() => currentTime);

      const mockJobResponse: AgentJobResponse = { job_id: 'job123', status: 'pending', created_at: 'time', polling_url: 'url' };
      const pollingError = new Error('Polling API error');
      (pollingError as any).apiError = { status: 500, body: { message: 'Server hiccup' } }; 

      (apiClient.post as any).mockResolvedValueOnce(mockJobResponse);
      (apiClient.get as any).mockRejectedValue(pollingError); // Always error

      const onStatusUpdate = vi.fn(); // Added for consistency
      const generatePromise = agentServiceInstance.generateContent(input, onStatusUpdate);

      // Simulate polls with errors and backoff
      // Error 1: apiClient.get fails. Loop waits pollingInterval (2s default) * 1.
      // Error 2: apiClient.get fails. Loop waits pollingInterval (2s default) * 2.
      // Error 3: apiClient.get fails. Loop waits pollingInterval (2s default) * 3. Then throws.
      await act(async () => { currentTime += 2000; vi.advanceTimersByTime(2000); }); // Poll 1, Error 1, Wait 2s
      await Promise.resolve();
      await act(async () => { currentTime += 4000; vi.advanceTimersByTime(4000); }); // Poll 2, Error 2, Wait 4s
      await Promise.resolve();
      await act(async () => { currentTime += 6000; vi.advanceTimersByTime(6000); }); // Poll 3, Error 3, Wait 6s (or throws before this if logic is fast)
      await Promise.resolve();
      // Potentially need one more small advance for the error to propagate if it's in a promise chain inside the loop
      await act(async () => { vi.advanceTimersByTime(100); }); 
      await Promise.resolve();

      const result = await generatePromise;
      expect(result.error).toContain('Encountered 3 consecutive API call errors while polling job job123: Server hiccup');
      expect(apiClient.get).toHaveBeenCalledTimes(3);

      dateSpy.mockRestore();
      vi.useRealTimers();
    }, 20000);

  });
}); 