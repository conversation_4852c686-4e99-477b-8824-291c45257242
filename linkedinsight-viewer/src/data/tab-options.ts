/**
 * Tab options with grayscale emojis for the LinkedInsight application
 *
 * Each tab includes:
 * - label: Display text for the tab
 * - value: Unique identifier for the tab
 * - emoji: Grayscale emoji icon for visual distinction
 */

export const tabOptions = [
  { label: "Overview", value: "overview", emoji: "📊" },
  { label: "Themes", value: "themes", emoji: "🏷️" },
  { label: "Hooks", value: "hooks", emoji: "🪝" },
  { label: "Body", value: "body", emoji: "📝" },
  { label: "Endings", value: "endings", emoji: "🏁" },
  { label: "Linguistic", value: "linguistic", emoji: "💬" },
  { label: "Agent", value: "agent", emoji: "✍️" },
];
