export type LoadingMessageCategory = 
  | 'humorous'     // Light-hearted, fun messages
  | 'technical'    // Messages that sound like real processing
  | 'philosophical'// Thought-provoking messages
  | 'meta';        // Self-referential loading messages

export interface LoadingMessage {
  text: string;
  category: LoadingMessageCategory;
  weight?: number; // Higher weight = appears more often (default: 1)
}

export const loadingMessages: LoadingMessage[] = [
  // Technical messages (appear more frequently)
  { text: "Analyzing transcript patterns...", category: "technical", weight: 2 },
  { text: "Processing language structures...", category: "technical", weight: 2 },
  { text: "Extracting semantic meaning...", category: "technical", weight: 2 },
  { text: "Identifying key themes...", category: "technical", weight: 2 },
  
  // Humorous messages
  { text: "Teaching AI to appreciate puns...", category: "humorous" },
  { text: "Calculating the square root of lettuce...", category: "humorous" },
  { text: "Doing fancy math on the cloud...", category: "humorous" },
  { text: "Training squirrels to type faster...", category: "humorous" },
  
  // Philosophical messages
  { text: "Contemplating digital consciousness...", category: "philosophical" },
  { text: "Pondering the nature of ideas...", category: "philosophical" },
  { text: "Discovering universal truths...", category: "philosophical" },
  { text: "Questioning the meaning of 'loading'...", category: "philosophical" },
  
  // Meta messages
  { text: "Generating more loading messages...", category: "meta" },
  { text: "Wondering why progress bars exist...", category: "meta" },
  { text: "Loading the loading screen loader...", category: "meta" },
  { text: "Preparing to prepare preparation...", category: "meta" }
]; 