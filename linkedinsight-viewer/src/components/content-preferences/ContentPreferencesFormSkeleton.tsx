import React from 'react';
import { FormContainer } from '../ui/form-container';

/**
 * Skeleton loading state for ContentPreferencesForm
 * Follows the existing skeleton pattern using Tailwind animate-pulse
 */
export function ContentPreferencesFormSkeleton() {
  return (
    <FormContainer
      title="Set Your Content Preferences"
      error={null}
      isLoading={false}
      loadingText=""
      onSubmit={(e) => e.preventDefault()}
      submitText="Save Preferences"
      isSubmitting={false}
      formClassName="form-container form-container-balanced animate-pulse"
    >
      {/* Transcript Name field skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-32" />
        <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>

      {/* Select field skeletons */}
      {[...Array(7)].map((_, index) => (
        <div key={index} className="space-y-1">
          <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-24" />
          <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-full flex items-center justify-between px-3">
            <div className="h-3.5 bg-gray-200 dark:bg-gray-600 rounded w-32" />
            <div className="h-4 w-4 bg-gray-200 dark:bg-gray-600 rounded" />
          </div>
        </div>
      ))}
    </FormContainer>
  );
}

export default ContentPreferencesFormSkeleton;