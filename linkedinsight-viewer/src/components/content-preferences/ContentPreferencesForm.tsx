import React, { useReducer, useState, ChangeEvent, FormEvent, useEffect, useCallback, memo } from 'react';
import { FormContainer } from '../ui/form-container';
import { FormField } from '../ui/form-field';
import { ContentPreferencesPageSkeleton } from './ContentPreferencesPageSkeleton';
import { 
  useContentPreferencesOptions, 
  useContentPreferences, 
  useUpdateContentPreferences 
} from '../../hooks/useContentPreferencesQueries';

interface ContentPreferencesFormData {
  transcriptName: string;
  opinionStyle: string;
  emojiUsage: string;
  voiceDistinctiveness: string;
  controversyApproach: string;
  humorStyle: string;
  bragLevel: string;
  readabilityStyle: string;
}

interface PreferenceOption {
  value: string;
  label: string;
}

const initialFormData: ContentPreferencesFormData = {
  transcriptName: '',
  opinionStyle: '',
  emojiUsage: '',
  voiceDistinctiveness: '',
  controversyApproach: '',
  humorStyle: '',
  bragLevel: '',
  readabilityStyle: ''
};

/**
 * PERFORMANCE NOTE: This form uses several optimizations to handle multiple select fields:
 * 
 * 1. MemoizedFormField - Prevents re-renders of unchanged fields
 * 2. useReducer - Batches state updates to avoid cascading re-renders
 * 3. Single handleSelectChange - Avoids creating 7 separate handlers
 * 
 * The form previously suffered from slow loading due to undefined animation classes
 * in the Select component. This has been fixed in select.tsx.
 */
const MemoizedFormField = memo(FormField);

// Reducer for form state to batch updates and prevent cascading re-renders
type FormAction = 
  | { type: 'UPDATE_FIELD'; field: string; value: string }
  | { type: 'SET_ALL_FIELDS'; data: ContentPreferencesFormData }
  | { type: 'RESET' };

function formReducer(state: ContentPreferencesFormData, action: FormAction): ContentPreferencesFormData {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return { ...state, [action.field]: action.value };
    case 'SET_ALL_FIELDS':
      return action.data;
    case 'RESET':
      return initialFormData;
    default:
      return state;
  }
}

export function ContentPreferencesForm() {
  const [formData, dispatch] = useReducer(formReducer, initialFormData);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch options and preferences using TanStack Query
  const { data: preferenceOptions = {}, isError: optionsError, isLoading: isLoadingOptions } = useContentPreferencesOptions();
  const { 
    data: preferences, 
    isLoading: isLoadingPreferences,
    isError: preferencesError,
    error: preferencesQueryError 
  } = useContentPreferences();
  
  // Mutation for saving preferences
  const updatePreferencesMutation = useUpdateContentPreferences();

  // Set form data when BOTH preferences AND options are loaded
  // This ensures select fields can properly display their values
  useEffect(() => {
    if (preferences && typeof preferences === 'object' && Object.keys(preferences).length > 0 &&
        preferenceOptions && Object.keys(preferenceOptions).length > 0) {
      // Use reducer to set all fields at once, preventing cascading re-renders
      dispatch({
        type: 'SET_ALL_FIELDS',
        data: {
          transcriptName: preferences.transcriptName || '',
          opinionStyle: preferences.opinionStyle || '',
          emojiUsage: preferences.emojiUsage || '',
          voiceDistinctiveness: preferences.voiceDistinctiveness || '',
          controversyApproach: preferences.controversyApproach || '',
          humorStyle: preferences.humorStyle || '',
          bragLevel: preferences.bragLevel || '',
          readabilityStyle: preferences.readabilityStyle || '',
        }
      });
    }
  }, [preferences, preferenceOptions]);
  
  // Handle errors
  useEffect(() => {
    if (preferencesError && preferencesQueryError) {
      const err = preferencesQueryError as any;
      if (err?.apiError?.status === 404) {
        // No preferences yet - this is OK, user will create them
        setError(null);
      } else {
        const errorMessage = err?.apiError?.body?.detail || 
                           err?.apiError?.body?.message || 
                           err?.message || 
                           'Failed to load preferences';
        setError(errorMessage);
      }
    } else if (optionsError) {
      console.error('Failed to fetch preference options, using empty options');
    } else {
      setError(null);
    }
  }, [preferencesError, preferencesQueryError, optionsError]);

  const handleInputChange = useCallback((e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    dispatch({ type: 'UPDATE_FIELD', field: name, value });
  }, []);

  // Single generic handler for all select fields
  const handleSelectChange = useCallback((field: string, value: string) => {
    dispatch({ type: 'UPDATE_FIELD', field, value });
  }, []);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);

    try {
      await updatePreferencesMutation.mutateAsync(formData);
      // Don't update form data here - React Query will update the cache
      // and the useEffect will handle updating the form if needed
    } catch (err: any) {
      // Error is already handled by the mutation hook
      setError(err.message || 'Failed to save preferences');
    }
  };

  // Show skeleton while either options or preferences are loading
  // This prevents the jarring empty form state
  if (isLoadingOptions || isLoadingPreferences) {
    return <ContentPreferencesPageSkeleton />;
  }

  return (
    <FormContainer
      title="Set Your Content Preferences"
      error={error}
      isLoading={false}
      loadingText="Loading content preferences..."
      onSubmit={handleSubmit}
      submitText={updatePreferencesMutation.isPending ? 'Saving Preferences...' : 'Save Preferences'}
      isSubmitting={updatePreferencesMutation.isPending}
      formClassName="form-container form-container-balanced"
    >
      <MemoizedFormField
        label="Your name (as it appears in transcripts)"
        id="transcriptName"
        name="transcriptName"
        value={formData.transcriptName}
        onChange={handleInputChange}
        placeholder="e.g., John Smith, J. Smith, or however you're typically identified"
        required
      />

      <MemoizedFormField
        label="Opinion style"
        id="opinionStyle"
        name="opinionStyle"
        value={formData.opinionStyle}
        onValueChange={(value) => handleSelectChange('opinionStyle', value)}
        type="select"
        options={preferenceOptions.opinion_style || []}
        required
      />

      <MemoizedFormField
        label="Emoji usage"
        id="emojiUsage"
        name="emojiUsage"
        value={formData.emojiUsage}
        onValueChange={(value) => handleSelectChange('emojiUsage', value)}
        type="select"
        options={preferenceOptions.emoji_usage || []}
        required
      />

      <MemoizedFormField
        label="Voice distinctiveness"
        id="voiceDistinctiveness"
        name="voiceDistinctiveness"
        value={formData.voiceDistinctiveness}
        onValueChange={(value) => handleSelectChange('voiceDistinctiveness', value)}
        type="select"
        options={preferenceOptions.voice_distinctiveness || []}
        required
      />

      <MemoizedFormField
        label="Controversy approach"
        id="controversyApproach"
        name="controversyApproach"
        value={formData.controversyApproach}
        onValueChange={(value) => handleSelectChange('controversyApproach', value)}
        type="select"
        options={preferenceOptions.controversy_approach || []}
        required
      />

      <MemoizedFormField
        label="Humor style"
        id="humorStyle"
        name="humorStyle"
        value={formData.humorStyle}
        onValueChange={(value) => handleSelectChange('humorStyle', value)}
        type="select"
        options={preferenceOptions.humor_style || []}
        required
      />

      <MemoizedFormField
        label="Brag level"
        id="bragLevel"
        name="bragLevel"
        value={formData.bragLevel}
        onValueChange={(value) => handleSelectChange('bragLevel', value)}
        type="select"
        options={preferenceOptions.brag_level || []}
        required
      />

      <MemoizedFormField
        label="Readability style"
        id="readabilityStyle"
        name="readabilityStyle"
        value={formData.readabilityStyle}
        onValueChange={(value) => handleSelectChange('readabilityStyle', value)}
        type="select"
        options={preferenceOptions.readability_style || []}
        required
      />
    </FormContainer>
  );
}

export default ContentPreferencesForm;