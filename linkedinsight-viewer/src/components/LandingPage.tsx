import { useNavigate } from 'react-router-dom';
import { useAuth, PricingTable } from '@clerk/clerk-react';
import { useEffect } from 'react';
import { Box } from './ui/Box';
import { Button } from './ui/button';
import { Layout } from './layout/Layout';
import { LoadingState } from './common/LoadingState';

function LandingPage() {
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();

  // Redirect to home if already signed in
  useEffect(() => {
    if (isSignedIn) {
      navigate('/home');
    }
  }, [isSignedIn, navigate]);

  // Show nothing while Clerk is determining auth status
  if (!isLoaded) {
    return null;
  }

  // If user is signed in, return null to prevent flash while redirecting
  if (isSignedIn) {
    return null;
  }

  return (
    <Layout noShadowContainer={true} showAuth={false} noPadding={true}>
      <div className="overflow-y-auto h-full" style={{ '--landing-container-padding': '1.25rem' } as React.CSSProperties}>
        {/* Hero Section */}
        <section className="pt-8 pb-8">
        <div className="max-w-[700px] mx-auto px-4">
          <Box shadow center padding="sm" style={{ '--container-padding': 'var(--landing-container-padding) 1.5rem' } as React.CSSProperties}>
            <h1 className="text-5xl font-mono font-medium mb-4 mt-0">
              Your expertise. Zero writing required.
            </h1>
            <p className="text-xl text-text-secondary mb-8">
              Share a call, get interviewed, or drop your notes. Get publish-ready LinkedIn content.
              <br />Join 500+ professionals posting consistently
            </p>
            <div className="flex gap-4 justify-center flex-wrap mb-4">
              <Button 
                onClick={() => navigate('/sign-up')}
                variant="primary"
                size="lg"
              >
                Start Free Trial
              </Button>
              <Button 
                onClick={() => document.getElementById('how-it-works')?.scrollIntoView({ behavior: 'smooth' })}
                variant="secondary"
                size="lg"
              >
                See How It Works
              </Button>
            </div>
          </Box>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-8" id="how-it-works">
        <div className="max-w-[900px] mx-auto px-4">
          <h2 className="text-4xl font-heading font-medium text-center mb-8">
            Your expertise, posted in 3 steps
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">🎙️</span>
                <h3 className="text-lg font-semibold mb-2">Share Your Content</h3>
                <p className="text-sm text-text-secondary">
                  Upload recordings, paste notes, or brainstorm with AI
                </p>
              </div>
            </Box>
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">✨</span>
                <h3 className="text-lg font-semibold mb-2">We Extract & Polish</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  AI finds your best insights and crafts engaging posts
                </p>
              </div>
            </Box>
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">🚀</span>
                <h3 className="text-lg font-semibold mb-2">Publish & Grow</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Review, tweak if needed, and share your content
                </p>
              </div>
            </Box>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-8" id="pricing">
        <div className="max-w-[900px] mx-auto px-4">
          <h2 className="text-4xl font-heading font-medium text-center mb-8">
            Simple pricing for busy professionals
          </h2>
          <div style={{ display: 'none' }}>
            <PricingTable />
          </div>
          <div className="pricing-grid">
            {/* Starter Plan */}
            <Box shadow>
              <div className="pricing-card">
                <div className="pricing-card__header">
                  <h3 className="pricing-card__title">Starter</h3>
                  <div className="pricing-card__price-container">
                    <span className="pricing-card__price">$49</span>
                    <span className="pricing-card__price-period">/month</span>
                  </div>
                  <p className="pricing-card__billing">Only billed monthly</p>
                </div>
                <div className="pricing-card__features">
                  <ul className="pricing-card__features-list">
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>5 AI-generated posts per month</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Voice-to-post conversion</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Basic analytics dashboard</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>LinkedIn integration</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Email support</span>
                    </li>
                  </ul>
                </div>
                <div className="pricing-card__cta">
                  <Button 
                    onClick={() => navigate('/sign-up')}
                    variant="primary"
                    className="w-full"
                  >
                    Subscribe
                  </Button>
                </div>
              </div>
            </Box>

            {/* Pro Plan - Recommended */}
            <Box shadow>
              <div className="pricing-card">
                <div className="pricing-card__header">
                  <span className="pricing-card__badge">RECOMMENDED</span>
                  <h3 className="pricing-card__title">Pro</h3>
                  <p className="pricing-card__subtitle">Unlimited Posts</p>
                  <div className="pricing-card__price-container">
                    <span className="pricing-card__price">$79</span>
                    <span className="pricing-card__price-period">/month</span>
                  </div>
                  <p className="pricing-card__billing">Only billed monthly</p>
                </div>
                <div className="pricing-card__features">
                  <ul className="pricing-card__features-list">
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Unlimited AI posts</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Advanced voice & text analysis</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Custom voice matching</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Post scheduling & automation</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Detailed performance analytics</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Priority support</span>
                    </li>
                  </ul>
                </div>
                <div className="pricing-card__cta">
                  <Button 
                    onClick={() => navigate('/sign-up')}
                    variant="primary"
                    className="w-full"
                  >
                    Subscribe
                  </Button>
                </div>
              </div>
            </Box>

            {/* Teams Plan */}
            <Box shadow>
              <div className="pricing-card">
                <div className="pricing-card__header">
                  <h3 className="pricing-card__title">Teams</h3>
                  <p className="pricing-card__subtitle">For teams</p>
                  <div className="pricing-card__price-container">
                    <span className="pricing-card__price">$199</span>
                    <span className="pricing-card__price-period">/month</span>
                  </div>
                  <p className="pricing-card__billing">Only billed monthly</p>
                </div>
                <div className="pricing-card__features">
                  <ul className="pricing-card__features-list">
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Everything in Pro</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Multi-user collaboration</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Team content library</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Brand voice consistency</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>API access</span>
                    </li>
                    <li className="pricing-card__feature">
                      <span className="pricing-card__feature-icon">✓</span>
                      <span>Dedicated account manager</span>
                    </li>
                  </ul>
                </div>
                <div className="pricing-card__cta">
                  <Button 
                    onClick={() => navigate('/sign-up')}
                    variant="secondary"
                    className="w-full"
                  >
                    Contact Sales
                  </Button>
                </div>
              </div>
            </Box>
          </div>
        </div>
      </section>

      {/* Target Users */}
      <section className="py-8">
        <div className="max-w-[900px] mx-auto px-4">
          <h2 className="text-4xl font-heading font-medium text-center mb-8">
            Built for professionals who value their time
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">👔</span>
                <h3 className="text-lg font-semibold mb-2">Founders & CEOs</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Share your vision without the writing burden
                </p>
              </div>
            </Box>
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">💼</span>
                <h3 className="text-lg font-semibold mb-2">Solopreneurs & Coaches</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Build authority while building your business
                </p>
              </div>
            </Box>
            <Box shadow>
              <div className="text-center">
                <span className="text-4xl mb-4 block grayscale opacity-80">💡</span>
                <h3 className="text-lg font-semibold mb-2">Consultants & Freelancers</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Turn client calls into thought leadership
                </p>
              </div>
            </Box>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-8 mb-8">
        <div className="max-w-[600px] mx-auto px-4">
          <Box shadow>
            <div className="text-center">
              <h2 className="text-3xl font-heading font-medium mb-4 text-text-primary">
                Ready to post consistently?
              </h2>
              <p className="text-lg mb-6 text-text-secondary">
                Free 14-day trial. No credit card required.
              </p>
              <Button 
                onClick={() => navigate('/sign-up')}
                variant="primary"
                size="lg"
              >
                Start Your Free Trial
              </Button>
            </div>
          </Box>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t-2 border-primary text-center text-sm text-text-secondary">
        <div className="max-w-[900px] mx-auto px-4">
          <p>&copy; 2025 LinkedInsight. All rights reserved.</p>
        </div>
      </footer>
      </div>
    </Layout>
  );
}

export default LandingPage;