import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { StreamingMessage } from '../common/StreamingMessage';
import { ChatMessage as ChatMessageType } from '../../types/agent';

interface ChatMessageProps {
  message: ChatMessageType;
  onStreamComplete?: (messageId: string, finalText: string) => void;
  onBriefUpdate?: (briefContent: string) => void;
  onError?: (error: string) => void;
  onToolCallStart?: (toolId: string, toolName: string) => void;
  onToolCallResult?: (toolId: string, toolName: string, result: string, isError: boolean) => void;
  showPenCursor?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onStreamComplete,
  onBriefUpdate,
  onError,
  onToolCallStart,
  onToolCallResult,
  showPenCursor = false,
}) => {
  const isUser = message.type === 'user';

  return (
    <div className={`${isUser ? 'ml-auto' : 'mr-auto'} p-3 max-w-[80%] flex`}>
      <div className={`${
        isUser 
          ? 'message-bubble-user' 
          : 'prose dark:prose-invert max-w-none'
      }`}>
        {isUser ? (
          message.text
        ) : message.isStreaming && message.streamUrl ? (
          <StreamingMessage 
            streamUrl={message.streamUrl}
            onComplete={(finalText) => onStreamComplete?.(message.id, finalText)}
            onBriefUpdate={onBriefUpdate}
            onError={onError}
            onToolCallStart={onToolCallStart}
            onToolCallResult={onToolCallResult}
            showPenCursor={showPenCursor}
          />
        ) : (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {message.text}
          </ReactMarkdown>
        )}
      </div>
    </div>
  );
};