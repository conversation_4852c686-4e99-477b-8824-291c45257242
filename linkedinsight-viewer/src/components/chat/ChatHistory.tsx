import React, { useRef, useEffect } from 'react';
import { ChatMessage } from './ChatMessage';
import { ChatMessage as ChatMessageType } from '../../types/agent';

interface ChatHistoryProps {
  messages: ChatMessageType[];
  isLoading?: boolean;
  error?: string | null;
  emptyStateText?: string;
  onStreamComplete?: (messageId: string, finalText: string) => void;
  onBriefUpdate?: (briefContent: string) => void;
  onError?: (error: string) => void;
  onToolCallStart?: (toolId: string, toolName: string) => void;
  onToolCallResult?: (toolId: string, toolName: string, result: string, isError: boolean) => void;
  showPenCursor?: boolean;
  className?: string;
  style?: React.CSSProperties;
  hasExistingContent?: boolean;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  messages,
  isLoading = false,
  error = null,
  emptyStateText = "Start by sharing what you'd like to write about...",
  onStreamComplete,
  onBriefUpdate,
  onError,
  onToolCallStart,
  onToolCallResult,
  showPenCursor = false,
  className = "",
  style,
  hasExistingContent = false,
}) => {
  const endRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    endRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className={`overflow-y-auto space-y-4 ${className}`} style={style}>
      {messages.length === 0 && (
        <div className="text-center text-gray-500 dark:text-gray-400 italic my-8">
          {emptyStateText}
        </div>
      )}

      {messages.map((message) => (
        <ChatMessage
          key={message.id}
          message={message}
          onStreamComplete={onStreamComplete}
          onBriefUpdate={onBriefUpdate}
          onError={onError}
          onToolCallStart={onToolCallStart}
          onToolCallResult={onToolCallResult}
          showPenCursor={showPenCursor}
        />
      ))}


      {error && (
        <div className="error-message">
          <div className="font-semibold">Error:</div>
          <div>{error}</div>
        </div>
      )}

      <div ref={endRef} />
    </div>
  );
};