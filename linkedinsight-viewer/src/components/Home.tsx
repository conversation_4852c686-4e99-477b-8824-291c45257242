import { Layout } from './layout/Layout';
import { useAuth, useUser } from '@clerk/clerk-react';
import { useEffect } from 'react';
import { Box } from './ui/Box';
import { useStaggerTransition } from '../hooks/useStaggerTransition';
import { OnboardingChecker } from './onboarding/OnboardingChecker';
import { HomePageSkeleton } from './home/<USER>';
import '../styles/grid-stagger-transition.css';

/**
 * Home component - main landing page for the application
 * Provides four main entry points for users to begin their content creation journey
 * Uses a minimalist grid layout with shadow containers
 */
function Home() {
  // Initialize auth and navigation
  const { isLoaded } = useAuth();
  const { user } = useUser();
  
  // Show skeleton while auth is loading
  if (!isLoaded || !user) {
    return <HomePageSkeleton />;
  }
  
  // Grid items configuration - 3 clear, benefit-focused options
  const gridItems = [
    { 
      path: '/transcript-input', 
      icon: '/microphone-icon.png', 
      title: 'Have a recording?', 
      description: 'Turn podcasts & meetings into multiple posts',
      className: 'home-masonry-tall' // First item is tall
    },
    { 
      path: '/interactive-interview', 
      icon: '/speech-bubble-icon.png', 
      title: 'Need to brainstorm?', 
      description: 'Answer questions, get your post',
      className: 'home-masonry-regular'
    },
    { 
      path: '/notes-input', 
      icon: '/notes-icon.png', 
      title: 'Have notes for a post?', 
      description: 'Watch as they transform into a polished post',
      className: 'home-masonry-regular' 
    }
  ];

  const { isTransitioning, clickedIndex, handleStaggerNavigation, cleanup } = useStaggerTransition({
    staggerDelay: 50,
    transitionDuration: 300
  });

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Main home interface - masonry style design
  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      <OnboardingChecker />
      <div className="home-masonry-container">
        {/* Header */}
        <button
          className="home-masonry-header grid-stagger-item"
          onClick={() => handleStaggerNavigation('/quick-start', -1, gridItems.length)}
          data-index={-1}
          data-clicked={clickedIndex === -1}
          disabled={isTransitioning}
        >
          <Box shadow className="w-full h-full" center>
            <h1 className="home-masonry-title">Your expertise. Zero writing required.</h1>
            <p className="home-masonry-subtitle">
              Your daily insights → LinkedIn posts, in your voice
              <br />New here? Click to read guide
            </p>
          </Box>
        </button>

        {/* Masonry grid */}
        <div 
          className="home-masonry-grid grid-stagger-container" 
          data-transitioning={isTransitioning}
        >
          {gridItems.map((item, index) => (
            <button
              key={item.path}
              className={`home-masonry-item ${item.className} grid-stagger-item`}
              onClick={() => handleStaggerNavigation(item.path, index, gridItems.length)}
              data-index={index}
              data-clicked={clickedIndex === index}
              disabled={isTransitioning}
            >
              <Box shadow className="w-full h-full">
                {item.icon.startsWith('/') ? (
                  <img 
                    src={item.icon} 
                    alt=""
                    className="home-masonry-icon-img"
                  />
                ) : (
                  <span className="home-masonry-icon">{item.icon}</span>
                )}
                <h2 className="home-masonry-item-title">{item.title}</h2>
                <p className="home-masonry-item-description">{item.description}</p>
              </Box>
            </button>
          ))}
        </div>
      </div>
    </Layout>
  );
}

export default Home;
