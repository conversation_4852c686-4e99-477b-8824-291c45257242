import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Layout } from './layout/Layout';
import { Button } from './ui/button';
import AgentPanes from './agent/AgentPanes';
import { AsyncBoundary } from './ui/AsyncBoundary';
import { ContentAgentPageSkeleton } from './agent/ContentAgentPageSkeleton';
import agentServiceInstance from '../services/AgentService';
import { useToast } from './ui/toast-provider';
import { getErrorMessage, formatError } from '../utils/errorHandling';
import { useSession } from '../hooks/useSession';
import type {
  GenerationStatusMap,
  GeneratedPostsMap,
  ExplanationsMap
} from '../types/contentAgent';
import { IdeaItem } from '../types/session';

// Helper function to map step keys to user-friendly messages
const getStepUserFriendlyMessage = (stepKey: string | null | undefined): string => {
  if (!stepKey) return 'Processing...';
  switch (stepKey) {
    case 'initializing':
      return 'Initializing generation...';
    case 'processing_theme':
      return 'Identifying theme...';
    case 'creating_hook':
      return 'Crafting hook...';
    case 'developing_body':
      return 'Drafting body...';
    case 'crafting_ending':
      return 'Finalizing ending...';
    case 'completed':
      return 'Finalizing...';
    default: {
      const formattedKey = stepKey.charAt(0).toUpperCase() + stepKey.slice(1).replace(/_/g, ' ');
      return `${formattedKey}...`;
    }
  }
};

/**
 * ContentAgentPage - Main page for the Content Agent feature
 *
 * This page handles the content generation view with proper header, breadcrumbs,
 * and layout structure. It shows the AgentPanes for content generation.
 */
const ContentAgentPage: React.FC = () => {
  const toast = useToast();
  const { session, updateSession, navigate, loading: sessionLoading } = useSession();

  // Initialize session state
  useEffect(() => {
    if (!session || session.step !== 'content') return;
    
    // Check if we're coming from ideas page with a selected idea
    if (session.flow === 'transcript' && session.selectedIdeaId && session.ideas) {
      const selectedIdea = session.ideas.find(idea => idea.id === session.selectedIdeaId);
      if (selectedIdea && !session.brief) {
        // Generate brief from selected idea
        const ideaContent = `# Content Generation from Idea

## Idea: ${selectedIdea.idea_text}

## Summary: ${selectedIdea.summary}

## Category: ${selectedIdea.category}

## Supporting Citations:
${selectedIdea.citations?.map((citation, index) => 
  `${index + 1}. "${citation.text || citation.content}"`
).join('\n') || 'No citations available'}`;

        updateSession({ 
          brief: ideaContent,
          postLength: session.postLength || 'medium'
        });
      }
    }
  }, [session, updateSession]);

  // Clean up jobs when component unmounts
  useEffect(() => {
    return () => {
      agentServiceInstance.cancelAllJobs();
    };
  }, []);

  // Extract session data
  const { brief, postLength } = useMemo(() => ({
    brief: session?.brief || '',
    postLength: session?.postLength || 'medium'
  }), [session]);

  const [generationStatusMap, setGenerationStatusMap] = useState<GenerationStatusMap>({});
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPostsMap>(session?.posts || {});
  const [explanations, setExplanations] = useState<ExplanationsMap>(session?.explanations || {});

  // Update local state when session changes
  useEffect(() => {
    if (session?.posts) {
      setGeneratedPosts(session.posts);
    }
    if (session?.explanations) {
      setExplanations(session.explanations);
    }
  }, [session?.posts, session?.explanations]);

  // Start generation if we have a brief and haven't generated yet
  useEffect(() => {
    if (!session || session.step !== 'content') return;
    
    const hasExistingPosts = session.posts && Object.keys(session.posts).length > 0;
    
    if (session.brief && !session.hasGenerated && !hasExistingPosts) {
      // Mark as generated immediately to prevent duplicates
      updateSession({ hasGenerated: true });
      startContentGeneration(['draft_a', 'draft_b'], session.brief);
    }
  }, [session?.brief, session?.hasGenerated, session?.step]);

  const startContentGeneration = async (draftList: string[], briefContent: string) => {

    const initialStatusMap: { [key: string]: { overallStatus: string; currentStepKey?: string | null; userFriendlyMessage?: string; } } = {};
    draftList.forEach(name => {
      initialStatusMap[name] = {
        overallStatus: 'pending',
        currentStepKey: null,
        userFriendlyMessage: 'Initializing generation...'
      };
    });
    setGenerationStatusMap(initialStatusMap);
    // Don't clear existing posts - preserve any that already exist

    const generationPromises = draftList.map(draftId => {
      return agentServiceInstance.generateContent(
        briefContent,
        (statusDetails) => {
          setGenerationStatusMap((prevMap: GenerationStatusMap) => ({
            ...prevMap,
            [draftId]: {
              overallStatus: statusDetails.overallStatus,
              currentStepKey: statusDetails.currentStepKey,
              userFriendlyMessage: statusDetails.overallStatus === 'completed' ? 'Completed!' :
                                   statusDetails.overallStatus === 'failed' ? 'Failed.' :
                                   getStepUserFriendlyMessage(statusDetails.currentStepKey)
            }
          }));
        },
        postLength,
        draftId
      )
      .then(async (response) => {
        const generatedPost = response.generated_post || null;
        const explanation = response.explanation || null;
        
        setGeneratedPosts((prevPosts: GeneratedPostsMap) => ({
          ...prevPosts,
          [draftId]: generatedPost
        }));
        setExplanations((prevExplanations: ExplanationsMap) => {
          console.log('[ContentAgentPage] Setting explanation for', draftId, ':', explanation);
          return {
            ...prevExplanations,
            [draftId]: explanation
          };
        });
        setGenerationStatusMap((prevMap: GenerationStatusMap) => ({
          ...prevMap,
          [draftId]: {
            overallStatus: 'completed',
            userFriendlyMessage: 'Post ready!',
            currentStepKey: null
          }
        }));

        // Save to session
        if (generatedPost) {
          const currentPosts = session?.posts || {};
          const currentExplanations = session?.explanations || {};
          await updateSession({
            posts: {
              ...currentPosts,
              [draftId]: generatedPost
            },
            explanations: {
              ...currentExplanations,
              [draftId]: explanation
            }
          });
        }

        if (response && response.error) {
          toast.warning('Generation issue', { description: response.error });
        }
        return { draftId, success: true };
      })
      .catch(err => {
        const errorMessage = getErrorMessage(err, 'Content generation');
        toast.error('Generation failed', { description: errorMessage });
        setGenerationStatusMap((prevMap: GenerationStatusMap) => ({
          ...prevMap,
          [draftId]: {
            overallStatus: 'failed',
            userFriendlyMessage: formatError(err, 'Generation failed'),
            currentStepKey: null
          }
        }));
        setGeneratedPosts((prevPosts: GeneratedPostsMap) => ({
          ...prevPosts,
          [draftId]: null
        }));
        return { draftId, success: false };
      });
    });

    try {
      await Promise.all(generationPromises);
    } catch (err: unknown) {
      // All errors are already handled in individual promise catch blocks
      // This catch is just for safety if Promise.all itself fails
      console.error('Promise.all error:', err);
    }
  };

  const handleCopyPost = (draftId: string, post: string) => {
    navigator.clipboard.writeText(post)
      .then(() => {
        toast.success('Copied to clipboard!', {
          description: 'Post is ready to paste.'
        });
      })
      .catch((err: unknown) => {
        console.error('Copy error:', err);
        toast.error('Copy failed', { description: getErrorMessage(err, 'Clipboard operation')});
      });
  };

  // Add back navigation handler
  const handleBackToBrief = () => {
    const navState = location.state as ContentAgentNavigationState | null;
    if (navState && 'ideaId' in navState && navState.ideaId) {
      navigateWithTransition(navigate, `/brief/${navState.ideaId}`, {
        state: {
          transcriptId: 'transcriptId' in navState ? navState.transcriptId : undefined,
          ideaId: navState.ideaId,
          idea: isIdeaNavigationState(navState) ? navState.idea : undefined
        }
      });
    } else {
      // Fallback to ideas page if no brief context
      navigateWithTransition(navigate, '/ideas', {
        state: { transcriptId: navState && 'transcriptId' in navState ? navState.transcriptId : undefined }
      });
    }
  };

  return (
    <Layout noShadowContainer activeTab="Content Agent">
      <div className="h-full flex flex-col">
        {/* Main content area with fixed height */}
        <div className="flex-grow min-h-0">
          <AsyncBoundary
            isLoading={isLoadingSession}
            isEmpty={!session}
            loadingComponent={<ContentAgentPageSkeleton />}
            emptyComponent={
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground">No session found. Please start a new content generation.</p>
              </div>
            }
          >
            {session ? (
              <AgentPanes
                generationStatusMap={generationStatusMap}
                generatedPosts={generatedPosts}
                explanations={explanations}
                setGeneratedPosts={setGeneratedPosts}
                onCopyPost={handleCopyPost}
                sourceState={{ 
                  brief: session.brief || '', 
                  postLength: session.postLength || 'medium',
                  sessionId: session.sessionId || (session as ContentGenerationSession & SessionFieldMapping).session_id || '',
                  generatedPosts: session.generatedPosts || {},
                  explanations: session.explanations || {},
                  ...(location.state as Record<string, unknown> || {}) // Pass through navigation state
                }}
              />
            ) : null}
          </AsyncBoundary>
        </div>
      </div>
    </Layout>
  );
};

export default ContentAgentPage;