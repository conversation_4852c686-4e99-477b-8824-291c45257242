import * as React from 'react'

export type Theme = 'light' | 'dark' | 'system'

export type ThemeContextType = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

export const ThemeContext = React.createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = React.useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  )

  React.useEffect(() => {
    const root = window.document.documentElement

    // Determine the actual theme value
    let actualTheme: 'light' | 'dark' = theme === 'system'
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : theme as 'light' | 'dark'

    // Set data-theme attribute - now the single source of truth for theming
    root.setAttribute('data-theme', actualTheme)
    
    // Update theme-color meta tag for mobile browsers
    const metaThemeColor = document.getElementById('theme-color-meta') as HTMLMetaElement
    if (metaThemeColor) {
      metaThemeColor.content = actualTheme === 'dark' ? '#0a0a0a' : '#ffffff'
    }
  }, [theme])

  const value = React.useMemo(
    () => ({
      theme,
      setTheme: (theme: Theme) => {
        localStorage.setItem(storageKey, theme)
        setTheme(theme)
      },
    }),
    [theme, storageKey]
  )

  return (
    <ThemeContext.Provider value={value} {...props}>
      {children}
    </ThemeContext.Provider>
  )
}
