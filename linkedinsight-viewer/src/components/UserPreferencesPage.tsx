import React from 'react';
import { Layout } from './layout/Layout';
import { ReusableDualPaneLayout } from './layout/ReusableDualPaneLayout';
import { ContentStrategyForm } from './content-strategy/ContentStrategyForm';
import { ContentPreferencesForm } from './content-preferences/ContentPreferencesForm';
import { Box } from './ui/Box';

/**
 * UserPreferencesPage - displays content strategy and preferences forms side by side
 */
function UserPreferencesPage() {
  return (
    <Layout noShadowContainer>
      <div className="flex items-center justify-center">
        <div className="w-full max-w-6xl mx-auto px-4">
          <ReusableDualPaneLayout
            className="items-center"
            leftPaneContent={
              <Box shadow>
                <div className="flex flex-col form-equal-height">
                  <ContentStrategyForm />
                </div>
              </Box>
            }
            rightPaneContent={
              <Box shadow>
                <div className="flex flex-col form-equal-height">
                  <ContentPreferencesForm />
                </div>
              </Box>
            }
          />
        </div>
      </div>
    </Layout>
  );
}

export default UserPreferencesPage;