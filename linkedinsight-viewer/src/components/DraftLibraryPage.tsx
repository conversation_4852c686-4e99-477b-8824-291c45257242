import { useState, useEffect, useRef } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
import { ShadowedTabsContainer, ScrollableTabPanel, ScrollableContentContainer } from './ui/ShadowedTabsContainer';
import { Button } from './ui/button';
import { format, parseISO, isValid } from 'date-fns';
import { TitleBar, TitleBarText } from './transcript/TitleBar';
import { navigateWithTransition } from '../utils/navigation';
import { useDraftsQuery } from '../hooks/useDraftsQuery';
import { useDeleteDraftMutation, useCopyDraftMutation } from '../hooks/useDraftMutations';
import { DraftCardSkeleton } from './DraftCardSkeleton';

interface DraftMetadata {
  hookPattern: string;
  bodyFramework: string;
  endingPattern: string;
  linguisticPattern?: string;
  wordCount: number;
  charCount: number;
}

interface Draft {
  draftId: string;
  userId: string;
  content: string;
  brief: string;
  metadata: DraftMetadata;
  postLength: string;
  draftType?: 'draft_a' | 'draft_b';
  sessionId?: string;
  jobId?: string;
  createdAt: string;
  updatedAt?: string;
}



/**
 * DraftLibraryPage - A paginated, searchable view of all user-generated drafts
 * Features:
 * - Real-time search as you type
 * - Pagination with 12 items per page
 * - Expand/collapse for full content view
 * - Copy to clipboard functionality
 * - Delete with confirmation
 */
export function DraftLibraryPage() {
  const { user, isLoaded: userLoaded } = useUser();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const searchDebounceRef = useRef<ReturnType<typeof setTimeout>>();

  const ITEMS_PER_PAGE = 12;
  const SEARCH_DEBOUNCE_MS = 300; // Debounce search for 300ms

  // Use TanStack Query for data fetching
  const {
    drafts,
    totalPages,
    isLoading: loading,
    isError,
    refetch,
    isFetching
  } = useDraftsQuery({
    page,
    limit: ITEMS_PER_PAGE,
    search: debouncedSearchTerm,
    enabled: userLoaded && !!user,
  });

  const error = isError ? 'Failed to load drafts. Please try again.' : null;

  // Debounce search input
  useEffect(() => {
    if (searchDebounceRef.current) {
      clearTimeout(searchDebounceRef.current);
    }

    searchDebounceRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      // Reset to page 1 when search changes
      if (searchTerm !== debouncedSearchTerm) {
        setPage(1);
      }
    }, SEARCH_DEBOUNCE_MS);

    return () => {
      if (searchDebounceRef.current) {
        clearTimeout(searchDebounceRef.current);
      }
    };
  }, [searchTerm, debouncedSearchTerm]);

  // Mutations
  const deleteDraftMutation = useDeleteDraftMutation();
  const copyDraftMutation = useCopyDraftMutation();

  const handleCopyDraft = async (draft: Draft): Promise<void> => {
    copyDraftMutation.mutate(draft.content);
  };

  const handleDeleteDraft = async (draftId: string): Promise<void> => {
    if (!window.confirm('Are you sure you want to delete this draft?')) {
      return;
    }

    // If deleting the last item on a page, go back one page
    if (drafts.length === 1 && page > 1) {
      setPage(page - 1);
    }

    deleteDraftMutation.mutate(draftId);
  };

  const handleEditDraft = (draft: Draft): void => {
    navigateWithTransition(navigate, '/editor', {
      state: {
        content: draft.content,
        metadata: {
          draftId: draft.draftId,
          postLength: draft.postLength || 'medium',
          brief: draft.brief,
          draftType: draft.draftType
        },
        sourceRoute: {
          path: '/draft-library',
          label: 'Draft Library'
        }
      }
    });
  };

  // Show skeleton while Clerk is loading user state
  if (!userLoaded || (!user && userLoaded)) {
    return (
      <ShadowedTabsContainer
        activeTab="library"
        onTabChange={() => { }} // Single tab, no change needed
        tabs={[{ label: 'Draft Library', value: 'library' }]}
        maxWidth="max-w-2xl"
      >
        <ScrollableTabPanel value="library" activeTab="library">
          {!userLoaded ? (
            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <DraftCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <p style={{ color: 'var(--text-secondary)' }}>Please sign in to view your draft library.</p>
            </div>
          )}
        </ScrollableTabPanel>
      </ShadowedTabsContainer>
    );
  }

  return (
    <ShadowedTabsContainer
      activeTab="library"
      onTabChange={() => { }} // Single tab, no change needed
      tabs={[{ label: 'Draft Library', value: 'library' }]}
      maxWidth="max-w-2xl"
    >
      <ScrollableTabPanel value="library" activeTab="library" noContentWrapper>
        <div className="flex flex-col h-full [scrollbar-gutter:stable]">

          {/* Search Bar - fixed header */}
          <div className="pt-[16px] pb-4 pl-6" style={{ paddingRight: '30px' }}>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setPage(1); // Reset to first page on search
              }}
              placeholder="Search drafts by content or brief..."
              className="w-full px-4 py-2 rounded-[4px] font-archivo placeholder:text-[var(--text-tertiary)] focus:outline-none focus:ring-1 focus:ring-offset-0 focus:ring-black/20 transition-all duration-150"
              style={{
                backgroundColor: 'var(--surface-input)',
                border: '1px solid var(--border-primary)',
                color: 'var(--text-primary)',
                height: '36px'
              }}
              aria-label="Search drafts"
            />
          </div>

          {/* Content - scrollable area */}
          <ScrollableContentContainer>
            {error ? (
              <div className="text-center py-16">
                <p className="mb-4" style={{ color: 'var(--text-error, #dc2626)' }}>{error}</p>
                <Button onClick={() => refetch()} variant="secondary">
                  Try Again
                </Button>
              </div>
            ) : loading ? (
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <DraftCardSkeleton key={i} />
                ))}
              </div>
            ) : drafts.length === 0 ? (
              <div className="p-8 text-center rounded-[4px]" style={{
                backgroundColor: 'var(--surface-primary)',
                border: '1px solid var(--border-primary)'
              }}>
                <div className="text-6xl mb-4 filter grayscale">📝</div>
                <p className="font-archivo text-lg mb-2" style={{ color: 'var(--text-primary)' }}>
                  {debouncedSearchTerm ? 'No drafts found' : 'No drafts yet'}
                </p>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                  {debouncedSearchTerm 
                    ? 'Try adjusting your search terms' 
                    : 'Generate some content to get started!'}
                </p>
              </div>
            ) : (
              <>
                {/* Draft List */}
                <div className="space-y-4">
                  {drafts.map((draft) => (
                    <DraftCard
                      key={draft.draftId}
                      draft={draft}
                      onCopy={handleCopyDraft}
                      onDelete={handleDeleteDraft}
                      onEdit={handleEditDraft}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center gap-4 mt-8">
                    <Button
                      onClick={() => setPage(p => Math.max(1, p - 1))}
                      disabled={page === 1}
                      variant="secondary"
                      size="sm"
                    >
                      Previous
                    </Button>
                    <span className="text-sm font-mono" style={{ color: 'var(--text-secondary)' }}>
                      Page {page} of {totalPages}
                    </span>
                    <Button
                      onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                      disabled={page === totalPages}
                      variant="secondary"
                      size="sm"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </ScrollableContentContainer>
        </div>
      </ScrollableTabPanel>
    </ShadowedTabsContainer>
  );
}

interface DraftCardProps {
  draft: Draft;
  onCopy: (draft: Draft) => void;
  onDelete: (draftId: string) => void;
  onEdit: (draft: Draft) => void;
}

/**
 * DraftCard - Individual draft display component
 * Shows preview with expand/collapse functionality
 */

function DraftCard({ draft, onCopy, onDelete, onEdit }: DraftCardProps) {
  const [expanded, setExpanded] = useState(false);

  // Constants for text truncation
  const PREVIEW_LENGTH = 280; // Approximately 2-3 lines
  const BRIEF_MAX_LENGTH = 100;

  const contentPreview = draft.content.length > PREVIEW_LENGTH
    ? draft.content.substring(0, PREVIEW_LENGTH) + '...'
    : draft.content;

  const briefPreview = draft.brief.length > BRIEF_MAX_LENGTH
    ? draft.brief.substring(0, BRIEF_MAX_LENGTH) + '...'
    : draft.brief;

  const formatDate = (dateString: string): string => {
    if (!dateString) return 'No date';

    try {
      const date = parseISO(dateString);
      return isValid(date) ? format(date, 'MMM d, yyyy h:mm a') : 'No date';
    } catch (error) {
      console.error('Date parsing error:', error, 'for dateString:', dateString);
      return 'No date';
    }
  };

  return (
    <div className="rounded-[4px] flex flex-col h-full" style={{
      backgroundColor: 'var(--surface-primary)'
    }}>
      <TitleBar>
        <div className="flex items-center justify-between w-full">
          <TitleBarText>
            {formatDate(draft.createdAt)}
          </TitleBarText>
          {draft.draftType && (
            <span className="text-xs px-2 py-1 rounded font-mono" style={{
              backgroundColor: 'var(--surface-secondary)',
              color: 'var(--text-secondary)'
            }}>
              {draft.draftType === 'draft_a' ? 'Draft A' : draft.draftType === 'draft_b' ? 'Draft B' : draft.draftType}
            </span>
          )}
        </div>
      </TitleBar>

      <div className="p-3 flex flex-col flex-1 border-b border-l border-r border-black dark:border-white rounded-b-lg">

        {/* Brief */}
        {draft.brief && (
          <div className="mb-2">
            <span className="font-semibold font-archivo">Brief: </span>
            <span className="font-archivo text-sm">{briefPreview}</span>
          </div>
        )}

        {/* Content Preview */}
        <div className="flex-1 mb-3">
          <div className="text-sm rounded px-2 py-1.5" style={{
            backgroundColor: 'var(--surface-secondary)',
            color: 'var(--text-primary)',
            border: '1px solid var(--border-subtle)'
          }}>
            {expanded ? (
              <div className="whitespace-pre-wrap">{draft.content}</div>
            ) : (
              <p>{contentPreview}</p>
            )}
          </div>
        </div>


        {/* Actions */}
        <div className="flex gap-2 mt-auto justify-end">
          {draft.content.length > PREVIEW_LENGTH && (
            <Button
              onClick={() => setExpanded(!expanded)}
              variant="outline"
              size="sm"
            >
              {expanded ? 'Collapse' : 'Expand'}
            </Button>
          )}
          <Button
            onClick={() => onEdit(draft)}
            variant="secondary"
            size="sm"
          >
            Edit
          </Button>
          <Button
            onClick={() => onCopy(draft)}
            variant="primary"
            size="sm"
          >
            Copy
          </Button>
          <Button
            onClick={() => onDelete(draft.draftId)}
            variant="secondary"
            size="sm"
          >
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
}