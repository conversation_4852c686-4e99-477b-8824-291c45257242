import { useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { usePostHog } from 'posthog-js/react';
import { useQueryClient } from '@tanstack/react-query';
import apiClient from '../../services/apiClient';

/**
 * PostHogIdentifier component
 * 
 * Automatically identifies users in PostHog after they sign in via Clerk.
 * 
 * TODO: REMOVE THIS COMPONENT BEFORE PUBLIC LAUNCH
 * This is only for development/beta testing to track individual user journeys.
 * For production, we should use anonymous analytics for privacy compliance.
 * 
 * @internal For development use only - remove before public launch
 */

export function PostHogIdentifier() {
  const { user, isLoaded } = useUser();
  const posthog = usePostHog();
  const queryClient = useQueryClient();

  useEffect(() => {
    // Only identify if user is loaded and signed in
    if (isLoaded && user) {
      // Identify user with their Clerk ID and email
      posthog.identify(user.id, {
        email: user.emailAddresses[0]?.emailAddress,
        name: user.fullName || user.firstName || 'Unknown',
        created_at: user.createdAt,
        // Add any other relevant user properties
      });
      
      // Prefetch terms status immediately when user is identified
      queryClient.prefetchQuery({
        queryKey: ['termsStatus'],
        queryFn: async () => {
          const response = await apiClient.get<{ data: any }>('/api/user/terms-status');
          if (!response) {
            throw new Error('Failed to fetch terms status');
          }
          // Extract the data property to match the main hook
          if (response && typeof response === 'object' && 'data' in response) {
            return (response as any).data;
          }
          return response;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      });
    }
  }, [isLoaded, user, posthog, queryClient]);

  // This component doesn't render anything
  return null;
}