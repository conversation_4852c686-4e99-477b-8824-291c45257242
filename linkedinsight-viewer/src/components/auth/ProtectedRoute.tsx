import React from 'react';
import { SignedIn, SignedOut, RedirectToSignIn, useAuth } from '@clerk/clerk-react';
import { Navigate, useLocation } from 'react-router-dom';
import { LoadingState } from '../common/LoadingState';
import { PostHogIdentifier } from './PostHogIdentifier';
import { useTermsStatus } from '../../hooks/useUserAgreements';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * ProtectedRoute component
 * 
 * Wraps components that require authentication.
 * Shows the component when signed in, redirects to sign in when signed out.
 * Displays a loading state while <PERSON> determines authentication status.
 */
export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isLoaded } = useAuth();
  const location = useLocation();
  const { data: termsStatus, isLoading: termsLoading } = useTermsStatus();

  // While Clerk is loading, pass through children to allow components to show their own loading states
  if (!isLoaded) {
    return <>{children}</>;
  }

  return (
    <>
      <SignedIn>
        <PostHogIdentifier />
        {/* Show skeleton while checking terms status */}
        {(() => {
          console.log('ProtectedRoute Debug:', {
            pathname: location.pathname,
            termsLoading,
            termsStatus,
            hasAccepted: termsStatus?.has_accepted,
            shouldRedirect: location.pathname !== '/terms' && !termsLoading && termsStatus && !termsStatus.has_accepted
          });
          
          if (location.pathname !== '/terms' && termsLoading) {
            return null; // Don't show anything while loading terms status
          } else if (location.pathname !== '/terms' && !termsLoading && termsStatus && !termsStatus.has_accepted) {
            return <Navigate to="/terms" state={{ from: location }} replace />;
          } else {
            return children;
          }
        })()}
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}