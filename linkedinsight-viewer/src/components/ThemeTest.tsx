export function ThemeTest() {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">Theme System Test</h2>
      
      <div className="space-y-2">
        <p className="text-gray-900 dark:text-white">
          This text should be black in light mode and white in dark mode
        </p>
        
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <p>This box should have light gray background in light mode and dark gray in dark mode</p>
        </div>
        
        <button className="px-4 py-2 bg-blue-500 dark:bg-blue-600 text-white rounded hover:bg-blue-600 dark:hover:bg-blue-700">
          Test Button with dark: utilities
        </button>
      </div>
      
      <div className="mt-4 p-4 border rounded">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Current theme attribute: <code>{document.documentElement.getAttribute('data-theme')}</code>
        </p>
      </div>
    </div>
  )
}