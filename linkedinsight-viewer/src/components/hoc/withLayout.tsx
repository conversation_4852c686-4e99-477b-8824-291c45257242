import React from 'react';
import { Layout } from '../layout/Layout';

export interface WithLayoutOptions {
  hasTabs?: boolean;
  fixedTabs?: boolean;
  activeTab?: string;
  noShadowContainer?: boolean;
  showAuth?: boolean;
  noPadding?: boolean;
}

/**
 * Higher-order component that wraps a component with the Layout component
 * 
 * @param Component - The component to wrap
 * @param options - Layout options to pass to the Layout component
 * @returns A new component wrapped with Layout
 */
export function withLayout<P extends object>(
  Component: React.ComponentType<P>,
  options: WithLayoutOptions = {}
) {
  return function WrappedComponent(props: P) {
    return (
      <Layout {...options}>
        <Component {...props} />
      </Layout>
    );
  };
}