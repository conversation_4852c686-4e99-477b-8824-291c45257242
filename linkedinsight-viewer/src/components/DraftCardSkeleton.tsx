import React from 'react';
import { TitleBar } from './transcript/TitleBar';

export function DraftCardSkeleton() {
  return (
    <div className="rounded-[4px] flex flex-col h-full animate-pulse" style={{
      backgroundColor: 'var(--surface-primary)'
    }}>
      <TitleBar>
        <div className="flex items-center justify-between w-full">
          <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
          <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
        </div>
      </TitleBar>
      
      <div className="p-3 flex flex-col flex-1 border-b border-l border-r border-black dark:border-white rounded-b-lg">
        {/* Brief skeleton */}
        <div className="mb-2">
          <span className="font-semibold font-archivo">Brief: </span>
          <div className="inline-block h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 ml-2"></div>
        </div>

        {/* Content Preview skeleton */}
        <div className="flex-1 mb-3">
          <div className="text-sm rounded px-2 py-1.5" style={{
            backgroundColor: 'var(--surface-secondary)',
            border: '1px solid var(--border-subtle)'
          }}>
            <div className="space-y-2">
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-4/5"></div>
            </div>
          </div>
        </div>

        {/* Actions skeleton */}
        <div className="flex gap-2 mt-auto justify-end">
          <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
          <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
          <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
          <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
        </div>
      </div>
    </div>
  );
}

export default DraftCardSkeleton;