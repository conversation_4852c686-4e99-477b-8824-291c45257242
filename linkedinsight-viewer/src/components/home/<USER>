import React from 'react';
import { Box } from '../ui/Box';
import { Layout } from '../layout/Layout';

export function HomePageSkeleton() {
  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      <div className="home-masonry-container animate-pulse">
        {/* Header skeleton */}
        <div className="home-masonry-header">
          <Box shadow className="w-full h-full" center>
            <div className="space-y-4">
              <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6 mx-auto"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3 mx-auto"></div>
              </div>
            </div>
          </Box>
        </div>

        {/* Masonry grid skeleton */}
        <div className="home-masonry-grid">
          {/* Tall item skeleton */}
          <div className="home-masonry-item home-masonry-tall">
            <Box shadow className="w-full h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 space-y-4">
                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded"></div>
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="space-y-2 w-full">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6 mx-auto"></div>
                </div>
              </div>
            </Box>
          </div>

          {/* Regular item skeleton 1 */}
          <div className="home-masonry-item home-masonry-regular">
            <Box shadow className="w-full h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 space-y-4">
                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded"></div>
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="space-y-2 w-full">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/5 mx-auto"></div>
                </div>
              </div>
            </Box>
          </div>

          {/* Regular item skeleton 2 */}
          <div className="home-masonry-item home-masonry-regular">
            <Box shadow className="w-full h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 space-y-4">
                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded"></div>
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="space-y-2 w-full">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/5 mx-auto"></div>
                </div>
              </div>
            </Box>
          </div>
        </div>
      </div>
    </Layout>
  );
}