import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useNavigate } from 'react-router-dom';
import { Layout } from './layout/Layout';
import { Box } from './ui/Box';
import { navigateWithTransition } from '../utils/navigation';

const QuickStartGuidePage: React.FC = () => {
  const [markdown, setMarkdown] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Load the markdown content
    fetch('/QUICK_START_GUIDE.md')
      .then(response => response.text())
      .then(text => {
        setMarkdown(text);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error loading quick start guide:', error);
        setMarkdown('# Error\n\nFailed to load the Quick Start Guide. Please try refreshing the page.');
        setLoading(false);
      });
  }, []);

  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      <div className="max-w-3xl mx-auto w-full h-full flex flex-col">
        <Box shadow className="flex-1 min-h-0">
          <div className="px-8 pt-2 pb-8 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-center">
              <div className="inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-r-transparent mb-3"></div>
              <p>Loading guide...</p>
            </div>
          </div>
        ) : (
          <div className="prose prose-lg max-w-none dark:prose-invert quick-start-content">
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({children}) => <h1 className="text-3xl font-bold mb-6 text-[var(--text-primary)]">{children}</h1>,
                    h2: ({children}) => {
                      const text = children?.toString() || '';
                      if (text.includes('Ready to Start?')) {
                        return (
                          <>
                            <h2 className="text-2xl font-bold mt-8 mb-4 text-[var(--text-primary)]">{children}</h2>
                            <div className="not-prose grid gap-4 mt-6 mb-8">
                              <button
                                onClick={() => navigateWithTransition(navigate, '/interactive-interview')}
                                className="p-4 bg-[var(--surface-secondary)] hover:bg-[var(--surface-tertiary)] rounded-lg border border-[var(--border-subtle)] transition-all duration-200 text-left group"
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <strong className="text-[var(--text-primary)] text-lg">New here?</strong>
                                    <span className="text-[var(--text-secondary)] ml-2">Start with the Interview Agent</span>
                                  </div>
                                  <span className="text-[var(--text-secondary)] group-hover:text-[var(--text-primary)] transition-colors">→</span>
                                </div>
                              </button>
                              <button
                                onClick={() => navigateWithTransition(navigate, '/transcript-input')}
                                className="p-4 bg-[var(--surface-secondary)] hover:bg-[var(--surface-tertiary)] rounded-lg border border-[var(--border-subtle)] transition-all duration-200 text-left group"
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <strong className="text-[var(--text-primary)] text-lg">Have content?</strong>
                                    <span className="text-[var(--text-secondary)] ml-2">Try Transcript Input</span>
                                  </div>
                                  <span className="text-[var(--text-secondary)] group-hover:text-[var(--text-primary)] transition-colors">→</span>
                                </div>
                              </button>
                              <button
                                onClick={() => navigateWithTransition(navigate, '/notes-input')}
                                className="p-4 bg-[var(--surface-secondary)] hover:bg-[var(--surface-tertiary)] rounded-lg border border-[var(--border-subtle)] transition-all duration-200 text-left group"
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <strong className="text-[var(--text-primary)] text-lg">Know your topic?</strong>
                                    <span className="text-[var(--text-secondary)] ml-2">Go straight to Brief Input</span>
                                  </div>
                                  <span className="text-[var(--text-secondary)] group-hover:text-[var(--text-primary)] transition-colors">→</span>
                                </div>
                              </button>
                            </div>
                          </>
                        );
                      }
                      return <h2 className="text-2xl font-bold mt-8 mb-4 text-[var(--text-primary)]">{children}</h2>;
                    },
                    h3: ({children}) => <h3 className="text-xl font-semibold mt-6 mb-3 text-[var(--text-primary)]">{children}</h3>,
                    p: ({children}) => <p className="mb-4 leading-relaxed text-[var(--text-secondary)]">{children}</p>,
                    ul: ({children}) => <ul className="list-disc pl-6 mb-4 space-y-2">{children}</ul>,
                    ol: ({children}) => {
                      // Hide the numbered list that follows "Ready to Start?" since we're replacing it with buttons
                      if (Array.isArray(children)) {
                        const firstChild = children[1] as React.ReactElement; // Get first li element (index 1 after React internals)
                        if (firstChild?.props?.children?.toString().includes('New here?')) {
                          return null;
                        }
                      }
                      return <ol className="list-decimal pl-6 mb-4 space-y-2">{children}</ol>;
                    },
                    li: ({children}) => <li className="text-[var(--text-secondary)]">{children}</li>,
                    strong: ({children}) => <strong className="font-semibold text-[var(--text-primary)]">{children}</strong>,
                    code: ({children}) => <code className="bg-[var(--surface-tertiary)] px-2 py-1 rounded text-sm">{children}</code>,
                    blockquote: ({children}) => (
                      <blockquote className="border-l-4 border-[var(--border-primary)] pl-4 my-4 italic text-[var(--text-secondary)]">
                        {children}
                      </blockquote>
                    ),
                  }}
                >
                  {markdown}
                </ReactMarkdown>
          </div>
        )}
          </div>
        </Box>
      </div>
    </Layout>
  );
};

export default QuickStartGuidePage;