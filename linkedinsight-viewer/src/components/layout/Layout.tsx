import React, { useEffect } from 'react';
import { Header } from '../ui/header';
import { Box } from '../ui/Box';

interface LayoutProps {
  children: React.ReactNode;
  hasTabs?: boolean;
  fixedTabs?: boolean;
  activeTab?: string;
  noShadowContainer?: boolean;
  showAuth?: boolean;
  noPadding?: boolean;
}

export function Layout({ children, hasTabs = false, fixedTabs = false, activeTab, noShadowContainer = false, showAuth = true, noPadding = false }: LayoutProps) {

  // Add the has-fixed-tabs class to the body when fixedTabs is true
  useEffect(() => {
    if (fixedTabs) {
      document.body.classList.add('has-fixed-tabs');
      return () => {
        document.body.classList.remove('has-fixed-tabs');
      };
    }
  }, [fixedTabs]);

  return (
    <div className="h-screen flex flex-col font-archivo overflow-hidden">
      <Header activeTab={activeTab} showAuth={showAuth} />
      <div className={`flex-1 flex flex-col mt-[3rem] ${noPadding ? '' : 'pt-[1rem] pb-[1rem] px-4 md:px-8'} overflow-hidden`}>
        {noPadding ? (
          <>
            {children}
          </>
        ) : (
          <div className="max-w-7xl mx-auto w-full h-full flex flex-col">
            {noShadowContainer ? (
              <div className="flex-1 min-h-0">
                {children}
              </div>
            ) : (
              <Box shadow className="flex-1 min-h-0" hasTabs={hasTabs} fixedTabs={fixedTabs} noBorder={hasTabs}>
                {children}
              </Box>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
