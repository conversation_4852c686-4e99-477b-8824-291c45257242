import React from 'react';

interface ReusableDualPaneLayoutProps {
  leftPaneContent: React.ReactNode;
  rightPaneContent: React.ReactNode;
  className?: string;
  leftPaneContainerClassName?: string;
  rightPaneContainerClassName?: string;
}

export const ReusableDualPaneLayout: React.FC<ReusableDualPaneLayoutProps> = ({
  leftPaneContent,
  rightPaneContent,
  className = '',
  leftPaneContainerClassName = '',
  rightPaneContainerClassName = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-8 h-full ${className}`}>
      {/* CRITICAL: flex flex-col min-h-0 is required on grid children to properly constrain height
          Without these classes, child content can overflow and break the layout */}
      <div className={`h-full flex flex-col min-h-0 ${leftPaneContainerClassName}`}>
        {leftPaneContent}
      </div>
      <div className={`h-full flex flex-col min-h-0 ${rightPaneContainerClassName}`}>
        {rightPaneContent}
      </div>
    </div>
  );
};