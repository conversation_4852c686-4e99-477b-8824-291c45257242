import React from 'react';
import { Layout } from './Layout';
import { ReusableDualPaneLayout } from './ReusableDualPaneLayout';

interface ChatInterfaceLayoutProps {
  leftPaneContent: React.ReactNode;
  rightPaneContent: React.ReactNode;
  activeTab: string;
}

/**
 * ChatInterfaceLayout - A specialized layout for dual-pane chat interfaces
 * 
 * This layout provides a fixed-height viewport structure where:
 * - The entire interface fills the viewport minus the header
 * - Both panes maintain fixed heights with internal scrolling
 * - No page-level scrolling occurs
 * 
 * Used by InteractiveInterviewAgent and EditorAgentPage
 */
export function ChatInterfaceLayout({ 
  leftPaneContent, 
  rightPaneContent, 
  activeTab 
}: ChatInterfaceLayoutProps) {
  return (
    <Layout noShadowContainer activeTab={activeTab}>
      <div className="h-full flex flex-col">
        <ReusableDualPaneLayout
          leftPaneContent={leftPaneContent}
          rightPaneContent={rightPaneContent}
          // CRITICAL: Use flex-1 min-h-0 instead of flex-grow to properly constrain height
          // IMPORTANT: Using centralized spacing class for consistent layout
          className="chat-interface-layout-margin-y-32px flex-1 min-h-0"
        />
      </div>
    </Layout>
  );
}