import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../ui/breadcrumb';
import { tabOptions } from '../../data/tab-options';
import { findRouteConfig } from '../../config/routes';
import { navigateWithTransition } from '../../utils/navigation';

interface BreadcrumbNavProps {
  activeTab?: string;
  className?: string;
}

/**
 * BreadcrumbNav component for navigation breadcrumbs
 *
 * Shows the current location in the app hierarchy:
 * Home -> Content Agent (for content generation)
 * Home -> [Page Name] (for other pages)
 */
export function BreadcrumbNav({ activeTab, className = "" }: BreadcrumbNavProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const path = location.pathname;

  // Custom link component that uses navigateWithTransition
  const TransitionLink: React.FC<{ to: string; state?: any; children: React.ReactNode; className?: string }> = ({ 
    to, 
    state, 
    children, 
    className 
  }) => {
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      navigateWithTransition(navigate, to, { state });
    };

    return (
      <a href={to} onClick={handleClick} className={className}>
        {children}
      </a>
    );
  };


  const breadcrumbElements: React.ReactElement[] = [];

  // 1. Home
  if (path === "/" || path === "/home") {
    breadcrumbElements.push(
      <BreadcrumbItem key="home">
        <BreadcrumbPage className="text-white font-medium">Home</BreadcrumbPage>
      </BreadcrumbItem>
    );
  } else {
    breadcrumbElements.push(
      <BreadcrumbItem key="home">
        <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
          <TransitionLink to="/home">Home</TransitionLink>
        </BreadcrumbLink>
      </BreadcrumbItem>
    );
  }

  // 2. Check route configuration for standard routes
  const routeConfig = findRouteConfig(path);
  if (routeConfig) {
    if (path === "/content-agent" || path.startsWith("/content-agent?")) {
      // Check if we're coming from the transcript/ideas flow
      const searchParams = new URLSearchParams(location.search);
      const sessionId = searchParams.get('session');
      const transcriptId = searchParams.get('transcriptId') || location.state?.transcriptId;
      
      if (transcriptId) {
        // Show: Home > Transcript > Ideas > Content Agent
        breadcrumbElements.push(<BreadcrumbSeparator key="sep-transcript" className="text-gray-400" />);
        breadcrumbElements.push(
          <BreadcrumbItem key="transcript">
            <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
              <TransitionLink to="/transcript-input">Transcript</TransitionLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
        );
        breadcrumbElements.push(<BreadcrumbSeparator key="sep-ideas" className="text-gray-400" />);
        breadcrumbElements.push(
          <BreadcrumbItem key="ideas">
            <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
              <TransitionLink to={`/ideas?transcriptId=${transcriptId}`} state={{ transcriptId }}>Ideas</TransitionLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
        );
        breadcrumbElements.push(<BreadcrumbSeparator key="sep-content" className="text-gray-400" />);
        breadcrumbElements.push(
          <BreadcrumbItem key="content-agent">
            <BreadcrumbPage className="text-white font-medium">Content Agent</BreadcrumbPage>
          </BreadcrumbItem>
        );
      } else {
        // Standard content agent breadcrumb
        breadcrumbElements.push(<BreadcrumbSeparator key="sep-content" className="text-gray-400" />);
        breadcrumbElements.push(
          <BreadcrumbItem key="content-agent">
            <BreadcrumbPage className="text-white font-medium">Content Agent</BreadcrumbPage>
          </BreadcrumbItem>
        );
      }
    } else if (path === "/ideas") {
      // Show: Home > Transcript > Ideas
      breadcrumbElements.push(<BreadcrumbSeparator key="sep-transcript" className="text-gray-400" />);
      breadcrumbElements.push(
        <BreadcrumbItem key="transcript">
          <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
            <TransitionLink to="/transcript-input" state={location.state}>Transcript</TransitionLink>
          </BreadcrumbLink>
        </BreadcrumbItem>
      );
      breadcrumbElements.push(<BreadcrumbSeparator key="sep-ideas" className="text-gray-400" />);
      breadcrumbElements.push(
        <BreadcrumbItem key="ideas">
          <BreadcrumbPage className="text-white font-medium">Ideas</BreadcrumbPage>
        </BreadcrumbItem>
      );
    } else if (path === "/editor") {
      // Show: Home > [Source Page] > Post Editor
      breadcrumbElements.push(<BreadcrumbSeparator key="sep-source" className="text-gray-400" />);
      
      // Check if we have source route information
      const sourceRoute = location.state?.sourceRoute;
      
      if (sourceRoute) {
        // Use the provided source route
        breadcrumbElements.push(
          <BreadcrumbItem key="source-page">
            <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
              <TransitionLink to={sourceRoute.path} state={location.state}>
                {sourceRoute.label}
              </TransitionLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
        );
      } else if (location.state?.sourceState?.sessionId) {
        // Legacy support for Content Agent with session
        breadcrumbElements.push(
          <BreadcrumbItem key="content-agent">
            <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
              <TransitionLink 
                to={`/content-agent?session=${location.state.sourceState.sessionId}`}
                state={{
                  ...location.state,
                  sourceState: location.state.sourceState,
                  preservedFromEditor: true
                }}
              >
                Content Agent
              </TransitionLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
        );
      } else {
        // Default to Content Agent if no source specified
        breadcrumbElements.push(
          <BreadcrumbItem key="content-agent">
            <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
              <TransitionLink to="/content-agent" state={location.state}>Content Agent</TransitionLink>
            </BreadcrumbLink>
          </BreadcrumbItem>
        );
      }
      
      breadcrumbElements.push(<BreadcrumbSeparator key="sep-editor" className="text-gray-400" />);
      breadcrumbElements.push(
        <BreadcrumbItem key="editor">
          <BreadcrumbPage className="text-white font-medium">Post Editor</BreadcrumbPage>
        </BreadcrumbItem>
      );
    } else {
      // Handle other routes using configuration
      breadcrumbElements.push(<BreadcrumbSeparator key="sep-route" className="text-gray-400" />);
      breadcrumbElements.push(
        <BreadcrumbItem key={routeConfig.path}>
          <BreadcrumbPage className="text-white font-medium">{routeConfig.breadcrumbLabel}</BreadcrumbPage>
        </BreadcrumbItem>
      );
    }
  }

  // Do not render if only "Home" is present and we are not on the home page,
  // or if somehow no elements (though "Home" is always added).
  // This avoids showing just "Home" as a link on other pages if no other segments apply.

  if (breadcrumbElements.length === 0) {
    return null; // Should not happen as "Home" is always added
  }

  // If only "Home" is in the breadcrumbs and it's just a link (meaning we are not on the home page)
  // and no other breadcrumb segments were added, it might be better to show nothing.
  // This condition is a bit tricky. Let's simplify: always show what's built.
  // The logic above tries to make the last item a BreadcrumbPage.

  return (
    <Breadcrumb className={`flex items-center ${className}`}>
      <BreadcrumbList className="text-white text-sm flex-wrap justify-center items-center">
        {breadcrumbElements}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
