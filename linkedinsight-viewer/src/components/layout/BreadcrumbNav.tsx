import React from 'react';
import { useLocation } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../ui/breadcrumb';
import { useSession } from '../../hooks/useSession';
import { getPageLabel } from '../../config/routes';

interface BreadcrumbNavProps {
  activeTab?: string;
  className?: string;
}

/**
 * BreadcrumbNav component for navigation breadcrumbs
 *
 * Shows the current location in the app hierarchy:
 * Home -> Content Agent (for content generation)
 * Home -> [Page Name] (for other pages)
 */
export function BreadcrumbNav({ activeTab, className = "" }: BreadcrumbNavProps) {
  const location = useLocation();
  const { session, navigate } = useSession();
  const path = location.pathname;

  // Custom link component that uses session-aware navigation
  const TransitionLink: React.FC<{ to: string; children: React.ReactNode; className?: string }> = ({ 
    to, 
    children, 
    className 
  }) => {
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      navigate(to);
    };

    return (
      <a href={to} onClick={handleClick} className={className}>
        {children}
      </a>
    );
  };


  // Use session breadcrumbs if available
  if (session?.breadcrumbs && session.breadcrumbs.length > 0) {
    // Build breadcrumbs from session
    return (
      <Breadcrumb className={`flex items-center ${className}`}>
        <BreadcrumbList className="text-white text-sm flex-wrap justify-center items-center">
          {session.breadcrumbs.map((crumb, index) => {
            const isLast = index === session.breadcrumbs.length - 1;
            const elements: React.ReactElement[] = [];
            
            if (index > 0) {
              elements.push(
                <BreadcrumbSeparator key={`sep-${index}`} className="text-gray-400" />
              );
            }
            
            if (isLast) {
              elements.push(
                <BreadcrumbItem key={crumb.path}>
                  <BreadcrumbPage className="text-white font-medium">
                    {crumb.label}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              );
            } else {
              elements.push(
                <BreadcrumbItem key={crumb.path}>
                  <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
                    <TransitionLink to={crumb.path}>{crumb.label}</TransitionLink>
                  </BreadcrumbLink>
                </BreadcrumbItem>
              );
            }
            
            return elements;
          }).flat()}
        </BreadcrumbList>
      </Breadcrumb>
    );
  }
  
  // Fallback to simple breadcrumbs based on current path
  const breadcrumbElements: React.ReactElement[] = [];
  
  // Always show Home
  if (path === "/" || path === "/home") {
    breadcrumbElements.push(
      <BreadcrumbItem key="home">
        <BreadcrumbPage className="text-white font-medium">Home</BreadcrumbPage>
      </BreadcrumbItem>
    );
  } else {
    breadcrumbElements.push(
      <BreadcrumbItem key="home">
        <BreadcrumbLink asChild className="text-white hover:text-gray-200 font-medium">
          <TransitionLink to="/home">Home</TransitionLink>
        </BreadcrumbLink>
      </BreadcrumbItem>
    );
    
    // Get label from centralized routes config
    const currentLabel = activeTab || getPageLabel(path);
    
    breadcrumbElements.push(<BreadcrumbSeparator key="sep" className="text-gray-400" />);
    breadcrumbElements.push(
      <BreadcrumbItem key="current">
        <BreadcrumbPage className="text-white font-medium">{currentLabel}</BreadcrumbPage>
      </BreadcrumbItem>
    );
  }

  // Do not render if only "Home" is present and we are not on the home page,
  // or if somehow no elements (though "Home" is always added).
  // This avoids showing just "Home" as a link on other pages if no other segments apply.

  if (breadcrumbElements.length === 0) {
    return null; // Should not happen as "Home" is always added
  }

  // If only "Home" is in the breadcrumbs and it's just a link (meaning we are not on the home page)
  // and no other breadcrumb segments were added, it might be better to show nothing.
  // This condition is a bit tricky. Let's simplify: always show what's built.
  // The logic above tries to make the last item a BreadcrumbPage.

  return (
    <Breadcrumb className={`flex items-center ${className}`}>
      <BreadcrumbList className="text-white text-sm flex-wrap justify-center items-center">
        {breadcrumbElements}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
