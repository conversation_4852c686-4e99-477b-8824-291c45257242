import React, { useEffect, useState, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ErrorService } from '../../utils/errorHandling';

interface StreamingMessageProps {
  streamUrl: string;
  onComplete?: (finalText: string) => void;
  onBriefUpdate?: (briefContent: string) => void;
  onError?: (error: string) => void;
  onToolCallStart?: (toolId: string, toolName: string) => void;
  onToolCallResult?: (toolId: string, toolName: string, result: string, isError: boolean) => void;
  showPenCursor?: boolean;
}

export function StreamingMessage({ 
  streamUrl, 
  onComplete, 
  onBriefUpdate,
  onError,
  onToolCallStart,
  onToolCallResult,
  showPenCursor = false
}: StreamingMessageProps) {
  const [text, setText] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState<boolean>(true);
  const [activeToolCalls, setActiveToolCalls] = useState<Array<{id: string, name: string}>>([]);

  // Use refs to hold the latest callback values to avoid stale closures
  const onCompleteRef = useRef(onComplete);
  const onBriefUpdateRef = useRef(onBriefUpdate);
  const onErrorRef = useRef(onError);
  const onToolCallStartRef = useRef(onToolCallStart);
  const onToolCallResultRef = useRef(onToolCallResult);

  // Update refs when callbacks change
  useEffect(() => { onCompleteRef.current = onComplete; }, [onComplete]);
  useEffect(() => { onBriefUpdateRef.current = onBriefUpdate; }, [onBriefUpdate]);
  useEffect(() => { onErrorRef.current = onError; }, [onError]);
  useEffect(() => { onToolCallStartRef.current = onToolCallStart; }, [onToolCallStart]);
  useEffect(() => { onToolCallResultRef.current = onToolCallResult; }, [onToolCallResult]);

  useEffect(() => {
    // Prevent duplicate connections
    if (!streamUrl || !isStreaming) return;
    
    let es: EventSource | null = null;
    
    // Add authentication token to URL
    const authenticateUrl = async () => {
      try {
        // Get Clerk token if available
        let authenticatedUrl = streamUrl;
        if (typeof window !== 'undefined' && window.Clerk && window.Clerk.session) {
          const token = await window.Clerk.session.getToken({ template: 'api_email' });
          if (token) {
            // Add token as query parameter
            const url = new URL(streamUrl, window.location.origin);
            url.searchParams.set('token', token);
            authenticatedUrl = url.toString();
          }
        }
        
        console.log(`Opening EventSource connection to: ${authenticatedUrl}`);
        es = new EventSource(authenticatedUrl);
        let fullText = '';

        // Handle content deltas
        es.addEventListener('content_delta', (e) => {
          try {
            const data = JSON.parse(e.data);
            if (data.text) {
              fullText += data.text;
              setText(fullText);
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing content_delta');
          }
        });

        // Handle brief updates
        es.addEventListener('brief_update', (e) => {
          try {
            const data = JSON.parse(e.data);
            if (data.content && onBriefUpdateRef.current) {
              onBriefUpdateRef.current(data.content);
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing brief_update');
          }
        });

        // Handle post updates (for editor)
        es.addEventListener('post_update', (e) => {
          try {
            const data = JSON.parse(e.data);
            if (data.content && onBriefUpdateRef.current) {
              onBriefUpdateRef.current(data.content);
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing post_update');
          }
        });

        // Handle tool call start
        es.addEventListener('tool_call_start', (e) => {
          try {
            const data = JSON.parse(e.data);
            if (data.tool_id && data.tool_name) {
              setActiveToolCalls(prev => [...prev, { id: data.tool_id, name: data.tool_name }]);
              if (onToolCallStartRef.current) {
                onToolCallStartRef.current(data.tool_id, data.tool_name);
              }
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing tool_call_start');
          }
        });

        // Handle tool call result
        es.addEventListener('tool_call_result', (e) => {
          try {
            const data = JSON.parse(e.data);
            if (data.tool_id) {
              setActiveToolCalls(prev => prev.filter(tc => tc.id !== data.tool_id));
              if (onToolCallResultRef.current) {
                onToolCallResultRef.current(data.tool_id, data.tool_name, data.result, data.is_error);
              }
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing tool_call_result');
          }
        });

        // Handle stream completion
        es.addEventListener('stream_complete', (e) => {
          try {
            const data = JSON.parse(e.data);
            
            if (data.brief_update && onBriefUpdateRef.current) {
              onBriefUpdateRef.current(data.brief_update);
            }
            
            setIsStreaming(false);
            if (es) {
              es.close();
            }
            
            if (onCompleteRef.current) {
              onCompleteRef.current(fullText);
            }
          } catch (err) {
            ErrorService.logError(err, 'Error parsing stream_complete');
          }
        });

        // Handle errors
        es.onerror = (error) => {
          ErrorService.logError(error, 'EventSource error');
          if (es && es.readyState === EventSource.CONNECTING) {
            // Don't treat reconnection attempts as errors
            return;
          }
          if (es && es.readyState !== EventSource.CLOSED && onErrorRef.current) {
            onErrorRef.current('Connection to stream lost');
          }
          setIsStreaming(false);
          if (es) {
            es.close();
          }
        };

      } catch (error) {
        ErrorService.logError(error, 'Error setting up EventSource');
        if (onErrorRef.current) {
          onErrorRef.current('Failed to establish connection');
        }
        setIsStreaming(false);
      }
    };

    authenticateUrl();

    // Cleanup
    return () => {
      if (es) {
        console.log(`Closing EventSource connection to: ${streamUrl}`);
        es.close();
      }
    };
  }, [streamUrl]); // Only depend on streamUrl to prevent recreating connection

  // Determine if we should show status and what message to display
  const showStatus = (isStreaming && text.length < 10) || activeToolCalls.length > 0;
  const statusMessage = activeToolCalls.length > 0 ? 'Working on brief...' : 'Thinking...';

  return (
    <>
      <ReactMarkdown remarkPlugins={[remarkGfm]}>
        {text}
      </ReactMarkdown>
      {showStatus && (
        <span className="streaming-status-indicator">
          <span className="status-spinner"></span>
          <span>{statusMessage}</span>
        </span>
      )}
    </>
  );
}