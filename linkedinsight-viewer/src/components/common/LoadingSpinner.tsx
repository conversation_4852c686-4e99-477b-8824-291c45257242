import React from 'react';

interface LoadingSpinnerProps {
  primaryText?: string;
  secondaryText?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * A loading spinner component with primary and secondary text indicators.
 * Shows task status information during async operations.
 */
export function LoadingSpinner({
  primaryText = 'Loading...',
  secondaryText,
  className = '',
  size = 'md'
}: LoadingSpinnerProps) {
  // Size mappings
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className={`flex items-center ${className}`}>
      <svg 
        className={`animate-spin ${sizeClasses[size]} text-black dark:text-white`} 
        viewBox="0 0 24 24"
      >
        <circle 
          className="opacity-25" 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          strokeWidth="4"
          fill="none"
        ></circle>
        <path 
          className="opacity-75" 
          fill="currentColor" 
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      
      {(primaryText || secondaryText) && (
        <div className="ml-2 font-mono">
          {primaryText && (
            <div className="text-black dark:text-white">
              {primaryText}
            </div>
          )}
          {secondaryText && (
            <div className="text-xs text-black/70 dark:text-white/70 mt-0.5">
              {secondaryText}
            </div>
          )}
        </div>
      )}
    </div>
  );
} 