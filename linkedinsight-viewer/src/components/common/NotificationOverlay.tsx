/**
 * NotificationOverlay Component
 * 
 * In-app notification system with custom sound support
 */

import React, { useEffect, useState } from 'react';
import { X } from 'lucide-react';

export interface NotificationData {
  id: string;
  title: string;
  body?: string;
  icon?: string;
  duration?: number;
  playSound?: boolean;
  soundUrl?: string;
}

interface NotificationOverlayProps {
  notification: NotificationData | null;
  onDismiss: () => void;
}

export const NotificationOverlay: React.FC<NotificationOverlayProps> = ({
  notification,
  onDismiss
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    if (notification) {
      // Show notification
      setIsVisible(true);
      setIsLeaving(false);

      // Play sound if enabled
      if (notification.playSound && notification.soundUrl) {
        const audio = new Audio(notification.soundUrl);
        audio.volume = 0.3;
        audio.play().catch(err => {
          console.warn('Failed to play notification sound:', err);
        });
      }

      // Auto-dismiss after duration
      const duration = notification.duration || 5000;
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [notification]);

  const handleDismiss = () => {
    setIsLeaving(true);
    setTimeout(() => {
      setIsVisible(false);
      setIsLeaving(false);
      onDismiss();
    }, 300); // Match animation duration
  };

  if (!isVisible || !notification) return null;

  return (
    <div
      className={`fixed top-4 right-4 z-50 transition-all duration-300 ${
        isLeaving ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'
      }`}
      style={{
        animation: !isLeaving ? 'slideIn 0.3s ease-out' : undefined
      }}
    >
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px]">
        <div className="flex items-start gap-3">
          {notification.icon && (
            <span className="text-2xl flex-shrink-0">{notification.icon}</span>
          )}
          <div className="flex-grow">
            <h4 className="font-semibold text-foreground">{notification.title}</h4>
            {notification.body && (
              <p className="text-sm text-muted-foreground mt-1">{notification.body}</p>
            )}
          </div>
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors"
            aria-label="Dismiss notification"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Global notification state manager
let notificationListener: ((notification: NotificationData | null) => void) | null = null;

export const showInAppNotification = (notification: Omit<NotificationData, 'id'>) => {
  const notificationWithId: NotificationData = {
    ...notification,
    id: `notification-${Date.now()}`
  };
  
  if (notificationListener) {
    notificationListener(notificationWithId);
  }
};

export const useNotificationOverlay = () => {
  const [notification, setNotification] = useState<NotificationData | null>(null);

  useEffect(() => {
    notificationListener = setNotification;
    return () => {
      notificationListener = null;
    };
  }, []);

  const dismiss = () => setNotification(null);

  return { notification, dismiss };
};