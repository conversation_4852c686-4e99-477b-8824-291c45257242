import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';

interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  fullScreen?: boolean;
  className?: string;
}

/**
 * LoadingState component
 * 
 * Standardized loading state display with optional message.
 * Can be used inline or as a full-screen loading state.
 */
export function LoadingState({ 
  message = 'Loading...', 
  size = 'medium',
  fullScreen = false,
  className = ''
}: LoadingStateProps) {
  // Map LoadingState sizes to LoadingSpinner sizes
  const sizeMap: Record<'small' | 'medium' | 'large', 'sm' | 'md' | 'lg'> = {
    small: 'sm',
    medium: 'md',
    large: 'lg'
  };

  const content = (
    <div className={`flex flex-col items-center justify-center gap-4 ${className}`}>
      <LoadingSpinner size={sizeMap[size]} />
      {message && (
        <p className={`text-gray-600 dark:text-gray-400 ${
          size === 'small' ? 'text-sm' : size === 'large' ? 'text-lg' : 'text-base'
        }`}>
          {message}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
}