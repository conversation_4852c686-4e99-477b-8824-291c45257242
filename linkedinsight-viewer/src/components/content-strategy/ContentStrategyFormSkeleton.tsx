import React from 'react';
import { FormContainer } from '../ui/form-container';

/**
 * Skeleton loading state for ContentStrategyForm
 * Follows the existing skeleton pattern using Tailwind animate-pulse
 */
export function ContentStrategyFormSkeleton() {
  return (
    <FormContainer
      title="Set Your Content Strategy"
      error={null}
      isLoading={false}
      loadingText=""
      onSubmit={(e) => e.preventDefault()}
      submitText="Save Strategy"
      isSubmitting={false}
      formClassName="form-container form-container-balanced animate-pulse"
    >
      {/* Industry field skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-24" />
        <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>

      {/* Offering textarea skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-48" />
        <div className="h-32 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>

      {/* Target Audience textarea skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-64" />
        <div className="h-32 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>

      {/* Key Differentiators textarea skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-40" />
        <div className="h-32 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>

      {/* Primary content goal field skeleton */}
      <div className="space-y-1">
        <div className="h-3.5 bg-gray-300 dark:bg-gray-700 rounded w-36" />
        <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-full" />
      </div>
    </FormContainer>
  );
}

export default ContentStrategyFormSkeleton;