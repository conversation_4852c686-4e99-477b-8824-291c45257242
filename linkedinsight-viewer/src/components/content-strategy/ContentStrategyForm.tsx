import { useState, ChangeEvent, FormEvent, useEffect } from 'react';
import { FormContainer } from '../ui/form-container';
import { FormField } from '../ui/form-field';
import { useContentStrategy, useUpdateContentStrategy } from '../../hooks/useContentStrategyQueries';

interface ContentStrategyFormData {
  industry: string;
  offering: string;
  targetAudience: string;
  differentiators: string; // Changed from keyDifferentiators for consistency with backend model
  contentGoal: string;
}

// API response type matching backend's ContentStrategy model with camelCase fields
interface ContentStrategyApiResponse {
  clerkUserId?: string;
  industry: string;
  offering: string;
  targetAudience: string;
  differentiators: string;
  contentGoal: string;
}

const initialFormData: ContentStrategyFormData = {
  industry: '',
  offering: '',
  targetAudience: '',
  differentiators: '',
  contentGoal: ''
};

export function ContentStrategyForm() {
  const [formData, setFormData] = useState<ContentStrategyFormData>(initialFormData);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch strategy using TanStack Query
  const { 
    data: strategy, 
    isLoading: isLoadingStrategy,
    isError: strategyError,
    error: strategyQueryError 
  } = useContentStrategy();
  
  // Mutation for saving strategy
  const updateStrategyMutation = useUpdateContentStrategy();

  // Set form data when strategy is loaded
  useEffect(() => {
    if (strategy && typeof strategy === 'object' && Object.keys(strategy).length > 0) {
      setFormData({
        industry: strategy.industry || '',
        offering: strategy.offering || '',
        targetAudience: strategy.targetAudience || '',
        differentiators: strategy.differentiators || '',
        contentGoal: strategy.contentGoal || '',
      });
    }
  }, [strategy]);
  
  // Handle errors
  useEffect(() => {
    if (strategyError && strategyQueryError) {
      const err = strategyQueryError as any;
      if (err?.apiError?.status === 404) {
        // No strategy yet - this is OK, user will create one
        // Don't show error for 404 - empty form is self-explanatory
        setError(null);
      } else {
        const errorMessage = err?.apiError?.body?.detail || 
                           err?.apiError?.body?.message || 
                           err?.message || 
                           'Failed to load strategy';
        setError(errorMessage);
      }
    } else {
      setError(null);
    }
  }, [strategyError, strategyQueryError]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);

    try {
      const result = await updateStrategyMutation.mutateAsync(formData);
      
      // Update form with the response data
      if (result && typeof result === 'object') {
        setFormData({
          industry: result.industry || '',
          offering: result.offering || '',
          targetAudience: result.targetAudience || '',
          differentiators: result.differentiators || '',
          contentGoal: result.contentGoal || '',
        });
      }
    } catch (err: any) {
      // Error is already handled by the mutation hook
      setError(err.message || 'Failed to save strategy');
    }
  };

  return (
    <FormContainer
      title="Set Your Content Strategy"
      error={error}
      isLoading={isLoadingStrategy}
      loadingText="Loading content strategy..."
      onSubmit={handleSubmit}
      submitText={updateStrategyMutation.isPending ? 'Saving Strategy...' : 'Save Strategy'}
      isSubmitting={updateStrategyMutation.isPending}
      formClassName="form-container form-container-balanced"
    >
        <FormField
          label="Your industry"
          id="industry"
          name="industry"
          value={formData.industry}
          onChange={handleInputChange}
          placeholder="e.g., SaaS, Real Estate, Coaching"
          required
        />

        <FormField
          label="Your offering (product/service)"
          id="offering"
          name="offering"
          type="textarea"
          rows={3}
          value={formData.offering}
          onChange={handleInputChange}
          placeholder="Briefly describe what you sell or do, e.g., 'AI-powered content generation for B2B marketers'"
          required
        />

        <FormField
          label="Your target audience (ideal customer profile)"
          id="targetAudience"
          name="targetAudience"
          type="textarea"
          rows={3}
          value={formData.targetAudience}
          onChange={handleInputChange}
          placeholder="e.g., CMOs at tech startups, HR Managers in enterprise companies, Solopreneurs in creative fields"
          required
        />

        <FormField
          label="Key differentiators (1-2 points)"
          id="differentiators"
          name="differentiators"
          type="textarea"
          rows={3}
          value={formData.differentiators}
          onChange={handleInputChange}
          placeholder="e.g., 1. 15+ years direct experience. 2. Focus on transparent, ethical AI."
          required
        />

        <FormField
          label="Primary content goal"
          id="contentGoal"
          name="contentGoal"
          value={formData.contentGoal}
          onChange={handleInputChange}
          placeholder="e.g., Generate qualified leads, Build thought leadership"
          required
        />
    </FormContainer>
  );
}

export default ContentStrategyForm;