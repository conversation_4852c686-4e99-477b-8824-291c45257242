import React from 'react';
import { Header } from '../ui/header';

interface ContentStrategyLayoutProps {
  children: React.ReactNode;
}

export function ContentStrategyLayout({ children }: ContentStrategyLayoutProps) {

  return (
    <div className="h-screen flex flex-col font-archivo overflow-hidden" style={{ backgroundColor: 'var(--surface-window)' }}>
      <Header activeTab="Content Strategy" />
      <div className="flex-1 flex flex-col justify-center mt-[3rem] px-4 md:px-8 overflow-auto">
        {children}
      </div>
    </div>
  );
}