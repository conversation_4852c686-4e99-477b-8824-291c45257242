import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ChatInterfaceLayout } from '../layout/ChatInterfaceLayout';
import { Box } from '../ui/Box';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Button } from '../ui/button';
import { GenerateButton } from '../ui/generate-button';
import { useToast } from '../ui/toast-provider';
import { LengthSelectorCompact } from '../ui/length-selector-compact';
import { ChatHistory } from '../chat/ChatHistory';
import { ChatInput, ChatInputRef } from '../chat/ChatInput';
import { ChatMessage, InterviewSession } from '../../types/agent';
import apiClient from '../../services/apiClient';
import { useSession } from '../../hooks/useSession';

const InteractiveInterviewAgent: React.FC = () => {
  // State for the component
  const [userInput, setUserInput] = useState<string>('');
  const chatInputRef = useRef<ChatInputRef>(null);
  const [session, setSession] = useState<InterviewSession>({
    sessionId: '',
    messages: [],
    briefContent: '',
    isLoading: false,
    error: null,
  });
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [selectedLength, setSelectedLength] = useState<string>('medium');
  const [isBriefUpdating, setIsBriefUpdating] = useState<boolean>(false);
  const briefUpdateTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Hooks
  const toast = useToast();
  const { session: appSession, updateSession, navigate } = useSession();

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (briefUpdateTimeoutRef.current) {
        clearTimeout(briefUpdateTimeoutRef.current);
      }
    };
  }, []);

  // Auto-focus input when it's empty and not loading
  useEffect(() => {
    if (!userInput && !session.isLoading && chatInputRef.current) {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(() => {
        chatInputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [userInput, session.isLoading]);

  // Memoized callback for handling stream completion
  const handleStreamComplete = useCallback((messageId: string, finalText: string) => {
    setSession(prev => ({
      ...prev,
      messages: prev.messages.map(m => 
        m.id === messageId ? { ...m, text: finalText, isStreaming: false } : m
      )
    }));
  }, []);

  // Memoized callback for brief updates
  const handleBriefUpdate = useCallback((briefContent: string) => {
    setSession(prev => ({ ...prev, briefContent }));
  }, []);

  // Memoized callback for errors
  const handleStreamError = useCallback((error: string) => {
    console.error('Streaming error:', error);
    setSession(prev => ({ ...prev, error }));
  }, []);

  // Memoized callbacks for tool events
  const handleToolCallStart = useCallback((toolId: string, toolName: string) => {
    console.log(`Tool ${toolName} started (${toolId})`);
    
    // Clear any existing timeout
    if (briefUpdateTimeoutRef.current) {
      clearTimeout(briefUpdateTimeoutRef.current);
      briefUpdateTimeoutRef.current = null;
    }
    
    setIsBriefUpdating(true);
  }, []);

  const handleToolCallResult = useCallback((toolId: string, toolName: string, result: string, _isError: boolean) => {
    console.log(`Tool ${toolName} completed (${toolId}):`, result);
    
    // Keep the indicator visible for at least 1.5 seconds
    briefUpdateTimeoutRef.current = setTimeout(() => {
      setIsBriefUpdating(false);
    }, 1500);
  }, []);

  // Handler for sending a message to the agent
  const handleSendMessage = async () => {
    if (!userInput.trim()) return;

    try {
      // Update local state immediately for responsive UI
      const newUserMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        text: userInput,
        timestamp: new Date(),
      };

      setSession((prevSession) => ({
        ...prevSession,
        messages: [...prevSession.messages, newUserMessage],
        isLoading: true,
        error: null,
      }));

      // Clear input after sending
      setUserInput('');

      // Make API call to backend using apiClient
      interface InterviewChatResponse {
        sessionId: string;
        messageId: string;
      }

      const responseData = await apiClient.post<InterviewChatResponse>('api/interview/turn', {
        sessionId: session.sessionId || null,
        userMessage: newUserMessage.text,
      });

      if (!responseData) {
        throw new Error('No response from server');
      }

      // Create a new agent message with streaming capabilities
      const messageId = responseData.messageId || `agent-${Date.now()}`;
      const streamUrl = `${apiClient.getBaseUrl()}/api/interview/stream/${responseData.sessionId}/${messageId}`;
      
      const newAgentMessage: ChatMessage = {
        id: messageId,
        type: 'agent',
        text: '', // Text will be filled by streaming
        timestamp: new Date(),
        isStreaming: true,
        streamUrl: streamUrl,
      };

      setSession((prevSession) => ({
        ...prevSession,
        sessionId: responseData.sessionId,
        messages: [...prevSession.messages, newAgentMessage],
        isLoading: false,
      }));
    } catch (error: any) {
      console.error('Error sending message:', error);
      
      let errorMessage = 'An unknown error occurred';
      if (error.apiError) {
        errorMessage = error.apiError.body?.detail || 
                      error.apiError.body?.message || 
                      `Failed to send message: ${error.apiError.statusText || error.message}`;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setSession((prevSession) => ({
        ...prevSession,
        isLoading: false,
        error: errorMessage,
      }));
    }
  };


  // Handler for generating post
  const handleGeneratePost = async () => {
    if (!session.briefContent || session.briefContent.trim() === '') {
      toast.error('No brief content available', {
        description: 'Please continue the interview to build your brief.'
      });
      return;
    }

    setIsGenerating(true);

    // Update session with interview data
    await updateSession({
      flow: 'interview',
      interview: {
        messages: session.messages.map(m => ({
          id: m.id,
          type: m.type,
          text: m.text,
          timestamp: m.timestamp
        })),
        brief: session.briefContent,
        sessionId: session.sessionId
      },
      brief: session.briefContent,
      postLength: selectedLength as 'short' | 'medium' | 'long',
      step: 'content'
    });

    // Navigate to content-agent page
    navigate('/content-agent');

    setIsGenerating(false);
  };

  // Render the conversation pane (left side)
  const renderConversationPane = () => (
    <Box shadow className="h-full relative">
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0">
        Conversation
      </div>

      <div className="absolute top-16 bottom-24 left-0 right-0 px-4">
        <ChatHistory
          messages={session.messages}
          isLoading={session.isLoading}
          error={session.error}
          onStreamComplete={handleStreamComplete}
          onBriefUpdate={handleBriefUpdate}
          onError={handleStreamError}
          onToolCallStart={handleToolCallStart}
          onToolCallResult={handleToolCallResult}
          showPenCursor={isBriefUpdating}
          className="h-full"
          hasExistingContent={!!session.briefContent}
        />
      </div>

      <div 
        className="absolute bottom-0 left-0 right-0 p-4" 
        style={{ 
          backgroundColor: 'var(--surface-primary)',
          borderBottomLeftRadius: 'var(--radius-lg)',
          borderBottomRightRadius: 'var(--radius-lg)'
        }}
      >
        <ChatInput
          ref={chatInputRef}
          value={userInput}
          onChange={setUserInput}
          onSubmit={handleSendMessage}
          disabled={session.isLoading}
        />
      </div>
      
    </Box>
  );

  // Render the brief pane (right side)
  const renderBriefPane = () => (
    <Box shadow className="h-full flex flex-col">
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0">
        Outline
      </div>

      <div className="flex-grow overflow-y-auto">
        {session.briefContent ? (
          <div 
            className="prose dark:prose-invert max-w-none p-4"
          >
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {session.briefContent}
            </ReactMarkdown>
          </div>
        ) : (
          <div className="text-center text-gray-500 dark:text-gray-400 italic my-8">
            Your outline will appear here as you chat...
          </div>
        )}
      </div>

      {session.briefContent && (
        <div className="bottom-action-panel">
          <LengthSelectorCompact 
            value={selectedLength}
            onChange={setSelectedLength}
          />
          <GenerateButton
            onGenerate={handleGeneratePost}
            disabled={session.isLoading}
            variant="primary"
            size="md"
            className="w-full"
            loadingText="Getting ready..."
          >
            Generate Post
          </GenerateButton>
        </div>
      )}
    </Box>
  );

  return (
    <ChatInterfaceLayout
      leftPaneContent={renderConversationPane()}
      rightPaneContent={renderBriefPane()}
      activeTab="Interactive Interview"
    />
  );
};

export default InteractiveInterviewAgent;