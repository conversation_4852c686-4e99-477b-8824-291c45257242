import React from 'react';
import { ReusableDualPaneLayout } from '../layout/ReusableDualPaneLayout';
import { Box } from '../ui/Box';

export const InterviewingAgentView: React.FC = () => {
  // Left pane content - Conversation
  const conversationContent = (
    <div className="h-full flex flex-col">
      <Box shadow className="flex-grow min-h-0 overflow-hidden flex flex-col mb-4">
        <div className="font-bold px-6 pt-6 pb-4 text-xl font-ibm-plex-mono dark:text-neutral-100">Conversation</div>
        
        <div className="flex-grow min-h-0 overflow-y-auto px-6">
          <div className="space-y-4 pb-4">
            <div>
              <div className="font-semibold">User:</div>
              <p>I need help creating content for my new software product launch.</p>
            </div>
            
            <div>
              <div className="font-semibold">Agent:</div>
              <p>I&apos;d be happy to help with your software product launch. Could you tell me more about your product and target audience?</p>
            </div>
            
            <div>
              <div className="font-semibold">User:</div>
              <p>It&apos;s a project management tool for remote teams with built-in time tracking.</p>
            </div>
            
            <div>
              <div className="font-semibold">Agent:</div>
              <p>That sounds useful! What are the key features that make your tool stand out from competitors?</p>
            </div>
            <div>
              <div className="font-semibold">User:</div>
              <p>It integrates directly with popular calendars and offers AI-powered task suggestions based on project scope.</p>
            </div>
            <div>
              <div className="font-semibold">Agent:</div>
              <p>Impressive! And what&apos;s the primary goal for this content? Brand awareness, lead generation, or something else?</p>
            </div>
          </div>
        </div>
      </Box>
      
      <div className="flex-shrink-0 p-4" style={{ backgroundColor: 'var(--surface-primary)' }}>
        <input 
          type="text" 
          disabled 
          placeholder="Type your message here..." 
          className="chat-input"
        />
      </div>
    </div>
  );
  
  // Right pane content - Live Brief
  const liveBriefContent = (
    <Box shadow className="h-full overflow-hidden flex flex-col">
      <div className="font-bold px-6 pt-6 pb-4 text-xl font-ibm-plex-mono dark:text-neutral-100">Live Brief</div>
      
      <div className="flex-grow min-h-0 overflow-y-auto px-6 pb-6">
        <div className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2">Key Objectives</h3>
          <p>Create engaging and informative content for the upcoming launch of &quot;TaskFlow&quot;, a new project management tool specifically designed for remote teams. The primary goal is to drive initial sign-ups and generate buzz within the target communities.</p>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Target Audience</h3>
          <p>Project managers, team leads, startup founders, and freelance professionals who predominantly work in or manage remote teams. They are tech-savvy and looking for tools to improve efficiency and collaboration.</p>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Tone of Voice</h3>
          <p>Professional, yet approachable and slightly informal. Confident, innovative, and helpful. Avoid overly technical jargon. Focus on benefits and solutions.</p>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Key Features to Highlight</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>Seamless project and task management boards.</li>
            <li>Integrated smart time tracking per task and project.</li>
            <li>AI-powered task suggestions based on project scope and historical data.</li>
            <li>Direct calendar integration (Google Calendar, Outlook Calendar).</li>
            <li>Real-time collaboration tools (comments, file sharing, mentions).</li>
            <li>Customizable reporting dashboards.</li>
          </ul>
        </div>
        <div>
          <h3 className="font-semibold mb-2">Call to Action</h3>
          <p>Encourage users to sign up for early access or a free trial. Highlight a launch discount or special offer for early adopters.</p>
        </div>
        </div>
      </div>
    </Box>
  );
  
  return (
    <ReusableDualPaneLayout
      leftPaneContent={conversationContent}
      rightPaneContent={liveBriefContent}
    />
  );
}; 