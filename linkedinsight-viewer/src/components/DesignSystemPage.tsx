import React from 'react';
import { Layout } from './layout/Layout';
import DesignTokenExplorer from './ui/DesignTokenExplorer';
import { Button } from './ui/button';
import { Box } from './ui/Box';

/**
 * DesignSystemPage component
 * 
 * This page displays the design system tokens and components for testing and verification.
 */
const DesignSystemPage: React.FC = () => {
  return (
    <Layout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8">Design System</h1>
        
        <Box shadow className="mb-8">
          <DesignTokenExplorer />
        </Box>
        
        <Box shadow className="mb-8">
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Button Component</h2>
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Default Button</Button>
              <Button variant="primary">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="outline">Outline Button</Button>
            </div>
          </div>
        </Box>
      </div>
    </Layout>
  );
};

export default DesignSystemPage;
