import React from 'react';

interface ChatMessageSkeletonProps {
  type?: 'user' | 'agent';
}

export function ChatMessageSkeleton({ type = 'agent' }: ChatMessageSkeletonProps) {
  const isUser = type === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} animate-pulse`}>
      <div className={`max-w-[80%] ${isUser ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-gray-50 dark:bg-gray-800'} rounded-lg p-4`}>
        <div className="flex items-start gap-3">
          {!isUser && (
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-700 rounded-full flex-shrink-0"></div>
          )}
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function ChatHistorySkeleton() {
  return (
    <div className="space-y-4">
      <ChatMessageSkeleton type="user" />
      <ChatMessageSkeleton type="agent" />
      <ChatMessageSkeleton type="user" />
      <ChatMessageSkeleton type="agent" />
    </div>
  );
}