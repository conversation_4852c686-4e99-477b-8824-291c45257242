import React, { useEffect, useRef, useState } from 'react';
import { DiffViewer } from '../shared/DiffViewer';

interface DiffEditorProps {
  originalContent: string;
  currentContent: string;
  onChange: (content: string) => void;
  placeholder?: string;
}

export function DiffEditor({ originalContent, currentContent, onChange, placeholder }: DiffEditorProps) {
  const [showDiff, setShowDiff] = useState(true);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const diffRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Save scroll position before toggling
  const handleToggleShowDiff = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Save current scroll position
    if (showDiff && diffRef.current) {
      scrollPositionRef.current = diffRef.current.scrollTop;
    } else if (!showDiff && textareaRef.current) {
      scrollPositionRef.current = textareaRef.current.scrollTop;
    }
    
    setShowDiff(!e.target.checked);
  };

  // Restore scroll position after toggling
  useEffect(() => {
    if (showDiff && diffRef.current) {
      diffRef.current.scrollTop = scrollPositionRef.current;
    } else if (!showDiff && textareaRef.current) {
      textareaRef.current.scrollTop = scrollPositionRef.current;
    }
  }, [showDiff]);

  // Diagnostic logging to identify height overflow issues (development only)
  useEffect(() => {
    if (import.meta.env.DEV) {
      const logHeights = () => {
        const container = containerRef.current;
        const diff = diffRef.current;
        const textarea = textareaRef.current;
        console.log('--- DiffEditor height diagnostic ---');
        console.log('window.innerHeight:', window.innerHeight);
        if (container) {
          console.log('container - offsetHeight:', container.offsetHeight, 'scrollHeight:', container.scrollHeight);
        }
        if (showDiff && diff) {
          console.log('diff div - offsetHeight:', diff.offsetHeight, 'scrollHeight:', diff.scrollHeight);
        }
        if (!showDiff && textarea) {
          console.log('textarea - offsetHeight:', textarea.offsetHeight, 'scrollHeight:', textarea.scrollHeight);
        }
        console.log('-------------------------------------');
      };

      logHeights(); // Initial log
      window.addEventListener('resize', logHeights);
      return () => window.removeEventListener('resize', logHeights);
    }
  }, [showDiff]);

  return (
    // Container uses flex-1 min-h-0 to properly constrain height in parent flex layout
    <div ref={containerRef} className="flex-1 min-h-0 flex flex-col px-4">
      <div className="flex justify-between items-center mb-2 px-4">
        <label className="flex items-center gap-2 text-sm">
          <input
            type="checkbox"
            checked={!showDiff}
            onChange={handleToggleShowDiff}
            className="rounded accent-black"
          />
          Hide changes
        </label>
      </div>
      
      {showDiff ? (
        // IMPORTANT: draft-textarea class is required here for consistent height constraints
        // DO NOT remove it - it ensures the diff viewer respects the flex container bounds
        <div 
          ref={diffRef}
          className="draft-textarea flex-1 min-h-0 p-4 overflow-auto font-mono text-sm"
        >
          <DiffViewer 
            originalContent={originalContent} 
            currentContent={currentContent}
          />
        </div>
      ) : (
        // Textarea: matching classes ensure consistent height/overflow behavior
        <textarea
          ref={textareaRef}
          value={currentContent}
          onChange={(e) => onChange(e.target.value)}
          className="draft-textarea flex-1 min-h-0 p-4 overflow-auto font-mono text-sm resize-none"
          placeholder={placeholder}
        />
      )}
    </div>
  );
}