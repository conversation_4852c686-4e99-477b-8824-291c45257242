import React from 'react';
import { ChatInterfaceLayout } from '../layout/ChatInterfaceLayout';
import { Box } from '../ui/Box';
import { ChatHistorySkeleton } from './ChatMessageSkeleton';
import { EditorPaneSkeleton } from './EditorPaneSkeleton';

export function EditorAgentSkeleton() {
  // Render the chat pane skeleton
  const renderChatPaneSkeleton = () => (
    <Box shadow className="h-full relative editor-pane">
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0 animate-pulse">
        <div className="h-7 bg-gray-300 dark:bg-gray-700 rounded w-48"></div>
      </div>

      <div className="absolute top-16 bottom-24 left-0 right-0 px-4 overflow-y-auto">
        <ChatHistorySkeleton />
      </div>

      <div className="absolute bottom-0 left-0 right-0 p-4" style={{ backgroundColor: 'var(--surface-primary)' }}>
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        </div>
      </div>
    </Box>
  );

  // Render the editor pane skeleton
  const renderEditorPaneSkeleton = () => (
    <Box shadow className="flex-1 min-h-0 flex flex-col editor-pane">
      <EditorPaneSkeleton />
    </Box>
  );

  return (
    <ChatInterfaceLayout
      leftPaneContent={renderChatPaneSkeleton()}
      rightPaneContent={renderEditorPaneSkeleton()}
      activeTab="Post Editor"
    />
  );
}