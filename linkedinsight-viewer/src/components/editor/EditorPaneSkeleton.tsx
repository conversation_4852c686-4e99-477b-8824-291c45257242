import React from 'react';

export function EditorPaneSkeleton() {
  return (
    <div className="flex-1 min-h-0 flex flex-col animate-pulse">
      {/* Title skeleton */}
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0 px-4 pt-4">
        <div className="h-7 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
      </div>

      {/* Editor area skeleton */}
      <div className="flex-1 px-4 pb-0 overflow-hidden">
        <div className="h-full bg-gray-100 dark:bg-gray-800 rounded-md p-4 space-y-3">
          {/* Simulate text lines */}
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-11/12"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/5"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
        </div>
      </div>

      {/* Bottom controls skeleton */}
      <div className="flex justify-between items-end px-4 pb-1 pt-4">
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
        <div className="flex gap-2">
          <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
          <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
        </div>
      </div>
    </div>
  );
}