import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChatInterfaceLayout } from '../layout/ChatInterfaceLayout';
import { ChatHistory } from '../chat/ChatHistory';
import { ChatInput } from '../chat/ChatInput';
import { EditorSession, ChatMessage } from '../../types/agent';
import { Button } from '../ui/button';
import { Box } from '../ui/Box';
import { useToast } from '../ui/toast-provider';
import apiClient from '../../services/apiClient';
import { navigateWithTransition } from '../../utils/navigation';
import { DiffEditor } from './DiffEditor';
import { Undo2, Redo2 } from 'lucide-react';
import { EditorAgentSkeleton } from './EditorAgentSkeleton';

interface EditorChatResponse {
  sessionId: string;
  messageId: string;
}

const EditorAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  
  // Get initial content from navigation state
  const initialContent = location.state?.content || '';
  const initialMetadata = location.state?.metadata || {};
  const sourceState = location.state?.sourceState || null; // Preserve original navigation state
  
  const [isInitializing, setIsInitializing] = useState(false);
  const [session, setSession] = useState<EditorSession>({
    sessionId: '',
    messages: [],
    isLoading: false,
    error: null,
    currentContent: initialContent,
    originalContent: initialContent,
    metadata: initialMetadata,
    lastAIContent: initialContent, // Track AI's last edit
    lastUserContent: null // Track content before revert
  });

  // Track if content has been modified
  const hasChanges = session.currentContent !== session.originalContent;
  const charDifference = session.currentContent.length - session.originalContent.length;
  const isReverted = session.lastUserContent !== null && session.currentContent === session.originalContent;
  
  const [userInput, setUserInput] = useState('');

  // Load session if sessionId is provided in metadata
  useEffect(() => {
    const loadSession = async () => {
      if (initialMetadata.sessionId && !session.sessionId) {
        setIsInitializing(true);
        try {
          // Simulate brief initialization delay
          await new Promise(resolve => setTimeout(resolve, 300));
          
          // Update session with loaded data
          setSession(prev => ({
            ...prev,
            sessionId: initialMetadata.sessionId,
            // In real app, would also load messages, content, etc.
          }));
        } catch (error) {
          console.error('Failed to load session:', error);
        } finally {
          setIsInitializing(false);
        }
      }
    };
    
    loadSession();
  }, [initialMetadata.sessionId]);

  const handleSendMessage = useCallback(async () => {
    if (!userInput.trim()) return;
    
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      text: userInput,
      timestamp: new Date()
    };

    setSession(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isLoading: true,
      error: null
    }));
    
    // Clear input
    setUserInput('');

    try {
      const response = await apiClient.post<EditorChatResponse>('/api/agent/editor/chat', {
        sessionId: session.sessionId || null,
        message: userInput,
        currentContent: session.currentContent,
        originalContent: session.originalContent, // Send original content too
        metadata: session.metadata
      });

      if (!response) {
        throw new Error('No response from server');
      }

      const streamUrl = `${apiClient.getBaseUrl()}/api/agent/editor/stream/${response.sessionId}/${response.messageId}`;
      
      const agentMessage: ChatMessage = {
        id: response.messageId,
        type: 'agent',
        text: '',
        timestamp: new Date(),
        isStreaming: true,
        streamUrl
      };

      setSession(prev => ({
        ...prev,
        sessionId: response.sessionId,
        messages: [...prev.messages, agentMessage],
        isLoading: false,
        // If this is a new session and we didn't have originalContent, set it now
        originalContent: prev.originalContent || prev.currentContent
      }));
    } catch (error) {
      // Error sending message
      setSession(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'An error occurred'
      }));
    }
  }, [userInput, session.sessionId, session.currentContent, session.metadata]);

  const handleStreamComplete = useCallback((messageId: string, fullText: string) => {
    setSession(prev => ({
      ...prev,
      messages: prev.messages.map(msg =>
        msg.id === messageId
          ? { ...msg, text: fullText, isStreaming: false, streamUrl: undefined }
          : msg
      )
    }));
  }, []);

  const handlePostUpdate = useCallback((updatedContent: string) => {
    setSession(prev => ({
      ...prev,
      currentContent: updatedContent,
      lastAIContent: updatedContent, // Track this as AI's edit
      lastUserContent: null // IMPORTANT: Clear revert state when AI updates to prevent stale undo/redo
    }));
  }, []);

  const handleContentChange = useCallback((newContent: string) => {
    setSession(prev => ({
      ...prev,
      currentContent: newContent,
      // Clear lastUserContent when user makes new changes after revert
      lastUserContent: prev.lastUserContent !== null && newContent !== prev.originalContent ? null : prev.lastUserContent
    }));
  }, []);

  const handleToggleRevert = useCallback(() => {
    setSession(prev => {
      if (isReverted) {
        // Restore user content
        return {
          ...prev,
          currentContent: prev.lastUserContent || prev.currentContent,
          lastUserContent: null
        };
      } else {
        // Revert to original
        return {
          ...prev,
          currentContent: prev.originalContent,
          lastUserContent: prev.currentContent
        };
      }
    });
  }, [isReverted]);

  const handleCopyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(session.currentContent)
      .then(() => {
        toast.success('Copied to clipboard!', {
          description: 'Your post is ready to paste.'
        });
      })
      .catch(err => {
        toast.error('Failed to copy', {
          description: 'Could not copy the post to clipboard.'
        });
        // Failed to copy
      });
  }, [session.currentContent, toast]);


  // Render the chat pane (left side)
  const renderChatPane = () => (
    <Box shadow className="h-full relative editor-pane">
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0">
        AI Editor Assistant
      </div>

      <div className="absolute top-16 bottom-24 left-0 right-0 px-4 overflow-y-auto">
        <ChatHistory
          messages={session.messages}
          isLoading={session.isLoading}
          error={session.error}
          onStreamComplete={handleStreamComplete}
          onBriefUpdate={handlePostUpdate}
          className="h-auto"
        />
      </div>

      <div className="absolute bottom-0 left-0 right-0 p-4" style={{ backgroundColor: 'var(--surface-primary)' }}>
        <ChatInput
          value={userInput}
          onChange={setUserInput}
          onSubmit={handleSendMessage}
          disabled={session.isLoading}
          placeholder="Ask me to edit or refine the post..."
        />
      </div>

      {session.error && (
        <div className="absolute bottom-20 left-4 right-4">
          <div className="error-message">
            {session.error}
          </div>
        </div>
      )}
    </Box>
  );

  // Render the editor pane (right side)
  const renderEditorPane = () => (
    <Box shadow className="flex-1 min-h-0 flex flex-col editor-pane">
      {/* Title with padding to align with content */}
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0 px-4 pt-4">
        Post Editor
      </div>

      {/* DiffEditor directly in flex container - no wrapper needed to prevent layout issues */}
      <DiffEditor
        originalContent={session.originalContent}
        currentContent={session.currentContent}
        onChange={handleContentChange}
        placeholder="Paste your content here or navigate from a generated post..."
      />

      <div className="flex justify-between items-end px-4 pb-1 pt-4">
        <div className="flex items-center gap-3">
          <p className="text-xs text-[var(--text-tertiary)] font-ibm-plex-mono mb-0">
            {`${session.currentContent.length}/3000 Chars`}
          </p>
          {hasChanges && (
            <span className="text-xs text-[var(--text-tertiary)] font-ibm-plex-mono">
              Modified ({charDifference > 0 ? '+' : ''}{charDifference} chars)
            </span>
          )}
        </div>
        <div className="action-buttons">
          {(hasChanges || isReverted) && (
            <Button
              onClick={handleToggleRevert}
              variant="secondary"
              size="sm"
              className="icon-button"
              title={isReverted ? "Restore changes" : "Revert to original"}
            >
              {isReverted ? <Redo2 className="h-4 w-4" /> : <Undo2 className="h-4 w-4" />}
            </Button>
          )}
          <Button
            onClick={() => {
              if (sourceState?.sessionId) {
                // Navigate back to session URL
                navigateWithTransition(navigate, `/content-agent?session=${sourceState.sessionId}`);
              } else if (sourceState) {
                // Fallback to state-based navigation
                navigateWithTransition(navigate, '/content-agent', { state: sourceState });
              } else {
                // Last resort: browser back
                navigateWithTransition(navigate, -1);
              }
            }}
            variant="secondary"
            size="md"
          >
            Back
          </Button>
          <Button
            onClick={handleCopyToClipboard}
            disabled={!session.currentContent}
            variant="primary"
            size="md"
          >
            Copy
          </Button>
        </div>
      </div>
    </Box>
  );

  // Show skeleton while initializing
  if (isInitializing) {
    return <EditorAgentSkeleton />;
  }

  return (
    <ChatInterfaceLayout
      leftPaneContent={renderChatPane()}
      rightPaneContent={renderEditorPane()}
      activeTab="Post Editor"
    />
  );
};

export default EditorAgentPage;