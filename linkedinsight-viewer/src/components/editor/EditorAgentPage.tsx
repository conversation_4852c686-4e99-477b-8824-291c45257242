import React, { useState, useCallback, useEffect } from 'react';
import { ChatInterfaceLayout } from '../layout/ChatInterfaceLayout';
import { ChatHistory } from '../chat/ChatHistory';
import { ChatInput } from '../chat/ChatInput';
import { EditorSession, ChatMessage } from '../../types/agent';
import { Button } from '../ui/button';
import { Box } from '../ui/Box';
import { useToast } from '../ui/toast-provider';
import apiClient from '../../services/apiClient';
import { DiffEditor } from './DiffEditor';
import { Undo2, Redo2 } from 'lucide-react';
import { EditorAgentSkeleton } from './EditorAgentSkeleton';
import { useSession } from '../../hooks/useSession';

interface EditorChatResponse {
  sessionId: string;
  messageId: string;
}

const EditorAgentPage: React.FC = () => {
  const toast = useToast();
  const { session: appSession, updateSession, navigate, goBack, loading: sessionLoading } = useSession();
  
  // Initialize editor content from session
  const [editorContent, setEditorContent] = useState('');
  const [originalContent, setOriginalContent] = useState('');
  const [lastAIContent, setLastAIContent] = useState('');
  const [lastUserContent, setLastUserContent] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editorSessionId, setEditorSessionId] = useState<string>('');

  // Initialize editor content from session
  useEffect(() => {
    if (!appSession || appSession.step !== 'editor') return;
    
    // Set content from session
    const content = appSession.editedContent || appSession.originalContent || 
                   (appSession.posts && appSession.posts.draft_a) || '';
    const original = appSession.originalContent || content;
    
    setEditorContent(content);
    setOriginalContent(original);
    setLastAIContent(appSession.lastAIContent || content);
    setLastUserContent(appSession.lastUserContent || null);
  }, [appSession]);
  
  // Track if content has been modified
  const hasChanges = editorContent !== originalContent;
  const charDifference = editorContent.length - originalContent.length;
  const isReverted = lastUserContent !== null && editorContent === originalContent;
  
  const [userInput, setUserInput] = useState('');

  const handleSendMessage = useCallback(async () => {
    if (!userInput.trim()) return;
    
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      text: userInput,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);
    
    // Clear input
    setUserInput('');

    try {
      const response = await apiClient.post<EditorChatResponse>('/api/agent/editor/chat', {
        sessionId: editorSessionId || null,
        message: userInput,
        currentContent: editorContent,
        originalContent: originalContent,
        metadata: {
          appSessionId: appSession?.id,
          flow: appSession?.flow,
          step: 'editor'
        }
      });

      if (!response) {
        throw new Error('No response from server');
      }

      // Update editor session ID if new
      if (!editorSessionId && response.sessionId) {
        setEditorSessionId(response.sessionId);
      }

      const streamUrl = `${apiClient.getBaseUrl()}/api/agent/editor/stream/${response.sessionId}/${response.messageId}`;
      
      const agentMessage: ChatMessage = {
        id: response.messageId,
        type: 'agent',
        text: '',
        timestamp: new Date(),
        isStreaming: true,
        streamUrl
      };

      setMessages(prev => [...prev, agentMessage]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      setError(error instanceof Error ? error.message : 'An error occurred');
    }
  }, [userInput, editorSessionId, editorContent, originalContent, appSession]);

  const handleStreamComplete = useCallback((messageId: string, fullText: string) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, text: fullText, isStreaming: false, streamUrl: undefined }
        : msg
    ));
  }, []);

  const handlePostUpdate = useCallback(async (updatedContent: string) => {
    setEditorContent(updatedContent);
    setLastAIContent(updatedContent);
    setLastUserContent(null);
    
    // Update session
    await updateSession({
      editedContent: updatedContent,
      lastAIContent: updatedContent,
      lastUserContent: null
    });
  }, [updateSession]);

  const handleContentChange = useCallback(async (newContent: string) => {
    setEditorContent(newContent);
    
    // Clear lastUserContent when user makes new changes after revert
    if (lastUserContent !== null && newContent !== originalContent) {
      setLastUserContent(null);
    }
    
    // Update session
    await updateSession({
      editedContent: newContent,
      lastUserContent: lastUserContent !== null && newContent !== originalContent ? null : lastUserContent
    });
  }, [originalContent, lastUserContent, updateSession]);

  const handleToggleRevert = useCallback(async () => {
    if (isReverted) {
      // Restore user content
      const contentToRestore = lastUserContent || editorContent;
      setEditorContent(contentToRestore);
      setLastUserContent(null);
      
      await updateSession({
        editedContent: contentToRestore,
        lastUserContent: null
      });
    } else {
      // Revert to original
      setEditorContent(originalContent);
      setLastUserContent(editorContent);
      
      await updateSession({
        editedContent: originalContent,
        lastUserContent: editorContent
      });
    }
  }, [isReverted, editorContent, originalContent, lastUserContent, updateSession]);

  const handleCopyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(editorContent)
      .then(() => {
        toast.success('Copied to clipboard!', {
          description: 'Your post is ready to paste.'
        });
      })
      .catch(err => {
        toast.error('Failed to copy', {
          description: 'Could not copy the post to clipboard.'
        });
      });
  }, [editorContent, toast]);


  // Render the chat pane (left side)
  const renderChatPane = () => (
    <Box shadow className="h-full relative editor-pane">
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0">
        AI Editor Assistant
      </div>

      <div className="absolute top-16 bottom-24 left-0 right-0 px-4 overflow-y-auto">
        <ChatHistory
          messages={messages}
          isLoading={isLoading}
          error={error}
          onStreamComplete={handleStreamComplete}
          onBriefUpdate={handlePostUpdate}
          className="h-auto"
        />
      </div>

      <div className="absolute bottom-0 left-0 right-0 p-4" style={{ backgroundColor: 'var(--surface-primary)' }}>
        <ChatInput
          value={userInput}
          onChange={setUserInput}
          onSubmit={handleSendMessage}
          disabled={isLoading}
          placeholder="Ask me to edit or refine the post..."
        />
      </div>

      {error && (
        <div className="absolute bottom-20 left-4 right-4">
          <div className="error-message">
            {error}
          </div>
        </div>
      )}
    </Box>
  );

  // Render the editor pane (right side)
  const renderEditorPane = () => (
    <Box shadow className="flex-1 min-h-0 flex flex-col editor-pane">
      {/* Title with padding to align with content */}
      <div className="font-bold mb-4 text-xl font-ibm-plex-mono dark:text-neutral-100 mt-0 px-4 pt-4">
        Post Editor
      </div>

      {/* DiffEditor directly in flex container - no wrapper needed to prevent layout issues */}
      <DiffEditor
        originalContent={originalContent}
        currentContent={editorContent}
        onChange={handleContentChange}
        placeholder="Paste your content here or navigate from a generated post..."
      />

      <div className="flex justify-between items-end px-4 pb-1 pt-4">
        <div className="flex items-center gap-3">
          <p className="text-xs text-[var(--text-tertiary)] font-ibm-plex-mono mb-0">
            {`${editorContent.length}/3000 Chars`}
          </p>
          {hasChanges && (
            <span className="text-xs text-[var(--text-tertiary)] font-ibm-plex-mono">
              Modified ({charDifference > 0 ? '+' : ''}{charDifference} chars)
            </span>
          )}
        </div>
        <div className="action-buttons">
          {(hasChanges || isReverted) && (
            <Button
              onClick={handleToggleRevert}
              variant="secondary"
              size="sm"
              className="icon-button"
              title={isReverted ? "Restore changes" : "Revert to original"}
            >
              {isReverted ? <Redo2 className="h-4 w-4" /> : <Undo2 className="h-4 w-4" />}
            </Button>
          )}
          <Button
            onClick={goBack}
            variant="secondary"
            size="md"
          >
            Back
          </Button>
          <Button
            onClick={handleCopyToClipboard}
            disabled={!editorContent}
            variant="primary"
            size="md"
          >
            Copy
          </Button>
        </div>
      </div>
    </Box>
  );

  // Show skeleton while loading session
  if (sessionLoading) {
    return <EditorAgentSkeleton />;
  }

  return (
    <ChatInterfaceLayout
      leftPaneContent={renderChatPane()}
      rightPaneContent={renderEditorPane()}
      activeTab="Post Editor"
    />
  );
};

export default EditorAgentPage;