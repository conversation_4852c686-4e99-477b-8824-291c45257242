import React, { useState } from 'react';
import { GenerateButton } from '../ui/generate-button';
import { cn } from "../../utils/utils";
import { LengthSelectorCompact } from '../ui/length-selector-compact';

interface NotesInputFormProps {
  onSubmit?: (notes: string, postLength: string) => void;
  isProcessing?: boolean;
}

/**
 * NotesInputForm component - A form for inputting notes to generate LinkedIn posts
 */
const NotesInputForm: React.FC<NotesInputFormProps> = ({ onSubmit }) => {
  const [notesContent, setNotesContent] = useState<string>('');
  const [selectedLength, setSelectedLength] = useState<string>('medium');

  const handleSubmit = async () => {
    if (onSubmit) {
      await onSubmit(notesContent, selectedLength);
    }
  };

  return (
    <div className="pt-8 px-8 pb-6">
      {/* Using design system classes */}
      <div className="text-2xl font-bold mb-6">Paste Your Notes Below:</div>
      <div>
        <textarea
          className={cn("textarea-input", "notes-textarea")}
          placeholder="Enter your notes here..."
          value={notesContent}
          onChange={(e) => setNotesContent(e.target.value)}
          style={{ height: '600px' }}
        />
      </div>
      <div className="flex justify-between items-center mt-6">
        <LengthSelectorCompact 
          value={selectedLength}
          onChange={setSelectedLength}
        />
        <GenerateButton 
          variant="default" 
          size="md" 
          onGenerate={handleSubmit}
          loadingText="Getting ready..."
        />
      </div>
    </div>
  );
};

export default NotesInputForm;
