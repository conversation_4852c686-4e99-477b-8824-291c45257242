import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { OnboardingModal } from './OnboardingModal';
import { useContentPreferences } from '../../hooks/useContentPreferencesQueries';
import { useContentStrategy } from '../../hooks/useContentStrategyQueries';

export function OnboardingChecker() {
  const [showModal, setShowModal] = useState(false);
  const navigate = useNavigate();
  
  const { data: preferences, isLoading: preferencesLoading } = useContentPreferences();
  const { data: strategy, isLoading: strategyLoading } = useContentStrategy();

  useEffect(() => {
    // Don't show modal while loading
    if (preferencesLoading || strategyLoading) {
      return;
    }

    // Check if either form is not filled
    // For preferences, check if we have data and at least one field is filled
    const hasPreferences = preferences && preferences.transcriptName && preferences.transcriptName.trim() !== '';
    const hasStrategy = strategy && strategy.industry && strategy.industry.trim() !== '';

    // Show modal if either form is not filled
    if (!hasPreferences || !hasStrategy) {
      setShowModal(true);
    }
  }, [preferences, strategy, preferencesLoading, strategyLoading]);

  const handleStartPersonalization = () => {
    setShowModal(false);
    navigate('/user-preferences');
  };

  if (!showModal) {
    return null;
  }

  return (
    <OnboardingModal 
      open={showModal}
      onOpenChange={setShowModal}
      onStartPersonalization={handleStartPersonalization}
    />
  );
}