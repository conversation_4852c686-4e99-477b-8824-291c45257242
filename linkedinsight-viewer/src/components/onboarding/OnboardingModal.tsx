import * as React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { Button } from '../ui/button';
import { cn } from '../../utils/utils';

interface OnboardingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStartPersonalization: () => void;
}

export function OnboardingModal({ 
  open, 
  onOpenChange,
  onStartPersonalization 
}: OnboardingModalProps) {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay 
          className={cn(
            "fixed inset-0 z-50",
            "bg-black/40 backdrop-blur-sm",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
          )}
        />
        <Dialog.Content 
          className={cn(
            "fixed left-[50%] top-[50%] z-50",
            "w-full max-w-[900px] translate-x-[-50%] translate-y-[-50%]",
            "bg-[var(--surface-primary)] border-2 border-[var(--border-primary)]",
            "rounded-lg shadow-[4px_4px_0px_0px_var(--border-primary)]",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
            "data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]",
            "data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",
            "duration-200"
          )}
        >
          <div className="flex gap-6 p-8">
            {/* Left side - Welcome content */}
            <div className="flex-1 pr-4">
              <h2 className="text-2xl font-bold font-archivo mb-4">
                Welcome! <span style={{ filter: 'grayscale(100%)' }}>👋</span>
              </h2>
              <p className="text-[var(--text-secondary)] mb-1 leading-relaxed font-sans">
                To really help you articulate your expertise in a compelling and effective manner, we highly recommend providing necessary context.
              </p>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold font-archivo text-lg mb-2">Strategy</h3>
                  <p className="text-[var(--text-secondary)] leading-relaxed font-sans">
                    Tell us about your industry, what you offer, and who you serve. 
                    This helps us craft posts that resonate with your specific audience.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-bold font-archivo text-lg mb-2">Preferences</h3>
                  <p className="text-[var(--text-secondary)] leading-relaxed font-sans">
                    Choose your writing style, emoji usage, and voice distinctiveness. 
                    This ensures every post sounds authentically like you.
                  </p>
                </div>
              </div>
            </div>

            {/* Right side - Preview */}
            <div className="flex-1 flex flex-col">
              <div className="bg-[var(--surface-secondary)] rounded-md p-6 flex-1">
                <div className="space-y-4">
                  <div className="bg-[var(--surface-primary)] rounded p-4 border border-[var(--border-subtle)]">
                    <p className="text-sm font-medium text-[var(--text-tertiary)] mb-2 font-ibm-plex-mono">Generic:</p>
                    <p className="text-sm leading-relaxed font-sans">
                      &quot;I&apos;m excited to share our company&apos;s new strategic direction.&quot;
                    </p>
                  </div>
                  
                  <div className="bg-[var(--surface-primary)] rounded p-4 border-2 border-[var(--border-primary)]">
                    <p className="text-sm font-medium text-[var(--text-primary)] mb-2 font-ibm-plex-mono">Personalized:</p>
                    <p className="text-sm leading-relaxed font-sans">
                      &quot;Three months ago, I had to choose between doubling down on our core product 
                      or diversifying. The data said diversify, but my gut said focus. Today, 
                      I&apos;m sharing why I chose to listen to my gut—and how it paid off.&quot;
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3 mt-4">
                <Button 
                  variant="primary"
                  size="lg"
                  onClick={onStartPersonalization}
                  className="font-medium flex-1"
                >
                  Personalize
                </Button>
                <Dialog.Close asChild>
                  <Button 
                    variant="outline"
                    size="lg"
                    className="font-medium flex-1"
                  >
                    Later
                  </Button>
                </Dialog.Close>
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}