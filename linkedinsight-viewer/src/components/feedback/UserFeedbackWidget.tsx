import React, { useState } from 'react';
import { MessageSquare, X } from 'lucide-react';
import { FormContainer } from '../ui/form-container';
import { FormField } from '../ui/form-field';
import { Box } from '../ui/Box';
import { useMutation } from '@tanstack/react-query';
import apiClient from '@/services/apiClient';

interface FeedbackData {
  feedback: string;
  url: string;
}

export function UserFeedbackWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState('');

  const feedbackMutation = useMutation({
    mutationFn: async (data: FeedbackData) => {
      const response = await apiClient.post<{ success: boolean; message?: string }>('/api/feedback', data);
      return response;
    },
    onSuccess: () => {
      setFeedback('');
      setTimeout(() => setIsOpen(false), 1500);
    }
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    feedbackMutation.mutate({
      feedback,
      url: window.location.href
    });
  };

  return (
    <>
      {/* Floating button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 bg-black text-white dark:bg-white dark:text-black p-3 rounded-full shadow-lg hover:scale-110 transition-transform z-50"
        aria-label="Send feedback"
      >
        <MessageSquare size={24} />
      </button>

      {/* Feedback modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-2xl mx-auto">
            <Box shadow className="relative">
              <button
                onClick={() => setIsOpen(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 z-10"
                aria-label="Close feedback form"
              >
                <X size={24} />
              </button>

              <FormContainer
                title="Send us feedback"
                error={feedbackMutation.error?.message}
                onSubmit={handleSubmit}
                submitText={feedbackMutation.isSuccess ? "Thanks! ✓" : "Submit"}
                isSubmitting={feedbackMutation.isPending}
                formClassName="form-container form-container-compact"
              >
                <FormField
                  label="Your feedback"
                  id="feedback"
                  name="feedback"
                  type="textarea"
                  rows={6}
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Tell us what you think..."
                  required
                />
              </FormContainer>
            </Box>
          </div>
        </div>
      )}
    </>
  );
}