import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface AgentExplanationProps {
  explanation: string;
}

/**
 * AgentExplanation component - Renders agent explanation with grayscale emoji funnel stages
 */
export function AgentExplanation({ explanation }: AgentExplanationProps) {
  return (
    <div className="draft-textarea prose prose-sm max-w-none grayscale-emoji [&>*:first-child]:mt-0">
      <ReactMarkdown remarkPlugins={[remarkGfm]}>
        {explanation}
      </ReactMarkdown>
    </div>
  );
}

export default AgentExplanation;