import React from 'react';
import { Box } from '../ui/Box';

export function ContentAgentPageSkeleton() {
  return (
    <div className="h-full flex flex-col animate-pulse">
      {/* Main content area with fixed height */}
      <div className="flex-grow min-h-0">
        <div className="h-full">
          {/* Dual pane layout skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full p-6">
            {/* Left pane skeleton */}
            <Box shadow className="w-full h-full">
              <div className="flex flex-col h-full pt-2">
                {/* Title skeleton */}
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-24 mb-4"></div>
                
                {/* Content area skeleton */}
                <div className="mb-4 flex-grow flex flex-col overflow-hidden">
                  <div className="draft-textarea h-full bg-gray-100 dark:bg-gray-800 rounded-md p-4">
                    {/* Simulated content lines */}
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/5"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </div>
                </div>
                
                {/* Button row skeleton */}
                <div className="flex gap-2 justify-between items-center pt-4">
                  <div className="flex gap-2">
                    <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                    <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
                  </div>
                  <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                </div>
              </div>
            </Box>

            {/* Right pane skeleton */}
            <Box shadow className="w-full h-full">
              <div className="flex flex-col h-full pt-2">
                {/* Title skeleton */}
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-24 mb-4"></div>
                
                {/* Content area skeleton */}
                <div className="mb-4 flex-grow flex flex-col overflow-hidden">
                  <div className="draft-textarea h-full bg-gray-100 dark:bg-gray-800 rounded-md p-4">
                    {/* Simulated content lines */}
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/5"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-4/5"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
                
                {/* Button row skeleton */}
                <div className="flex gap-2 justify-between items-center pt-4">
                  <div className="flex gap-2">
                    <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                    <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
                  </div>
                  <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                </div>
              </div>
            </Box>
          </div>
        </div>
      </div>
    </div>
  );
}