import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Button } from '../ui/button';
import { useToast } from '../ui/toast-provider';
import { ReusableDualPaneLayout } from '../layout/ReusableDualPaneLayout';
import { Box } from '../ui/Box';
import { navigateWithTransition } from '../../utils/navigation';
import { AgentExplanation } from './AgentExplanation';

interface AgentPanesProps {
  generationStatusMap: {
    [draftId: string]: {
      overallStatus: string;
      currentStepKey?: string | null;
      userFriendlyMessage?: string;
    } | undefined;
  };
  generatedPosts: { [draftId: string]: string | null };
  explanations?: { [draftId: string]: string | null }; // Explanation data for each draft
  setGeneratedPosts?: React.Dispatch<React.SetStateAction<{ [draftId: string]: string | null }>>;
  onCopyPost?: (draftId: string, post: string) => void;
  sourceState?: any; // State to pass back when navigating from editor
}

/**
 * AgentPanes component - Displays two draft panes side by side
 *
 * This component shows two panes with generated content drafts using vector-based
 * pattern selection. It handles loading states and displays status updates.
 */
const AgentPanes: React.FC<AgentPanesProps> = ({
  generationStatusMap,
  generatedPosts,
  explanations,
  setGeneratedPosts,
  onCopyPost,
  sourceState
}) => {
  // State to track which drafts are showing explanations vs content
  const [showingExplanation, setShowingExplanation] = useState<{ [draftId: string]: boolean }>({});
  
  const toast = useToast();
  const navigate = useNavigate();

  // Handle copying post to clipboard
  const handleCopy = (draftId: string) => {
    const post = generatedPosts[draftId];
    if (post && onCopyPost) {
      onCopyPost(draftId, post);
    } else if (post) {
      navigator.clipboard.writeText(post)
        .then(() => {
          toast.success('Copied to clipboard!', {
            description: 'Draft is ready to paste.'
          });
        })
        .catch(() => {
          toast.error('Copy failed', {
            description: 'Could not copy the draft.'
          });
        });
    }
  };

  // Toggle explanation view for a specific draft
  const toggleExplanation = (draftId: string) => {
    setShowingExplanation(prev => ({
      ...prev,
      [draftId]: !prev[draftId]
    }));
  };

  // Helper function to render individual pane content
  const renderPaneContent = (draftId: string, index: number) => {
    const currentDraftStatus = generationStatusMap[draftId];
    const isDraftGenerating = currentDraftStatus && currentDraftStatus.overallStatus !== 'completed' && currentDraftStatus.overallStatus !== 'failed';
    const isShowingExplanation = showingExplanation[draftId] || false;
    const hasExplanation = explanations && explanations[draftId];

    return (
      <Box
        shadow
        className="w-full h-full agent-pane"
      >
        <div className="flex flex-col h-full pt-2">
          <h2 className="text-xl font-bold mb-4 font-ibm-plex-mono mt-0">
            {`Draft #${index + 1}:`}
          </h2>
          <div className="mb-4 flex-grow flex flex-col overflow-hidden">
            {isDraftGenerating ? (
              <div className="draft-container flex flex-col items-center justify-center h-full">
                <div className="flex flex-col items-center justify-center h-full">
                  <span className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent mb-4"></span>
                  <p className="text-center text-muted-foreground">
                    {currentDraftStatus?.userFriendlyMessage || 'Generating content...'}
                  </p>
                </div>
              </div>
            ) : (
              <>
                {isShowingExplanation ? (
                  <AgentExplanation 
                    explanation={explanations?.[draftId] || 'No explanation available...'}
                  />
                ) : (
                  <textarea
                    id={`draft-${index}`}
                    className="draft-textarea"
                    placeholder="Generated LinkedIn post will appear here..."
                    value={generatedPosts[draftId] || ''}
                    onChange={(e) => {
                      if (setGeneratedPosts) {
                        setGeneratedPosts(prevPosts => ({
                          ...prevPosts,
                          [draftId]: e.target.value
                        }));
                      }
                    }}
                  />
                )}
              </>
            )}
          </div>
          <div className="flex justify-between items-baseline mt-auto">
            <p className="text-xs text-muted-foreground font-ibm-plex-mono">
              {isShowingExplanation 
                ? `Explanation`
                : `${(generatedPosts[draftId] || '').length}/3000 Chars`
              }
            </p>
            <div className="action-buttons">
              {/* Back to Ideas button when coming from transcript */}
              {sourceState?.transcriptId && (
                <Button
                  variant="outline"
                  size="md"
                  onClick={() => {
                    navigateWithTransition(navigate, `/ideas?transcriptId=${sourceState.transcriptId}`, {
                      state: { 
                        transcriptId: sourceState.transcriptId
                      }
                    });
                  }}
                >
                  ← Ideas
                </Button>
              )}
              {hasExplanation && (
                <Button
                  variant="outline"
                  size="md"
                  onClick={() => toggleExplanation(draftId)}
                  disabled={isDraftGenerating}
                >
                  {isShowingExplanation ? "Draft" : "Explain"}
                </Button>
              )}
              <Button
                variant="secondary"
                size="md"
                onClick={() => {
                  navigateWithTransition(navigate, '/editor', {
                    state: {
                      content: generatedPosts[draftId],
                      metadata: {
                        draftId,
                        postLength: 'medium' // You might want to pass this from props
                      },
                      sourceState: sourceState, // Pass the source state for back navigation
                      sourceRoute: {
                        path: '/content-agent',
                        label: 'Content Agent'
                      }
                    }
                  });
                }}
                disabled={isDraftGenerating || !generatedPosts[draftId]}
              >
                Edit
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={() => handleCopy(draftId)}
                disabled={isDraftGenerating || !generatedPosts[draftId]}
              >
                Copy
              </Button>
            </div>
          </div>
        </div>
      </Box>
    );
  };

  // Prepare pane contents using fixed draft IDs
  const leftPane = renderPaneContent("draft_a", 0);
  const rightPane = renderPaneContent("draft_b", 1);

  return (
    <ReusableDualPaneLayout
      leftPaneContent={leftPane}
      rightPaneContent={rightPane}
      className="h-full" // Use full height from parent container
    />
  );
};

export default AgentPanes;
