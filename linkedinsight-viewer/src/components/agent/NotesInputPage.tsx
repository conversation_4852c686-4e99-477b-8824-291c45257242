import React, { useState } from 'react';
import NotesInputForm from '../notes/NotesInputForm';
import { Box } from '../ui/Box';
import { Layout } from '../layout/Layout';
import { useSession } from '../../hooks/useSession';

interface NotesInputPageProps {
  onGenerationComplete?: () => void;
}

const NotesInputPage: React.FC<NotesInputPageProps> = () => {
  const [error] = useState<string | null>(null);
  const { updateSession, navigate } = useSession();

  const handleNotesSubmit = async (notesContent: string, postLength: string) => {
    // Update session with notes
    await updateSession({
      flow: 'notes',
      notes: notesContent,
      brief: notesContent,
      postLength: postLength as 'short' | 'medium' | 'long',
      step: 'content'
    });
    
    // Navigate to content-agent page
    navigate('/content-agent');
  };

  
  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      {(
        <div className="flex items-center justify-center min-h-full">
          <div className="w-full max-w-2xl mx-auto px-4 relative z-20">
            <Box shadow padding="none">
              <NotesInputForm onSubmit={handleNotesSubmit} isProcessing={false} />
              {error && (
                <div className="mt-4 p-4 bg-destructive/10 border border-destructive rounded-md text-destructive">
                  {error}
                </div>
              )}
            </Box>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default NotesInputPage; 