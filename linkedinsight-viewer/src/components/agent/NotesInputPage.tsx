import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import NotesInputForm from '../notes/NotesInputForm';
import { Box } from '../ui/Box';
import { Layout } from '../layout/Layout';
// import { navigateToContentGeneration } from '../../utils/briefNavigation';

interface NotesInputPageProps {
  // Props from TransitionComponent that might still be relevant, like onTransitionComplete
  onGenerationComplete?: () => void; // Example: Renamed for clarity
}


const NotesInputPage: React.FC<NotesInputPageProps> = () => {
  // State primarily from BriefToAgentFlow, adapted for this component's structure
  const [error] = useState<string | null>(null);
  // const [transitionClass, setTransitionClass] = useState<string>(''); // Temporarily ignore

  // const { getToken } = useAuth();
  const navigate = useNavigate();

  const handleNotesSubmit = async (notesContent: string, postLength: string) => {
    // Navigate to content-agent page
    navigate('/content-agent', {
      state: {
        brief: notesContent,
        postLength: postLength
      }
    });
  };

  
  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      {(
        <div className="flex items-center justify-center min-h-full">
          <div className="w-full max-w-2xl mx-auto px-4 relative z-20">
            <Box shadow padding="none">
              <NotesInputForm onSubmit={handleNotesSubmit} isProcessing={false} />
              {error && (
                <div className="mt-4 p-4 bg-destructive/10 border border-destructive rounded-md text-destructive">
                  {error}
                </div>
              )}
            </Box>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default NotesInputPage; 