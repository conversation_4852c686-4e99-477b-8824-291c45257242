import React from 'react';

export function TranscriptInputSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div>
        {/* Title skeleton */}
        <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-48 mb-4"></div>
        
        {/* Radio buttons skeleton */}
        <div className="space-y-2 pl-4">
          <div className="flex items-center gap-2">
            <div className="w-[18px] h-[18px] bg-gray-300 dark:bg-gray-700 rounded-full"></div>
            <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-[18px] h-[18px] bg-gray-300 dark:bg-gray-700 rounded-full"></div>
            <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
          </div>
        </div>
      </div>

      {/* File upload skeleton */}
      <div className="flex border border-gray-300 dark:border-gray-700 overflow-hidden" style={{ borderRadius: 'var(--radius-md)' }}>
        <div className="h-10 bg-gray-300 dark:bg-gray-700 w-32"></div>
        <div className="flex-1 flex items-center px-4" style={{ backgroundColor: 'var(--surface-input)' }}>
          <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
        </div>
      </div>

      {/* Button skeleton */}
      <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
    </div>
  );
}