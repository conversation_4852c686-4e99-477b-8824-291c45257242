/**
 * Shared citation component for displaying structured citation data (speaker, timestamp, text)
 * or plain text citations during streaming.
 */

import { Citation as CitationType } from '../../types/idea';

interface CitationProps {
  citation?: CitationType;
  text?: string;
  showIcon?: boolean;
}

export function Citation({ citation, text, showIcon = false }: CitationProps) {
  return (
    <blockquote className="border-l-4 border-[#A0A0A0] dark:border-muted bg-[#D9D9D9]/50 dark:bg-[#808080]/80 pl-4 italic py-2">
      <div className="text-black dark:text-white">
        {showIcon && <span className="font-bold not-italic [filter:grayscale(100%)]">📜 </span>}
        {citation ? citation.text : text}
        {citation && (citation.speaker || citation.timestamp) && (
          <div className="text-xs text-muted-foreground mt-1 not-italic">
            {citation.speaker && `${citation.speaker}`}
            {citation.speaker && citation.timestamp && " - "}
            {citation.timestamp && citation.timestamp}
          </div>
        )}
      </div>
    </blockquote>
  );
} 