import { useState, useEffect } from 'react';
import { Layout } from '../layout/Layout';
import { TranscriptInput } from './TranscriptInput';
import { TranscriptInputSkeleton } from './TranscriptInputSkeleton';
import { Progress } from '../ui/progress';
import { Box } from '../ui/Box';
import { AsyncBoundary } from '../ui/AsyncBoundary';
import { useLoadingMessage } from '../../hooks/useLoadingMessage';
import { useSession } from '../../hooks/useSession';
import apiClient from '../../services/apiClient';
import { useToast } from '../ui/toast-provider';

export function TranscriptInputPage() {
  const loadingMessage = useLoadingMessage();
  const [isInitializing, setIsInitializing] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { updateSession, navigate } = useSession();
  const toast = useToast();
  
  // Simulate initial setup (checking for drafts, loading preferences, etc.)
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitializing(false);
    }, 500); // Quick initialization
    
    return () => clearTimeout(timer);
  }, []);

  const handleTranscriptSubmit = async (content: string) => {
    setIsProcessing(true);
    setError(null);
    
    try {
      // Update session with transcript
      await updateSession({ 
        flow: 'transcript',
        transcript: content,
        step: 'ideas'
      });
      
      // Process transcript
      const response = await apiClient.post('/api/transcript/process', {
        transcript: content
      });
      
      if (response && response.transcript_id) {
        // Update session with transcript ID
        await updateSession({
          transcriptId: response.transcript_id
        });
        
        // Navigate to ideas page
        navigate('/ideas');
      } else {
        throw new Error('No transcript ID received from server');
      }
    } catch (err) {
      console.error('Failed to process transcript:', err);
      setError(err instanceof Error ? err : new Error('Failed to process transcript'));
      toast.error('Failed to process transcript', {
        description: err instanceof Error ? err.message : 'Please try again'
      });
    } finally {
      setIsProcessing(false);
    }
  };


  return (
    <Layout hasTabs={false} noShadowContainer={true}>
      {/* Container positioning: pt-8 for top-fixed, or items-center justify-center min-h-full for centered */}
      <div className="flex justify-center pt-8">
        <div className="w-full max-w-2xl mx-auto px-4 relative z-20">
          <Box shadow>
            <AsyncBoundary
              isLoading={isInitializing}
              isError={!!error}
              error={error}
              loadingComponent={<TranscriptInputSkeleton />}
              errorComponent={
                <div className="space-y-4">
                  <TranscriptInput 
                    onSubmit={handleTranscriptSubmit}
                    isProcessing={isProcessing}
                  />
                  <div className="mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                    <p className="text-red-800 dark:text-red-200 text-sm font-medium">
                      {error?.message || 'Failed to extract ideas'}
                    </p>
                  </div>
                </div>
              }
            >
              {isProcessing ? (
                <div className="space-y-4 ml-4">
                  <p className="text-left text-gray-600 dark:text-gray-400 italic font-mono">
                    {loadingMessage}
                  </p>
                  <Progress />
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                    This may take 1-2 minutes. It is worth the wait, though.
                  </p>
                </div>
              ) : (
                <TranscriptInput 
                  onSubmit={handleTranscriptSubmit}
                  isProcessing={processTranscriptMutation.isPending}
                />
              )}
            </AsyncBoundary>
          </Box>
        </div>
      </div>
    </Layout>
  );
}