import React from 'react';

interface StarRatingProps {
  rating: number;
  total?: number;
  onRatingChange?: (newRating: number) => void;
  readOnly?: boolean;
}

/**
 * StarRating component for displaying and optionally changing ratings
 * 
 * @param rating - Current rating value
 * @param total - Total number of stars (default: 5)
 * @param onRatingChange - Optional callback for when rating changes
 * @param readOnly - Whether the rating can be changed (default: true)
 */
export function StarRating({ 
  rating, 
  total = 5, 
  onRatingChange,
  readOnly = true
}: StarRatingProps) {
  return (
    <div className="flex">
      {[...Array(total)].map((_, i) => (
        <span 
          key={i} 
          className={`text-lg ${!readOnly ? 'cursor-pointer' : ''}`}
          onClick={() => {
            if (!readOnly && onRatingChange) {
              onRatingChange(i + 1);
            }
          }}
        >
          {i < rating ? "★" : "☆"}
        </span>
      ))}
    </div>
  );
}

export default StarRating;
