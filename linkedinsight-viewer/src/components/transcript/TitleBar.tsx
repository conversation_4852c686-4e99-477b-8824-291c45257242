import React, { ReactNode, forwardRef, useEffect, useRef, useCallback } from 'react';

interface TitleBarProps {
  children: ReactNode;
  className?: string;
}

export function TitleBar({ children, className = '' }: TitleBarProps) {
  return (
    <div className={`px-3 py-2 bg-[#D9D9D9] border border-black dark:border-white dark:bg-[#1a1a1a] rounded-t-lg flex items-center justify-between min-h-[28px] ${className}`}>
      {children}
    </div>
  );
}

interface TitleBarTextProps {
  children: ReactNode;
  className?: string;
}

export function TitleBarText({ children, className = '' }: TitleBarTextProps) {
  return (
    <h3 className={`text-base font-bold font-mono whitespace-nowrap flex items-center m-0 p-0 overflow-hidden text-ellipsis mr-2 text-black dark:text-white ${className}`}>
      {children}
    </h3>
  );
}

interface TitleBarNumberedProps {
  number: number;
  className?: string;
}

export function TitleBarNumbered({ number, className = '' }: TitleBarNumberedProps) {
  return (
    <div className={`w-14 flex items-center justify-center ${className}`}>
      <span className="font-mono font-bold italic text-black dark:text-white">
        {number.toString().padStart(2, '0')}
      </span>
    </div>
  );
}

interface DragHandleProps {
  className?: string;
}

export function DragHandle({ className = '' }: DragHandleProps) {
  return (
    <div
      className={`w-10 pl-2 flex flex-col items-center justify-center cursor-grab active:cursor-grabbing hover:opacity-80 transition-opacity ${className}`}
    >
      <div className="flex flex-col gap-1.5">
        <div className="w-5 h-0.5 bg-black/40 dark:bg-white/40 rounded-full"></div>
        <div className="w-5 h-0.5 bg-black/40 dark:bg-white/40 rounded-full"></div>
        <div className="w-5 h-0.5 bg-black/40 dark:bg-white/40 rounded-full"></div>
      </div>
    </div>
  );
}

interface TitleBarInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onBlur?: () => void;
}

export const TitleBarInput = forwardRef<HTMLTextAreaElement, TitleBarInputProps>(
  ({ value, onChange, onKeyDown, onBlur }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement | null>(null);

    const adjustHeight = useCallback(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight}px`;
      }
    }, []);

    useEffect(() => {
      adjustHeight();
    }, [value, adjustHeight]);

    return (
      <textarea
        ref={(element) => {
          textareaRef.current = element;
          if (typeof ref === 'function') {
            ref(element);
          } else if (ref) {
            ref.current = element;
          }
        }}
        value={value}
        onChange={(e) => {
          onChange(e);
          adjustHeight();
        }}
        onKeyDown={onKeyDown}
        onBlur={onBlur}
        placeholder="Enter section title"
        rows={1}
        className="font-archivo bg-[#F5F5F5] dark:bg-[#333333] border border-black/20 dark:border-white/20 px-3 py-1.5 w-[300px] text-black dark:text-white rounded-sm resize-none overflow-hidden"
      />
    );
  }
);