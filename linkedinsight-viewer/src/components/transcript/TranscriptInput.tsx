import { useState, useEffect } from "react"
import { transcript<PERSON>pi } from "../../services/TranscriptService"
import { Button } from "../ui/button"
import { ErrorService } from "../../utils/errorHandling"

interface TranscriptInputProps {
  onSubmit: (content: string) => Promise<void>
  isProcessing: boolean
}

export function TranscriptInput({ onSubmit, isProcessing }: TranscriptInputProps) {
  const [inputMethod, setInputMethod] = useState<"upload" | "paste">("upload")
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [pastedContent, setPastedContent] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isParsingFile, setIsParsingFile] = useState(false)

  // Clear error when input method changes or when file is selected
  useEffect(() => {
    setError(null)
  }, [inputMethod, selectedFile])

  const handleSubmit = async () => {
    setError(null)

    if (inputMethod === "upload" && !selectedFile) {
      setError("Please select a file to process")
      return
    }

    if (inputMethod === "paste" && !pastedContent.trim()) {
      setError("Please enter some content to process")
      return
    }

    try {
      let content = "";

      // Handle file upload
      if (inputMethod === "upload" && selectedFile) {
        const fileName = selectedFile.name.toLowerCase();
        const fileType = selectedFile.type;

        // Use backend parser for .docx and .pdf files
        const useBackendParser = fileName.endsWith(".docx") || fileName.endsWith(".pdf") || fileType === "application/pdf";

        if (useBackendParser) {
          try {
            setIsParsingFile(true);
            // Determine parsing message based on file type
            const parsingMessage = fileName.endsWith(".pdf") || fileType === "application/pdf" ? "Parsing PDF..." : "Parsing DOCX...";
            // Note: Actual button text update logic might be different based on FR-FE-5
            console.log(parsingMessage); // Placeholder for UI update
            content = await transcriptApi.parseFile(selectedFile);
          } catch (parseError) {
            const errorMsg = ErrorService.getMessage(parseError, 'File parsing');
            setError(errorMsg);
            ErrorService.logError(parseError, 'File parsing error');
            setIsParsingFile(false);
            return;
          } finally {
            setIsParsingFile(false);
          }
        } else {
          // For other file types (txt, md), use the browser's text() method
          try {
            content = await selectedFile.text();
          } catch (readError) {
             setError(ErrorService.getMessage(readError, 'File reading'));
             ErrorService.logError(readError, 'File reading error');
             return;
          }
        }
      } else {
        // For pasted content, use as is
        content = pastedContent;
      }

      // Submit the content for processing
      await onSubmit(content);
    } catch (error) {
      setError(ErrorService.getMessage(error, 'Transcript processing'))
      ErrorService.logError(error, 'Processing error')
    }
  }

  return (
    <div className="space-y-6 -mt-6">
      <div>
        <h2 className="font-mono font-semibold italic text-2xl mb-4 text-black dark:text-white">Transcript Input:</h2>
        <div className="space-y-2 pl-4">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="inputMethod"
              value="upload"
              checked={inputMethod === "upload"}
              onChange={(e) => setInputMethod(e.target.value as "upload" | "paste")}
              className="appearance-none w-[18px] h-[18px] rounded-full border border-black dark:border-white bg-[#D9D9D9] dark:bg-[#1a1a1a] checked:bg-[#666666] dark:checked:bg-gray-400 cursor-pointer transition-colors"
            />
            <span className="text-base text-black dark:text-white">Upload File</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="inputMethod"
              value="paste"
              checked={inputMethod === "paste"}
              onChange={(e) => setInputMethod(e.target.value as "upload" | "paste")}
              className="appearance-none w-[18px] h-[18px] rounded-full border border-black dark:border-white bg-[#D9D9D9] dark:bg-[#1a1a1a] checked:bg-[#666666] dark:checked:bg-gray-400 cursor-pointer transition-colors"
            />
            <span className="text-base text-black dark:text-white">Paste Content</span>
          </label>
        </div>
      </div>

      {inputMethod === "upload" && (
        <div className="flex border border-primary overflow-hidden" style={{ borderRadius: 'var(--radius-md)' }}>
          <button
            onClick={() => document.getElementById("file-input")?.click()}
            className="button button-dark button-md"
            style={{ borderRadius: '0', borderRight: '1px solid var(--border-primary)' }}
          >
            Choose File
          </button>
          <div className="flex-1 flex items-center px-4" style={{ backgroundColor: 'var(--surface-input)' }}>
            <span style={{ color: 'var(--text-tertiary)' }}>
              {selectedFile ? 
                (selectedFile.name.length > 50 ? 
                  selectedFile.name.slice(0, 47) + '...' : 
                  selectedFile.name) : 
                "No file chosen"}
            </span>
          </div>
          <input
            type="file"
            id="file-input"
            className="hidden"
            accept=".txt,.docx,.md,.pdf,text/plain,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
            onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
          />
        </div>
      )}

      {inputMethod === "paste" && (
        <textarea
          className="textarea-input"
          placeholder="Paste transcript content here..."
          value={pastedContent}
          onChange={(e) => setPastedContent(e.target.value)}
        />
      )}

      {error && (
        <div className="flex items-center gap-2 text-amber-700 dark:text-amber-500">
          <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
          <span className="text-sm">{error}</span>
        </div>
      )}

      <Button
        onClick={handleSubmit}
        variant="primary"
        disabled={isProcessing || isParsingFile}
        className="w-full"
      >
        {isParsingFile ? "Parsing File..." : isProcessing ? "Processing..." : "Process Transcript"}
      </Button>
    </div>
  )
}