import React from 'react';
import { TitleBar } from './TitleBar';

export function IdeaCardSkeleton() {
  return (
    <div className="mb-6 bg-card text-card-foreground animate-pulse">
      <TitleBar>
        <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
        <div className="w-8 flex-shrink-0"></div>
      </TitleBar>
      <div className="p-4 border-b border-l border-r border-black dark:border-white rounded-b-lg">
        <div className="flex justify-between mb-2 gap-4">
          <div className="flex items-center">
            <span className="font-semibold font-archivo">Funnel Stage:&nbsp;</span>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
          </div>
          <div className="flex items-center whitespace-nowrap flex-shrink-0">
            <span className="font-semibold font-archivo">Engagement Score:&nbsp;</span>
            <div className="flex gap-1">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="w-4 h-4 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
              ))}
            </div>
          </div>
        </div>
        <div className="mb-4">
          <span className="font-semibold font-archivo">Summary: </span>
          <div className="space-y-2 mt-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
        <div className="mb-4">
          <div className="mb-2">
            <span className="font-semibold font-archivo">Supporting Quotes: </span>
          </div>
          <div className="space-y-2">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="p-3 bg-gray-100 dark:bg-gray-800 rounded-md">
                <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
          <div className="h-9 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
        </div>
      </div>
    </div>
  );
}

export default IdeaCardSkeleton;