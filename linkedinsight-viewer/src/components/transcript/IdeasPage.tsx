import { useState, useEffect } from 'react';
import { ShadowedTabsContainer, TabPanel } from '../ui/ShadowedTabsContainer';
import { IdeaCard } from './IdeaCard';
import { IdeaCardSkeleton } from './IdeaCardSkeleton';
import { Idea } from '../../types/idea';
import { isMockModeEnabled } from '../../utils/mockMode';
import { useTranscriptIdeasQuery } from '../../hooks/useTranscriptIdeasQuery';
import { AsyncBoundary, checkIsEmpty } from '../ui/AsyncBoundary';
import { useMarkIdeaGenerated } from '../../hooks/useMarkIdeaGenerated';
import { useToast } from '../ui/toast-provider';
import { MOCK_IDEAS } from '../../services/mockData';
import { useSession } from '../../hooks/useSession';
import { IdeaItem } from '../../types/session';



export function IdeasPage() {
  const [activeTab, setActiveTab] = useState("ideas");
  const { session, updateSession, navigate } = useSession();
  const toast = useToast();
  
  // Get transcript ID from session
  const transcriptId = session?.transcriptId || '';
  
  // Use hooks for data fetching and mutations
  const { ideas, generatedIdeas, isLoading, isFetching, isError, error, refetch } = useTranscriptIdeasQuery(transcriptId);
  const { markAsGenerated } = useMarkIdeaGenerated();
  
  // Store ideas in session when loaded
  useEffect(() => {
    if (ideas && ideas.length > 0) {
      const ideaItems: IdeaItem[] = ideas.map(idea => ({
        id: idea.id,
        idea_text: idea.idea_text,
        summary: idea.summary || '',
        category: idea.category || '',
        citations: idea.citations
      }));
      updateSession({ ideas: ideaItems });
    }
  }, [ideas, updateSession]);
  
  // In mock mode, ensure we have a transcript ID
  useEffect(() => {
    if (isMockModeEnabled() && !transcriptId) {
      // Set a mock transcript ID for testing
      updateSession({ transcriptId: 'mock-transcript-id' });
    }
  }, [transcriptId, updateSession]);
  


  // Determine what ideas to show based on mock mode and available data
  const getIdeasToShow = (): Idea[] => {
    // If mock mode is enabled, always show mock data for UI/UX debugging
    if (isMockModeEnabled()) {
      return MOCK_IDEAS;
    }
    
    // Ideas are already in frontend format from the hook
    if (activeTab === 'generated') {
      return generatedIdeas;
    }
    return ideas;
  };

  const displayIdeas = getIdeasToShow();
  
  // Show extraction prompt only in real mode when no transcript is selected
  if (!isMockModeEnabled() && !transcriptId) {
    return (
      <ShadowedTabsContainer
        activeTab={activeTab}
        onTabChange={setActiveTab}
        tabs={[{ label: 'Ideas', value: 'ideas' }]}
        maxWidth="max-w-2xl"
      >
        <TabPanel value="ideas" activeTab={activeTab}>
          <div className="text-center py-8">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              No transcript selected. Please process a transcript first.
            </p>
            <button
              onClick={() => navigate('/transcript-input')}
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Go to Transcript Input
            </button>
          </div>
        </TabPanel>
      </ShadowedTabsContainer>
    );
  }

  const tabs = [
    { label: 'Ideas', value: 'ideas' },
    { label: 'Already Generated', value: 'generated' }
  ];

  // Update post length in session when user changes selection
  const handlePostLengthChange = (newLength: string) => {
    updateSession({ postLength: newLength as 'short' | 'medium' | 'long' });
  };

  // Component's handler for content generation
  const handleGenerateContent = async (idea: Idea) => {
    if (!transcriptId) {
      toast.error("No transcript ID available", {
        description: "Please go back and select a transcript."
      });
      return;
    }
    
    try {
      // Create the session content from the idea
      const ideaContent = `# Content Brief from Idea

**Idea**: ${idea.idea_text}

**Summary**: ${idea.summary || 'No summary available'}

**Category**: ${idea.category || 'General'}

**Supporting Quotes**:
${idea.citations?.map((citation: any, index: number) => 
  `${index + 1}. "${citation.text || citation.content}"`
).join('\n') || 'No citations available'}`;
      
      // Mark the idea as generated before navigating
      if (idea.id) {
        await markAsGenerated(idea.id, transcriptId);
        // Refetch ideas to update the UI
        refetch();
      }
      
      // Update session and navigate
      await updateSession({
        selectedIdeaId: idea.id,
        brief: ideaContent,
        postLength: session?.postLength || 'medium',
        step: 'content',
        flow: 'transcript'
      });
      
      navigate('/content-agent');
    } catch (error) {
      toast.error('Failed to start content generation', {
        description: error instanceof Error ? error.message : 'Please try again.'
      });
    }
  };

  const handleCustomizeAndGenerate = () => {
    // TODO: Handle customization flow then brief generation
  };

  // Common AsyncBoundary props for both tabs
  const asyncBoundaryProps = {
    isLoading: isLoading && !isMockModeEnabled(),
    isFetching,
    isError: isError && !isMockModeEnabled(),
    error,
    isEmpty: checkIsEmpty(displayIdeas),
    onRetry: refetch,
    loadingComponent: (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <IdeaCardSkeleton key={i} />
        ))}
      </div>
    ),
    errorComponent: (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400 mb-4">
          {error?.message.includes('Failed to fetch') 
            ? 'Unable to connect to server. Please check your connection and try again.'
            : `Error: ${error?.message}`
          }
        </p>
        <div className="space-x-4">
          <button
            onClick={refetch}
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Try Again
          </button>
          <button
            onClick={() => navigate('/transcript-input')}
            className="text-gray-600 dark:text-gray-400 hover:underline"
          >
            New Transcript
          </button>
        </div>
      </div>
    ),
  };

  // Component for rendering idea cards
  const IdeaCardList = ({ selectable }: { selectable: boolean }) => (
    <div className="space-y-4">
      {displayIdeas.map((idea) => (
        <IdeaCard
          key={idea.id}
          idea={idea}
          onGenerateBrief={handleGenerateContent}
          onCustomizeAndGenerate={handleCustomizeAndGenerate}
          selectable={selectable}
          editable={false}
          postLength={session?.postLength || 'medium'}
          onPostLengthChange={handlePostLengthChange}
        />
      ))}
    </div>
  );

  return (
    <ShadowedTabsContainer 
      activeTab={activeTab} 
      onTabChange={setActiveTab} 
      tabs={tabs}
      maxWidth="max-w-2xl"
    >
      <TabPanel value="ideas" activeTab={activeTab}>
        <AsyncBoundary
          {...asyncBoundaryProps}
          emptyComponent={
            <div className="flex items-center justify-center" style={{ minHeight: '200px' }}>
              <div className="text-center text-gray-500">
                No ideas available.
              </div>
            </div>
          }
        >
          <IdeaCardList selectable={true} />
        </AsyncBoundary>
      </TabPanel>
      <TabPanel value="generated" activeTab={activeTab}>
        <AsyncBoundary
          {...asyncBoundaryProps}
          emptyComponent={
            <div className="flex items-center justify-center" style={{ minHeight: '200px' }}>
              <div className="text-center text-gray-500">
                No generated content yet. Select an idea from the Ideas tab to generate your first post.
              </div>
            </div>
          }
        >
          <IdeaCardList selectable={false} />
        </AsyncBoundary>
      </TabPanel>
    </ShadowedTabsContainer>
  );
}