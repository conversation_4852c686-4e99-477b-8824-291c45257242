import React, { useState, useEffect } from 'react';
import { Idea } from '../../types/idea';
import { TitleBar, TitleBarText } from './TitleBar';
import { Button } from '../ui/button';
import { GenerateButton } from '../ui/generate-button';
import { Citation } from './Citation';
import { StarRating } from './StarRating';
import ConfigService from '../../services/ConfigService';
import { LengthSelectorCompact } from '../ui/length-selector-compact';

// Component-level state for funnel stage configuration
interface FunnelConfig {
  displayName: string;
  tooltip: string;
}

// Default config while loading
const DEFAULT_FUNNEL_CONFIG: FunnelConfig = {
  displayName: 'Not classified',
  tooltip: 'Funnel stage not determined'
};

interface IdeaCardProps {
  idea: Idea;
  onSelect?: (idea: Idea) => void;
  onGenerateBrief?: (idea: Idea, postLength: string) => void;
  onCustomizeAndGenerate?: (idea: Idea) => void;
  onEdit?: (idea: Idea) => void;
  onSave?: (idea: Idea) => void;
  editable?: boolean;
  selectable?: boolean;
}

/**
 * IdeaCard component for displaying an idea with optional editing and selection capabilities
 *
 * @param idea - The idea to display
 * @param onSelect - Optional callback for when the idea is selected
 * @param onEdit - Optional callback for when the idea is edited
 * @param onSave - Optional callback for when edits are saved
 * @param editable - Whether the idea can be edited (default: false)
 * @param selectable - Whether the idea can be selected (default: true)
 */
export function IdeaCard({
  idea,
  onSelect,
  onGenerateBrief,
  onCustomizeAndGenerate,
  onEdit,
  onSave,
  editable = false,
  selectable = true
}: IdeaCardProps) {
  // console.log(`[IdeaCard] Rendering idea: ${idea.id}`, idea); // Keep commented out
  const [isEditing, setIsEditing] = useState(false);
  const [selectedLength, setSelectedLength] = useState<string>('medium');

  // State for individual editable fields
  const [editTitle, setEditTitle] = useState('');
  const [editSummary, setEditSummary] = useState('');
  const [editCategory, setEditCategory] = useState('');
  const [editEngagement, setEditEngagement] = useState(0);
  
  // State for funnel configuration
  const [funnelConfig, setFunnelConfig] = useState<FunnelConfig>(DEFAULT_FUNNEL_CONFIG);
  const [allFunnelStages, setAllFunnelStages] = useState<Array<{value: string, label: string}>>([]);
  
  // Fetch funnel configuration on mount and when category changes
  useEffect(() => {
    ConfigService.getFunnelStageConfig(idea.category).then(config => {
      setFunnelConfig({
        displayName: config.displayName,
        tooltip: config.tooltip
      });
    }).catch(error => {
      console.error('Failed to load funnel config:', error);
      setFunnelConfig(DEFAULT_FUNNEL_CONFIG);
    });
  }, [idea.category]);
  
  // Fetch all funnel stages for the dropdown
  useEffect(() => {
    ConfigService.getStrategyConfig().then(config => {
      const stages = Object.entries(config.funnel_stages).map(([key, value]) => ({
        value: key,
        label: value.displayName
      }));
      setAllFunnelStages(stages);
    }).catch(error => {
      console.error('Failed to load funnel stages:', error);
      // Fallback to basic stages
      setAllFunnelStages([
        { value: 'unclassified', label: 'Not classified' },
        { value: 'top_of_funnel', label: 'Top of Funnel' },
        { value: 'middle_funnel', label: 'Middle of Funnel' },
        { value: 'bottom_funnel', label: 'Bottom Funnel' }
      ]);
    });
  }, []);

  // Handle edit button click - Initialize edit state here
  const handleEditClick = () => {
    setEditTitle(idea.idea_text);
    setEditSummary(idea.summary || '');
    setEditCategory(idea.category || '');
    setEditEngagement(idea.engagement || 0);
    setIsEditing(true);
    if (onEdit) {
      onEdit(idea);
    }
  };

  // Handle save button click - Construct updated idea and call onSave
  const handleSaveClick = () => {
    if (onSave) {
      const updatedIdea: Idea = {
        ...idea,
        idea_text: editTitle,
        summary: editSummary,
        category: editCategory,
        engagement: editEngagement,
      };
      // console.log(`[IdeaCard] Saving updated idea:`, updatedIdea); // Keep commented out
      onSave(updatedIdea);
    }
    setIsEditing(false);
  };

  // Handle cancel button click - Just exit editing mode
  const handleCancelClick = () => {
    // No need to reset edit states, they'll be re-initialized on next edit click
    setIsEditing(false);
  };

  // Handle input changes - Update specific edit state
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (name === 'idea_text') setEditTitle(value);
    else if (name === 'summary') setEditSummary(value);
    else if (name === 'category') setEditCategory(value);
  };

  // Handle engagement rating change - Update specific edit state
  const handleEngagementChange = (value: number) => {
    setEditEngagement(value);
  };

  // Render edit mode
  if (isEditing) {
    return (
      <div className="mb-6 bg-card text-card-foreground">
        <TitleBar>
          <input
            name="idea_text" // Keep name for handleChange logic
            value={editTitle} // Use edit state
            onChange={handleChange}
            className="text-base font-bold font-mono border-none bg-transparent dark:text-white flex-1 flex items-center m-0 p-0 outline-none"
            maxLength={50}
          />
          <div className="w-8 flex-shrink-0"></div> {/* Spacer */}
        </TitleBar>
        <div className="p-4 border-b border-l border-r border-black dark:border-white rounded-b-lg dark:text-gray-200">
          <div className="flex justify-between mb-2">
            <div className="whitespace-nowrap flex items-center">
              <span className="font-semibold font-archivo">Funnel Stage:&nbsp;</span>
              <select
                name="category"
                value={editCategory}
                onChange={(e) => setEditCategory(e.target.value)}
                className="form-input font-archivo w-auto grayscale"
              >
                {allFunnelStages.map(stage => (
                  <option key={stage.value} value={stage.value}>
                    {stage.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center whitespace-nowrap">
              <span className="font-semibold font-archivo">Engagement Score:&nbsp;</span>
              <StarRating
                rating={editEngagement} // Use edit state
                onRatingChange={handleEngagementChange}
                readOnly={false}
              />
            </div>
          </div>
          <div className="mb-4">
            <span className="font-semibold font-archivo">Summary: </span>
            <textarea
              name="summary" // Keep name
              value={editSummary} // Use edit state
              onChange={handleChange}
              className="textarea-input font-archivo mt-1 min-h-[80px]"
              rows={3}
            />
          </div>
          {/* Citations are not editable, read from original idea prop */}
          {idea.citations && idea.citations.length > 0 && (
            <div className="mb-4">
              <div className="mb-2">
                <span className="font-semibold font-archivo">Supporting Quotes: </span>
              </div>
              <div className="space-y-2">
                {idea.citations.map((citation, index: number) => (
                  <Citation key={index} citation={citation} />
                ))}
              </div>
            </div>
          )}
          <div className="flex justify-end space-x-2">
            <Button onClick={handleCancelClick} variant="secondary" size="md">Cancel</Button>
            <Button onClick={handleSaveClick} variant="primary" size="md">Save Changes</Button>
          </div>
        </div>
      </div>
    );
  }

  // Render view mode (reads directly from idea prop)
  return (
    <div className="mb-6 bg-card text-card-foreground">
      <TitleBar>
        <TitleBarText>{idea.idea_text}</TitleBarText>
        {editable && (
          <Button
            onClick={handleEditClick} // Triggers edit mode and state initialization
            variant="outline"
            size="sm"
            className="w-8 flex-shrink-0 opacity-40 hover:opacity-100"
            title="Edit idea"
            aria-label="Edit idea"
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              className="text-black/70 hover:text-black/90 dark:text-white/70 dark:hover:text-white/90 transition-colors"
            >
              <path d="M10.5859 0.585788C11.3669 -0.195262 12.6332 -0.195262 13.4142 0.585788C14.1953 1.36684 14.1953 2.63316 13.4142 3.41421L12.6213 4.20711L9.79289 1.37868L10.5859 0.585788Z" fill="currentColor"/>
              <path d="M8.37868 2.79289L0 11.1716V14H2.82842L11.2071 5.62132L8.37868 2.79289Z" fill="currentColor"/>
            </svg>
          </Button>
        )}
      </TitleBar>
      <div className="p-4 border-b border-l border-r border-black dark:border-white rounded-b-lg dark:text-gray-200">
        <div className="flex justify-between mb-2 gap-4">
          <div className="flex items-center">
            <span className="font-semibold font-archivo">Funnel Stage:&nbsp;</span>
            <span className="font-archivo grayscale relative group cursor-help">
              {funnelConfig.displayName}
              <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-1 text-sm text-white bg-gray-900 rounded-md whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                {funnelConfig.tooltip}
              </span>
            </span>
          </div>
          <div className="flex items-center whitespace-nowrap flex-shrink-0">
            <span className="font-semibold font-archivo">Engagement Score:&nbsp;</span>
            <span title={idea.engagement_rationale}>
              <StarRating rating={idea.engagement || 0} />
            </span>
          </div>
        </div>
        <div className="mb-4">
          <span className="font-semibold font-archivo">Summary: </span>
          <span className="font-archivo">{idea.summary}</span>
        </div>
        {idea.citations && idea.citations.length > 0 && (
          <div className="mb-4">
            <div className="mb-2">
              <span className="font-semibold font-archivo">Supporting Quotes: </span>
            </div>
            <div className="space-y-2">
              {idea.citations.map((citation, index: number) => (
                <Citation key={index} citation={citation} />
              ))}
            </div>
          </div>
        )}
        {selectable && (onGenerateBrief || onCustomizeAndGenerate || onSelect) && (
          <div className="flex justify-between items-center">
            {onGenerateBrief ? (
              <>
                <LengthSelectorCompact
                  value={selectedLength}
                  onChange={setSelectedLength}
                />
                <GenerateButton
                  onGenerate={async () => onGenerateBrief(idea, selectedLength)}
                  variant="primary"
                  size="md"
                  loadingText="Generating Content..."
                >
                  Generate
                </GenerateButton>
              </>
            ) : (
              <div />
            )}
            {/* TODO: Implement customize & generate functionality
                - Allow users to guide AI attention to specific transcript topics
                - Add external context not in the transcript
                - Add specific details (metrics, testimonials, case studies)
            {onCustomizeAndGenerate && (
              <Button
                onClick={() => onCustomizeAndGenerate(idea)}
                variant="secondary"
                size="md"
              >
                Customize & Generate
              </Button>
            )}
            */}
            {onSelect && !onGenerateBrief && !onCustomizeAndGenerate && (
              <Button
                onClick={() => onSelect(idea)}
                variant="primary"
                size="md"
              >
                Select Idea
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default IdeaCard;
