import React from 'react';
import { But<PERSON> } from '../ui/button';
import { GenerateButton } from '../ui/generate-button';
import { Idea } from '../../types/idea';

interface IdeasBoardProps {
  ideas: Idea[];
  onGenerateBrief: (idea: Idea) => void;
  onCustomizeAndGenerate?: (idea: Idea) => void;
  isGenerating?: boolean;
  generatingIdeaId?: string;
}

export function IdeasBoard({ 
  ideas, 
  onGenerateBrief,
  onCustomizeAndGenerate,
  isGenerating = false, 
  generatingIdeaId 
}: IdeasBoardProps) {
  
  if (!ideas || ideas.length === 0) {
    return (
      <div className="bg-[#F5F5F5] dark:bg-[#1a1a1a] border border-black dark:border-white rounded-[4px] p-8 text-center">
        <div className="text-6xl mb-4 filter grayscale">💡</div>
        <p className="text-black dark:text-white font-mono">
          No ideas extracted yet. Upload a transcript to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="font-mono font-semibold italic text-2xl text-black dark:text-white">
        Extracted Ideas ({ideas.length})
      </h2>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {ideas.map((idea) => (
          <div 
            key={idea.id} 
            className="bg-[#F5F5F5] dark:bg-[#1a1a1a] border border-black dark:border-white rounded-[4px] p-4 space-y-4"
          >
            <div>
              <h3 className="font-semibold text-black dark:text-white mb-2 line-clamp-2">
                {idea.idea_text}
              </h3>
              {idea.citations && idea.citations.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs font-mono text-[#666666] dark:text-gray-400 mb-2">
                    Citations ({idea.citations.length}):
                  </p>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {idea.citations.map((citation, citIndex) => (
                      <div 
                        key={citIndex} 
                        className="text-xs text-[#666666] dark:text-gray-400 bg-[#EEEEEE] dark:bg-gray-700 rounded px-2 py-1"
                      >
                        <span className="font-medium">&quot;{citation.text}&quot;</span>
                        <span className="ml-1 text-[10px] opacity-70">
                          ({citation.start_index}-{citation.end_index})
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex gap-2">
              <GenerateButton
                onGenerate={async () => onGenerateBrief(idea)}
                variant="primary"
                disabled={isGenerating && generatingIdeaId !== idea.idea_text}
                className="flex-1"
                loadingText="Generating Brief..."
              >
                Generate Brief
              </GenerateButton>
              {onCustomizeAndGenerate && (
                <Button
                  onClick={() => onCustomizeAndGenerate(idea)}
                  variant="secondary"
                  disabled={isGenerating}
                  className="flex-1"
                >
                  Customize & Generate
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}