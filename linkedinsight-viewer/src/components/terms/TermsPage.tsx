import * as React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { Box } from '../ui/Box';
import { useAcceptTerms } from '../../hooks/useUserAgreements';
import { useToast } from '../ui/toast-provider';
import { cn } from '../../utils/utils';

export function TermsPage() {
  const navigate = useNavigate();
  const toast = useToast();
  const [hasScrolledToBottom, setHasScrolledToBottom] = React.useState(false);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);
  const acceptTermsMutation = useAcceptTerms();
  const [isAccepting, setIsAccepting] = React.useState(false);

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const element = event.currentTarget;
    const threshold = 50; // pixels from bottom
    const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < threshold;
    
    if (isNearBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAccept = async () => {
    setIsAccepting(true);
    try {
      await acceptTermsMutation.mutateAsync({});
      toast.success('Terms accepted successfully!');
      navigate('/home');
    } catch (error) {
      console.error('Failed to accept terms:', error);
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDecline = () => {
    // Sign out and redirect to sign-in page
    window.location.href = '/sign-in';
  };

  return (
    <Box center maxWidth="4xl" padding="responsive" className="min-h-screen py-16">
      <Box shadow className="w-full">
        <div className="px-6 pb-6 border-b border-[var(--border-subtle)]">
          <h1 className="text-3xl font-bold font-archivo">
            Terms of Service & Privacy Policy
          </h1>
        </div>

        <div 
          ref={scrollAreaRef}
          className="p-6 max-h-[60vh] overflow-y-auto"
          onScroll={handleScroll}
        >
          <div className="space-y-6">
            <section>
              <div className="space-y-4 text-[var(--text-secondary)]">
                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">1. Acceptance of Terms</h3>
                  <p>By accessing or using LinkedInsight, you agree to be bound by these Terms of Service.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">2. Alpha Service Disclaimer</h3>
                  <p>LinkedInsight is provided "AS IS" during the alpha period. We make no warranties about service reliability, accuracy, or availability. Data loss may occur, and features may change or be removed without notice.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">3. AI-Generated Content & Accuracy</h3>
                  <p>LinkedInsight uses artificial intelligence to analyze and generate content. AI systems can produce inaccurate, incomplete, or misleading information ("hallucinations"). While we've implemented measures to improve accuracy, AI-generated content may contain errors, biases, or inappropriate material. You are responsible for reviewing, fact-checking, and validating all AI outputs before use. We make no warranties about the accuracy, completeness, or reliability of AI-generated content.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">4. User Content & Licensing</h3>
                  <p>You retain ownership of your content. By using our service, you grant us a license to process, analyze, and store your content for the purpose of providing our services. All feedback provided during alpha may be used to improve the product.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">5. Limitation of Liability</h3>
                  <p>LinkedInsight shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the service.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">6. Acceptable Use</h3>
                  <p>You agree not to use LinkedInsight to: (a) violate any laws or regulations; (b) generate, distribute, or store illegal, harmful, or offensive content; (c) harass, threaten, or impersonate others; (d) attempt to gain unauthorized access to our systems; (e) reverse engineer or create competing services; or (f) violate others' intellectual property rights. We reserve the right to suspend accounts that violate these terms.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">7. Age Requirements</h3>
                  <p>You must be at least 13 years old to use LinkedInsight. If you are under 18, you represent that you have parental or guardian consent to use this service.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">8. Governing Law</h3>
                  <p>These terms are governed by the laws of Pennsylvania, United States, without regard to conflict of law principles. Any disputes will be resolved in the courts of Pennsylvania.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">9. Termination</h3>
                  <p>We may terminate or suspend your access at any time, with or without cause or notice.</p>
                </div>
                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">10. Information We Collect</h3>
                  <p>We collect information you provide directly (profile data, content) and automatically (usage data, device information).</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">11. How We Use Your Information</h3>
                  <p>We use your information to provide and improve our services, personalize your experience, and communicate with you about the service.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">12. Data Security</h3>
                  <p>We implement reasonable security measures to protect your data. However, no method of transmission over the Internet is 100% secure.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">13. Third-Party Services</h3>
                  <p>We use third-party services (Clerk for authentication, MongoDB for data storage) that have their own privacy policies.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">14. Your Rights</h3>
                  <p>You may request access to, correction of, or deletion of your personal data at any time.</p>
                </div>

                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">15. Contact Information</h3>
                  <p>If you have questions about these terms or need to report violations, please contact <NAME_EMAIL>.</p>
                </div>
              </div>
            </section>
          </div>
        </div>

        <div className="p-6 border-t border-[var(--border-subtle)]">
          <div className="flex gap-3 justify-center">
            <Button 
              variant="primary"
              size="lg"
              onClick={handleAccept}
              disabled={!hasScrolledToBottom || isAccepting}
              className="min-w-[160px]"
            >
              {isAccepting ? 'Accepting...' : 'Accept & Continue'}
            </Button>
            <Button 
              variant="outline"
              size="lg"
              onClick={handleDecline}
              disabled={isAccepting}
            >
              Decline
            </Button>
          </div>
        </div>
      </Box>
    </Box>
  );
}