import React from 'react';

/**
 * DesignTokenExplorer component
 * 
 * This component displays the current design tokens for testing and verification.
 * It shows color swatches for various token categories.
 */
const DesignTokenExplorer: React.FC = () => {
  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold mb-4">Design System Token Explorer</h1>
      
      <div>
        <h2 className="text-xl font-bold mb-2">Surface Tokens</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <ColorSwatch name="surface-primary" variable="--surface-primary" />
          <ColorSwatch name="surface-secondary" variable="--surface-secondary" />
          <ColorSwatch name="surface-tertiary" variable="--surface-tertiary" />
          <ColorSwatch name="surface-input" variable="--surface-input" />
          <ColorSwatch name="surface-input-hover" variable="--surface-input-hover" />
          <ColorSwatch name="surface-input-focus" variable="--surface-input-focus" />
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-2">Text Tokens</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <ColorSwatch name="text-primary" variable="--text-primary" />
          <ColorSwatch name="text-secondary" variable="--text-secondary" />
          <ColorSwatch name="text-tertiary" variable="--text-tertiary" />
          <ColorSwatch name="text-on-primary" variable="--text-on-primary" />
          <ColorSwatch name="text-on-secondary" variable="--text-on-secondary" />
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-2">Border Tokens</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <ColorSwatch name="border-subtle" variable="--border-subtle" />
          <ColorSwatch name="border-input" variable="--border-input" />
          <ColorSwatch name="border-input-focus" variable="--border-input-focus" />
          <ColorSwatch name="border-primary" variable="--border-primary" />
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-2">Component-Specific Tokens</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <ColorSwatch name="textarea-bg" variable="--textarea-bg" />
          <ColorSwatch name="textarea-bg-hover" variable="--textarea-bg-hover" />
          <ColorSwatch name="textarea-bg-focus" variable="--textarea-bg-focus" />
          <ColorSwatch name="shadow-container-bg" variable="--shadow-container-bg" />
          <ColorSwatch name="draft-bg" variable="--draft-bg" />
          <ColorSwatch name="button-primary-bg" variable="--button-primary-bg" />
          <ColorSwatch name="button-primary-text" variable="--button-primary-text" />
        </div>
      </div>
    </div>
  );
};

interface ColorSwatchProps {
  name: string;
  variable: string;
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({ name, variable }) => {
  return (
    <div className="border border-border rounded-md overflow-hidden">
      <div 
        className="h-16 w-full" 
        style={{ backgroundColor: `var(${variable})` }}
      />
      <div className="p-2 text-sm">
        <div className="font-bold">{name}</div>
        <div className="text-xs text-muted-foreground font-mono">{variable}</div>
      </div>
    </div>
  );
};

export default DesignTokenExplorer;
