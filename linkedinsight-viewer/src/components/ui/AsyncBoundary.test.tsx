import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { AsyncBoundary, checkIsEmpty } from './AsyncBoundary';

describe('AsyncBoundary', () => {
  const mockRetry = vi.fn();

  describe('component states', () => {
    it('should show loading state when isLoading is true', () => {
      render(
        <AsyncBoundary isLoading={true}>
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('should show custom loading component when provided', () => {
      render(
        <AsyncBoundary 
          isLoading={true}
          loadingComponent={<div>Custom Loading</div>}
        >
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Custom Loading')).toBeInTheDocument();
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });

    it('should show error state when isError is true', () => {
      const error = new Error('Test error');
      render(
        <AsyncBoundary isError={true} error={error} onRetry={mockRetry}>
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Test error')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('should show custom error component when provided', () => {
      const error = new Error('Test error');
      render(
        <AsyncBoundary 
          isError={true} 
          error={error}
          errorComponent={<div>Custom Error: {error.message}</div>}
        >
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Custom Error: Test error')).toBeInTheDocument();
      expect(screen.queryByText('Try Again')).not.toBeInTheDocument();
    });

    it('should show empty state when isEmpty is true', () => {
      render(
        <AsyncBoundary isEmpty={true}>
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('No data available.')).toBeInTheDocument();
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('should show custom empty component when provided', () => {
      render(
        <AsyncBoundary 
          isEmpty={true}
          emptyComponent={<div>Custom Empty State</div>}
        >
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Custom Empty State')).toBeInTheDocument();
      expect(screen.queryByText('No data available.')).not.toBeInTheDocument();
    });

    it('should show children when no special state is active', () => {
      render(
        <AsyncBoundary>
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Content')).toBeInTheDocument();
    });

    it('should handle network error messages specially', () => {
      const error = new Error('Failed to fetch');
      render(
        <AsyncBoundary isError={true} error={error}>
          <div>Content</div>
        </AsyncBoundary>
      );
      
      expect(screen.getByText('Unable to connect to server. Please check your connection and try again.')).toBeInTheDocument();
    });
  });

  describe('checkIsEmpty utility', () => {
    it('should return true for empty array', () => {
      expect(checkIsEmpty([])).toBe(true);
    });

    it('should return false for non-empty array', () => {
      expect(checkIsEmpty([1, 2, 3])).toBe(false);
    });

    it('should return true for empty object', () => {
      expect(checkIsEmpty({})).toBe(true);
    });

    it('should return false for non-empty object', () => {
      expect(checkIsEmpty({ a: 1 })).toBe(false);
    });

    it('should return true for null', () => {
      expect(checkIsEmpty(null)).toBe(true);
    });

    it('should return true for undefined', () => {
      expect(checkIsEmpty(undefined)).toBe(true);
    });

    it('should return false for non-empty string', () => {
      expect(checkIsEmpty('test')).toBe(false);
    });

    it('should return true for empty string', () => {
      expect(checkIsEmpty('')).toBe(true);
    });
  });
});