import React from 'react';
import { Layout } from '../layout/Layout';
import { Box } from './Box';
import { Tabs, TabPanel } from './custom-tabs';

interface ShadowedTabsContainerProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  tabs: { label: string; value: string; emoji?: string; disabled?: boolean }[];
  children: React.ReactNode;
  maxWidth?: string;
}


interface ScrollableTabPanelProps {
  value: string;
  activeTab: string;
  children: React.ReactNode;
  className?: string;
  noContentWrapper?: boolean;
}

/**
 * ScrollableTabPanel - A TabPanel with built-in scrolling
 * 
 * THE SINGLE SOURCE OF TRUTH for scrollable tab content.
 * This component ensures consistent behavior across all tabbed interfaces.
 * 
 * Features:
 * - Removes parent Tabs padding and re-applies it inside the scrollable area
 * - Scrollbar positioned against the right edge
 * - Consistent bottom padding (pb-6)
 * - Fills available height with flex
 */
export function ScrollableTabPanel({ 
  value, 
  activeTab, 
  children, 
  className = "",
  noContentWrapper = false
}: ScrollableTabPanelProps) {
  if (value !== activeTab) return null;
  
  // For custom layouts (like DraftLibraryPage with search bar)
  // Wrap the entire custom layout in the standard scrolling structure
  if (noContentWrapper) {
    return (
      <div className="flex flex-col h-full -mx-6 -mb-6 -mt-[16px]">
        {children}
      </div>
    );
  }
  
  // Standard layout with scrolling
  return (
    <div className="flex flex-col h-full -mx-6 -mb-6 -mt-[16px]">
      <div className="h-[16px]"></div>
      <div className={`flex-1 overflow-y-auto overflow-x-hidden min-h-0 [scrollbar-gutter:stable] ${className}`}>
        <div className="space-y-4 px-6">
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * ScrollableContentContainer - For use WITHIN ScrollableTabPanel with noContentWrapper
 * This provides the scrollable container WITH standard content padding
 * Children should NOT add their own px-6 or pb-6 padding!
 */
export function ScrollableContentContainer({ 
  children, 
  className = "" 
}: { children: React.ReactNode; className?: string }) {
  return (
    <div className={`flex-1 overflow-y-auto overflow-x-hidden min-h-0 [scrollbar-gutter:stable] ${className}`}>
      <div className="px-6 pb-6">
        {children}
      </div>
    </div>
  );
}

/**
 * ShadowedTabsContainer - The SINGLE SOURCE OF TRUTH for tabbed interfaces with shadows
 * 
 * This component provides a robust, reusable solution for tabs with proper shadow effects.
 * It uses the empirically-proven pattern: shadow-container-content no-padding border-0
 * 
 * Key features:
 * - White background with right/bottom shadows
 * - No double border issues
 * - Proper tab styling
 * - Configurable width
 * - Type-safe props
 * - Optional built-in scrolling with proper padding
 * 
 * Usage:
 * ```tsx
 * // With scrolling (recommended for content-heavy pages)
 * <ShadowedTabsContainer activeTab={tab} onTabChange={setTab} tabs={tabs} enableScrolling>
 *   <ScrollableTabPanel value="tab1" activeTab={tab}>Content</ScrollableTabPanel>
 * </ShadowedTabsContainer>
 * 
 * // Without scrolling (for simple content)
 * <ShadowedTabsContainer activeTab={tab} onTabChange={setTab} tabs={tabs}>
 *   <TabPanel value="tab1" activeTab={tab}>Content</TabPanel>
 * </ShadowedTabsContainer>
 * ```
 */
export function ShadowedTabsContainer({ 
  activeTab, 
  onTabChange, 
  tabs, 
  children, 
  maxWidth = "max-w-2xl"
}: ShadowedTabsContainerProps) {
  // Container now always uses flex and has a max-height
  // It will naturally be as tall as its content up to the max-height limit
  // Adjusted to 5rem: header (3rem) + layout padding (2rem total)
  const containerClasses = `${maxWidth} mx-auto flex flex-col h-auto max-h-[calc(100vh-5rem)]`;
  
  return (
    <Layout hasTabs={false} noShadowContainer activeTab={activeTab}>
      <div className={containerClasses}>
        <Box shadow hasTabs padding="none" className="flex-grow min-h-0 flex flex-col">
          <Tabs
            activeTab={activeTab}
            onTabChange={onTabChange}
            tabs={tabs}
            className="h-full"
          >
            {children}
          </Tabs>
        </Box>
      </div>
    </Layout>
  );
}

// Re-export TabPanel for convenience
export { TabPanel };