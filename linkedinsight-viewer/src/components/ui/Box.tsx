import React, { forwardRef } from 'react';
import { cn } from '../../utils/utils';

interface BoxProps extends React.HTMLAttributes<HTMLDivElement> {
  // Layout
  center?: boolean | 'horizontal' | 'vertical';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl';
  fullHeight?: boolean;
  
  // Visual
  shadow?: boolean;
  noBorder?: boolean;
  borderWidth?: 'default' | 'thick';
  
  // Spacing
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'responsive';
  
  // Tabs integration
  hasTabs?: boolean;
  fixedTabs?: boolean;
}

/**
 * Box - The single container component for all layout needs
 * Combines the functionality of ShadowContainer, CenteredContainer, and PageContainer
 */
export const Box = forwardRef<HTMLDivElement, BoxProps>(({
  children,
  className,
  
  // Layout
  center = false,
  maxWidth,
  fullHeight = false,
  
  // Visual
  shadow = false,
  noBorder = false,
  borderWidth,
  
  // Spacing
  padding = 'md',
  
  // Tabs
  hasTabs = false,
  fixedTabs = false,
  
  ...props
}, ref) => {
  const maxWidthClasses = {
    'sm': 'max-w-sm',
    'md': 'max-w-md',
    'lg': 'max-w-lg',
    'xl': 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
  };

  const paddingClasses = {
    'none': '',
    'sm': 'p-4',
    'md': 'p-6',
    'lg': 'p-8',
    'responsive': 'px-4 md:px-8'
  };

  // Handle centering logic
  const isCenteredVertical = center === true || center === 'vertical';
  const isCenteredHorizontal = center === true || center === 'horizontal';

  // Shadow container wrapper (only if shadow is true)
  if (shadow) {
    const borderWidthStyle = borderWidth === 'thick' 
      ? { '--border-width': 'var(--border-width-thick)' } as React.CSSProperties
      : undefined;

    return (
      <div className={cn("shadow-container", className)} ref={ref} {...props}>
        <div
          className={cn(
            "shadow-container-content",
            noBorder && "border-0",
            hasTabs && "has-tabs",
            !hasTabs && !isCenteredVertical && "overflow-y-auto",
            padding === 'none' && "no-padding",
            fixedTabs && hasTabs && "shadow-container-fixed",
            isCenteredVertical && "center-content",
            isCenteredVertical && padding === 'sm' && "compact-padding"
          )}
          style={borderWidthStyle}
        >
          {children}
        </div>
      </div>
    );
  }

  // Centered container logic (vertical centering)
  if (isCenteredVertical && !fullHeight) {
    return (
      <div className="flex items-center justify-center min-h-full" ref={ref} {...props}>
        <div className={cn(
          maxWidth && maxWidthClasses[maxWidth],
          "w-full",
          isCenteredHorizontal && "mx-auto",
          paddingClasses[padding],
          "relative z-20",
          className
        )}>
          {children}
        </div>
      </div>
    );
  }

  // Full height centered container
  if (fullHeight && isCenteredVertical) {
    return (
      <div className="w-full h-full px-4" ref={ref} {...props}>
        <div className={cn(
          maxWidth && maxWidthClasses[maxWidth],
          "w-full mx-auto h-full relative z-20",
          className
        )}>
          {children}
        </div>
      </div>
    );
  }

  // Basic container (just width constraints and padding)
  return (
    <div 
      className={cn(
        "w-full",
        maxWidth && maxWidthClasses[maxWidth],
        isCenteredHorizontal && "mx-auto",
        paddingClasses[padding],
        fullHeight && "h-full",
        className
      )}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  );
});

Box.displayName = 'Box';