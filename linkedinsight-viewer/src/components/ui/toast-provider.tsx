import React, { createContext, useContext } from 'react';
import { toast as sonnerToast, Toaster as SonnerToaster } from 'sonner';

// Define the context type
type ToastContextType = {
  success: (message: string, options?: ToastOptions) => void;
  error: (message: string, options?: ToastOptions) => void;
  info: (message: string, options?: ToastOptions) => void;
  warning: (message: string, options?: ToastOptions) => void;
  loading: (message: string, options?: ToastOptions) => void;
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: unknown) => string);
    },
    options?: ToastOptions
  ) => void;
};

// Define toast options
type ToastOptions = {
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
};

// Create the context with default values
const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  // Create toast methods
  const success = (message: string, options?: ToastOptions) => {
    sonnerToast.success(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action && {
        label: options.action.label,
        onClick: options.action.onClick,
      },
      dismissible: options?.dismissible,
    });
  };

  const error = (message: string, options?: ToastOptions) => {
    sonnerToast.error(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action && {
        label: options.action.label,
        onClick: options.action.onClick,
      },
      dismissible: options?.dismissible,
    });
  };

  const info = (message: string, options?: ToastOptions) => {
    sonnerToast.info(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action && {
        label: options.action.label,
        onClick: options.action.onClick,
      },
      dismissible: options?.dismissible,
    });
  };

  const warning = (message: string, options?: ToastOptions) => {
    sonnerToast.warning(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action && {
        label: options.action.label,
        onClick: options.action.onClick,
      },
      dismissible: options?.dismissible,
    });
  };

  const loading = (message: string, options?: ToastOptions) => {
    sonnerToast.loading(message, {
      description: options?.description,
      duration: options?.duration,
    });
  };

  const promise = <T,>(
    promiseToast: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: unknown) => string);
    },
    options?: ToastOptions
  ): void => {
    sonnerToast.promise(promiseToast, {
      ...messages,
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
      dismissible: options?.dismissible
    });
  };

  return (
    <ToastContext.Provider
      value={{
        success,
        error,
        info,
        warning,
        loading,
        promise,
      }}
    >
      <SonnerToaster
        position="top-right"
        theme="system" 
        closeButton={false}
        richColors={false}
        expand={false}
        duration={3000}
      />
      {children}
    </ToastContext.Provider>
  );
}

// Hook to use the toast context
export function useToast() {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}