import React from 'react';

export interface LengthOption {
  value: string;
  label: string;
  description?: string;
}

interface LengthSelectorProps {
  value: string;
  onChange: (value: string) => void;
  options?: LengthOption[];
  label?: string;
  className?: string;
}

const defaultOptions: LengthOption[] = [
  { value: 'short', label: 'Short', description: '150-200 words' },
  { value: 'medium', label: 'Medium', description: '201-300 words' },
  { value: 'long', label: 'Long', description: '301-500 words' }
];

export function LengthSelector({ 
  value, 
  onChange, 
  options = defaultOptions,
  label = 'Post Length:',
  className = ""
}: LengthSelectorProps) {
  return (
    <div className={`flex gap-3 items-center ${className}`}>
      <span className="inline-label">{label}</span>
      <div className="radio-inline">
        {options.map((option) => (
          <div key={option.value} className="radio-option">
            <input 
              type="radio" 
              id={`length-${option.value}`}
              name="postLength" 
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(e.target.value)}
            />
            <label 
              htmlFor={`length-${option.value}`}
              title={option.description}
            >
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
}

export default LengthSelector;