import React from 'react';

export interface LengthOption {
  value: string;
  label: string;
  description?: string;
}

interface LengthSelectorProps {
  value: string;
  onChange: (value: string) => void;
  options?: LengthOption[];
  label?: string;
  className?: string;
}

const defaultOptions: LengthOption[] = [
  { value: 'short', label: 'Short', description: '150-200 words' },
  { value: 'medium', label: 'Medium', description: '201-300 words' },
  { value: 'long', label: 'Long', description: '301-500 words' }
];

export function LengthSelector({ 
  value, 
  onChange, 
  options = defaultOptions,
  label = 'Post Length:',
  className = ""
}: LengthSelectorProps) {
  return (
    <div className={`length-selector ${className}`}>
      <span className="length-selector-label">
        {label}
      </span>
      <div className="length-selector-options">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`[LengthSelector] Button clicked: ${option.value}`);
              console.log(`[LengthSelector] Current value: ${value}`);
              onChange(option.value);
            }}
            className={`length-selector-button ${value === option.value ? 'active' : ''}`}
            title={option.description}
            aria-label={`Select ${option.label} post length`}
          >
            <span>{option.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}

export default LengthSelector;