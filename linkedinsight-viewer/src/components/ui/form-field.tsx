import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';

interface FormFieldProps {
  label: string;
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  type?: 'text' | 'textarea' | 'select';
  rows?: number;
  options?: Array<{ value: string; label: string }>;
  className?: string;
}

export function FormField({ 
  label, 
  id, 
  name, 
  value, 
  onChange, 
  onValueChange,
  placeholder, 
  required = false, 
  type = 'text', 
  rows = 3,
  options = [],
  className = ""
}: FormFieldProps) {
  const renderInput = () => {
    switch (type) {
      case 'textarea':
        return (
          <textarea
            id={id}
            name={name}
            rows={rows}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            className={`textarea-input ${className}`}
            required={required}
          />
        );
      case 'select':
        return (
          <Select value={value} onValueChange={onValueChange}>
            <SelectTrigger className={`form-input ${className}`}>
              <SelectValue placeholder="Please select..." />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      default:
        return (
          <input
            type="text"
            id={id}
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            className={`form-input ${className}`}
            required={required}
          />
        );
    }
  };

  return (
    <div className="form-field">
      <label htmlFor={id} className="form-label">
        {label}:
      </label>
      {renderInput()}
    </div>
  );
}