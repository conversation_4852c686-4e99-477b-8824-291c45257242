import React from 'react';

interface TabProps {
  label: string;
  value: string;
  emoji?: string;
  isActive: boolean;
  onClick: () => void;
  isLast?: boolean;
  /**
   * Whether the tab is disabled and non-clickable.
   * Used for features that are not yet implemented or available.
   */
  disabled?: boolean;
}

interface TabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  tabs: {
    label: string;
    value: string;
    emoji?: string;
    disabled?: boolean;
  }[];
  children: React.ReactNode;
  className?: string;
  fixedTabs?: boolean;
  disabledTabs?: string[];
}

export function Tab({ label, emoji, isActive, onClick, isLast = false, disabled = false }: TabProps) {
  return (
    <button
      className={`tab ${isActive ? 'active' : ''} ${!isLast ? 'border-r border-black dark:border-white' : ''}`}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      aria-disabled={disabled}
      title={disabled && !isActive ? "Tab locked while agent is processing" : ""}
    >
      {emoji && <span className="tab-emoji" aria-hidden="true">{emoji}</span>}
      {label}
    </button>
  );
}

export function Tabs({ activeTab, onTabChange, tabs, children, className = '', fixedTabs = false, disabledTabs = [] }: TabsProps) {
  // Always use flex layout for better adaptability
  return (
    <div className={`flex flex-col h-full transition-opacity duration-300 ${className}`}>
      {/* Tab headers: fixed, non-scrollable */}
      <div className="flex-shrink-0 flex border-b border-black dark:border-white">
        {tabs.map((tab, index) => (
          <Tab
            key={tab.value}
            label={tab.label}
            value={tab.value}
            emoji={tab.emoji}
            isActive={activeTab === tab.value}
            onClick={() => onTabChange(tab.value)}
            isLast={index === tabs.length - 1}
            disabled={tab.disabled || disabledTabs.includes(tab.value)}
          />
        ))}
      </div>
      
      {/* Content area: grows to fill available space, scrolls when needed */}
      <div className="flex-grow min-h-0 overflow-y-auto">
        {/* Keep padding wrapper inside scrollable area */}
        <div className="px-6 pb-6 pt-[16px]">
          {children}
        </div>
      </div>
    </div>
  );
}

export function TabPanel({ value, activeTab, children }: { value: string; activeTab: string; children: React.ReactNode }) {
  if (value !== activeTab) return null;
  return <>{children}</>;
}
