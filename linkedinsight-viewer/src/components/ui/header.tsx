import { ThemeToggle } from "./theme-toggle"
import { useNavigate, useLocation } from "react-router-dom"
import { SignedIn, SignedOut, SignInButton } from "@clerk/clerk-react"
import { BreadcrumbNav } from "../layout/BreadcrumbNav"
import { UserMenu } from "./user-menu"
import { shouldShowBreadcrumbs } from "../../config/routes"
import { navigateWithTransition } from "../../utils/navigation"

interface HeaderProps {
  activeTab?: string;
  showAuth?: boolean;
}

export function Header({ activeTab, showAuth = true }: HeaderProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogoClick = () => {
    navigateWithTransition(navigate, showAuth ? '/home' : '/');
  };

  // Use centralized route configuration to determine breadcrumb visibility
  const showBreadcrumbs = shouldShowBreadcrumbs(location.pathname, activeTab);
  
  // Hide auth buttons on sign-in and sign-up pages
  const isAuthPage = location.pathname.includes('/sign-in') || location.pathname.includes('/sign-up');

  return (
    <header className="bg-[#2d2d2d] px-6 py-2 fixed top-0 left-0 right-0 z-50 flex items-center h-[3rem]">
      <div className="flex items-center w-full">
        {/* Left - Logo */}
        <div
          className="flex items-center cursor-pointer hover:opacity-80 transition-opacity p-1 rounded"
          onClick={handleLogoClick}
          title="Go to Home"
        >
          {/* STORYD Logo (without text) */}
          <img
            src="/STORYD_Icon_Black.png"
            alt="Logo"
            width={20}
            height={20}
            className="brightness-[10] invert"
          />
        </div>

        {/* Center - Breadcrumbs */}
        <div className="flex-1 flex justify-center items-center">
          {showBreadcrumbs && (
            <SignedIn>
              <div className="mx-4 flex items-center">
                <BreadcrumbNav
                  activeTab={activeTab}
                  className="mb-0"
                />
              </div>
            </SignedIn>
          )}
        </div>

        {/* Right - Theme Toggle and Auth Controls */}
        <div className="flex items-center gap-4">
          {showAuth ? (
            <>
              <SignedOut>
                <SignInButton mode="modal">
                  <button className="px-4 py-1 rounded-md bg-white text-black hover:bg-gray-200 transition-colors">
                    Sign In
                  </button>
                </SignInButton>
              </SignedOut>

              <ThemeToggle />

              <SignedIn>
                <UserMenu />
              </SignedIn>
            </>
          ) : (
            <>
              {!isAuthPage && (
                <>
                  <a href="/sign-in" className="text-white hover:opacity-80 transition-opacity text-sm">
                    Log In
                  </a>
                  <a href="/sign-up" className="px-3 py-1.5 rounded-md bg-white text-black hover:bg-gray-200 transition-colors text-sm font-medium">
                    Get Started
                  </a>
                </>
              )}
              <ThemeToggle />
            </>
          )}
        </div>
      </div>
    </header>
  )
}
