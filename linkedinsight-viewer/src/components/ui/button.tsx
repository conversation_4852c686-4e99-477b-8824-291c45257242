import * as React from "react"
import { cn } from "../../utils/utils"
import { LoadingSpinner } from '../common/LoadingSpinner';

const Button = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "primary" | "secondary" | "outline";
    size?: "sm" | "md" | "lg" | "xl";
    loading?: boolean;
    loadingText?: string;
  }
>(({ className, variant = "default", size = "md", loading = false, loadingText, children, disabled, ...props }, ref) => (
  <button
    ref={ref}
    disabled={loading || disabled}
    className={cn(
      // Base styles
      "inline-flex items-center justify-center rounded-sm font-medium font-ibm-plex-mono cursor-pointer relative",
      // Transform and transition with more obvious effect
      "transform transition-all duration-150",
      // Scale effect
      "active:scale-[0.98] hover:scale-[1.02]",
      // Disabled state
      "disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed",

      // Variants - using our design system tokens
      variant === "default" && "bg-black text-white hover:bg-black/90 dark:bg-white dark:text-black dark:hover:bg-white/90",
      variant === "primary" && "bg-black text-white hover:bg-black/90 dark:bg-white dark:text-black dark:hover:bg-white/90 transition-colors",
      variant === "secondary" && "button-secondary transition-colors border border-black/20 dark:border-white/20",
      variant === "outline" && "border border-black dark:border-white bg-transparent text-black dark:text-white hover:bg-black/5 dark:hover:bg-white/10 transition-colors",

      // Sizes - slightly reduced from previous values
      size === "sm" && "h-7 px-3 py-1 text-sm",
      size === "md" && "h-9 px-4 py-1.5",
      size === "lg" && "h-10 px-5 py-1.5",
      size === "xl" && "h-12 px-6 py-2 text-base",

      className
    )}
    {...props}
  >
    {loading ? (
      <>
        <LoadingSpinner size="sm" className="absolute" />
        <span className="invisible">{children}</span>
      </>
    ) : (
      children
    )}
  </button>
))
Button.displayName = "Button"

export { Button }
