import { ReactNode } from 'react';
import { ErrorService } from '../../utils/errorHandling';

interface AsyncBoundaryProps {
  isLoading?: boolean;
  isFetching?: boolean;
  isError?: boolean;
  error?: Error | null;
  isEmpty?: boolean;
  children: ReactNode;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
  emptyComponent?: ReactNode;
  onRetry?: () => void;
  showLoadingOnRefetch?: boolean;
}

export function AsyncBoundary({
  isLoading = false,
  isFetching = false,
  isError = false,
  error = null,
  isEmpty = false,
  children,
  loadingComponent,
  errorComponent,
  emptyComponent,
  onRetry,
  showLoadingOnRefetch = false,
}: AsyncBoundaryProps) {
  // Show loading state on initial load or when refetching (if enabled)
  const showLoading = isLoading || (showLoadingOnRefetch && isFetching);
  
  if (showLoading) {
    return <>{loadingComponent || <DefaultLoadingState />}</>;
  }

  if (isError && error) {
    return <>{errorComponent || <DefaultErrorState error={error} onRetry={onRetry} />}</>;
  }

  if (isEmpty) {
    return <>{emptyComponent || <DefaultEmptyState />}</>;
  }

  return <>{children}</>;
}

// Default Loading State - Empty by default to avoid double loading indicators
function DefaultLoadingState() {
  return null;
}

// Default Error State
interface DefaultErrorStateProps {
  error: Error;
  onRetry?: () => void;
}

function DefaultErrorState({ error, onRetry }: DefaultErrorStateProps) {
  const errorMessage = ErrorService.getMessage(error, 'Loading data');

  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <div className="text-center">
        <p className="text-red-600 dark:text-red-400 mb-4">{errorMessage}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}

// Default Empty State
function DefaultEmptyState() {
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <div className="text-center text-gray-500">
        No data available.
      </div>
    </div>
  );
}

// Utility function to check if an array is empty
export function checkIsEmpty(data: unknown): boolean {
  if (Array.isArray(data)) {
    return data.length === 0;
  }
  if (data && typeof data === 'object') {
    return Object.keys(data).length === 0;
  }
  return !data;
}