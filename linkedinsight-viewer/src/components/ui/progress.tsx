"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cn } from "../../utils/utils"

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative flex-grow h-10 rounded-lg overflow-hidden mb-0",
        "bg-[#D9D9D9] border border-black",
        className
      )}
      {...props}
    >
      <div className="absolute inset-0 w-full h-full">
        <div className="h-full bg-[#3D3D3D]" />
        <div className="absolute inset-0 bg-dots" />
        <div className="absolute inset-0 animate-pulse-propagation" />
      </div>
    </ProgressPrimitive.Root>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
