import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser, useClerk } from '@clerk/clerk-react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { navigateWithTransition } from '../../utils/navigation';

export function UserMenu() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const navigate = useNavigate();
  
  // Mock mode toggle (<NAME_EMAIL>)
  const [mockMode, setMockMode] = useState(() => {
    return localStorage.getItem('mockMode') === 'true';
  });
  
  const isDeveloper = user?.primaryEmailAddress?.emailAddress === '<EMAIL>';
  
  useEffect(() => {
    localStorage.setItem('mockMode', mockMode.toString());
  }, [mockMode]);

  const handleSignOut = () => {
    signOut();
  };

  const handleContentStrategyClick = () => {
    navigateWithTransition(navigate, '/content-strategy-form');
  };


  const handleContentPreferencesClick = () => {
    navigateWithTransition(navigate, '/content-preferences');
  };

  const handleDraftLibraryClick = () => {
    navigateWithTransition(navigate, '/draft-library');
  };

  const handleAccountClick = () => {
    // Open Clerk's user profile modal
    if (window.Clerk) {
      window.Clerk.openUserProfile();
    }
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button
          className="rounded-full w-8 h-8 overflow-hidden focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          aria-label="User menu"
        >
          {user?.imageUrl ? (
            <img
              src={user.imageUrl}
              alt="User avatar"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-600">
              {user?.firstName?.[0] || user?.username?.[0] || '?'}
            </div>
          )}
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="dropdown-menu-content"
          sideOffset={5}
          align="end"
        >
          {/* User info */}
          <div className="dropdown-menu-user-info">
            <p className="dropdown-menu-user-name">
              {user?.fullName || user?.username}
            </p>
            <p className="dropdown-menu-user-email">
              {user?.primaryEmailAddress?.emailAddress}
            </p>
          </div>

          {/* Direct links - Content Preferences and Content Strategy */}
          <DropdownMenu.Item
            className="dropdown-menu-item"
            onSelect={handleContentPreferencesClick}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="dropdown-menu-item-icon"
            >
              <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            Content Preferences
          </DropdownMenu.Item>

          <DropdownMenu.Item
            className="dropdown-menu-item"
            onSelect={handleContentStrategyClick}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="dropdown-menu-item-icon"
            >
              <path d="M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1V21c0 .4.2.8.5 1.1.3.3.7.5 1.1.5h7.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z"/>
              <path d="M15 2v5h5"/>
              <path d="M10 12h2"/>
              <path d="M10 16h4"/>
              <path d="M10 20h4"/>
            </svg>
            Content Strategy
          </DropdownMenu.Item>


          <DropdownMenu.Item
            className="dropdown-menu-item"
            onSelect={handleDraftLibraryClick}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="dropdown-menu-item-icon"
            >
              <path d="M12 20h9"></path>
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
            </svg>
            Draft Library
          </DropdownMenu.Item>


          {/* Developer-only mock mode toggle */}
          {isDeveloper && (
            <DropdownMenu.Item
              className="dropdown-menu-item"
              onSelect={() => setMockMode(!mockMode)}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
                className="dropdown-menu-item-icon"
              >
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
                <path d="M8 14h.01"/>
                <path d="M12 14h.01"/>
                <path d="M16 14h.01"/>
                <path d="M8 18h.01"/>
                <path d="M12 18h.01"/>
                <path d="M16 18h.01"/>
              </svg>
              Mock Mode: {mockMode ? 'ON' : 'OFF'}
            </DropdownMenu.Item>
          )}

          <DropdownMenu.Separator className="dropdown-menu-separator" />

          {/* Account management */}
          <DropdownMenu.Item
            className="dropdown-menu-item"
            onSelect={handleAccountClick}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="dropdown-menu-item-icon"
            >
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            Manage Account
          </DropdownMenu.Item>

          <DropdownMenu.Separator className="dropdown-menu-separator" />

          {/* Sign out */}
          <DropdownMenu.Item
            className="dropdown-menu-item"
            onSelect={handleSignOut}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className="dropdown-menu-item-icon"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Sign Out
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}