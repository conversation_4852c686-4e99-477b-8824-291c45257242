import React from 'react';
import { Button } from './button';
import { useGenerateAction } from '../../hooks/useGenerateAction';

interface GenerateButtonProps extends Omit<React.ComponentProps<typeof Button>, 'onClick' | 'loading'> {
  onGenerate: () => Promise<any>;
  loadingText?: string;
}

/**
 * A specialized button component for generation actions
 * Handles loading state automatically and prevents double-clicks
 */
export function GenerateButton({ 
  onGenerate, 
  children = "Generate",
  loadingText = "Generating...",
  ...props 
}: GenerateButtonProps) {
  const { isGenerating, executeGenerate } = useGenerateAction();
  
  return (
    <Button
      {...props}
      loading={isGenerating}
      loadingText={loadingText}
      onClick={() => executeGenerate(onGenerate)}
    >
      {children}
    </Button>
  );
}