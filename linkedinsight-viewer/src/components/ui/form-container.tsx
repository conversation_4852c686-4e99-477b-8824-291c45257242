import React from 'react';

interface FormContainerProps {
  title: string;
  error?: string | null;
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  submitText: string;
  isSubmitting?: boolean;
  submitButtonClass?: string;
  formClassName?: string;
}

export function FormContainer({
  title,
  error,
  isLoading = false,
  loadingText = "Loading...",
  children,
  onSubmit,
  submitText,
  isSubmitting = false,
  submitButtonClass = "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 dark:bg-white dark:text-black dark:hover:bg-gray-200",
  formClassName = "form-container"
}: FormContainerProps) {
  if (isLoading) {
    return (
      <div className="form-loading-container">
        <p>{loadingText}</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="form-title">{title}</h1>
      {error && (
        <div className="form-error" role="alert">
          <strong className="form-error-strong">Error: </strong>
          <span>{error}</span>
        </div>
      )}
      <form onSubmit={onSubmit} className={formClassName}>
        {children}
        <div className="form-submit-container">
          <button
            type="submit"
            disabled={isSubmitting}
            className="button button-primary button-lg"
          >
            {submitText}
          </button>
        </div>
      </form>
    </div>
  );
}