import React from 'react';

export interface LengthOption {
  value: string;
  label: string;
  description?: string;
}

interface LengthSelectorCompactProps {
  value: string;
  onChange: (value: string) => void;
  options?: LengthOption[];
  label?: string;
  className?: string;
}

const defaultOptions: LengthOption[] = [
  { value: 'short', label: 'Short', description: '150-200 words' },
  { value: 'medium', label: 'Medium', description: '201-300 words' },
  { value: 'long', label: 'Long', description: '301-500 words' }
];

export function LengthSelectorCompact({ 
  value, 
  onChange, 
  options = defaultOptions,
  label = 'Post Length:',
  className = ""
}: LengthSelectorCompactProps) {
  return (
    <div className={`length-selector-compact ${className}`}>
      <span className="length-selector-label">
        {label}
      </span>
      <div className="length-selector-options">
        {options.map((option) => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={`length-selector-button ${value === option.value ? 'active' : ''}`}
            title={option.description}
          >
            <span>{option.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}

export default LengthSelectorCompact;