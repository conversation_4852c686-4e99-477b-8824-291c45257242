import React, { useState, useRef, useEffect } from 'react';
import { DiffViewer } from './DiffViewer';

interface DraftTextareaProps {
  id?: string;
  value: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  originalContent?: string | null;
  showDiffToggle?: boolean;
  className?: string;
}

export function DraftTextarea({ 
  id,
  value, 
  onChange, 
  placeholder,
  disabled = false,
  originalContent = null,
  showDiffToggle = true,
  className = ''
}: DraftTextareaProps) {
  const [showDiff, setShowDiff] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const diffRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);

  // Only show diff toggle if we have original content to compare
  const canShowDiff = showDiffToggle && originalContent && originalContent !== value;

  // Handle toggle show diff
  const handleToggleShowDiff = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Save current scroll position
    if (showDiff && diffRef.current) {
      scrollPositionRef.current = diffRef.current.scrollTop;
    } else if (!showDiff && textareaRef.current) {
      scrollPositionRef.current = textareaRef.current.scrollTop;
    }
    
    setShowDiff(!e.target.checked);
  };

  // Restore scroll position after toggling
  useEffect(() => {
    if (showDiff && diffRef.current) {
      diffRef.current.scrollTop = scrollPositionRef.current;
    } else if (!showDiff && textareaRef.current) {
      textareaRef.current.scrollTop = scrollPositionRef.current;
    }
  }, [showDiff]);

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {canShowDiff && (
        <div className="flex justify-between items-center mb-2">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={!showDiff}
              onChange={handleToggleShowDiff}
              className="rounded accent-black"
            />
            Hide changes
          </label>
        </div>
      )}
      
      {showDiff && originalContent ? (
        <div 
          ref={diffRef}
          className="draft-textarea h-full p-4 overflow-auto"
        >
          <DiffViewer 
            originalContent={originalContent} 
            currentContent={value}
          />
        </div>
      ) : (
        <textarea
          id={id}
          ref={textareaRef}
          className="draft-textarea"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          disabled={disabled}
        />
      )}
    </div>
  );
}