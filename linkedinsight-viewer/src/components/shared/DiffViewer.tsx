import React from 'react';
import * as Diff from 'diff';

interface DiffViewerProps {
  originalContent: string;
  currentContent: string;
  className?: string;
}

export function DiffViewer({ originalContent, currentContent, className = '' }: DiffViewerProps) {
  const renderDiff = () => {
    // If no original content or both are empty, don't show diff
    if (!originalContent && !currentContent) {
      return <span className="text-muted-foreground">No content to compare</span>;
    }
    
    // If no original but we have current, don't highlight everything as new
    if (!originalContent && currentContent) {
      return <span>{currentContent}</span>;
    }
    
    // If content is identical, just show it without diff
    if (originalContent === currentContent) {
      return <span>{currentContent}</span>;
    }
    
    // Use diffWords for word-level comparison
    const diff = Diff.diffWords(originalContent, currentContent);
    
    return diff.map((part, index) => {
      let style: React.CSSProperties = {};
      
      if (part.added) {
        style = { 
          backgroundColor: '#a7f3d0',
          textDecoration: 'underline',
          textUnderlineOffset: '3px'
        };
      } else if (part.removed) {
        style = { 
          backgroundColor: '#fecaca',
          textDecoration: 'line-through',
          opacity: 0.7
        };
      }
      
      return (
        <span key={index} style={style}>
          {part.value}
        </span>
      );
    });
  };

  return (
    <div className={`whitespace-pre-wrap font-mono text-sm ${className}`}>
      {renderDiff()}
    </div>
  );
}