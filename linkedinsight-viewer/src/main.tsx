import React, { useContext, useEffect, lazy, Suspense } from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { ClerkProvider, SignIn, SignUp } from '@clerk/clerk-react'
import { dark } from '@clerk/themes'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { queryClient } from './lib/query-client'
import { applyClerkStyles } from './utils/clerkStyles'
import { ProtectedRoute } from './components/auth/ProtectedRoute'
import { ErrorBoundary } from './components/common/ErrorBoundary'
import Home from './components/Home.tsx'
import LandingPage from './components/LandingPage.tsx'
import './styles/globals.css'
import './styles/toast.css'
import './styles/page-transitions.css'
import { Theme<PERSON>rovider, ThemeContext } from './components/context/ThemeProvider'
import { ToastProvider } from './components/ui/toast-provider'
import { PostHogProvider } from 'posthog-js/react'
import { Header } from './components/ui/header'
import { DraftLibraryPage } from './components/DraftLibraryPage.tsx'
import ContentStrategyPage from './components/content-strategy/ContentStrategyPage'
import ContentPreferencesPage from './components/content-preferences/ContentPreferencesPage'
import UserPreferencesPage from './components/UserPreferencesPage'
import { IdeasPage } from './components/transcript/IdeasPage'
import NotesInputPage from './components/agent/NotesInputPage'
import { TranscriptInputPage } from './components/transcript/TranscriptInputPage'
import { UserFeedbackWidget } from './components/feedback/UserFeedbackWidget'
import { TermsPage } from './components/terms/TermsPage'

// Disable console logs in production builds
if (import.meta.env.PROD) {
  console.log = () => {};
}

// Import your Publishable Key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key")
}

// Get the base URL for redirects
const appUrl = import.meta.env.PROD
  ? 'https://linkedinsight-viewer.vercel.app/home'
  : 'http://localhost:5175/home';

// Additional lazy-loaded pages that were previously imported statically
const ContentAgentPage = lazy(() => import('./components/ContentAgentPage.tsx'));
const DesignSystemPage = lazy(() => import('./components/DesignSystemPage.tsx'));
const QuickStartGuidePage = lazy(() => import('./components/QuickStartGuidePage'));
const EditorAgentPage = lazy(() => import('./components/editor/EditorAgentPage'));
const InterviewingAgentView = lazy(() => import('./components/interview/InterviewingAgentView').then(m => ({ default: m.InterviewingAgentView })));
const InteractiveInterviewAgent = lazy(() => import('./components/interview/InteractiveInterviewAgent').then(m => ({ default: m.default })));

// New component to bridge ThemeContext and ClerkProvider
function AppRoot() {
  const themeContext = useContext(ThemeContext);
  
  // Apply Clerk styles when component mounts
  useEffect(() => {
    applyClerkStyles();
  }, []);

  // Ensure themeContext is defined before accessing theme, provide a default or handle loading
  // For simplicity, assuming it's always defined after ThemeProvider wraps it.
  // A more robust approach might involve checking if themeContext is undefined if ThemeProvider could be conditionally rendered
  // or if there's an initial undefined state from ThemeContext.
  const currentTheme = themeContext ? themeContext.theme : 'system'; // Default to system or light if context is not yet ready

  let clerkTheme;
  if (currentTheme === 'dark') {
    clerkTheme = dark;
  } else if (currentTheme === 'system') {
    // For system, Clerk might pick up OS theme, or you can check matchMedia here too
    // For now, let's default system to light for Clerk, or let Clerk handle it by not passing baseTheme
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (systemPrefersDark) {
        clerkTheme = dark;
    }
    // If systemPrefersDark is false, clerkTheme remains undefined, letting Clerk use its default light theme.
  }
  // If currentTheme is 'light', clerkTheme is also undefined.

  return (
    <ClerkProvider
      publishableKey={PUBLISHABLE_KEY}
      appearance={{
        baseTheme: clerkTheme,
        layout: {
          socialButtonsPlacement: 'bottom',
          socialButtonsVariant: 'iconButton',
        },
        elements: {
          rootBox: 'mx-auto',
          card: 'mx-auto',
        },
        variables: {
          colorPrimary: '#000000'
        }
      }}
    >
      <BrowserRouter>
        <UserFeedbackWidget />
        <Routes>
          {/* Public routes */}
          <Route
            path="/sign-in/*"
            element={
              <>
                <Header showAuth={false} />
                <SignIn routing="path" path="/sign-in" forceRedirectUrl={appUrl} />
              </>
            }
          />
          <Route
            path="/sign-up/*"
            element={
              <>
                <Header showAuth={false} />
                <SignUp routing="path" path="/sign-up" forceRedirectUrl={appUrl} />
              </>
            }
          />

          {/* Public landing page */}
          <Route path="/" element={<LandingPage />} />
          
          {/* Terms page - protected by Clerk auth but not terms */}
          <Route
            path="/terms"
            element={
              <ProtectedRoute>
                <TermsPage />
              </ProtectedRoute>
            }
          />
          
          {/* Protected routes - require both auth and terms acceptance */}
          <Route
            path="/home"
            element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            }
          />
          <Route
            path="/content-agent"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <ContentAgentPage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/notes-input"
            element={
              <ProtectedRoute>
                <NotesInputPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/draft-library"
            element={
              <ProtectedRoute>
                <DraftLibraryPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/content-strategy-form"
            element={
              <ProtectedRoute>
                <ContentStrategyPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/content-preferences"
            element={
              <ProtectedRoute>
                <ContentPreferencesPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user-preferences"
            element={
              <ProtectedRoute>
                <UserPreferencesPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/interview"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <InterviewingAgentView />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/interactive-interview"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <InteractiveInterviewAgent />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/design-system"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <DesignSystemPage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/transcript-input"
            element={
              <ProtectedRoute>
                <TranscriptInputPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/ideas"
            element={
              <ProtectedRoute>
                <IdeasPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/quick-start"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <QuickStartGuidePage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/editor"
            element={
              <ProtectedRoute>
                <Suspense fallback={null}>
                  <EditorAgentPage />
                </Suspense>
              </ProtectedRoute>
            }
          />
        </Routes>
      </BrowserRouter>
    </ClerkProvider>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <PostHogProvider
          apiKey={import.meta.env.VITE_PUBLIC_POSTHOG_KEY}
          options={{
            api_host: import.meta.env.VITE_PUBLIC_POSTHOG_HOST,
            capture_exceptions: true,
            debug: import.meta.env.MODE === "development",
          }}
        >
          <ToastProvider>
            <AppRoot />
            {/* DevTools only in development */}
            {import.meta.env.MODE === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </ToastProvider>
        </PostHogProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>,
)
