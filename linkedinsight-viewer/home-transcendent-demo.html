<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedInsight | Transcendent Minimal Interface</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@300;400;500&family=Archivo:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        /* RESET TO NOTHING */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --void: #000000;
            --light: #ffffff;
            --whisper: #f0f0f0;
            --echo: #666666;
            --phi: 1.618033988749895;
        }

        html, body {
            height: 100%;
            overflow: hidden;
        }

        body {
            font-family: 'IBM Plex Mono', monospace;
            background: var(--light);
            color: var(--void);
            cursor: crosshair;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        /* Navigation dots */
        .nav-minimal {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .nav-dot {
            width: 8px;
            height: 8px;
            background: var(--void);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
        }

        .nav-dot:hover,
        .nav-dot.active {
            transform: scale(2);
            background: var(--echo);
        }

        /* DESIGN 1: VOID INTERFACE */
        .void-interface {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
        }

        .void-interface.active {
            opacity: 1;
            pointer-events: all;
        }

        .void-center {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .void-text {
            position: absolute;
            font-size: clamp(2rem, 5vw, 4rem);
            font-weight: 300;
            letter-spacing: -0.02em;
            text-align: center;
            transition: all 0.8s cubic-bezier(0.23, 1, 0.320, 1);
        }

        .void-options {
            position: absolute;
            inset: 0;
            pointer-events: none;
        }

        .void-option {
            position: absolute;
            font-size: 0.875rem;
            letter-spacing: 0.1em;
            text-transform: uppercase;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
            cursor: pointer;
            pointer-events: all;
        }

        .void-interface:hover .void-option {
            opacity: 1;
            transform: scale(1);
        }

        .void-option:nth-child(1) { top: 20%; left: 20%; }
        .void-option:nth-child(2) { top: 20%; right: 20%; }
        .void-option:nth-child(3) { bottom: 20%; left: 50%; transform: translateX(-50%); }

        .void-option:hover {
            letter-spacing: 0.3em;
        }

        /* DESIGN 2: TERMINAL POETRY */
        .terminal-poetry {
            position: absolute;
            inset: 0;
            background: var(--light);
            padding: 2rem;
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
            overflow: auto;
        }

        .terminal-poetry.active {
            opacity: 1;
            pointer-events: all;
        }

        .terminal-line {
            font-family: 'IBM Plex Mono', monospace;
            font-size: 1rem;
            line-height: 1.8;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: typeIn 0.5s ease forwards;
        }

        .terminal-line:nth-child(1) { animation-delay: 0.1s; }
        .terminal-line:nth-child(2) { animation-delay: 0.3s; }
        .terminal-line:nth-child(3) { animation-delay: 0.5s; }
        .terminal-line:nth-child(4) { animation-delay: 0.7s; }
        .terminal-line:nth-child(5) { animation-delay: 0.9s; }
        .terminal-line:nth-child(6) { animation-delay: 1.1s; }
        .terminal-line:nth-child(7) { animation-delay: 1.3s; }
        .terminal-line:nth-child(8) { animation-delay: 1.5s; }

        .terminal-cursor {
            display: inline-block;
            width: 0.5em;
            height: 1.2em;
            background: var(--void);
            animation: blink 1s infinite;
        }

        .terminal-link {
            color: var(--void);
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .terminal-link:hover {
            border-bottom-color: var(--void);
        }

        @keyframes typeIn {
            to { opacity: 1; }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* DESIGN 3: BAUHAUS GRID */
        .bauhaus-grid {
            position: absolute;
            inset: 0;
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(8, 1fr);
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
        }

        .bauhaus-grid.active {
            opacity: 1;
            pointer-events: all;
        }

        .bauhaus-block {
            border: 1px solid var(--void);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            text-align: center;
            padding: 1rem;
            overflow: hidden;
        }

        .bauhaus-primary {
            grid-column: 2 / 7;
            grid-row: 2 / 5;
            font-size: 2rem;
            font-weight: 300;
        }

        .bauhaus-option-1 {
            grid-column: 2 / 4;
            grid-row: 6 / 8;
        }

        .bauhaus-option-2 {
            grid-column: 4 / 6;
            grid-row: 6 / 8;
        }

        .bauhaus-option-3 {
            grid-column: 6 / 8;
            grid-row: 6 / 8;
        }

        .bauhaus-block:hover {
            background: var(--void);
            color: var(--light);
        }

        /* DESIGN 4: CONSTELLATION */
        .constellation {
            position: absolute;
            inset: 0;
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
            overflow: hidden;
        }

        .constellation.active {
            opacity: 1;
            pointer-events: all;
        }

        .constellation-canvas {
            position: absolute;
            inset: 0;
        }

        .star {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--void);
            border-radius: 50%;
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .star.major {
            width: 8px;
            height: 8px;
            opacity: 1;
            cursor: pointer;
        }

        .star.major:hover {
            transform: scale(3);
            opacity: 1;
        }

        .constellation-line {
            position: absolute;
            height: 1px;
            background: var(--void);
            opacity: 0.1;
            transform-origin: left center;
            pointer-events: none;
        }

        .constellation-label {
            position: absolute;
            font-size: 0.75rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            transform: translate(-50%, -150%);
        }

        .star.major:hover + .constellation-label {
            opacity: 1;
        }

        /* DESIGN 5: GOLDEN RATIO SPIRAL */
        .golden-spiral {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
        }

        .golden-spiral.active {
            opacity: 1;
            pointer-events: all;
        }

        .spiral-container {
            position: relative;
            width: 500px;
            height: 500px;
            animation: spiralRotate 60s linear infinite;
        }

        .spiral-segment {
            position: absolute;
            border: 1px solid var(--void);
            border-radius: 50%;
            opacity: 0.1;
        }

        .spiral-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            animation: spiralRotate 60s linear infinite reverse;
        }

        .spiral-title {
            font-size: 2rem;
            font-weight: 300;
            text-align: center;
            margin-bottom: 2rem;
        }

        .spiral-options {
            display: flex;
            gap: 2rem;
        }

        .spiral-option {
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .spiral-option:hover {
            letter-spacing: 0.3em;
        }

        @keyframes spiralRotate {
            to { transform: rotate(360deg); }
        }

        /* DESIGN 6: ISOMETRIC INTERFACE */
        .isometric {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
            perspective: 1000px;
        }

        .isometric.active {
            opacity: 1;
            pointer-events: all;
        }

        .iso-container {
            transform-style: preserve-3d;
            transform: rotateX(30deg) rotateY(-45deg);
            transition: transform 0.6s ease;
        }

        .iso-plane {
            position: absolute;
            width: 300px;
            height: 300px;
            border: 2px solid var(--void);
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .iso-plane:nth-child(1) {
            transform: translateZ(150px);
        }

        .iso-plane:nth-child(2) {
            transform: rotateY(90deg) translateZ(150px);
        }

        .iso-plane:nth-child(3) {
            transform: rotateY(-90deg) translateZ(150px);
        }

        .iso-plane:hover {
            background: var(--void);
            color: var(--light);
        }

        .isometric:hover .iso-container {
            transform: rotateX(30deg) rotateY(-45deg) scale(1.1);
        }

        /* DESIGN 7: MORSE CODE */
        .morse-interface {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
        }

        .morse-interface.active {
            opacity: 1;
            pointer-events: all;
        }

        .morse-message {
            font-size: 4rem;
            font-weight: 300;
            letter-spacing: 0.5em;
            text-align: center;
        }

        .morse-dot {
            display: inline-block;
            width: 0.2em;
            height: 0.2em;
            background: var(--void);
            border-radius: 50%;
            margin: 0 0.1em;
            animation: morseFlash 2s infinite;
        }

        .morse-dash {
            display: inline-block;
            width: 0.6em;
            height: 0.1em;
            background: var(--void);
            margin: 0 0.1em;
            animation: morseFlash 2s infinite;
        }

        .morse-options {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 3rem;
        }

        .morse-link {
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.2em;
            cursor: pointer;
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .morse-link:hover {
            opacity: 1;
            letter-spacing: 0.4em;
        }

        @keyframes morseFlash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* DESIGN 8: NEGATIVE SPACE */
        .negative-space {
            position: absolute;
            inset: 0;
            background: var(--void);
            opacity: 0;
            pointer-events: none;
            transition: opacity 1s ease;
            overflow: hidden;
        }

        .negative-space.active {
            opacity: 1;
            pointer-events: all;
        }

        .negative-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 15vw;
            font-weight: 900;
            color: var(--light);
            text-align: center;
            line-height: 0.8;
            letter-spacing: -0.05em;
            mix-blend-mode: difference;
            pointer-events: none;
        }

        .negative-options {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .negative-option {
            width: 200px;
            height: 200px;
            background: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .negative-option:hover {
            transform: scale(1.2);
        }

        /* Mobile */
        @media (max-width: 768px) {
            .void-text { font-size: clamp(1.5rem, 4vw, 2rem); }
            .bauhaus-primary { font-size: 1.5rem; }
            .spiral-container { width: 300px; height: 300px; }
            .iso-plane { width: 150px; height: 150px; }
            .negative-text { font-size: 20vw; }
            .morse-message { font-size: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navigation -->
        <div class="nav-minimal">
            <div class="nav-dot active" data-view="0"></div>
            <div class="nav-dot" data-view="1"></div>
            <div class="nav-dot" data-view="2"></div>
            <div class="nav-dot" data-view="3"></div>
            <div class="nav-dot" data-view="4"></div>
            <div class="nav-dot" data-view="5"></div>
            <div class="nav-dot" data-view="6"></div>
            <div class="nav-dot" data-view="7"></div>
        </div>

        <!-- Design 1: Void Interface -->
        <div class="void-interface active" data-design="0">
            <div class="void-center">
                <h1 class="void-text">why write<br>when you've<br>already said it?</h1>
                <div class="void-options">
                    <div class="void-option">record</div>
                    <div class="void-option">speak</div>
                    <div class="void-option">notes</div>
                </div>
            </div>
        </div>

        <!-- Design 2: Terminal Poetry -->
        <div class="terminal-poetry" data-design="1">
            <div class="terminal-line">$ linkedinsight --init</div>
            <div class="terminal-line">&nbsp;</div>
            <div class="terminal-line">> why write when you've already said it?</div>
            <div class="terminal-line">> you share insights daily.</div>
            <div class="terminal-line">> we turn them into linkedin posts.</div>
            <div class="terminal-line">&nbsp;</div>
            <div class="terminal-line">$ select input method:</div>
            <div class="terminal-line">  [<span class="terminal-link">recording</span>] [<span class="terminal-link">interview</span>] [<span class="terminal-link">notes</span>]<span class="terminal-cursor"></span></div>
        </div>

        <!-- Design 3: Bauhaus Grid -->
        <div class="bauhaus-grid" data-design="2">
            <div class="bauhaus-block bauhaus-primary">
                why write<br>when you've<br>already said it?
            </div>
            <div class="bauhaus-block bauhaus-option-1">recording</div>
            <div class="bauhaus-block bauhaus-option-2">interview</div>
            <div class="bauhaus-block bauhaus-option-3">notes</div>
        </div>

        <!-- Design 4: Constellation -->
        <div class="constellation" data-design="3">
            <div class="constellation-canvas" id="constellation-canvas"></div>
        </div>

        <!-- Design 5: Golden Spiral -->
        <div class="golden-spiral" data-design="4">
            <div class="spiral-container">
                <div class="spiral-segment" style="width: 100%; height: 100%; top: 0; left: 0;"></div>
                <div class="spiral-segment" style="width: 61.8%; height: 61.8%; top: 0; right: 0;"></div>
                <div class="spiral-segment" style="width: 38.2%; height: 38.2%; bottom: 0; right: 0;"></div>
                <div class="spiral-segment" style="width: 23.6%; height: 23.6%; bottom: 0; left: 38.2%;"></div>
                <div class="spiral-content">
                    <h1 class="spiral-title">why write when<br>you've already said it?</h1>
                    <div class="spiral-options">
                        <span class="spiral-option">record</span>
                        <span class="spiral-option">speak</span>
                        <span class="spiral-option">write</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design 6: Isometric -->
        <div class="isometric" data-design="5">
            <div class="iso-container">
                <div class="iso-plane">recording</div>
                <div class="iso-plane">interview</div>
                <div class="iso-plane">notes</div>
            </div>
        </div>

        <!-- Design 7: Morse Code -->
        <div class="morse-interface" data-design="6">
            <div>
                <div class="morse-message">
                    <span class="morse-dot"></span><span class="morse-dash"></span><span class="morse-dash"></span>
                    <span style="margin: 0 1em;"></span>
                    <span class="morse-dash"></span><span class="morse-dot"></span><span class="morse-dot"></span><span class="morse-dot"></span>
                    <span style="margin: 0 1em;"></span>
                    <span class="morse-dot"></span><span class="morse-dash"></span><span class="morse-dot"></span><span class="morse-dot"></span>
                    <span style="margin: 0 1em;"></span>
                    <span class="morse-dot"></span><span class="morse-dot"></span>
                </div>
                <div class="morse-options">
                    <span class="morse-link">RECORD</span>
                    <span class="morse-link">SPEAK</span>
                    <span class="morse-link">NOTES</span>
                </div>
            </div>
        </div>

        <!-- Design 8: Negative Space -->
        <div class="negative-space" data-design="7">
            <h1 class="negative-text">WHY<br>WRITE</h1>
            <div class="negative-options">
                <div class="negative-option">recording</div>
                <div class="negative-option">interview</div>
                <div class="negative-option">notes</div>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        const dots = document.querySelectorAll('.nav-dot');
        const designs = document.querySelectorAll('[data-design]');
        
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const view = dot.dataset.view;
                
                // Update active states
                dots.forEach(d => d.classList.remove('active'));
                dot.classList.add('active');
                
                designs.forEach(d => d.classList.remove('active'));
                document.querySelector(`[data-design="${view}"]`).classList.add('active');
            });
        });

        // Constellation generation
        function generateConstellation() {
            const canvas = document.getElementById('constellation-canvas');
            const stars = [];
            
            // Generate random stars
            for (let i = 0; i < 50; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                canvas.appendChild(star);
                stars.push(star);
            }
            
            // Major stars (interactive)
            const majorPositions = [
                { x: 50, y: 30, label: 'why write when you\'ve already said it?' },
                { x: 25, y: 60, label: 'recording' },
                { x: 50, y: 70, label: 'interview' },
                { x: 75, y: 60, label: 'notes' }
            ];
            
            const majorStars = [];
            majorPositions.forEach((pos, i) => {
                const star = document.createElement('div');
                star.className = 'star major';
                star.style.left = pos.x + '%';
                star.style.top = pos.y + '%';
                canvas.appendChild(star);
                
                const label = document.createElement('div');
                label.className = 'constellation-label';
                label.textContent = pos.label;
                label.style.left = pos.x + '%';
                label.style.top = pos.y + '%';
                canvas.appendChild(label);
                
                majorStars.push({ element: star, x: pos.x, y: pos.y });
            });
            
            // Connect major stars
            for (let i = 1; i < majorStars.length; i++) {
                const line = document.createElement('div');
                line.className = 'constellation-line';
                
                const x1 = majorStars[0].x;
                const y1 = majorStars[0].y;
                const x2 = majorStars[i].x;
                const y2 = majorStars[i].y;
                
                const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
                
                line.style.width = length + '%';
                line.style.left = x1 + '%';
                line.style.top = y1 + '%';
                line.style.transform = `rotate(${angle}deg)`;
                
                canvas.appendChild(line);
            }
        }
        
        generateConstellation();

        // Keyboard navigation
        let currentView = 0;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                currentView = (currentView + 1) % designs.length;
                dots[currentView].click();
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                currentView = (currentView - 1 + designs.length) % designs.length;
                dots[currentView].click();
            }
        });
    </script>
</body>
</html>