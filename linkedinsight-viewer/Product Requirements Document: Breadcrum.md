Product Requirements Document: Breadcrumb Navigation for Content Agent
1. Introduction

This document outlines the requirements for implementing a breadcrumb navigation system within the Content Agent feature. The goal is to improve user orientation and provide a clear path back to the initial brief input stage from the content generation view. This enhancement aims to make the user flow more intuitive, especially within the context of the CreatorDraftGenerator component that transitions between a brief input form and the agent panes for content display.

2. Goals

Improve User Navigation: Provide users with a clear understanding of their current location within the Content Agent flow.

Enable Easy Return: Allow users to easily navigate from the content generation view (AgentPanes) back to the brief input form (BriefInputForm).

Maintain UI Consistency: Utilize existing breadcrumb components (breadcrumb.tsx, BreadcrumbNav.tsx) where possible to ensure a consistent look and feel, adhering to the DRY (Don't Repeat Yourself) principle.

Enhance User Experience: Make the transition and navigation between these two states feel more like distinct steps in a process.

3. Target User

Users of the LinkedInsight platform who are utilizing the Content Agent feature to generate content based on creative briefs.

4. User Stories

As a user, when I submit a brief and transition to the content generation view (AgentPanes), I want to see a breadcrumb trail like "Home > Content Agent" so I know where I am in the application.

As a user, when I am viewing the generated content in AgentPanes and see the "Home > Content Agent" breadcrumb, I want to be able to click on "Home" to easily return to the BriefInputForm to submit a new brief or modify my previous one.

As a user, when I am on the initial BriefInputForm view, I want to see a breadcrumb indicating I am at "Home" (or the starting point of this flow).

5. Proposed Solution & Scope

5.1. Breadcrumb Display

Initial View (BriefInputForm active within CreatorDraftGenerator):

A breadcrumb should be displayed, indicating the current location as "Home" (or a similar term representing the start of the content creation flow). This "Home" would be displayed as the current page (not a link).

Content Generation View (AgentPanes active within CreatorDraftGenerator):

Upon successful brief submission and transition to the AgentPanes view:

The breadcrumb display should change to "Home > Content Agent".

"Home" should be a clickable link.

"Content Agent" should be displayed as the current page (not a link).

5.2. Navigation Behavior

Clicking "Home" from "Content Agent" view:

The user should be navigated back to the BriefInputForm view within the CreatorDraftGenerator.

The state of CreatorDraftGenerator should reset to allow for new brief input.

The breadcrumb should update to show "Home" as the current page.

5.3. Component Reusability

The implementation should leverage the existing UI components:

ui/breadcrumb.tsx (for primitive breadcrumb elements like Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator).

layout/BreadcrumbNav.tsx (the higher-level navigation component). Efforts should be made to use or adapt BreadcrumbNav.tsx if feasible, or use the primitives directly if more flexibility is required for this specific stateful navigation within a single route.

6. Technical Considerations

State Management:

The CreatorDraftGenerator.tsx component currently manages the state to switch between BriefInputForm and AgentPanes (e.g., using showBriefInput and showAgentPanes states). This internal state will drive the breadcrumb display and the behavior of the "Home" link.

Routing and URL Endpoints:

Current State: The transition between BriefInputForm and AgentPanes happens internally within CreatorDraftGenerator.tsx, likely without a URL change (both views are under the same route, e.g., /).

Impact on Breadcrumbs:

If the URL does not change, the "Home" link in the breadcrumb (when in "Content Agent" view) cannot be a simple Link to="/" if the component is already at /, as this might just re-render the component in its current state or cause a full page reload without the desired view switch.

An onClick handler on the "Home" breadcrumb item will be necessary to trigger the internal state change in CreatorDraftGenerator.tsx to revert to the BriefInputForm view.

Question to Address: Does the Content Agent (specifically the AgentPanes view) require its own URL endpoint (e.g., /content-agent) for this breadcrumb navigation to work robustly and align with standard web navigation patterns (including browser back/forward button behavior)?

Recommendation: While an initial implementation can be achieved without a new URL by handling navigation via component state, introducing a distinct URL for the "Content Agent" view is highly recommended for a cleaner, more scalable, and user-friendly navigation model. This would allow BreadcrumbNav.tsx to function more naturally based on routes.

DRY Principle:

Care must be taken to integrate the breadcrumbs in a way that reuses existing components effectively. If BreadcrumbNav.tsx cannot be directly used due to its path-driven nature and the current single-route setup, the primitive components from breadcrumb.tsx should be used directly within CreatorDraftGenerator.tsx.

7. Success Metrics

Users can successfully navigate back to the BriefInputForm from the AgentPanes view using the breadcrumb.

The breadcrumb display accurately reflects the user's current state within the CreatorDraftGenerator flow.

The solution is implemented with minimal code duplication, leveraging existing breadcrumb utilities.

8. Open Questions / Future Considerations

URL Strategy: Final decision on whether to implement a dedicated URL for the "Content Agent" view. This PRD leans towards recommending it for a more robust solution.

State Preservation: If navigating "Home" from "Content Agent" should preserve any previously entered brief data for quick editing, or if it should always reset to a blank form. (Current assumption: resets for a new brief).

Integration with Layout.tsx and Header.tsx: How will these breadcrumbs integrate with the main page header? Header.tsx already uses BreadcrumbNav. The CreatorDraftGenerator might need to render its own BreadcrumbNav instance or pass specific props to the main one if the header is meant to be globally aware of this sub-navigation. Given CreatorDraftGenerator is a full-page experience, it might render its own header section or a sub-header for these breadcrumbs. The current CreatorDraftGenerator renders in a fixed inset-0 container, suggesting it takes over the screen.