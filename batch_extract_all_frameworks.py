#!/usr/bin/env python3
"""
Batch extract body frameworks from all LLM-friendly analysis markdown files.

This script processes all creators' body analyses and extracts frameworks
for embedding into the vector search system.
"""

import os
import re
import json
import glob
from typing import List, Dict, Any
from datetime import datetime
from test_markdown_framework_parser import parse_body_frameworks, extract_frameworks_section


def find_all_body_analysis_files() -> List[tuple]:
    """Find all body analysis markdown files."""
    
    pattern = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/*/*_body_llm_friendly_analysis_*.md"
    files = glob.glob(pattern)
    
    creator_files = []
    for file_path in files:
        # Extract creator name from path
        creator_name = file_path.split('/')[-2]  # Directory name is creator name
        creator_files.append((creator_name, file_path))
    
    return sorted(creator_files)


def process_all_creators() -> Dict[str, Any]:
    """Process all creators and extract frameworks."""
    
    print("🚀 Starting Batch Framework Extraction")
    print("=" * 50)
    
    creator_files = find_all_body_analysis_files()
    print(f"📁 Found {len(creator_files)} body analysis files")
    
    all_frameworks = []
    creator_results = []
    successful_creators = 0
    failed_creators = 0
    
    for creator_name, file_path in creator_files:
        print(f"\n📖 Processing: {creator_name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract frameworks section
            frameworks_section = extract_frameworks_section(content)
            if not frameworks_section or len(frameworks_section) < 500:
                print(f"   ⚠️  No substantial frameworks section found")
                failed_creators += 1
                continue
            
            # Parse frameworks
            frameworks = parse_body_frameworks(frameworks_section, creator_name)
            
            if not frameworks:
                print(f"   ⚠️  No frameworks extracted")
                failed_creators += 1
                continue
            
            # Quality check
            frameworks_with_examples = sum(1 for f in frameworks if f.get('example') and len(f['example']) > 100)
            total_example_chars = sum(len(f.get('example', '')) for f in frameworks)
            
            print(f"   ✅ Extracted {len(frameworks)} frameworks")
            print(f"   📝 {frameworks_with_examples} with substantial examples ({total_example_chars} chars)")
            
            # Add to results
            all_frameworks.extend(frameworks)
            
            creator_result = {
                "creator_name": creator_name,
                "file_path": file_path,
                "frameworks_count": len(frameworks),
                "frameworks_with_examples": frameworks_with_examples,
                "total_example_chars": total_example_chars,
                "frameworks": frameworks
            }
            creator_results.append(creator_result)
            successful_creators += 1
            
        except Exception as e:
            print(f"   ❌ Error processing {creator_name}: {e}")
            failed_creators += 1
            continue
    
    return {
        "extraction_timestamp": datetime.now().isoformat(),
        "total_creators_found": len(creator_files),
        "successful_creators": successful_creators,
        "failed_creators": failed_creators,
        "total_frameworks": len(all_frameworks),
        "all_frameworks": all_frameworks,
        "creator_results": creator_results
    }


def main():
    """Main batch processing function."""
    
    results = process_all_creators()
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"/Users/<USER>/LinkedInsight/batch_frameworks_extraction_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    # Summary analysis
    print(f"\n📊 BATCH EXTRACTION SUMMARY")
    print("=" * 40)
    
    print(f"   Creators found: {results['total_creators_found']}")
    print(f"   Successful extractions: {results['successful_creators']}")
    print(f"   Failed extractions: {results['failed_creators']}")
    success_rate = results['successful_creators']/max(results['total_creators_found'], 1) * 100
    print(f"   Success rate: {success_rate:.1f}%")
    
    print(f"\n   📦 Framework Results:")
    print(f"   Total frameworks extracted: {results['total_frameworks']}")
    print(f"   Average frameworks per creator: {results['total_frameworks']/max(results['successful_creators'], 1):.1f}")
    
    # Calculate quality metrics
    all_frameworks = results['all_frameworks']
    frameworks_with_examples = sum(1 for f in all_frameworks if f.get('example') and len(f['example']) > 100)
    total_example_chars = sum(len(f.get('example', '')) for f in all_frameworks)
    avg_example_length = total_example_chars / max(frameworks_with_examples, 1)
    
    print(f"   Frameworks with substantial examples: {frameworks_with_examples}")
    print(f"   Total example content: {total_example_chars:,} characters")
    print(f"   Average example length: {avg_example_length:.0f} characters")
    
    # Show top creators by framework count
    creator_results = results['creator_results']
    top_creators = sorted(creator_results, key=lambda x: x['frameworks_count'], reverse=True)[:5]
    
    print(f"\n   🏆 Top Creators by Framework Count:")
    for i, creator in enumerate(top_creators, 1):
        print(f"   {i}. {creator['creator_name']}: {creator['frameworks_count']} frameworks")
    
    # Quality warnings
    if frameworks_with_examples < results['total_frameworks'] * 0.8:
        print(f"\n   ⚠️  Low example coverage: {frameworks_with_examples}/{results['total_frameworks']} frameworks have examples")
    
    if avg_example_length < 400:
        print(f"\n   ⚠️  Short examples: Average {avg_example_length:.0f} chars (should be 500+ for rich content)")
    
    if results['total_frameworks'] < results['successful_creators'] * 3:
        print(f"\n   ⚠️  Low framework density: {results['total_frameworks']/results['successful_creators']:.1f} per creator (expect 4+)")
    
    print(f"\n✅ Batch extraction completed!")
    print(f"📁 Framework database ready for embedding pipeline at: {output_file}")


if __name__ == "__main__":
    main()