# Clerk Authentication
# Your Clerk instance URL, e.g. https://eager-wallaby-71.clerk.accounts.dev
CLERK_ISSUER="https://your-instance.clerk.accounts.dev"

# JWKS URL - if not provided, it will be constructed from CLERK_ISSUER
CLERK_JWKS_URL="https://your-instance.clerk.accounts.dev/.well-known/jwks.json"

# Frontend URL - used for CORS configuration
FRONTEND_URL="https://linkedinsight-viewer.vercel.app"

# Backend URL - used for CORS configuration
BACKEND_URL="https://linkedinsight-api.herokuapp.com"

# MongoDB connection string
MONGODB_URI="mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=YourAppName"
