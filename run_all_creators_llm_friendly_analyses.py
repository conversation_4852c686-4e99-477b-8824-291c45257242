#!/usr/bin/env python
"""
Run all LLM-friendly analyses for all creators in the database.

This script:
1. Queries the database to get all creator names
2. Checks which creators already have directories in LLM_friendly_Analyses
3. Runs analyses only for creators who don't have directories yet
4. Creates subdirectories for each creator and saves the analyses there

The analyses run for each creator are:
- themes_json
- hook_json
- ending_json
- linguistic_json
- body_llm_friendly
"""

import os
import sys
import argparse
import asyncio
import logging
import sqlite3
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("all_creators_llm_friendly_analyses.log")
    ]
)
logger = logging.getLogger(__name__)

# Import necessary modules
from run_all_llm_friendly_analyses import run_all_llm_friendly_analyses

# Default configuration
DEFAULT_NUM_POSTS = 50
DEFAULT_ANALYSES_DIR = os.path.join("LLM_friendly_Analyses")
DEFAULT_DB_PATH = os.path.join("data", "db", "linkedin_content.db")

def get_creators_from_db(db_path: str) -> List[str]:
    """
    Get all creator names from the database.

    Args:
        db_path: Path to the SQLite database

    Returns:
        List of creator names
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Query for all creator names
        cursor.execute("SELECT creator_name FROM creators")
        creators = [row[0] for row in cursor.fetchall()]

        # Close the connection
        conn.close()

        return creators

    except Exception as e:
        logger.error(f"Error getting creators from database: {e}")
        return []

def get_creators_with_post_counts(db_path: str) -> Dict[str, int]:
    """
    Get all creator names with their post counts from the database.

    Args:
        db_path: Path to the SQLite database

    Returns:
        Dictionary mapping creator names to post counts
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Query for all creator names with post counts
        cursor.execute("""
            SELECT c.creator_name, COUNT(p.post_id) as post_count
            FROM creators c
            LEFT JOIN posts p ON c.creator_id = p.creator_id
            GROUP BY c.creator_name
            ORDER BY post_count DESC
        """)

        creators_with_counts = {row[0]: row[1] for row in cursor.fetchall()}

        # Close the connection
        conn.close()

        return creators_with_counts

    except Exception as e:
        logger.error(f"Error getting creators with post counts from database: {e}")
        return {}

def get_existing_creator_dirs(analyses_dir: str) -> List[str]:
    """
    Get list of creators who already have directories in the analyses directory.

    Args:
        analyses_dir: Path to the analyses directory

    Returns:
        List of creator names with existing directories
    """
    try:
        # Get all subdirectories in the analyses directory
        return [d for d in os.listdir(analyses_dir)
               if os.path.isdir(os.path.join(analyses_dir, d)) and not d.startswith('.')]
    except Exception as e:
        logger.error(f"Error listing directories in {analyses_dir}: {e}")
        return []

async def run_analyses_for_creators(
    creators: List[str],
    num_posts: int = DEFAULT_NUM_POSTS,
    analyses_dir: str = DEFAULT_ANALYSES_DIR,
    force_rerun: bool = False,
    min_posts_required: int = 10,
    creators_with_counts: Optional[Dict[str, int]] = None
) -> Dict[str, Dict[str, bool]]:
    """
    Run all LLM-friendly analyses for the specified creators.

    Args:
        creators: List of creator names
        num_posts: Number of posts to analyze
        analyses_dir: Directory for analyses
        force_rerun: Whether to force rerun of existing analyses
        min_posts_required: Minimum number of posts required to run analyses
        creators_with_counts: Dictionary mapping creator names to post counts

    Returns:
        Dictionary mapping creator names to analysis results
    """
    results = {}

    for creator_name in creators:
        # Skip creators with too few posts
        if creators_with_counts and creator_name in creators_with_counts:
            post_count = creators_with_counts[creator_name]
            if post_count < min_posts_required:
                logger.warning(f"Skipping {creator_name} - only has {post_count} posts (minimum required: {min_posts_required})")
                results[creator_name] = {"skipped": f"Only has {post_count} posts"}
                continue

        logger.info(f"Running analyses for {creator_name}...")

        # Create directory for this creator if it doesn't exist
        creator_dir = os.path.join(analyses_dir, creator_name)
        os.makedirs(creator_dir, exist_ok=True)

        try:
            # Run all analyses for this creator
            creator_results = await run_all_llm_friendly_analyses(
                creator_name=creator_name,
                num_posts=num_posts,
                json_dir=analyses_dir,
                markdown_dir=analyses_dir,
                force_rerun=force_rerun
            )

            results[creator_name] = creator_results

        except Exception as e:
            logger.error(f"Error running analyses for {creator_name}: {e}")
            results[creator_name] = {"error": str(e)}

    return results

# We're not using MongoDB for now, so this function is removed

async def main_async():
    """Async main entry point."""
    parser = argparse.ArgumentParser(
        description="Run all LLM-friendly analyses for all creators in the database",
        epilog="Example: python run_all_creators_llm_friendly_analyses.py --num-posts 50 --force"
    )
    parser.add_argument("--db-path", default=DEFAULT_DB_PATH,
                      help=f"Path to the SQLite database (default: {DEFAULT_DB_PATH})")
    parser.add_argument("--num-posts", type=int, default=DEFAULT_NUM_POSTS,
                      help=f"Number of posts to analyze (default: {DEFAULT_NUM_POSTS})")
    parser.add_argument("--analyses-dir", default=DEFAULT_ANALYSES_DIR,
                      help=f"Directory for analyses (default: {DEFAULT_ANALYSES_DIR})")
    parser.add_argument("--force", action="store_true",
                      help="Force rerun of existing analyses")
    parser.add_argument("--min-posts", type=int, default=10,
                      help="Minimum number of posts required to run analyses (default: 10)")
    parser.add_argument("--specific-creator",
                      help="Run analyses for a specific creator only")

    args = parser.parse_args()

    # Get creators from database
    if args.specific_creator:
        all_creators = [args.specific_creator]
        logger.info(f"Running analyses for specific creator: {args.specific_creator}")
    else:
        all_creators = get_creators_from_db(args.db_path)
        logger.info(f"Found {len(all_creators)} creators in the database")

    # Get post counts for all creators
    creators_with_counts = get_creators_with_post_counts(args.db_path)
    logger.info(f"Creator post counts: {creators_with_counts}")

    # Get existing creator directories
    existing_dirs = get_existing_creator_dirs(args.analyses_dir)
    logger.info(f"Found {len(existing_dirs)} existing creator directories: {existing_dirs}")

    # Filter out creators who already have directories (unless force_rerun is True)
    if not args.force:
        creators_to_analyze = [c for c in all_creators if c not in existing_dirs]
        logger.info(f"Will analyze {len(creators_to_analyze)} creators (skipping {len(existing_dirs)} existing)")
    else:
        creators_to_analyze = all_creators
        logger.info(f"Will analyze all {len(creators_to_analyze)} creators (force mode)")

    if not creators_to_analyze:
        logger.info("No creators to analyze. All creators already have directories.")
        sys.exit(0)

    # Run analyses for filtered creators
    logger.info(f"Starting all LLM-friendly analyses for {len(creators_to_analyze)} creators...")

    try:
        results = await run_analyses_for_creators(
            creators=creators_to_analyze,
            num_posts=args.num_posts,
            analyses_dir=args.analyses_dir,
            force_rerun=args.force,
            min_posts_required=args.min_posts,
            creators_with_counts=creators_with_counts
        )

        # Check if all analyses were successful
        all_success = True
        for creator_name, creator_results in results.items():
            if "error" in creator_results or "skipped" in creator_results:
                continue  # Skip error or skipped creators when checking success

            if not all(creator_results.values()):
                all_success = False
                failed_analyses = [analysis_type for analysis_type, success in creator_results.items() if not success]
                logger.error(f"Failed analyses for {creator_name}: {failed_analyses}")

        if all_success:
            logger.info(f"All analyses completed successfully for all creators!")
            sys.exit(0)
        else:
            logger.error(f"Some analyses failed.")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Error running analyses: {e}")
        sys.exit(1)

def main():
    """Main entry point."""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
