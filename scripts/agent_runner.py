#!/usr/bin/env python
"""Agent runner for LinkedInsight.

This script serves two purposes:
1. It can be used as a CLI tool for running the agent directly from the command line
2. It can be called from the frontend to generate content using the agent

The script maintains the same functionality regardless of how it's invoked,
ensuring consistent behavior across both interfaces.
"""

import asyncio
import argparse
import json
import sys
import os
import datetime
from typing import Dict, Any, List, Optional

# Add project root to path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.api.agent_service import AgentService

async def run_agent(
    input_text: str,
    creator_name: str,
    verbose: bool = False,
    conversation_id: Optional[str] = None,
    conversation_history: Optional[List[Dict[str, str]]] = None,
    output_file: Optional[str] = None
) -> Dict[str, Any]:
    """Run the agent with the given input and creator.

    This function can be called both from the CLI and from the frontend.
    It handles the core agent functionality of generating content based on input.

    Args:
        input_text: User input text
        creator_name: Name of the creator to emulate
        verbose: Whether to show verbose output (primarily for CLI use)
        conversation_id: Optional ID for continuing a conversation
        conversation_history: Optional history of previous messages
        output_file: Optional file path to save the output (primarily for CLI use)

    Returns:
        The agent's response as a dictionary containing the generated post,
        explanation, and other metadata
    """
    print(f"\nRunning agent with creator: {creator_name}")
    print(f"Input: {input_text}\n")

    try:
        # Call the agent service
        result = await AgentService.generate_content(
            input_text=input_text,
            creator_name=creator_name,
            conversation_id=conversation_id,
            conversation_history=conversation_history
        )

        # Check if we got questions or a generated post
        if "questions" in result and result["questions"]:
            print("Agent has questions:")
            for i, question in enumerate(result["questions"], 1):
                print(f"  {i}. {question}")

        if "generated_post" in result and result["generated_post"]:
            print("\nGenerated Post:")
            print("-" * 80)
            print(result["generated_post"])
            print("-" * 80)

            if "explanation" in result and result["explanation"]:
                print("\nExplanation:")
                print("-" * 80)
                print(result["explanation"])
                print("-" * 80)

        if "error" in result and result["error"]:
            print(f"\nError: {result['error']}")

        if verbose:
            print("\nFull Response:")
            print(json.dumps(result, indent=2))

        # Save output to file if specified
        if output_file and "generated_post" in result and result["generated_post"]:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Generated Post ({creator_name})\n\n")
                    f.write(result["generated_post"])
                    f.write("\n\n")

                    if "explanation" in result and result["explanation"]:
                        f.write("\n\n---\n\n")
                        f.write(result["explanation"])

                    # Add thinking logs if available
                    if "thinking_logs" in result and result["thinking_logs"]:
                        f.write("\n\n---\n\n")
                        f.write("# Thinking Logs\n\n")
                        for step_name, thinking in result["thinking_logs"].items():
                            f.write(f"## Step: {step_name}\n\n")
                            f.write("```\n")
                            f.write(thinking)
                            f.write("\n```\n\n")

                print(f"\nOutput saved to: {output_file}")
            except Exception as e:
                print(f"Error saving output to file: {e}")

        return result

    except Exception as e:
        print(f"Error running agent: {e}")
        return {"error": str(e)}

async def interactive_mode(creator_name: str, verbose: bool = False, output_dir: Optional[str] = None):
    """Run the agent in interactive mode (CLI only).

    This function provides a command-line interface for having a multi-turn
    conversation with the agent. It's designed for CLI use only and not
    intended to be called from the frontend.

    Args:
        creator_name: Name of the creator to emulate
        verbose: Whether to show verbose output
        output_dir: Optional directory to save generated posts
    """
    print(f"\n=== Interactive Agent Mode (Creator: {creator_name}) ===")
    print("Type 'exit' or 'quit' to end the session.")
    print("Type 'change creator <name>' to change the creator.")

    conversation_id = None
    conversation_history = []
    current_creator = creator_name

    while True:
        try:
            # Get user input
            user_input = input("\nYour input: ").strip()

            # Check for exit command
            if user_input.lower() in ['exit', 'quit']:
                print("Exiting interactive mode.")
                break

            # Check for change creator command
            if user_input.lower().startswith('change creator '):
                new_creator = user_input[len('change creator '):].strip()
                print(f"Changing creator from {current_creator} to {new_creator}")
                current_creator = new_creator
                # Reset conversation when changing creator
                conversation_id = None
                conversation_history = []
                continue

            # Generate output filename if output directory is specified
            output_file = None
            if output_dir:
                # Create the directory if it doesn't exist
                os.makedirs(output_dir, exist_ok=True)

                # Generate a timestamp-based filename
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(
                    output_dir,
                    f"{current_creator}_{timestamp}.md"
                )

            # Call the agent
            result = await run_agent(
                user_input,
                current_creator,
                verbose,
                conversation_id,
                conversation_history,
                output_file
            )

            # Update conversation state
            if "conversation_id" in result:
                conversation_id = result["conversation_id"]

            # Add to conversation history
            conversation_history.append({
                "role": "user",
                "content": user_input
            })

            if "generated_post" in result and result["generated_post"]:
                conversation_history.append({
                    "role": "agent",
                    "content": result["generated_post"]
                })
            elif "questions" in result and result["questions"]:
                questions_text = "\n".join([f"- {q}" for q in result["questions"]])
                conversation_history.append({
                    "role": "agent",
                    "content": f"I need more information:\n{questions_text}"
                })

        except KeyboardInterrupt:
            print("\nExiting interactive mode.")
            break
        except Exception as e:
            print(f"Error in interactive mode: {e}")

def main():
    """Parse command-line arguments and run the agent.

    This function is the entry point for CLI usage only.
    When called from the frontend, the run_agent function should be used directly.
    """
    parser = argparse.ArgumentParser(description="Run the LinkedInsight content generation agent")
    parser.add_argument("--input", "-i", help="Input text for the agent")
    parser.add_argument("--creator", "-c", default="joshlowman",
                        help="Creator name to emulate (default: joshlowman)")
    parser.add_argument("--verbose", "-v", action="store_true",
                        help="Show verbose output")
    parser.add_argument("--interactive", "-I", action="store_true",
                        help="Run in interactive mode")
    parser.add_argument("--output", "-o", help="Output file to save the generated post (markdown format). If not provided, a file will be created with creator name and timestamp.")
    parser.add_argument("--output-dir", "-d", help="Output directory for saving posts in interactive mode")

    args = parser.parse_args()

    if args.interactive:
        # Run in interactive mode
        asyncio.run(interactive_mode(args.creator, args.verbose, args.output_dir))
    elif args.input:
        # Generate output filename if not provided
        output_file = args.output
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"agent_output_{args.creator}_{timestamp}.md"

        # Run with provided input
        asyncio.run(run_agent(args.input, args.creator, args.verbose, output_file=output_file))
    else:
        parser.print_help()
        print("\nError: Either --input or --interactive is required.")
        sys.exit(1)

if __name__ == "__main__":
    main()
