#!/usr/bin/env python3
"""
Fix Analysis Types Script
------------------------
Updates analysis type names in MongoDB to match what the API expects.

This script specifically fixes the analysis types for the creator "jolyonvar<PERSON>"
by renaming:
- body_analysis_test_md to body_md
- endings_test_md to endings_md
- linguistic_analysis_test_md to linguistic_md

Usage:
    python scripts/fix_analysis_types.py [--dry-run]

Options:
    --dry-run  Show what would be updated without making changes
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = Path(__file__).parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.utils.mongo_utils_async import get_async_mongo_db, ANALYSES_COLLECTION

async def fix_analysis_types(dry_run=False):
    """
    Fix analysis type names in MongoDB.

    Args:
        dry_run: If True, show what would be updated without making changes
    """
    # Get MongoDB database
    db = await get_async_mongo_db()

    # Define the mappings of incorrect to correct analysis types for each creator
    creator_mappings = {
        "jolyonvarley": {
            "body_analysis_test_md": "body_md",
            "endings_test_md": "endings_md",
            "linguistic_analysis_test_md": "linguistic_md"
        },
        "joshlowman": {
            "linguistic_analysis_md": "linguistic_md"
        }
    }

    # Process each creator
    for creator_name, type_mappings in creator_mappings.items():
        logger.info(f"Processing creator: {creator_name}")

        # Process each mapping for this creator
        for old_type, new_type in type_mappings.items():
            # Find documents with the old analysis type
            query = {
                "creator_name": creator_name,
                "analysis_type": old_type
            }

            count = await db[ANALYSES_COLLECTION].count_documents(query)

            if count == 0:
                logger.info(f"No documents found with analysis_type={old_type}")
                continue

            logger.info(f"Found {count} document(s) with analysis_type={old_type}")

            # Update the analysis type
            if not dry_run:
                result = await db[ANALYSES_COLLECTION].update_many(
                    query,
                    {"$set": {"analysis_type": new_type}}
                )

                logger.info(f"Updated {result.modified_count} document(s): {old_type} -> {new_type}")
            else:
                logger.info(f"[DRY RUN] Would update {count} document(s): {old_type} -> {new_type}")

async def main_async():
    """Async entry point"""
    parser = argparse.ArgumentParser(description="Fix analysis type names in MongoDB")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be updated without making changes")
    args = parser.parse_args()

    logger.info(
        f"Starting analysis type fix {'(DRY RUN)' if args.dry_run else ''}"
    )

    await fix_analysis_types(args.dry_run)

    logger.info(
        f"Analysis type fix {'(DRY RUN) ' if args.dry_run else ''}completed"
    )

def main():
    import asyncio

    asyncio.run(main_async())

if __name__ == "__main__":
    main()
