#!/usr/bin/env python3
"""
Migration script to move analysis files from outputs/ directory to MongoDB.

This script:
1. Traverses the outputs/ directory
2. Identifies analysis files (.md and .json)
3. Parses creator names, analysis types, and timestamps from filenames
4. Reads file content and associated metadata
5. Inserts data into MongoDB
6. Verifies the inserted data
7. Archives the original files after successful migration
"""

import os
import sys
import logging
import argparse
import re
import json
import yaml
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = Path(__file__).parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.config import Config, OUTPUTS_DIR
from src.utils.mongo_utils_async import get_async_mongo_db, CREATORS_COLLECTION, ANALYSES_COLLECTION

# Constants
ARCHIVE_DIR = os.path.join(root_dir, "outputs_archive")

def parse_filename(filename: str) -> Tuple[str, str, datetime]:
    """
    Parse creator name, analysis type, and timestamp from filename.

    Expected format: {creator_name}_{analysis_type}_{timestamp}.{extension}

    Args:
        filename: The filename to parse

    Returns:
        Tuple of (creator_name, analysis_type, timestamp)

    Raises:
        ValueError: If the filename doesn't match the expected pattern
    """
    # Extract base filename without extension
    base_filename = os.path.splitext(os.path.basename(filename))[0]

    # Try different patterns in order of specificity
    patterns = [
        # Standard pattern with 14-digit timestamp
        r"^([a-zA-Z0-9\-]+)_([a-zA-Z]+)_(\d{14})$",
        # Pattern with 12-digit timestamp
        r"^([a-zA-Z0-9\-]+)_([a-zA-Z]+)_(\d{12})$",
        # Pattern with analysis_v2 or similar
        r"^([a-zA-Z0-9\-]+)_([a-zA-Z_]+\d*)_(\d{14})$",
        # Pattern with analysis_v2 and 12-digit timestamp
        r"^([a-zA-Z0-9\-]+)_([a-zA-Z_]+\d*)_(\d{12})$",
        # Fallback pattern for special cases
        r"^([a-zA-Z0-9\-]+)_([a-zA-Z0-9_\-]+)_(\d+)$"
    ]

    match = None
    for pattern in patterns:
        match = re.match(pattern, base_filename)
        if match:
            break

    if not match:
        # Special case for files without timestamps
        # Extract creator from directory name
        dir_name = os.path.basename(os.path.dirname(filename))
        if dir_name and base_filename.startswith(dir_name):
            # Try to extract analysis type
            remaining = base_filename[len(dir_name):].strip('_')
            if remaining:
                creator_name = dir_name
                analysis_type = remaining
                timestamp = datetime.now()
                logger.warning(f"Using special case parsing for {filename}: creator={creator_name}, type={analysis_type}")
                return creator_name, analysis_type, timestamp

        raise ValueError(f"Filename '{filename}' doesn't match the expected pattern")

    creator_name = match.group(1)
    analysis_type = match.group(2)
    timestamp_str = match.group(3)

    # Parse timestamp
    try:
        if len(timestamp_str) == 14:
            timestamp = datetime.strptime(timestamp_str, "%Y%m%d%H%M%S")
        elif len(timestamp_str) == 12:
            # Handle 12-digit timestamps (missing seconds)
            timestamp = datetime.strptime(timestamp_str, "%Y%m%d%H%M")
        else:
            # Fallback for other timestamp formats
            logger.warning(f"Unusual timestamp format '{timestamp_str}' in {filename}, using current time")
            timestamp = datetime.now()
    except ValueError as e:
        logger.warning(f"Error parsing timestamp '{timestamp_str}': {e}")
        # Fallback to current time if timestamp parsing fails
        timestamp = datetime.now()

    return creator_name, analysis_type, timestamp

def read_metadata_file(metadata_path: str) -> Dict[str, Any]:
    """
    Read metadata from a YAML file.

    Args:
        metadata_path: Path to the metadata YAML file

    Returns:
        Dictionary containing the metadata

    Raises:
        FileNotFoundError: If the metadata file doesn't exist
        yaml.YAMLError: If the metadata file is not valid YAML
    """
    if not os.path.exists(metadata_path):
        raise FileNotFoundError(f"Metadata file '{metadata_path}' not found")

    # Read the file content
    with open(metadata_path, 'r') as f:
        content = f.read()

    # Remove YAML document markers
    content = content.replace('---', '')

    # Parse YAML
    try:
        metadata = yaml.safe_load(content)
    except yaml.YAMLError as e:
        logger.warning(f"Error parsing YAML from {metadata_path}: {e}")
        metadata = {}

    return metadata or {}

async def migrate_file(file_path: str, db, dry_run: bool = False) -> bool:
    """
    Migrate a single file to MongoDB.

    Args:
        file_path: Path to the file to migrate
        db: MongoDB database instance
        dry_run: If True, don't actually insert data into MongoDB

    Returns:
        True if migration was successful, False otherwise
    """
    try:
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Skip non-analysis files
        if ext not in ['.md', '.json']:
            logger.debug(f"Skipping non-analysis file: {file_path}")
            return True

        # Skip raw response and metadata files
        if '_raw_response' in file_path or '_metadata' in file_path:
            logger.debug(f"Skipping auxiliary file: {file_path}")
            return True

        # Parse filename
        creator_name, analysis_type, timestamp = parse_filename(file_path)

        # Determine full analysis type (with _md or _json suffix)
        full_analysis_type = f"{analysis_type}_{ext[1:]}"

        # Read file content
        with open(file_path, 'r') as f:
            content = f.read()

        # Look for metadata file
        metadata_path = f"{os.path.splitext(file_path)[0]}_metadata.yaml"
        metadata = {}

        if os.path.exists(metadata_path):
            metadata = read_metadata_file(metadata_path)
        elif ext == '.json':
            # For JSON files, try to extract metadata from the JSON itself
            try:
                json_data = json.loads(content)
                metadata = {
                    'creator': json_data.get('creator'),
                    'date_range': json_data.get('date_range'),
                    'posts_analyzed': json_data.get('posts_analyzed'),
                    'analysis_date': json_data.get('analysis_date')
                }
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Failed to extract metadata from JSON: {e}")

        # Convert any date objects to datetime for MongoDB compatibility
        if 'analysis_date' in metadata:
            try:
                # If it's a string, try to parse it
                if isinstance(metadata['analysis_date'], str):
                    try:
                        metadata['analysis_date'] = datetime.strptime(metadata['analysis_date'], "%Y-%m-%d")
                    except ValueError:
                        # If parsing fails, just leave it as a string
                        pass
                # If it's a date object, convert to datetime
                elif str(type(metadata['analysis_date'])) == "<class 'datetime.date'>":
                    metadata['analysis_date'] = datetime.combine(
                        metadata['analysis_date'],
                        datetime.min.time()
                    )
            except Exception as e:
                logger.warning(f"Failed to convert analysis_date: {e}")
                # Remove problematic field if conversion fails
                metadata.pop('analysis_date', None)

        # Prepare document for analyses collection
        analysis_doc = {
            'creator_name': creator_name,
            'analysis_type': full_analysis_type,
            'generated_at': timestamp,
            'content': content,
            'metadata': metadata,
            'file_source_path': file_path
        }

        # Prepare document for creators collection
        creator_doc = {
            'creator_name': creator_name,
            'last_analyzed_at': timestamp
        }

        if not dry_run:
            # Insert or update creator document
            await db[CREATORS_COLLECTION].update_one(
                {'creator_name': creator_name},
                {'$set': creator_doc},
                upsert=True
            )

            # Insert analysis document
            result = await db[ANALYSES_COLLECTION].insert_one(analysis_doc)

            # Verify insertion
            inserted_doc = await db[ANALYSES_COLLECTION].find_one({'_id': result.inserted_id})
            if not inserted_doc:
                raise ValueError(f"Failed to verify insertion of document for {file_path}")

            # Verify content matches
            if inserted_doc['content'] != content:
                raise ValueError(f"Content mismatch for {file_path}")

            logger.info(f"Successfully migrated {file_path} to MongoDB")
        else:
            logger.info(f"[DRY RUN] Would migrate {file_path} to MongoDB")

        return True

    except Exception as e:
        logger.error(f"Error migrating {file_path}: {e}")
        return False

def archive_file(file_path: str, dry_run: bool = False) -> bool:
    """
    Archive a file by moving it to the archive directory.

    Args:
        file_path: Path to the file to archive
        dry_run: If True, don't actually move the file

    Returns:
        True if archiving was successful, False otherwise
    """
    try:
        # Create relative path from OUTPUTS_DIR
        rel_path = os.path.relpath(file_path, OUTPUTS_DIR)

        # Construct archive path
        archive_path = os.path.join(ARCHIVE_DIR, rel_path)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(archive_path), exist_ok=True)

        if not dry_run:
            # Move file to archive
            shutil.move(file_path, archive_path)
            logger.debug(f"Archived {file_path} to {archive_path}")
        else:
            logger.debug(f"[DRY RUN] Would archive {file_path} to {archive_path}")

        return True

    except Exception as e:
        logger.error(f"Error archiving {file_path}: {e}")
        return False

async def migrate_outputs_directory(dry_run: bool = False, limit: int = None, creator: str = None) -> None:
    """
    Migrate all analysis files in the outputs directory to MongoDB.

    Args:
        dry_run: If True, don't actually insert data into MongoDB or archive files
        limit: Maximum number of files to migrate (None for all files)
        creator: Only migrate files for this creator (None for all creators)
    """
    # Get MongoDB database
    db = await get_async_mongo_db()

    # Create archive directory if it doesn't exist
    if not dry_run:
        os.makedirs(ARCHIVE_DIR, exist_ok=True)

    # Keep track of how many files we've migrated
    migrated_count = 0

    # Walk through outputs directory
    for root, _, files in os.walk(OUTPUTS_DIR):
        # If creator is specified, only process files in that creator's directory
        if creator and creator not in root:
            continue

        for file in files:
            file_path = os.path.join(root, file)

            # Skip files that are already in the archive directory
            if ARCHIVE_DIR in file_path:
                continue

            # Migrate file
            success = await migrate_file(file_path, db, dry_run)

            # Stop on first error
            if not success:
                logger.error(f"Migration failed for {file_path}. Stopping.")
                return

            # Archive file if migration was successful
            if success and not dry_run:
                # Only archive .md, .json, and their associated files
                _, ext = os.path.splitext(file_path)
                if ext.lower() in ['.md', '.json', '.yaml', '.txt']:
                    archive_file(file_path, dry_run)

            # Increment counter for successful migrations
            if success and ext.lower() in ['.md', '.json']:
                migrated_count += 1

            # Check if we've reached the limit
            if limit and migrated_count >= limit:
                logger.info(f"Reached limit of {limit} files. Stopping.")
                return

async def main_async():
    """Async entry point for the script."""
    parser = argparse.ArgumentParser(description="Migrate analysis files from outputs/ directory to MongoDB")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually insert data into MongoDB or archive files")
    parser.add_argument("--limit", type=int, help="Maximum number of files to migrate")
    parser.add_argument("--creator", type=str, help="Only migrate files for this creator")
    args = parser.parse_args()

    logger.info(f"Starting migration {'(DRY RUN)' if args.dry_run else ''}")
    if args.limit:
        logger.info(f"Limiting to {args.limit} files")
    if args.creator:
        logger.info(f"Only migrating files for creator: {args.creator}")

    # Migrate outputs directory
    await migrate_outputs_directory(args.dry_run, args.limit, args.creator)

    logger.info(f"Migration {'(DRY RUN) ' if args.dry_run else ''}completed")

def main():
    import asyncio

    asyncio.run(main_async())

if __name__ == "__main__":
    main()
