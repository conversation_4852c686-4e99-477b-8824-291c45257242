#!/usr/bin/env python3
"""
Batch embed extracted body frameworks from markdown parsing to MongoDB.
Uses the rich framework data extracted programmatically from LLM-friendly analyses.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import voyageai
from pymongo import AsyncMongoClient
from pymongo import UpdateOne
import logging
import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB connection
MONGO_URI = os.getenv("MONGODB_URI")
if not MONGO_URI:
    raise ValueError("MONGODB_URI environment variable not set")

# Voyage AI client
VOYAGE_AI_API_KEY = os.getenv("VOYAGE_AI_API_KEY")
if not VOYAGE_AI_API_KEY:
    raise ValueError("VOYAGE_AI_API_KEY environment variable not set")

voyage_client = voyageai.Client(api_key=VOYAGE_AI_API_KEY)


async def load_extracted_frameworks(file_path: str) -> List[Dict[str, Any]]:
    """Load frameworks from the batch extraction JSON file."""
    
    logger.info(f"Loading frameworks from: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    all_frameworks = data.get('all_frameworks', [])
    
    logger.info(f"Loaded {len(all_frameworks)} frameworks from {data.get('successful_creators', 0)} creators")
    
    # Add metadata
    for framework in all_frameworks:
        framework['extraction_method'] = 'markdown_parser'
        framework['extraction_timestamp'] = data.get('extraction_timestamp')
        framework['pattern_type'] = 'body'  # Ensure this is set
    
    return all_frameworks


async def embed_frameworks_batch(frameworks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Embed a batch of frameworks using Voyage AI."""
    if not frameworks:
        return []
    
    # Prepare texts for embedding (rich content combining all framework components)
    texts = []
    for framework in frameworks:
        # Create comprehensive text for embedding including the full example
        text_parts = [
            f"Framework: {framework.get('name', '')}",
            f"Description: {framework.get('description', '')}",
            f"Implementation: {' | '.join(framework.get('implementation_steps', []))}",
            f"Example: {framework.get('example', '')}",  # This is the key difference - include full example
            f"Creator: {framework.get('creator_name', '')}"
        ]
        text = " | ".join(filter(None, text_parts))  # Filter out empty parts
        texts.append(text)
    
    try:
        # Get embeddings from Voyage AI in batches (API limits)
        batch_size = 128  # Voyage AI batch limit
        all_embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            result = voyage_client.embed(
                batch_texts,
                model="voyage-3.5",
                input_type="document"
            )
            
            all_embeddings.extend(result.embeddings)
            logger.info(f"Embedded batch {i//batch_size + 1}: {len(batch_texts)} frameworks")
        
        # Add embeddings to frameworks
        for i, framework in enumerate(frameworks):
            framework["embedding"] = all_embeddings[i]
            framework["embedding_model"] = "voyage-3.5"
            framework["embedding_created_at"] = datetime.utcnow()
            
            # Ensure we have all required fields for MongoDB
            framework["pattern_id"] = f"{framework.get('creator_name', 'unknown')}_{framework.get('name', 'unknown').lower().replace(' ', '_')}"
            framework["category"] = "body_framework"
        
        logger.info(f"Successfully embedded {len(frameworks)} frameworks")
        return frameworks
        
    except Exception as e:
        logger.error(f"Error embedding batch: {e}")
        return []


async def save_frameworks_to_mongodb(db, frameworks: List[Dict[str, Any]]):
    """Save frameworks with embeddings to MongoDB."""
    if not frameworks:
        logger.warning("No frameworks to save")
        return
    
    collection = db.pattern_embeddings
    
    # Prepare bulk operations
    operations = []
    
    for framework in frameworks:
        # Use upsert to avoid duplicates
        filter_criteria = {
            "pattern_id": framework["pattern_id"],
            "category": "body_framework"
        }
        
        update_doc = {
            "$set": framework,
            "$setOnInsert": {
                "created_at": datetime.utcnow()
            }
        }
        
        operations.append(UpdateOne(filter_criteria, update_doc, upsert=True))
    
    try:
        # Execute bulk operations
        result = await collection.bulk_write(operations)
        
        logger.info(f"MongoDB results:")
        logger.info(f"  Matched: {result.matched_count}")
        logger.info(f"  Modified: {result.modified_count}")
        logger.info(f"  Upserted: {result.upserted_count}")
        logger.info(f"  Total operations: {len(operations)}")
        
    except Exception as e:
        logger.error(f"Error saving to MongoDB: {e}")
        raise


async def create_vector_index_if_needed(db):
    """Create vector search index for pattern_embeddings collection if it doesn't exist."""
    
    collection = db.pattern_embeddings
    
    try:
        # Check if vector index exists
        indexes = await collection.list_search_indexes().to_list(length=None)
        vector_index_exists = any(idx.get('name') == 'vector_index' for idx in indexes)
        
        if not vector_index_exists:
            logger.info("Creating vector search index...")
            
            # Define the vector search index
            index_definition = {
                "fields": [
                    {
                        "type": "vector",
                        "path": "embedding",
                        "numDimensions": 1024,  # voyage-3.5 dimension
                        "similarity": "cosine"
                    },
                    {
                        "type": "filter",
                        "path": "category"
                    },
                    {
                        "type": "filter", 
                        "path": "pattern_type"
                    },
                    {
                        "type": "filter",
                        "path": "creator_name"
                    }
                ]
            }
            
            await collection.create_search_index(
                index_definition,
                name="vector_index"
            )
            
            logger.info("Vector search index created successfully")
        else:
            logger.info("Vector search index already exists")
            
    except Exception as e:
        logger.warning(f"Could not create/check vector index: {e}")


async def main():
    """Main function to embed frameworks and save to MongoDB."""
    
    logger.info("🚀 Starting Framework Embedding Pipeline")
    logger.info("=" * 50)
    
    # Load extracted frameworks
    framework_file = "/Users/<USER>/LinkedInsight/batch_frameworks_extraction_20250531_072244.json"
    frameworks = await load_extracted_frameworks(framework_file)
    
    if not frameworks:
        logger.error("No frameworks found to embed")
        return
    
    # Filter to only frameworks with substantial examples for better embeddings
    frameworks_with_examples = [
        f for f in frameworks 
        if f.get('example') and len(f.get('example', '')) > 100
    ]
    
    logger.info(f"📝 Using {len(frameworks_with_examples)} frameworks with substantial examples")
    logger.info(f"   (Skipping {len(frameworks) - len(frameworks_with_examples)} frameworks without examples)")
    
    # Embed frameworks
    logger.info("🤖 Embedding frameworks with Voyage AI...")
    embedded_frameworks = await embed_frameworks_batch(frameworks_with_examples)
    
    if not embedded_frameworks:
        logger.error("No frameworks were successfully embedded")
        return
    
    # Connect to MongoDB and save
    logger.info("💾 Saving to MongoDB...")
    client = AsyncMongoClient(MONGO_URI)
    
    try:
        db = client.linkedinsight
        
        # Create vector index if needed
        await create_vector_index_if_needed(db)
        
        # Save frameworks
        await save_frameworks_to_mongodb(db, embedded_frameworks)
        
        # Summary statistics
        logger.info("\n📊 EMBEDDING SUMMARY")
        logger.info("=" * 30)
        logger.info(f"   Total frameworks processed: {len(frameworks)}")
        logger.info(f"   Frameworks with examples: {len(frameworks_with_examples)}")
        logger.info(f"   Successfully embedded: {len(embedded_frameworks)}")
        logger.info(f"   Average example length: {sum(len(f.get('example', '')) for f in embedded_frameworks) // len(embedded_frameworks)} chars")
        
        # Show sample framework names
        logger.info(f"\n   📦 Sample Framework Names:")
        for i, fw in enumerate(embedded_frameworks[:5]):
            logger.info(f"   {i+1}. {fw.get('name')} ({fw.get('creator_name')})")
        
        logger.info(f"\n✅ Framework embedding completed!")
        logger.info(f"🔍 Ready for vector search in MongoDB Atlas")
        
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())