#!/usr/bin/env python3
"""
Insert the generated pattern embeddings into MongoDB.
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_mongo_client():
    """Get MongoDB client."""
    mongo_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
    return MongoClient(mongo_uri)

def insert_patterns_to_mongodb(patterns_file: str = "outputs/patterns_with_embeddings/patterns_with_embeddings.json"):
    """Insert patterns with embeddings into MongoDB."""
    
    # Load patterns
    with open(patterns_file, 'r') as f:
        patterns = json.load(f)
    
    print(f"Loaded {len(patterns)} patterns from {patterns_file}")
    
    # Convert ISO date strings back to datetime objects
    for pattern in patterns:
        if isinstance(pattern.get("indexed_at"), str):
            pattern["indexed_at"] = datetime.fromisoformat(pattern["indexed_at"].replace('+00:00', '+00:00'))
        if isinstance(pattern.get("metadata", {}).get("extracted_at"), str):
            pattern["metadata"]["extracted_at"] = datetime.fromisoformat(
                pattern["metadata"]["extracted_at"].replace('+00:00', '+00:00')
            )
    
    # Connect to MongoDB
    client = get_mongo_client()
    db = client[os.getenv("MONGODB_DATABASE", "storyd_ai")]
    
    # Create test collection
    collection_name = "pattern_embeddings_test"
    collection = db[collection_name]
    
    # Clear existing data
    result = collection.delete_many({})
    print(f"Cleared {result.deleted_count} existing documents from {collection_name}")
    
    # Insert new patterns
    result = collection.insert_many(patterns)
    print(f"Inserted {len(result.inserted_ids)} patterns into {collection_name}")
    
    # Print sample pattern info
    sample = collection.find_one()
    print(f"\nSample pattern:")
    print(f"  Creator: {sample['creator_id']}")
    print(f"  Type: {sample['analysis_type']}")
    print(f"  Name: {sample['pattern_name']}")
    print(f"  Embedding dimensions: {sample['embedding_dimensions']}")
    
    # Print vector search index instructions
    print("\n" + "="*60)
    print("NEXT STEP: Create Vector Search Index in MongoDB Atlas")
    print("="*60)
    print("\n1. Go to your MongoDB Atlas cluster")
    print("2. Navigate to the 'Search' tab")
    print("3. Create a new index with:")
    print(f"   - Database: {db.name}")
    print(f"   - Collection: {collection_name}")
    print("   - Index Name: pattern_embeddings_vector_index")
    print("   - JSON Configuration:")
    
    index_config = {
        "mappings": {
            "dynamic": True,
            "fields": {
                "embedding": {
                    "type": "knnVector",
                    "dimensions": 1024,
                    "similarity": "cosine"
                }
            }
        }
    }
    
    print(json.dumps(index_config, indent=2))
    print("\n4. Click 'Create Search Index' and wait for it to build (~1-2 minutes)")
    
    return collection_name

if __name__ == "__main__":
    collection_name = insert_patterns_to_mongodb()
    print(f"\n✅ Success! Patterns inserted into collection: {collection_name}")