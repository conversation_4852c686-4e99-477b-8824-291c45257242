#!/usr/bin/env python3
"""
Just extract retentionadam bundles and upload to MongoDB. No embeddings.
"""

import json
import re
import os
import sys
from datetime import datetime, timezone

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.mongo_utils import get_mongo_client

def extract_bundles_simple(text):
    """Extract bundle objects from text using simple pattern matching."""
    
    bundles = []
    
    # Find all bundle_name occurrences
    bundle_starts = []
    for match in re.finditer(r'"bundle_name":\s*"([^"]+)"', text):
        bundle_starts.append({
            'name': match.group(1),
            'start_pos': match.start(),
        })
    
    print(f"Found {len(bundle_starts)} bundles:")
    for bundle in bundle_starts:
        print(f"  - {bundle['name']}")
    
    # For each bundle, extract the surrounding object
    for i, bundle_info in enumerate(bundle_starts):
        try:
            # Find the opening brace before this bundle_name
            search_start = max(0, bundle_info['start_pos'] - 200)
            preceding_text = text[search_start:bundle_info['start_pos']]
            
            last_brace = preceding_text.rfind('{')
            if last_brace == -1:
                continue
            
            object_start = search_start + last_brace
            
            # Find the end of this object
            if i + 1 < len(bundle_starts):
                next_bundle_pos = bundle_starts[i + 1]['start_pos']
                between_text = text[bundle_info['start_pos']:next_bundle_pos]
                last_close_brace = between_text.rfind('}')
                if last_close_brace != -1:
                    object_end = bundle_info['start_pos'] + last_close_brace + 1
                else:
                    object_end = next_bundle_pos
            else:
                # Last bundle
                remaining_text = text[bundle_info['start_pos']:]
                brace_count = 0
                object_end = len(text)
                for j, char in enumerate(remaining_text):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count <= -1:
                            object_end = bundle_info['start_pos'] + j + 1
                            break
            
            bundle_text = text[object_start:object_end]
            
            bundles.append({
                'name': bundle_info['name'],
                'text': bundle_text,
                'length': len(bundle_text)
            })
            
        except Exception as e:
            print(f"Error extracting bundle {bundle_info['name']}: {e}")
    
    return bundles

def upload_retentionadam_bundles():
    """Extract and upload retentionadam bundles to MongoDB."""
    
    file_path = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/retentionadam/retentionadam_bundle_discovery_analysis_20250601144400.json"
    
    print("🔍 Processing retentionadam bundle discovery...")
    
    # Get bundle text from nested JSON
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'bundles' in data and isinstance(data['bundles'], str):
            bundle_text = data['bundles']
            print(f"✅ Extracted nested JSON string ({len(bundle_text)} chars)")
        else:
            print("❌ No nested bundles string found")
            return False
    except Exception as e:
        print(f"❌ Failed to read file: {e}")
        return False
    
    # Extract individual bundles
    bundles = extract_bundles_simple(bundle_text)
    if not bundles:
        print("❌ No bundles extracted")
        return False
    
    print(f"\n📦 Extracted {len(bundles)} bundles")
    
    # Create documents (no embeddings)
    documents = []
    
    for i, bundle in enumerate(bundles):
        print(f"\n📝 Creating document for bundle {i+1}: {bundle['name']}")
        
        # Extract bundle description for metadata
        bundle_description = ""
        usage_context = ""
        
        desc_match = re.search(r'"bundle_description":\s*"([^"]+)"', bundle['text'])
        if desc_match:
            bundle_description = desc_match.group(1)
        
        context_match = re.search(r'"usage_context":\s*"([^"]+)"', bundle['text'])
        if context_match:
            usage_context = context_match.group(1)
        
        print(f"   Description: {bundle_description[:80]}...")
        print(f"   Usage context: {usage_context[:80]}...")
        
        # Create document
        doc = {
            'creator_name': 'retentionadam',
            'bundle_name': bundle['name'],
            'bundle_description': bundle_description,
            'usage_context': usage_context,
            'bundle_text': bundle['text'],
            'text_length': bundle['length'],
            'pattern_type': 'linguistic_bundle',
            'source_file': file_path,
            'created_at': datetime.now(timezone.utc),
            'version': 1
        }
        
        documents.append(doc)
        print(f"   ✅ Document ready")
    
    # Store in MongoDB
    if documents:
        print(f"\n💾 Storing {len(documents)} documents in MongoDB...")
        
        try:
            client = get_mongo_client()
            db = client.linkedinsight
            collection = db.pattern_embeddings
            
            # Clear existing retentionadam bundles
            deleted = collection.delete_many({
                'creator_name': 'retentionadam',
                'pattern_type': 'linguistic_bundle'
            })
            print(f"🗑️  Deleted {deleted.deleted_count} existing retentionadam bundle documents")
            
            # Insert new documents
            result = collection.insert_many(documents)
            print(f"✅ Stored {len(result.inserted_ids)} bundle documents")
            
            # Show what we stored
            print(f"\n📊 Stored bundles:")
            for doc in documents:
                print(f"   - {doc['bundle_name']}")
                print(f"     Length: {doc['text_length']} chars")
            
            return True
            
        except Exception as e:
            print(f"❌ MongoDB storage failed: {e}")
            return False
    
    return False

if __name__ == "__main__":
    success = upload_retentionadam_bundles()
    if success:
        print(f"\n🎉 retentionadam bundles uploaded successfully!")
        print(f"Ready for embedding step later.")
    else:
        print(f"\n❌ Upload failed")