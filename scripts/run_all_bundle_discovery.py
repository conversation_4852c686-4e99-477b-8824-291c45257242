#!/usr/bin/env python3
"""
Run bundle discovery analysis for all creators in the database.

This script:
1. Queries the database to get all creator names with post counts
2. Checks which creators already have bundle discovery analysis files
3. Runs bundle discovery only for creators who don't have results yet (unless --force)
4. Handles errors gracefully - if one creator fails, continues with others
5. Provides detailed logging and progress tracking
6. Supports command line options for specific creators and force re-run

Based on test_bundle_discovery.py but scaled for all creators with robust error handling.
"""

import asyncio
import json
import os
import sys
import sqlite3
import datetime
import logging
import argparse
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from src.infrastructure.llm.citations_claude import CitationsAnthropic
from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig
from src.utils.json_utils import extract_json_from_text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("all_bundle_discovery.log")
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Default configuration
DEFAULT_NUM_POSTS = 50
DEFAULT_OUTPUT_DIR = "LLM_friendly_Analyses"
DEFAULT_DB_PATH = "data/db/linkedin_content.db"
MIN_POSTS_REQUIRED = 15

def load_bundle_discovery_prompt() -> str:
    """Load the bundle discovery prompt."""
    prompt_path = "prompts/LLM_JSON_analyses_prompts/LLM_JSON_bundle_discovery_prompt.md"
    with open(prompt_path, 'r', encoding='utf-8') as f:
        return f.read()

def load_system_prompt() -> str:
    """Load the system prompt."""
    system_prompt_path = "prompts/LLM_JSON_analyses_prompts/LLM_JSON_system_prompt.txt"
    with open(system_prompt_path, 'r', encoding='utf-8') as f:
        return f.read()

def get_creators_with_post_counts(db_path: str) -> Dict[str, int]:
    """
    Get all creator names with their post counts from the database.

    Args:
        db_path: Path to the SQLite database

    Returns:
        Dictionary mapping creator names to post counts
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Query for all creator names with post counts
        cursor.execute("""
            SELECT c.creator_name, COUNT(p.post_id) as post_count
            FROM creators c
            LEFT JOIN posts p ON c.creator_id = p.creator_id
            GROUP BY c.creator_name
            HAVING post_count > 0
            ORDER BY post_count DESC
        """)

        creators_with_counts = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()
        return creators_with_counts

    except Exception as e:
        logger.error(f"Error getting creators with post counts from database: {e}")
        return {}

def get_creator_posts(creator_name: str, db_path: str, limit: int = 50) -> List[Dict[str, Any]]:
    """Get posts for a creator from the database."""
    posts_for_api = []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get creator ID
        cursor.execute("SELECT creator_id FROM creators WHERE creator_name = ?", (creator_name,))
        result = cursor.fetchone()
        if not result:
            logger.error(f"Creator '{creator_name}' not found.")
            return []
        creator_id = result[0]
        
        # Fetch posts ordered by engagement
        cursor.execute("""
            SELECT post_id, content, likes, comments, shares, date_posted
            FROM posts
            WHERE creator_id = ?
            ORDER BY (likes + comments + shares) DESC
            LIMIT ?
        """, (creator_id, limit))
        
        posts = cursor.fetchall()
        
        if not posts:
            logger.warning(f"No posts found for creator '{creator_name}'.")
            return []
        
        for post_id, content, likes, comments, shares, date_posted in posts:
            # Format the document with full content and engagement metrics
            document_content = f"POST ID: {post_id}\\n\\nFULL POST:\\n{content}\\n\\nENGAGEMENT METRICS:\\nLikes: {likes}\\nComments: {comments}\\nShares: {shares}"
            
            posts_for_api.append({
                "type": "document",
                "source": {
                    "type": "text",
                    "media_type": "text/plain",
                    "data": document_content
                }
            })
        
        logger.info(f"Loaded {len(posts)} posts for creator '{creator_name}'")
        return posts_for_api
        
    except sqlite3.Error as e:
        logger.error(f"Database error for creator '{creator_name}': {e}")
        return []
    finally:
        if conn:
            conn.close()

def check_existing_bundle_discovery(creator_name: str, output_dir: str) -> bool:
    """
    Check if bundle discovery analysis already exists for a creator.
    
    Args:
        creator_name: Name of the creator
        output_dir: Output directory for analyses
        
    Returns:
        True if bundle discovery analysis exists, False otherwise
    """
    creator_dir = os.path.join(output_dir, creator_name)
    if not os.path.exists(creator_dir):
        return False
    
    # Look for bundle discovery files
    for filename in os.listdir(creator_dir):
        if "bundle_discovery" in filename and filename.endswith((".json", ".txt")):
            return True
    
    return False

async def run_bundle_discovery_analysis(
    creator_name: str, 
    db_path: str, 
    output_dir: str, 
    num_posts: int = 50
) -> Tuple[bool, Optional[str], Optional[str], Optional[str]]:
    """
    Run bundle discovery analysis on a creator.
    
    Args:
        creator_name: Name of the creator
        db_path: Path to the database
        output_dir: Output directory for results
        num_posts: Number of posts to analyze
        
    Returns:
        Tuple of (success, error_message, raw_file_path, json_file_path)
    """
    
    logger.info(f"🔍 Starting bundle discovery analysis for {creator_name}")
    
    try:
        # 1. Load prompts
        system_prompt = load_system_prompt()
        user_prompt = load_bundle_discovery_prompt()
        
        # 2. Get creator posts
        posts = get_creator_posts(creator_name, db_path, num_posts)
        if not posts:
            return False, "No posts found", None, None
        
        # 3. Initialize Citations Claude client  
        from src.config import Config
        config = AnthropicConfig(
            api_key=Config.ANTHROPIC_API_KEY,
            beta_header=Config.CLAUDE_BETA_HEADER
        )
        llm_client = AnthropicClient(config)
        client = CitationsAnthropic(llm_client)
        
        # 4. Prepare the transcript from all posts
        transcript_parts = []
        for i, post in enumerate(posts, 1):
            post_data = post["source"]["data"]
            transcript_parts.append(f"=== POST {i} ===\\n{post_data}\\n")
        
        transcript = "\\n".join(transcript_parts)
        
        # 5. Prepare the user prompt with creator context
        full_prompt = f"Creator: {creator_name}\\n\\n{user_prompt}"
        
        # 6. Call the API with Citations and Interleaved Thinking
        logger.info(f"Calling Claude API for {creator_name}...")
        tool_response = await client.create_transcript_tool_call(
            transcript=transcript,
            model="claude-sonnet-4-20250514",
            system_prompt=system_prompt,
            user_prompt=full_prompt,
            tool_name="extract_linguistic_bundles",
            max_tokens=20000,
            thinking={
                "type": "enabled",
                "budget_tokens": 20000
            }
        )
        
        if not tool_response:
            return False, "No tool response received from Citations Claude", None, None
        
        # Extract the raw response text for saving
        response_text = str(tool_response)
        logger.info(f"Received response for {creator_name} ({len(response_text)} characters)")
        
        # 7. Create output directory
        creator_output_dir = os.path.join(output_dir, creator_name)
        os.makedirs(creator_output_dir, exist_ok=True)
        
        # 8. Generate timestamp for filename
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 9. Save raw response FIRST (critical for debugging)
        raw_response_file = os.path.join(
            creator_output_dir,
            f"{creator_name}_bundle_discovery_analysis_{timestamp}_raw_response.txt"
        )
        
        with open(raw_response_file, 'w', encoding='utf-8') as f:
            f.write(response_text)
        
        logger.info(f"✅ Saved raw response for {creator_name}")
        
        # 10. Extract JSON from Citations tool response
        if isinstance(tool_response, dict):
            # Check if the response has the nested JSON string issue
            if "bundles" in tool_response and isinstance(tool_response["bundles"], str):
                logger.info(f"Found nested JSON string in bundles key for {creator_name}, parsing...")
                try:
                    # Try direct JSON parsing first for the bundles string
                    bundles_data = json.loads(tool_response["bundles"])
                    # Reconstruct the full response with parsed bundles
                    json_data = tool_response.copy()
                    json_data["bundles"] = bundles_data
                    logger.info(f"Successfully parsed nested JSON string for {creator_name}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse nested JSON for {creator_name}: {e}")
                    # Fallback to extract_json_from_text
                    try:
                        json_data = extract_json_from_text(tool_response["bundles"])
                    except json.JSONDecodeError as e2:
                        logger.error(f"Fallback parsing also failed for {creator_name}: {e2}")
                        json_data = {}
            else:
                json_data = tool_response
        else:
            # Fallback to parsing response_text if tool_response isn't a dict
            json_data = extract_json_from_text(response_text)
        
        if not json_data:
            return False, "Failed to extract valid JSON from Citations response", raw_response_file, None
        
        # 11. Save parsed JSON
        json_output_file = os.path.join(
            creator_output_dir,
            f"{creator_name}_bundle_discovery_analysis_{timestamp}.json"
        )
        
        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Saved parsed JSON for {creator_name}")
        
        # 12. Quick analysis of results
        bundle_count = json_data.get("discovered_bundle_count", 0)
        total_posts = json_data.get("total_posts_analyzed", 0)
        
        logger.info(f"📊 {creator_name} RESULTS: {total_posts} posts analyzed, {bundle_count} bundles discovered")
        
        return True, None, raw_response_file, json_output_file
        
    except Exception as e:
        error_msg = f"Error during bundle discovery analysis: {str(e)}"
        logger.error(f"❌ {creator_name}: {error_msg}")
        return False, error_msg, None, None

async def run_batch_bundle_discovery(
    creators_to_analyze: List[str],
    db_path: str,
    output_dir: str,
    num_posts: int = DEFAULT_NUM_POSTS
) -> Dict[str, Dict[str, Any]]:
    """
    Run bundle discovery analysis for multiple creators.
    
    Args:
        creators_to_analyze: List of creator names to analyze
        db_path: Path to the database
        output_dir: Output directory for results  
        num_posts: Number of posts to analyze per creator
        
    Returns:
        Dictionary mapping creator names to results
    """
    results = {}
    total_creators = len(creators_to_analyze)
    
    logger.info(f"🚀 Starting batch bundle discovery for {total_creators} creators")
    
    for i, creator_name in enumerate(creators_to_analyze, 1):
        logger.info(f"\\n📍 Processing creator {i}/{total_creators}: {creator_name}")
        logger.info("=" * 60)
        
        try:
            success, error_msg, raw_file, json_file = await run_bundle_discovery_analysis(
                creator_name=creator_name,
                db_path=db_path,
                output_dir=output_dir,
                num_posts=num_posts
            )
            
            results[creator_name] = {
                "success": success,
                "error": error_msg,
                "raw_file": raw_file,
                "json_file": json_file,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            if success:
                logger.info(f"✅ {creator_name}: Bundle discovery completed successfully")
            else:
                logger.error(f"❌ {creator_name}: {error_msg}")
                
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(f"💥 {creator_name}: {error_msg}")
            results[creator_name] = {
                "success": False,
                "error": error_msg,
                "raw_file": None,
                "json_file": None,
                "timestamp": datetime.datetime.now().isoformat()
            }
    
    return results

async def main_async():
    """Async main entry point."""
    parser = argparse.ArgumentParser(
        description="Run bundle discovery analysis for all creators in the database",
        epilog="Example: python run_all_bundle_discovery.py --num-posts 50 --force"
    )
    parser.add_argument("--db-path", default=DEFAULT_DB_PATH,
                      help=f"Path to the SQLite database (default: {DEFAULT_DB_PATH})")
    parser.add_argument("--num-posts", type=int, default=DEFAULT_NUM_POSTS,
                      help=f"Number of posts to analyze (default: {DEFAULT_NUM_POSTS})")
    parser.add_argument("--output-dir", default=DEFAULT_OUTPUT_DIR,
                      help=f"Output directory for analyses (default: {DEFAULT_OUTPUT_DIR})")
    parser.add_argument("--force", action="store_true",
                      help="Force rerun of existing bundle discovery analyses")
    parser.add_argument("--min-posts", type=int, default=MIN_POSTS_REQUIRED,
                      help=f"Minimum number of posts required (default: {MIN_POSTS_REQUIRED})")
    parser.add_argument("--specific-creator",
                      help="Run bundle discovery for a specific creator only")
    parser.add_argument("--max-creators", type=int,
                      help="Maximum number of creators to process (for testing)")

    args = parser.parse_args()

    # Get creators from database with post counts
    if args.specific_creator:
        # For specific creator, we still need to validate they exist and have enough posts
        all_creators_with_counts = get_creators_with_post_counts(args.db_path)
        if args.specific_creator not in all_creators_with_counts:
            logger.error(f"Creator '{args.specific_creator}' not found in database")
            sys.exit(1)
        creators_with_counts = {args.specific_creator: all_creators_with_counts[args.specific_creator]}
        logger.info(f"Running bundle discovery for specific creator: {args.specific_creator}")
    else:
        creators_with_counts = get_creators_with_post_counts(args.db_path)
        logger.info(f"Found {len(creators_with_counts)} creators in the database")

    if not creators_with_counts:
        logger.error("No creators found in database")
        sys.exit(1)

    # Filter creators by minimum post count
    filtered_creators = {
        name: count for name, count in creators_with_counts.items() 
        if count >= args.min_posts
    }
    
    if len(filtered_creators) < len(creators_with_counts):
        excluded_count = len(creators_with_counts) - len(filtered_creators)
        logger.info(f"Excluded {excluded_count} creators with fewer than {args.min_posts} posts")

    # Check existing analyses
    creators_to_analyze = []
    for creator_name in filtered_creators.keys():
        if args.force or not check_existing_bundle_discovery(creator_name, args.output_dir):
            creators_to_analyze.append(creator_name)
        else:
            logger.info(f"Skipping {creator_name} - bundle discovery already exists")

    # Apply max creators limit if specified
    if args.max_creators and len(creators_to_analyze) > args.max_creators:
        creators_to_analyze = creators_to_analyze[:args.max_creators]
        logger.info(f"Limited to first {args.max_creators} creators for testing")

    if not creators_to_analyze:
        logger.info("No creators to analyze. All creators either have existing analyses or were filtered out.")
        sys.exit(0)

    logger.info(f"Will analyze {len(creators_to_analyze)} creators")
    for creator in creators_to_analyze:
        post_count = filtered_creators[creator]
        logger.info(f"  - {creator}: {post_count} posts")

    # Run batch bundle discovery
    try:
        results = await run_batch_bundle_discovery(
            creators_to_analyze=creators_to_analyze,
            db_path=args.db_path,
            output_dir=args.output_dir,
            num_posts=args.num_posts
        )

        # Summarize results
        successful = sum(1 for r in results.values() if r["success"])
        failed = len(results) - successful
        
        logger.info(f"\\n🎉 BATCH BUNDLE DISCOVERY COMPLETE")
        logger.info(f"📊 Total creators processed: {len(results)}")
        logger.info(f"✅ Successful: {successful}")
        logger.info(f"❌ Failed: {failed}")
        
        if failed > 0:
            logger.info(f"\\n❌ Failed creators:")
            for creator, result in results.items():
                if not result["success"]:
                    logger.info(f"  - {creator}: {result['error']}")

        # Save summary results
        summary_file = os.path.join(args.output_dir, f"bundle_discovery_batch_summary_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total_processed": len(results),
                    "successful": successful,
                    "failed": failed,
                    "timestamp": datetime.datetime.now().isoformat()
                },
                "results": results
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📁 Summary saved to: {summary_file}")

        # Exit with appropriate code
        sys.exit(0 if failed == 0 else 1)

    except Exception as e:
        logger.error(f"Critical error during batch processing: {e}")
        sys.exit(1)

def main():
    """Main entry point."""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()