#!/usr/bin/env python3
"""
Batch process ALL creator analyses and generate embeddings for pattern matching.

This script:
1. Extracts all patterns from MongoDB LLM_friendly_analyses
2. Generates embeddings using Voyage AI
3. Stores them in pattern_embeddings collection
"""

import os
import json
import time
import re
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import voyageai
from pymongo import MongoClient
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class ComprehensivePatternExtractor:
    """Extract ALL patterns from creator analyses."""
    
    def __init__(self):
        self.client = MongoClient(os.getenv("MONGODB_URI"))
        self.db = self.client["linkedinsight"]
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        
    def extract_all_patterns(self) -> List[Dict[str, Any]]:
        """Extract patterns from all creators in the database."""
        all_patterns = []
        
        # Get all creator analyses
        analyses = list(self.db.LLM_friendly_analyses.find({}))
        logger.info(f"Found {len(analyses)} creator analyses to process")
        
        for analysis in analyses:
            creator_name = analysis.get("creator_name")
            if not creator_name:
                continue
                
            logger.info(f"Processing {creator_name}...")
            
            # Extract patterns from each analysis type
            patterns = []
            
            # Extract hook patterns
            hook_patterns = self.extract_hook_patterns(analysis)
            patterns.extend(hook_patterns)
            logger.info(f"  - Extracted {len(hook_patterns)} hook patterns")
            
            # Extract ending patterns
            ending_patterns = self.extract_ending_patterns(analysis)
            patterns.extend(ending_patterns)
            logger.info(f"  - Extracted {len(ending_patterns)} ending patterns")
            
            # Extract theme patterns
            theme_patterns = self.extract_theme_patterns(analysis)
            patterns.extend(theme_patterns)
            logger.info(f"  - Extracted {len(theme_patterns)} theme patterns")
            
            # Extract body patterns from markdown
            body_patterns = self.extract_body_patterns(analysis)
            patterns.extend(body_patterns)
            logger.info(f"  - Extracted {len(body_patterns)} body patterns")
            
            all_patterns.extend(patterns)
        
        logger.info(f"\nTotal patterns extracted: {len(all_patterns)}")
        return all_patterns
    
    def extract_hook_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract hook patterns from analysis."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        
        # Get hook analysis
        hook_data = None
        for key in ["hook_analysis_data", "hook_analysis"]:
            if key in analysis_data:
                hook_data = analysis_data[key]
                break
        
        if not hook_data:
            return patterns
        
        # Extract archetypes
        archetypes = hook_data.get("2_dominant_hook_archetypes", [])
        for idx, archetype in enumerate(archetypes):
            pattern = self._create_pattern_doc(
                creator_name=analysis.get("creator_name"),
                analysis_type="hook",
                pattern_index=idx,
                pattern_name=archetype.get("archetype_name", f"Hook Pattern {idx}"),
                content={
                    "description": archetype.get("description", ""),
                    "example": archetype.get("example", ""),
                    "psychological_impact": archetype.get("psychological_impact", ""),
                    "frequency": archetype.get("frequency", "Unknown")
                },
                source_analysis_id=str(analysis.get("_id"))
            )
            patterns.append(pattern)
            
        return patterns
    
    def extract_ending_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract ending patterns from analysis."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        
        # Get ending analysis
        ending_data = None
        for key in ["ending_analysis_data", "ending_analysis"]:
            if key in analysis_data:
                ending_data = analysis_data[key]
                break
        
        if not ending_data:
            return patterns
        
        # Extract ending types
        endings = ending_data.get("2_dominant_ending_types", [])
        for idx, ending in enumerate(endings):
            # Handle both dict and string formats
            if isinstance(ending, dict):
                pattern_name = ending.get("ending_type", f"Ending Pattern {idx}")
                description = ending.get("description", "")
                example = ending.get("example", "")
                effect = ending.get("psychological_effect", "")
                frequency = ending.get("frequency", "Unknown")
            else:
                # Some endings might be stored as strings
                pattern_name = str(ending)
                description = ""
                example = ""
                effect = ""
                frequency = "Unknown"
            
            pattern = self._create_pattern_doc(
                creator_name=analysis.get("creator_name"),
                analysis_type="ending",
                pattern_index=idx,
                pattern_name=pattern_name,
                content={
                    "description": description,
                    "example": example,
                    "psychological_effect": effect,
                    "frequency": frequency
                },
                source_analysis_id=str(analysis.get("_id"))
            )
            patterns.append(pattern)
            
        return patterns
    
    def extract_theme_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract theme patterns from analysis."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        
        # Get theme analysis - check multiple possible field names
        theme_data = None
        for key in ["theme_analysis_data", "themes_analysis_data", "theme_analysis"]:
            if key in analysis_data:
                theme_data = analysis_data[key]
                break
        
        if not theme_data:
            return patterns
        
        # Extract primary themes - FIXED field name
        themes = theme_data.get("2_primary_content_themes", [])
        for idx, theme in enumerate(themes):
            pattern = self._create_pattern_doc(
                creator_name=analysis.get("creator_name"),
                analysis_type="theme",
                pattern_index=idx,
                pattern_name=theme.get("theme_name", f"Theme {idx}"),
                content={
                    "description": theme.get("description", ""),
                    "example_posts": theme.get("example_posts", []),
                    "psychological_appeal": theme.get("psychological_appeal", ""),
                    "frequency": theme.get("frequency_percentage", "Unknown")
                },
                source_analysis_id=str(analysis.get("_id"))
            )
            patterns.append(pattern)
            
        return patterns
    
    def extract_body_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract body structure patterns from markdown analysis."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        
        # Get body analysis markdown
        body_data = analysis_data.get("body_analysis_data", "")
        if not body_data or not isinstance(body_data, str):
            return patterns
        
        # Parse markdown sections for structural patterns
        # Look for ### headings that indicate pattern types
        pattern_sections = re.findall(
            r'###\s*\d*\.?\s*([^\n]+)\n(.*?)(?=###|\Z)', 
            body_data, 
            re.DOTALL
        )
        
        for idx, (pattern_name, content) in enumerate(pattern_sections):
            # Skip non-pattern sections
            if any(skip in pattern_name.lower() for skip in [
                'executive summary', 'analysis', 'compatibility', 
                'implementation', 'visual element', 'formatting'
            ]):
                continue
            
            # Extract key information from the section
            description_match = re.search(
                r'(?:This\s+(?:framework|structure|pattern)[^.]+\.)', 
                content, 
                re.IGNORECASE
            )
            description = description_match.group(0) if description_match else ""
            
            # Extract example if present
            example_match = re.search(
                r'\*\*Example:\*\*\s*\n```\n(.*?)\n```', 
                content, 
                re.DOTALL
            )
            example = example_match.group(1) if example_match else ""
            
            # Extract character count if mentioned
            char_count_match = re.search(
                r'\*\*Character Count:\*\*\s*([^\n]+)', 
                content
            )
            char_count = char_count_match.group(1) if char_count_match else ""
            
            pattern = self._create_pattern_doc(
                creator_name=analysis.get("creator_name"),
                analysis_type="body",
                pattern_index=idx,
                pattern_name=pattern_name.strip(),
                content={
                    "description": description,
                    "example": example,
                    "character_count": char_count,
                    "full_content": content[:1000]  # Include some context
                },
                source_analysis_id=str(analysis.get("_id"))
            )
            patterns.append(pattern)
            
        return patterns
    
    def _create_pattern_doc(
        self, 
        creator_name: str,
        analysis_type: str,
        pattern_index: int,
        pattern_name: str,
        content: Dict[str, Any],
        source_analysis_id: str
    ) -> Dict[str, Any]:
        """Create a standardized pattern document."""
        # Build searchable text from all content
        searchable_parts = [pattern_name]
        
        for key, value in content.items():
            if isinstance(value, str) and value:
                searchable_parts.append(value)
            elif isinstance(value, list):
                # Handle lists (like example_posts)
                searchable_parts.extend(str(v) for v in value if v)
        
        searchable_text = " ".join(searchable_parts)
        
        return {
            "creator_id": creator_name,
            "analysis_type": analysis_type,
            "pattern_index": pattern_index,
            "pattern_name": pattern_name,
            "content": content,
            "metadata": {
                "extracted_from": f"{analysis_type}_analysis",
                "extracted_at": datetime.now(timezone.utc),
                "source_analysis_id": source_analysis_id
            },
            "searchable_text": searchable_text
        }


class BatchEmbeddingProcessor:
    """Process patterns in batches and generate embeddings."""
    
    def __init__(self, rate_limit_delay: int = 20):
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        self.rate_limit_delay = rate_limit_delay
        
    def generate_embeddings_batch(
        self, 
        patterns: List[Dict[str, Any]], 
        batch_size: int = 5
    ) -> List[Dict[str, Any]]:
        """Generate embeddings for patterns in batches."""
        patterns_with_embeddings = []
        
        total_batches = (len(patterns) + batch_size - 1) // batch_size
        logger.info(f"Processing {len(patterns)} patterns in {total_batches} batches...")
        
        for i in range(0, len(patterns), batch_size):
            batch = patterns[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            logger.info(f"\nBatch {batch_num}/{total_batches}")
            
            # Extract texts for embedding
            texts = [p.get("searchable_text", "") for p in batch]
            
            try:
                # Generate embeddings
                result = self.voyage_client.embed(texts, model="voyage-3.5")
                embeddings = result.embeddings
                
                # Add embeddings to patterns
                for pattern, embedding in zip(batch, embeddings):
                    pattern["embedding"] = embedding
                    pattern["embedding_model"] = "voyage-3.5"
                    pattern["embedding_dimensions"] = len(embedding)
                    pattern["indexed_at"] = datetime.now(timezone.utc)
                    patterns_with_embeddings.append(pattern)
                
                logger.info(f"  ✓ Generated {len(embeddings)} embeddings")
                
                # Rate limiting
                if i + batch_size < len(patterns):
                    logger.info(f"  Waiting {self.rate_limit_delay}s for rate limit...")
                    time.sleep(self.rate_limit_delay)
                    
            except Exception as e:
                logger.error(f"  ✗ Error in batch {batch_num}: {e}")
                # Continue with next batch
                
        return patterns_with_embeddings


def main():
    """Run the full batch embedding pipeline."""
    logger.info("Starting batch embedding pipeline")
    logger.info("=" * 60)
    
    # 1. Extract all patterns
    extractor = ComprehensivePatternExtractor()
    patterns = extractor.extract_all_patterns()
    
    if not patterns:
        logger.error("No patterns extracted!")
        return
    
    # 2. Generate embeddings
    processor = BatchEmbeddingProcessor(rate_limit_delay=20)
    patterns_with_embeddings = processor.generate_embeddings_batch(patterns, batch_size=5)
    
    logger.info(f"\nSuccessfully generated embeddings for {len(patterns_with_embeddings)} patterns")
    
    # 3. Save to MongoDB
    logger.info("\nSaving to MongoDB...")
    
    client = MongoClient(os.getenv("MONGODB_URI"))
    db = client["linkedinsight"]
    collection = db["pattern_embeddings"]  # Production collection
    
    # Clear existing data (optional - comment out to append)
    # collection.delete_many({})
    
    # Insert new patterns
    if patterns_with_embeddings:
        result = collection.insert_many(patterns_with_embeddings)
        logger.info(f"✓ Inserted {len(result.inserted_ids)} patterns into pattern_embeddings")
    
    # 4. Summary statistics
    logger.info("\n" + "=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    
    # Count by type
    type_counts = {}
    creator_counts = {}
    
    for p in patterns_with_embeddings:
        # Count by type
        p_type = p.get("analysis_type", "unknown")
        type_counts[p_type] = type_counts.get(p_type, 0) + 1
        
        # Count by creator
        creator = p.get("creator_id", "unknown")
        creator_counts[creator] = creator_counts.get(creator, 0) + 1
    
    logger.info("\nPatterns by type:")
    for p_type, count in sorted(type_counts.items()):
        logger.info(f"  {p_type}: {count}")
    
    logger.info(f"\nTotal creators processed: {len(creator_counts)}")
    logger.info("\nTop creators by pattern count:")
    for creator, count in sorted(creator_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        logger.info(f"  {creator}: {count} patterns")
    
    logger.info("\n✅ Batch embedding pipeline complete!")
    logger.info("\nNext steps:")
    logger.info("1. Create vector search index on 'pattern_embeddings' collection")
    logger.info("2. Update vector_pattern_selector.py to use 'pattern_embeddings' collection")
    logger.info("3. Test with real queries")


if __name__ == "__main__":
    main()