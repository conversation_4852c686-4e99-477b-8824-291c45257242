#!/usr/bin/env python
"""Local agent runner for LinkedInsight.

This script provides a CLI tool for running the local agent directly from the command line.
It uses local analysis files instead of MongoDB for content generation.
"""

import asyncio
import argparse
import json
import sys
import os
import datetime
import time
from typing import Dict, Any, List, Optional, Callable

# Add project root to path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.api.local_agent_service import LocalAgentService

# Default directories
DEFAULT_JSON_DIR = os.path.join(project_root, "LLM_friendly_Analyses")
DEFAULT_MARKDOWN_DIR = os.path.join(project_root, "outputs", "local_analysis")
DEFAULT_OUTPUT_DIR = os.path.join(project_root, "outputs", "agent_output")

# Ensure output directory exists
os.makedirs(DEFAULT_OUTPUT_DIR, exist_ok=True)

# Step names for progress tracking
STEP_NAMES = {
    "loading_analyses": "Loading creator analyses",
    "processing_theme": "Selecting theme",
    "creating_hook": "Creating hook",
    "developing_body": "Developing body content",
    "crafting_ending": "Crafting ending",
    "completed": "Generation completed"
}

async def run_local_agent(
    input_text: str,
    creator_name: str,
    json_dir: Optional[str] = None,
    markdown_dir: Optional[str] = None,
    verbose: bool = False,
    output_file: Optional[str] = None,
    show_progress: bool = True
) -> Dict[str, Any]:
    """Run the local agent with the given input and creator.

    Args:
        input_text: User input text
        creator_name: Name of the creator to emulate
        json_dir: Directory for JSON analyses
        markdown_dir: Directory for markdown analyses
        verbose: Whether to show verbose output
        output_file: Optional file path to save the output
        show_progress: Whether to show progress updates

    Returns:
        The agent's response as a dictionary
    """
    print(f"\nRunning local agent with creator: {creator_name}")
    print(f"Input: {input_text}\n")

    # Use default directories if not specified
    if json_dir is None:
        json_dir = DEFAULT_JSON_DIR
    if markdown_dir is None:
        markdown_dir = DEFAULT_MARKDOWN_DIR

    # Create a progress callback if show_progress is True
    progress_callback = None
    if show_progress:
        def update_progress(step_name: str):
            step_description = STEP_NAMES.get(step_name, step_name)
            print(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] {step_description}...")
        progress_callback = update_progress

    try:
        # Start timing
        start_time = time.time()

        # Call the local agent service
        result = await LocalAgentService.generate_content(
            input_text=input_text,
            creator_name=creator_name,
            json_dir=json_dir,
            markdown_dir=markdown_dir,
            update_step_callback=progress_callback
        )

        # Calculate elapsed time
        elapsed_time = time.time() - start_time
        print(f"\nGeneration completed in {elapsed_time:.2f} seconds\n")

        # Check if we got an error
        if "error" in result:
            print(f"Error: {result['error']}")
            return result

        # Display the generated post
        if "generated_post" in result:
            print("\nGenerated Post:")
            print("-" * 80)
            print(result["generated_post"])
            print("-" * 80)

            # Display the explanation if verbose is True
            if verbose and "explanation" in result:
                print("\nExplanation:")
                print("-" * 80)
                print(result["explanation"])
                print("-" * 80)

            # Save output to file if specified
            if output_file and "generated_post" in result:
                try:
                    # Create output directory if it doesn't exist
                    output_dir = os.path.dirname(output_file)
                    if output_dir:
                        os.makedirs(output_dir, exist_ok=True)

                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"# Generated Post ({creator_name})\n\n")
                        f.write(result["generated_post"])
                        f.write("\n\n")

                        if "explanation" in result:
                            f.write("\n\n---\n\n")
                            f.write(result["explanation"])

                        # Add thinking logs if available and verbose is True
                        if verbose and "thinking_logs" in result:
                            f.write("\n\n---\n\n")
                            f.write("# Thinking Logs\n\n")
                            for step_name, thinking in result["thinking_logs"].items():
                                f.write(f"## Step: {step_name}\n\n")
                                f.write("```\n")
                                f.write(thinking)
                                f.write("\n```\n\n")

                    print(f"\nOutput saved to: {output_file}")
                except Exception as e:
                    print(f"Error saving output to file: {e}")

        return result

    except Exception as e:
        print(f"Error running local agent: {e}")
        return {"error": str(e)}

async def interactive_mode(
    creator_name: str,
    json_dir: Optional[str] = None,
    markdown_dir: Optional[str] = None,
    verbose: bool = False,
    output_dir: Optional[str] = None
):
    """Run the local agent in interactive mode.

    Args:
        creator_name: Name of the creator to emulate
        json_dir: Directory for JSON analyses
        markdown_dir: Directory for markdown analyses
        verbose: Whether to show verbose output
        output_dir: Optional directory to save generated posts
    """
    print(f"\n=== Interactive Local Agent Mode (Creator: {creator_name}) ===")
    print("Type 'exit' or 'quit' to end the session.")
    print("Type 'change creator <name>' to change the creator.")

    current_creator = creator_name

    while True:
        try:
            # Get user input
            user_input = input("\nEnter your brief or draft post: ")

            # Check for exit command
            if user_input.lower() in ["exit", "quit"]:
                print("Exiting interactive mode.")
                break

            # Check for change creator command
            if user_input.lower().startswith("change creator "):
                new_creator = user_input[len("change creator "):].strip()
                if new_creator:
                    current_creator = new_creator
                    print(f"Changed creator to: {current_creator}")
                else:
                    print("Invalid creator name.")
                continue

            # Skip empty input
            if not user_input.strip():
                print("Please enter a brief or draft post.")
                continue

            # Generate output filename if output directory is specified
            output_file = None
            if output_dir:
                # Create the directory if it doesn't exist
                os.makedirs(output_dir, exist_ok=True)

                # Generate a timestamp-based filename
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(
                    output_dir,
                    f"{current_creator}_{timestamp}.md"
                )

            # Call the local agent
            await run_local_agent(
                user_input,
                current_creator,
                json_dir,
                markdown_dir,
                verbose,
                output_file
            )

        except KeyboardInterrupt:
            print("\nExiting interactive mode.")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Main entry point for the script.

    This function is the entry point for CLI usage.
    """
    parser = argparse.ArgumentParser(
        description="Run the LinkedInsight local agent using local analysis files",
        epilog="Example: python local_agent_runner.py -i 'My draft post' -c retentionadam -v"
    )
    parser.add_argument("--input", "-i", help="Input text for the agent")
    parser.add_argument("--creator", "-c", default="retentionadam",
                        help="Creator name to emulate (default: retentionadam)")
    parser.add_argument("--json-dir", "-j", default=DEFAULT_JSON_DIR,
                        help=f"Directory for JSON analyses (default: {DEFAULT_JSON_DIR})")
    parser.add_argument("--markdown-dir", "-m", default=DEFAULT_MARKDOWN_DIR,
                        help=f"Directory for markdown analyses (default: {DEFAULT_MARKDOWN_DIR})")
    parser.add_argument("--verbose", "-v", action="store_true",
                        help="Show verbose output including explanation and thinking logs")
    parser.add_argument("--interactive", "-I", action="store_true",
                        help="Run in interactive mode")
    parser.add_argument("--output", "-o",
                        help="Output file path (default: auto-generated in outputs/agent_output)")
    parser.add_argument("--output-dir", "-O", default=DEFAULT_OUTPUT_DIR,
                        help=f"Output directory for interactive mode (default: {DEFAULT_OUTPUT_DIR})")
    parser.add_argument("--no-progress", action="store_true",
                        help="Disable progress updates during generation")

    args = parser.parse_args()

    if args.interactive:
        # Run in interactive mode
        asyncio.run(interactive_mode(
            args.creator,
            args.json_dir,
            args.markdown_dir,
            args.verbose,
            args.output_dir
        ))
    elif args.input:
        # Generate output filename if not provided
        output_file = args.output
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(
                args.output_dir,
                f"agent_output_{args.creator}_{timestamp}.md"
            )

        # Run with provided input
        asyncio.run(run_local_agent(
            args.input,
            args.creator,
            args.json_dir,
            args.markdown_dir,
            args.verbose,
            output_file,
            not args.no_progress
        ))
    else:
        parser.print_help()
        print("\nError: Either --input or --interactive is required.")
        sys.exit(1)

if __name__ == "__main__":
    main()
