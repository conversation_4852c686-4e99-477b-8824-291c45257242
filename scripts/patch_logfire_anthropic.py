#!/usr/bin/env python3
"""
Patch for Logfire Anthropic Integration
--------------------------------------
This script patches the Logfire Anthropic integration to handle ThinkingBlock objects correctly.

The issue is in the on_response function, which tries to access a 'name' attribute on blocks
that might be ThinkingBlock objects, which don't have this attribute.

Usage:
    python scripts/patch_logfire_anthropic.py
"""

import os
import sys
import logging
from pathlib import Path
import importlib.util
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_logfire_anthropic_path():
    """Find the path to the Logfire Anthropic integration module."""
    try:
        # Try to import the module to get its path
        import logfire._internal.integrations.llm_providers.anthropic as logfire_anthropic
        return logfire_anthropic.__file__
    except ImportError:
        logger.error("Could not import Logfire Anthropic integration module.")
        return None

def backup_file(file_path):
    """Create a backup of the file."""
    backup_path = f"{file_path}.bak"
    shutil.copy2(file_path, backup_path)
    logger.info(f"Created backup at {backup_path}")
    return backup_path

def patch_logfire_anthropic(file_path):
    """Patch the Logfire Anthropic integration module."""
    with open(file_path, 'r') as f:
        content = f.read()

    # Original problematic code
    original_code = """def on_response(response: ResponseT, span: LogfireSpan) -> ResponseT:
    \"\"\"Updates the span based on the type of response.\"\"\"
    if isinstance(response, Message):  # pragma: no branch
        block = response.content[0]
        message: dict[str, Any] = {'role': 'assistant'}
        if block.type == 'text':
            message['content'] = block.text
        else:
            message['tool_calls'] = [
                {
                    'function': {
                        'arguments': block.model_dump_json(include={'input'}),
                        'name': block.name,  # type: ignore
                    }
                }
                for block in response.content
            ]
        span.set_attribute('response_data', {'message': message, 'usage': response.usage})
    return response"""

    # Patched code that handles ThinkingBlock objects
    patched_code = """def on_response(response: ResponseT, span: LogfireSpan) -> ResponseT:
    \"\"\"Updates the span based on the type of response.\"\"\"
    if isinstance(response, Message):  # pragma: no branch
        block = response.content[0]
        message: dict[str, Any] = {'role': 'assistant'}
        if block.type == 'text':
            message['content'] = block.text
        else:
            # Only include blocks that have a 'name' attribute (skip ThinkingBlock)
            message['tool_calls'] = [
                {
                    'function': {
                        'arguments': block.model_dump_json(include={'input'}),
                        'name': block.name,  # type: ignore
                    }
                }
                for block in response.content
                if hasattr(block, 'name')
            ]
        span.set_attribute('response_data', {'message': message, 'usage': response.usage})
    return response"""

    # Replace the original code with the patched code
    if original_code in content:
        patched_content = content.replace(original_code, patched_code)
        with open(file_path, 'w') as f:
            f.write(patched_content)
        logger.info(f"Successfully patched {file_path}")
        return True
    else:
        logger.error(f"Could not find the original code in {file_path}")
        return False

def main():
    """Main entry point for the script."""
    # Find the Logfire Anthropic integration module
    file_path = find_logfire_anthropic_path()
    if not file_path:
        logger.error("Could not find Logfire Anthropic integration module.")
        return False

    logger.info(f"Found Logfire Anthropic integration module at {file_path}")

    # Create a backup of the file
    backup_path = backup_file(file_path)

    # Patch the file
    success = patch_logfire_anthropic(file_path)
    if success:
        logger.info("Successfully patched Logfire Anthropic integration.")
        logger.info(f"Original file backed up at {backup_path}")
        return True
    else:
        logger.error("Failed to patch Logfire Anthropic integration.")
        logger.info(f"You can restore the original file from {backup_path}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
