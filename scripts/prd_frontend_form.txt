Product Requirements Document: React Content Strategy Form

1. Overview

*   Feature: User Content Strategy Form (React Component)
*   Purpose: Allow new users to input their initial content strategy and existing users to view/update their strategy. This data is crucial for personalizing AI-generated content within the application.
*   Depends on: FastAPI backend with `/api/v1/content-strategy` endpoints (GET/POST).

2. Goals

*   Collect essential strategic information from users.
*   Provide a clear and easy-to-use interface for inputting and updating this information.
*   Ensure data is saved and retrieved correctly via the backend API.
*   Visually integrate with the existing application's look and feel (including light/dark themes) using components like `ShadowContainer`.

3. User Stories

*   As a new user, after my first login, if I haven't set my content strategy, I want to be automatically directed to a form so I can input my strategy details.
*   As a user, I want to be able to access the content strategy form from a menu opened by my profile icon so I can view or update my strategy.
*   As a user, when I open the content strategy form, I want to see my previously saved strategy pre-filled in the fields if it exists.
*   As a user, I want to be able to fill in my Industry, Offering (Product/Service), Target Audience (ICP), Key Differentiators, and Primary Content Goal.
*   As a user, when I submit the form, I want my strategy to be saved.
*   As a user, I want to receive clear feedback (success or error messages) when I try to save my strategy.
*   As a user, after successfully saving my strategy (especially for the first time), I want to be navigated to the main application area.

4. User Flow & Interaction

*   4.1. New User / Strategy Not Set (Post-Login):
    *   System calls `GET /api/v1/content-strategy`.
    *   If 404 (Not Found): Redirect to the Content Strategy Form page (`/content-strategy` or similar route).
    *   If 200 (Found): Proceed to dashboard.
*   4.2. Access via Profile Icon Menu:
    *   User clicks profile icon (e.g., Clerk `UserButton`).
    *   A dropdown menu appears.
    *   One menu item: "Content Strategy" (or "Set/Update Content Strategy").
    *   Clicking menu item navigates to the Content Strategy Form page.
*   4.3. Form Page Behavior:
    *   Loading: On page load, call `GET /api/v1/content-strategy`. Display loading indicator (e.g., spinner within `ShadowContainer`).
    *   Data Display: If data is fetched successfully, pre-fill form fields. If 404 or error during fetch (for existing user access), show an empty form or a subtle error notice and allow input.
    *   Input Fields: Industry, Offering, Target Audience, Key Differentiators, Primary Content Goal (Text inputs/textareas as per `onboarding_form_mockup.html`).
    *   Container: Utilize `ShadowContainer` for the form's visual frame.
*   4.4. Form Submission:
    *   User clicks "Save Strategy" button.
    *   Button is disabled; loading state initiated (e.g., spinner on button or form overlay).
    *   Call `POST /api/v1/content-strategy` with form data.
    *   On Success (200 OK):
        *   Display success toast: "Strategy Saved!"
        *   If redirected from Flow 4.1: Navigate to the main dashboard.
        *   If accessed via Profile Menu (Flow 4.2): Navigate to main dashboard.
    *   On API Error (e.g., 500):
        *   Display error toast/message on form: "Failed to save strategy. Please try again." (or more specific API error if available).
        *   Re-enable "Save Strategy" button.

5. UI Components & Styling

*   Main Container: `ShadowContainer` component.
*   Form Fields: Standard text inputs and textareas.
    *   Industry: Text input.
    *   Offering: Textarea.
    *   Target Audience: Textarea.
    *   Differentiators: Textarea.
    *   Content Goal: Text input.
*   Button: "Save Strategy".
*   Styling: Adhere to existing application's light/dark theme system and UI conventions (e.g., from `linkedinsight-viewer`, likely ShadCN/Tailwind if those are in use).
*   Notifications: Use existing toast/notification system for success/error messages.
*   Responsiveness: Form should be usable on typical desktop and tablet screen sizes. Mobile responsiveness is a plus but can be a secondary priority if not explicitly required for MVP.

6. Non-functional Requirements

*   Error Handling: Gracefully handle API errors and display user-friendly messages.
*   State Management: Effectively manage form input state, loading states, and error states within the React component.

7. Future Considerations (Out of Scope for MVP)

*   Full "Settings" page with multiple sections.
*   More advanced validation on form fields (client-side or server-side beyond basic presence).
*   Specific navigation back to "previous page" after update from menu (if more complex than redirecting to dashboard). 