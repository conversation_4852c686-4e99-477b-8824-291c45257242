#!/usr/bin/env python
"""
Utility script to delete a creator and all associated data from MongoDB.

Usage:
    python scripts/delete_creator.py --creator_name <creator_name> [--dry_run]

Arguments:
    --creator_name: Name of the creator to delete
    --dry_run: Optional flag to show what would be deleted without actually deleting

Example:
    python scripts/delete_creator.py --creator_name benja<PERSON><PERSON><PERSON>er
    python scripts/delete_creator.py --creator_name benja<PERSON><PERSON><PERSON>er --dry_run
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add project root to system path
root_dir = Path(__file__).parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import MongoDB utilities
try:
    from src.utils.mongo_utils_async import get_async_mongo_db
except ImportError:
    logger.error("Failed to import MongoDB utilities. Make sure you're running this script from the project root.")
    sys.exit(1)

async def get_collection_stats(db, creator_name: str) -> Dict[str, int]:
    """
    Get statistics about how many documents would be deleted from each collection.

    Args:
        db: MongoDB database instance
        creator_name: Name of the creator

    Returns:
        Dictionary with collection names as keys and document counts as values
    """
    stats = {}

    # Check creators collection
    stats['creators'] = await db.creators.count_documents({"creator_name": creator_name})

    # Check analyses collection
    stats['analyses'] = await db.analyses.count_documents({"creator_name": creator_name})

    # Check markdown_content collection
    stats['markdown_content'] = await db.markdown_content.count_documents({"creator_name": creator_name})

    # Check posts collection
    stats['posts'] = await db.posts.count_documents({"creator_name": creator_name})

    # Check jobs collection
    stats['jobs'] = await db.jobs.count_documents({"creator_name": creator_name})

    # Check agent_jobs collection if it exists
    stats['agent_jobs'] = await db.agent_jobs.count_documents({"creator_name": creator_name})

    return stats

async def delete_creator(creator_name: str, dry_run: bool = False) -> Dict[str, int]:
    """
    Delete a creator and all associated data from MongoDB.

    Args:
        creator_name: Name of the creator to delete
        dry_run: If True, only show what would be deleted without actually deleting

    Returns:
        Dictionary with collection names as keys and deleted document counts as values
    """
    # Async DB instance
    db = await get_async_mongo_db()

    # Get statistics before deletion
    stats = await get_collection_stats(db, creator_name)

    # Print what will be deleted
    logger.info(f"{'Would delete' if dry_run else 'Deleting'} creator: {creator_name}")
    for collection, count in stats.items():
        logger.info(f"  - {collection}: {count} documents")

    # If no documents found, warn the user
    if all(count == 0 for count in stats.values()):
        logger.warning(f"No documents found for creator: {creator_name}")
        return stats

    # If dry run, return without deleting
    if dry_run:
        logger.info("Dry run completed. No documents were deleted.")
        return stats

    # Confirm deletion
    confirmation = input(f"Are you sure you want to delete all data for creator '{creator_name}'? This cannot be undone. (y/N): ")
    if confirmation.lower() != 'y':
        logger.info("Deletion cancelled.")
        return stats

    # Delete from creators collection
    result = await db.creators.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from creators collection")

    # Delete from analyses collection
    result = await db.analyses.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from analyses collection")

    # Delete from markdown_content collection
    result = await db.markdown_content.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from markdown_content collection")

    # Delete from posts collection
    result = await db.posts.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from posts collection")

    # Delete from jobs collection
    result = await db.jobs.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from jobs collection")

    # Delete from agent_jobs collection if it exists
    result = await db.agent_jobs.delete_many({"creator_name": creator_name})
    logger.info(f"Deleted {result.deleted_count} documents from agent_jobs collection")

    logger.info(f"Successfully deleted all data for creator: {creator_name}")

    return stats

async def main_async():
    """Async CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Delete a creator and all associated data from MongoDB"
    )
    parser.add_argument("--creator_name", required=True, help="Name of the creator to delete")
    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Show what would be deleted without actually deleting",
    )

    args = parser.parse_args()

    try:
        await delete_creator(args.creator_name, args.dry_run)
    except Exception as exc:
        logger.error("Error deleting creator: %s", exc)
        sys.exit(1)

def main():
    import asyncio

    asyncio.run(main_async())

if __name__ == "__main__":
    main()
