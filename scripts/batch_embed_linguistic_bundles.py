#!/usr/bin/env python3
"""
<PERSON><PERSON> embed all linguistic style bundles from the extracted bundle files.
Uses Voyage AI to embed comprehensive linguistic patterns for vector search.
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any
from datetime import datetime
import voyageai
from pymongo import AsyncMongoClient, UpdateOne
import logging
from pathlib import Path
import glob

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB connection
MONGO_URI = os.getenv("MONGODB_URI")
if not MONGO_URI:
    raise ValueError("MONGODB_URI environment variable not set")

# Voyage AI client
VOYAGE_AI_API_KEY = os.getenv("VOYAGE_AI_API_KEY")
if not VOYAGE_AI_API_KEY:
    raise ValueError("VOYAGE_AI_API_KEY environment variable not set")

voyage_client = voyageai.Client(api_key=VOYAGE_AI_API_KEY)

# Rate limiting
BATCH_SIZE = 10
RATE_LIMIT_DELAY = 20  # seconds between batches (3 RPM limit)


def load_all_linguistic_bundles() -> List[Dict[str, Any]]:
    """Load all linguistic bundle files and extract individual bundles."""
    all_bundles = []
    bundle_files = glob.glob("/Users/<USER>/LinkedInsight/linguistic_style_bundles/*_linguistic_style_bundles.json")
    
    logger.info(f"Found {len(bundle_files)} linguistic bundle files")
    
    for file_path in bundle_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                bundle_data = json.load(f)
            
            creator_name = bundle_data.get("creator_name", "unknown")
            bundles = bundle_data.get("bundles", [])
            
            if not bundles:
                logger.warning(f"No bundles found in {file_path}")
                continue
            
            # Extract individual bundles with metadata
            for bundle in bundles:
                bundle_record = {
                    "creator_name": creator_name,
                    "bundle_name": bundle.get("bundle_name", ""),
                    "content_purpose": bundle.get("content_purpose", ""),
                    "when_to_use": bundle.get("when_to_use", ""),
                    "pattern_type": "linguistic",
                    "analysis_type": "linguistic_bundle",
                    "source_file": file_path,
                    "extraction_timestamp": bundle_data.get("extraction_timestamp", "")
                }
                # Add the bundle content directly to the record
                bundle_record.update(bundle)
                all_bundles.append(bundle_record)
            
            logger.info(f"Loaded {len(bundles)} bundles from {creator_name}")
            
        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
    
    logger.info(f"Total linguistic bundles loaded: {len(all_bundles)}")
    return all_bundles


def create_embedding_text(bundle: Dict[str, Any]) -> str:
    """Create comprehensive text representation for embedding."""
    
    bundle_data = bundle.get("bundle_data", {})
    
    # Core bundle information
    text_parts = [
        f"Bundle: {bundle.get('bundle_name', '')}",
        f"Purpose: {bundle.get('content_purpose', '')}",
        f"When to use: {bundle.get('when_to_use', '')}",
        f"Creator: {bundle.get('creator_name', '')}"
    ]
    
    # Extract sentence patterns
    sentence_patterns = bundle_data.get("sentence_patterns", [])
    if sentence_patterns:
        patterns_text = "Sentence patterns: " + " | ".join([
            f"{p.get('name', '')}: {p.get('example_template', '')} (Example: {p.get('creator_example', '')})"
            for p in sentence_patterns
        ])
        text_parts.append(patterns_text)
    
    # Extract vocabulary specifics
    vocab = bundle_data.get("vocabulary_specifics", {})
    if vocab:
        vocab_parts = []
        
        # Preferred terms
        preferred = vocab.get("preferred_industry_terms", [])
        if preferred:
            vocab_parts.append(f"Preferred terms: {', '.join(preferred)}")
        
        # Avoided terms
        avoided = vocab.get("avoided_terms", [])
        if avoided:
            vocab_parts.append(f"Avoided terms: {', '.join(avoided)}")
        
        # Unique lexicon
        lexicon = vocab.get("unique_creator_lexicon", [])
        if lexicon:
            lexicon_text = " | ".join([
                f"{item.get('term', '')}: {item.get('meaning', '')}"
                for item in lexicon
            ])
            vocab_parts.append(f"Unique terms: {lexicon_text}")
        
        if vocab_parts:
            text_parts.append("Vocabulary: " + " | ".join(vocab_parts))
    
    # Extract rhetorical devices
    rhetoric = bundle_data.get("rhetorical_devices", [])
    if rhetoric:
        rhetoric_text = "Rhetorical devices: " + " | ".join([
            f"{r.get('device', '')} ({r.get('frequency', '')}): {r.get('example', '')}"
            for r in rhetoric
        ])
        text_parts.append(rhetoric_text)
    
    # Extract formatting rules
    formatting = bundle_data.get("formatting_rules", {})
    if formatting:
        format_parts = []
        
        emphasis = formatting.get("text_formatting_for_emphasis", [])
        if emphasis:
            format_parts.append(f"Emphasis: {', '.join(emphasis)}")
        
        impact_phrases = formatting.get("use_of_standalone_impact_phrases", {})
        if impact_phrases:
            format_parts.append(f"Impact phrases: {impact_phrases.get('example', '')}")
        
        if format_parts:
            text_parts.append("Formatting: " + " | ".join(format_parts))
    
    # Extract linguistic dos
    dos = bundle_data.get("linguistic_dos", [])
    if dos:
        dos_text = "Linguistic rules: " + " | ".join([
            f"{d.get('rule', '')} (Priority: {d.get('priority', '')})"
            for d in dos
        ])
        text_parts.append(dos_text)
    
    # Extract contextual application rules
    context_rules = bundle_data.get("contextual_application_rules", [])
    if context_rules:
        context_text = "Context rules: " + " | ".join([
            f"When {c.get('when', '')}: apply {c.get('apply', '')}"
            for c in context_rules
        ])
        text_parts.append(context_text)
    
    return " | ".join(text_parts)


async def embed_bundles_batch(bundles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Embed a batch of linguistic bundles using Voyage AI."""
    if not bundles:
        return []
    
    # Prepare texts for embedding
    texts = []
    for bundle in bundles:
        text = create_embedding_text(bundle)
        texts.append(text)
    
    try:
        # Get embeddings from Voyage AI
        result = voyage_client.embed(
            texts,
            model="voyage-3.5",
            input_type="document"
        )
        
        # Add embeddings to bundles
        for i, bundle in enumerate(bundles):
            bundle["embedding"] = result.embeddings[i]
            bundle["embedding_model"] = "voyage-3.5"
            bundle["embedding_created_at"] = datetime.utcnow()
            bundle["embedding_text"] = texts[i]
        
        logger.info(f"Successfully embedded {len(bundles)} linguistic bundles")
        return bundles
        
    except Exception as e:
        logger.error(f"Error embedding batch: {e}")
        return []


async def save_bundles_to_mongodb(db, bundles: List[Dict[str, Any]]):
    """Save linguistic bundles with embeddings to MongoDB."""
    if not bundles:
        return
    
    collection = db.pattern_embeddings
    
    # Prepare bulk operations
    operations = []
    for bundle in bundles:
        # Create unique identifier
        bundle_id = f"{bundle['creator_name']}_linguistic_{bundle['bundle_name']}"
        
        # Clean up the bundle data for storage
        bundle_record = dict(bundle)  # Copy all bundle data
        bundle_record.update({
            "pattern_id": bundle_id,
            "pattern_type": "linguistic_bundle",
            "analysis_type": "linguistic_analysis",
            "embedding": bundle["embedding"],
            "embedding_model": bundle["embedding_model"],
            "embedding_created_at": bundle["embedding_created_at"],
            "embedding_text": bundle["embedding_text"]
        })
        
        operations.append(
            UpdateOne(
                {"pattern_id": bundle_id},
                {"$set": bundle_record},
                upsert=True
            )
        )
    
    # Execute bulk write
    if operations:
        result = await collection.bulk_write(operations)
        logger.info(f"Saved {result.modified_count} modified, {result.upserted_count} new linguistic bundles")


async def main():
    """Main function to process all linguistic bundles."""
    # Connect to MongoDB
    client = AsyncMongoClient(MONGO_URI)
    
    # Extract database name from URI or use default
    uri_db_part = MONGO_URI.split("/")[-1].split("?")[0]
    db_name = uri_db_part or "linkedinsight"
    db = client[db_name]
    
    try:
        logger.info("🎨 Starting Linguistic Bundle Embedding")
        logger.info("=" * 60)
        
        # Load all linguistic bundles
        all_bundles = load_all_linguistic_bundles()
        
        if not all_bundles:
            logger.error("No linguistic bundles found!")
            return
        
        logger.info(f"Processing {len(all_bundles)} linguistic bundles")
        
        # Group bundles by creator for summary
        creator_counts = {}
        for bundle in all_bundles:
            creator = bundle.get('creator_name', 'unknown')
            creator_counts[creator] = creator_counts.get(creator, 0) + 1
        
        logger.info("Bundles per creator:")
        for creator, count in sorted(creator_counts.items()):
            logger.info(f"  {creator}: {count} bundles")
        
        # Process bundles in batches for embedding
        total_embedded = 0
        for i in range(0, len(all_bundles), BATCH_SIZE):
            batch = all_bundles[i:i + BATCH_SIZE]
            batch_num = i // BATCH_SIZE + 1
            total_batches = (len(all_bundles) + BATCH_SIZE - 1) // BATCH_SIZE
            
            logger.info(f"Processing batch {batch_num} of {total_batches} ({len(batch)} bundles)")
            
            # Embed the batch
            embedded_batch = await embed_bundles_batch(batch)
            
            if embedded_batch:
                # Save to MongoDB
                await save_bundles_to_mongodb(db, embedded_batch)
                total_embedded += len(embedded_batch)
            
            # Rate limiting
            if i + BATCH_SIZE < len(all_bundles):
                logger.info(f"Waiting {RATE_LIMIT_DELAY} seconds for rate limit...")
                await asyncio.sleep(RATE_LIMIT_DELAY)
        
        # Print summary
        logger.info("\n=== Linguistic Bundle Embedding Summary ===")
        logger.info(f"Total bundles found: {len(all_bundles)}")
        logger.info(f"Total bundles embedded: {total_embedded}")
        logger.info(f"Total creators: {len(creator_counts)}")
        
        logger.info("\nFinal embedding counts per creator:")
        for creator, count in sorted(creator_counts.items()):
            logger.info(f"  {creator}: {count} linguistic bundles")
        
        logger.info("\n✅ Linguistic bundle embedding completed!")
        
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())