#!/usr/bin/env python3
"""
<PERSON><PERSON> embed all body patterns from LLM-friendly analyses in MongoDB.
Uses AI-assisted extraction to handle the complex markdown format.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import voyageai
from pymongo import AsyncMongoClient
from pymongo import UpdateOne
import logging
import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
import anthropic
from src.utils.json_utils import extract_json_from_text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB connection
MONGO_URI = os.getenv("MONGODB_URI")
if not MONGO_URI:
    raise ValueError("MONGODB_URI environment variable not set")

# Voyage AI client
VOYAGE_AI_API_KEY = os.getenv("VOYAGE_AI_API_KEY")
if not VOYAGE_AI_API_KEY:
    raise ValueError("VOYAGE_AI_API_KEY environment variable not set")

voyage_client = voyageai.Client(api_key=VOYAGE_AI_API_KEY)

# Anthropic client for AI-assisted extraction
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
if not ANTHROPIC_API_KEY:
    raise ValueError("ANTHROPIC_API_KEY environment variable not set")

anthropic_client = anthropic.Client(api_key=ANTHROPIC_API_KEY)

# Rate limiting
BATCH_SIZE = 10
RATE_LIMIT_DELAY = 20  # seconds between batches (3 RPM limit)

EXTRACTION_PROMPT = """You are analyzing a body structure analysis document. Your task is to extract ALL distinct patterns, frameworks, and structures mentioned in the document.

For each pattern, extract:
1. pattern_name: The name/title of the pattern
2. description: Brief description of the pattern  
3. when_to_use: When this pattern is most effective
4. target_length: Character count range (e.g., "1500-2000")
5. theme_compatibility: Which themes it works well with (as a string or list)
6. implementation_steps: Key steps to implement the pattern (as an array)

Return the patterns as a JSON array inside a markdown code block. Include ALL patterns mentioned, including both structural patterns and frameworks/models.

Example format:
```json
[
  {
    "pattern_name": "...",
    "description": "...",
    "when_to_use": "...",
    "target_length": "...",
    "theme_compatibility": "...",
    "implementation_steps": [...]
  }
]
```"""


async def extract_body_patterns_with_ai(body_text: str, creator_name: str) -> List[Dict[str, Any]]:
    """Extract body patterns from markdown text using AI assistance."""
    try:
        response = anthropic_client.messages.create(
            model="claude-sonnet-4-20250514",
            max_tokens=4000,
            temperature=0,
            messages=[
                {
                    "role": "user",
                    "content": f"{EXTRACTION_PROMPT}\n\nDocument to analyze:\n\n{body_text}"
                }
            ]
        )
        
        # Parse the response using our json_utils
        patterns_data = extract_json_from_text(response.content[0].text)
        
        if not isinstance(patterns_data, list):
            logger.error(f"Expected list of patterns for {creator_name}, got {type(patterns_data)}")
            return []
        
        logger.info(f"Extracted {len(patterns_data)} body patterns from {creator_name}")
        return patterns_data
        
    except Exception as e:
        logger.error(f"Error extracting patterns for {creator_name}: {e}")
        return []


async def get_all_body_analyses(db) -> List[Dict[str, Any]]:
    """Fetch all body LLM-friendly analyses from MongoDB."""
    analyses = []
    
    # Query for body LLM-friendly analyses
    cursor = db.LLM_friendly_analyses.find({
        "analysis_type": "body"
    })
    
    async for doc in cursor:
        analyses.append(doc)
    
    logger.info(f"Found {len(analyses)} body LLM-friendly analyses")
    return analyses


async def extract_patterns_from_analysis(analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract patterns from a single body analysis using AI."""
    patterns = []
    creator_name = analysis.get("creator_name", "unknown")
    
    try:
        # Get the body text from llm_analysis_data
        body_text = analysis.get("llm_analysis_data", "")
        
        if not body_text:
            logger.warning(f"No body text found for {creator_name}")
            return patterns
        
        # Use AI to extract patterns
        extracted_patterns = await extract_body_patterns_with_ai(body_text, creator_name)
        
        # Add metadata to each pattern
        for pattern in extracted_patterns:
            pattern["creator_name"] = creator_name
            pattern["pattern_type"] = "body"
            pattern["source_analysis_id"] = str(analysis["_id"])
            patterns.append(pattern)
        
        return patterns
        
    except Exception as e:
        logger.error(f"Error extracting patterns from {creator_name}: {e}")
        return patterns


async def embed_patterns_batch(patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Embed a batch of patterns using Voyage AI."""
    if not patterns:
        return []
    
    # Prepare texts for embedding
    texts = []
    for pattern in patterns:
        # Create comprehensive text for embedding
        text_parts = [
            f"Pattern: {pattern.get('pattern_name', '')}",
            f"Description: {pattern.get('description', '')}",
            f"When to use: {pattern.get('when_to_use', '')}",
            f"Target length: {pattern.get('target_length', '')}",
            f"Theme compatibility: {pattern.get('theme_compatibility', '')}",
            f"Implementation: {' '.join(pattern.get('implementation_steps', []))}"
        ]
        text = " | ".join(text_parts)
        texts.append(text)
    
    try:
        # Get embeddings from Voyage AI
        result = voyage_client.embed(
            texts,
            model="voyage-3.5",
            input_type="document"
        )
        
        # Add embeddings to patterns
        for i, pattern in enumerate(patterns):
            pattern["embedding"] = result.embeddings[i]
            pattern["embedding_model"] = "voyage-3.5"
            pattern["embedding_created_at"] = datetime.utcnow()
        
        logger.info(f"Successfully embedded {len(patterns)} patterns")
        return patterns
        
    except Exception as e:
        logger.error(f"Error embedding batch: {e}")
        return []


async def save_patterns_to_mongodb(db, patterns: List[Dict[str, Any]]):
    """Save patterns with embeddings to MongoDB."""
    if not patterns:
        return
    
    collection = db.pattern_embeddings
    
    # Prepare bulk operations
    operations = []
    for pattern in patterns:
        # Create unique identifier
        pattern_id = f"{pattern['creator_name']}_{pattern['pattern_type']}_{pattern['pattern_name']}"
        
        operations.append(
            UpdateOne(
                {"pattern_id": pattern_id},
                {"$set": pattern},
                upsert=True
            )
        )
    
    # Execute bulk write
    if operations:
        result = await collection.bulk_write(operations)
        logger.info(f"Saved {result.modified_count} modified, {result.upserted_count} new patterns")


async def main():
    """Main function to process all body analyses."""
    # Connect to MongoDB
    client = AsyncMongoClient(MONGO_URI)
    
    # Extract database name from URI or use default
    uri_db_part = MONGO_URI.split("/")[-1].split("?")[0]
    db_name = uri_db_part or "linkedinsight"
    db = client[db_name]
    
    try:
        # Get all body analyses
        analyses = await get_all_body_analyses(db)
        
        all_patterns = []
        total_extracted = 0
        
        # Extract patterns from each analysis
        for analysis in analyses:
            patterns = await extract_patterns_from_analysis(analysis)
            all_patterns.extend(patterns)
            total_extracted += len(patterns)
            logger.info(f"Total patterns extracted so far: {total_extracted}")
        
        logger.info(f"\nExtracted {len(all_patterns)} total body patterns")
        
        # Process patterns in batches for embedding
        for i in range(0, len(all_patterns), BATCH_SIZE):
            batch = all_patterns[i:i + BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1} of {(len(all_patterns) + BATCH_SIZE - 1)//BATCH_SIZE}")
            
            # Embed the batch
            embedded_batch = await embed_patterns_batch(batch)
            
            # Save to MongoDB
            await save_patterns_to_mongodb(db, embedded_batch)
            
            # Rate limiting
            if i + BATCH_SIZE < len(all_patterns):
                logger.info(f"Waiting {RATE_LIMIT_DELAY} seconds for rate limit...")
                await asyncio.sleep(RATE_LIMIT_DELAY)
        
        # Print summary
        logger.info("\n=== Body Pattern Embedding Summary ===")
        logger.info(f"Total analyses processed: {len(analyses)}")
        logger.info(f"Total patterns extracted: {len(all_patterns)}")
        
        # Count patterns by creator
        creator_counts = {}
        for pattern in all_patterns:
            creator = pattern.get('creator_name', 'unknown')
            creator_counts[creator] = creator_counts.get(creator, 0) + 1
        
        logger.info("\nPatterns per creator:")
        for creator, count in sorted(creator_counts.items()):
            logger.info(f"  {creator}: {count} patterns")
        
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())