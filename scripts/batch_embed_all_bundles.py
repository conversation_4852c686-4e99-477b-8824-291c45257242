#!/usr/bin/env python3
"""
Batch create embeddings for all linguistic bundles in MongoDB.
"""

import os
import sys
import voyageai
from datetime import datetime, timezone

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.mongo_utils import get_mongo_client

def create_embedding(text, model="voyage-3.5"):
    """Create embedding using Voyage AI."""
    
    # Initialize Voyage client with API key from environment
    api_key = os.getenv('VOYAGE_AI_API_KEY')
    if not api_key:
        raise ValueError("VOYAGE_AI_API_KEY environment variable not set")
    
    voyage_client = voyageai.Client(api_key=api_key)
    
    try:
        result = voyage_client.embed(
            texts=[text],
            model=model,
            input_type="document"
        )
        return result.embeddings[0]
    except Exception as e:
        print(f"❌ Embedding failed: {e}")
        return None

def batch_embed_bundles():
    """Find all linguistic bundles without embeddings and create them."""
    
    print("🚀 Starting batch embedding of linguistic bundles...")
    
    try:
        client = get_mongo_client()
        db = client.linkedinsight
        collection = db.pattern_embeddings
        
        # Find all linguistic bundles without embeddings
        query = {
            'pattern_type': 'linguistic_bundle',
            'embedding': {'$exists': False}
        }
        
        bundles = list(collection.find(query))
        
        if not bundles:
            print("❌ No bundles found without embeddings")
            return
        
        print(f"📦 Found {len(bundles)} bundles needing embeddings")
        
        # Group by creator for progress tracking
        creators = {}
        for bundle in bundles:
            creator = bundle['creator_name']
            if creator not in creators:
                creators[creator] = []
            creators[creator].append(bundle)
        
        print(f"👥 Creators to process: {list(creators.keys())}")
        
        embedded_count = 0
        failed_count = 0
        
        for creator_name, creator_bundles in creators.items():
            print(f"\n🔍 Processing {creator_name} ({len(creator_bundles)} bundles)...")
            
            for i, bundle in enumerate(creator_bundles):
                print(f"   {i+1}/{len(creator_bundles)}: {bundle['bundle_name']}")
                
                # Create embedding text
                embedding_text = f"Bundle: {bundle['bundle_name']}\n"
                embedding_text += f"Description: {bundle['bundle_description']}\n"
                embedding_text += f"Usage: {bundle['usage_context']}\n\n"
                embedding_text += bundle['bundle_text']
                
                print(f"      Text length: {len(embedding_text)} chars")
                
                # Create embedding
                embedding = create_embedding(embedding_text)
                if embedding:
                    # Update document with embedding
                    update_result = collection.update_one(
                        {'_id': bundle['_id']},
                        {
                            '$set': {
                                'embedding': embedding,
                                'embedding_text': embedding_text,
                                'embedding_model': 'voyage-3.5',
                                'embedding_dimension': len(embedding),
                                'embedded_at': datetime.now(timezone.utc)
                            }
                        }
                    )
                    
                    if update_result.modified_count > 0:
                        print(f"      ✅ Embedded (dim: {len(embedding)})")
                        embedded_count += 1
                    else:
                        print(f"      ❌ Update failed")
                        failed_count += 1
                else:
                    print(f"      ❌ Embedding creation failed")
                    failed_count += 1
        
        print(f"\n📊 Embedding complete:")
        print(f"   ✅ Successfully embedded: {embedded_count}")
        print(f"   ❌ Failed: {failed_count}")
        print(f"   📈 Success rate: {embedded_count/(embedded_count+failed_count)*100:.1f}%")
        
        return embedded_count > 0
        
    except Exception as e:
        print(f"❌ Batch embedding failed: {e}")
        return False

def check_embedding_status():
    """Check current embedding status."""
    
    print("📊 Checking embedding status...")
    
    try:
        client = get_mongo_client()
        db = client.linkedinsight
        collection = db.pattern_embeddings
        
        # Count bundles with and without embeddings
        total_bundles = collection.count_documents({'pattern_type': 'linguistic_bundle'})
        embedded_bundles = collection.count_documents({
            'pattern_type': 'linguistic_bundle',
            'embedding': {'$exists': True}
        })
        unembedded_bundles = total_bundles - embedded_bundles
        
        print(f"   Total bundles: {total_bundles}")
        print(f"   With embeddings: {embedded_bundles}")
        print(f"   Without embeddings: {unembedded_bundles}")
        
        if unembedded_bundles > 0:
            print(f"   📋 Ready to embed {unembedded_bundles} bundles")
        else:
            print(f"   ✅ All bundles are embedded!")
        
        return unembedded_bundles
        
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        return -1

if __name__ == "__main__":
    # Check status first
    unembedded = check_embedding_status()
    
    if unembedded > 0:
        print(f"\n🚀 Starting embedding process...")
        success = batch_embed_bundles()
        if success:
            print(f"\n🎉 Batch embedding completed!")
            print(f"Ready for vector search testing.")
        else:
            print(f"\n❌ Batch embedding failed")
    elif unembedded == 0:
        print(f"\n✅ All bundles already embedded!")
    else:
        print(f"\n❌ Could not check status")