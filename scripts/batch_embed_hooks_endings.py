#!/usr/bin/env python3
"""
Batch process hook and ending analyses and generate embeddings for pattern matching.

This script focuses on hooks and endings first since they have consistent key structures.
"""

import os
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from pathlib import Path
import voyageai
from pymongo import MongoClient
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


class HookEndingPatternExtractor:
    """Extract hook and ending patterns from creator analyses."""
    
    def __init__(self):
        self.client = MongoClient(os.getenv("MONGODB_URI"))
        self.db = self.client["linkedinsight"]
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        
    def extract_all_patterns(self) -> List[Dict[str, Any]]:
        """Extract hook and ending patterns from all creators."""
        all_patterns = []
        
        # Process hook analyses
        hook_analyses = list(self.db.LLM_friendly_analyses.find({"analysis_type": "hook"}))
        logger.info(f"Found {len(hook_analyses)} hook analyses to process")
        
        for analysis in hook_analyses:
            creator_name = analysis.get("creator_name")
            if not creator_name:
                continue
                
            patterns = self.extract_hook_patterns(analysis)
            all_patterns.extend(patterns)
            logger.info(f"Extracted {len(patterns)} hook patterns from {creator_name}")
        
        # Process ending analyses
        ending_analyses = list(self.db.LLM_friendly_analyses.find({"analysis_type": "ending"}))
        logger.info(f"Found {len(ending_analyses)} ending analyses to process")
        
        for analysis in ending_analyses:
            creator_name = analysis.get("creator_name")
            if not creator_name:
                continue
                
            patterns = self.extract_ending_patterns(analysis)
            all_patterns.extend(patterns)
            logger.info(f"Extracted {len(patterns)} ending patterns from {creator_name}")
        
        logger.info(f"\nTotal patterns extracted: {len(all_patterns)}")
        return all_patterns
    
    def extract_hook_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract hook patterns using the correct key names."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        creator_name = analysis.get("creator_name")
        
        # Get hook archetypes with correct key name
        archetypes = analysis_data.get("2_dominant_hook_archetypes", [])
        
        for idx, archetype in enumerate(archetypes):
            if not isinstance(archetype, dict):
                continue
                
            # Build searchable text from all available content
            searchable_parts = []
            
            # Add archetype name
            archetype_name = archetype.get("archetype_name", f"Hook Pattern {idx}")
            searchable_parts.append(archetype_name)
            
            # Add description (using correct key)
            description = archetype.get("description_and_key_elements", "")
            if description:
                searchable_parts.append(description)
            
            # Add example (using correct key)
            example = archetype.get("creator_example_best", "")
            if example:
                searchable_parts.append(example)
            
            # Add psychological impact
            impact = archetype.get("typical_psychological_impact", "")
            if impact:
                searchable_parts.append(impact)
            
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "hook",
                "pattern_index": idx,
                "pattern_name": archetype_name,
                "content": {
                    "description": description,
                    "example": example,
                    "psychological_impact": impact,
                    "frequency": archetype.get("estimated_frequency_category", "Unknown")
                },
                "metadata": {
                    "extracted_from": "hook_analysis",
                    "extracted_at": datetime.now(timezone.utc),
                    "source_analysis_id": str(analysis.get("_id", ""))
                },
                "searchable_text": " ".join(searchable_parts)
            }
            patterns.append(pattern)
            
        return patterns
    
    def extract_ending_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract ending patterns using the correct key names."""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        creator_name = analysis.get("creator_name")
        
        # Get ending types with correct key name
        ending_types = analysis_data.get("2_dominant_ending_types", [])
        
        for idx, ending in enumerate(ending_types):
            if not isinstance(ending, dict):
                continue
                
            # Build searchable text from all available content
            searchable_parts = []
            
            # Add ending type name
            ending_name = ending.get("ending_type_name", f"Ending Pattern {idx}")
            searchable_parts.append(ending_name)
            
            # Add description (using correct key)
            description = ending.get("description_and_key_elements", "")
            if description:
                searchable_parts.append(description)
            
            # Add example (using correct key)
            example = ending.get("creator_example_best", "")
            if example:
                searchable_parts.append(example)
            
            # Add engagement impact (different from hook's psychological impact)
            impact = ending.get("typical_engagement_impact", "")
            if impact:
                searchable_parts.append(impact)
            
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "ending",
                "pattern_index": idx,
                "pattern_name": ending_name,
                "content": {
                    "description": description,
                    "example": example,
                    "engagement_impact": impact,
                    "frequency": ending.get("estimated_frequency_category", "Unknown")
                },
                "metadata": {
                    "extracted_from": "ending_analysis",
                    "extracted_at": datetime.now(timezone.utc),
                    "source_analysis_id": str(analysis.get("_id", ""))
                },
                "searchable_text": " ".join(searchable_parts)
            }
            patterns.append(pattern)
            
        return patterns


class BatchEmbeddingProcessor:
    """Process patterns in batches and generate embeddings."""
    
    def __init__(self, rate_limit_delay: int = 20):
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        self.rate_limit_delay = rate_limit_delay
        
    def generate_embeddings_batch(
        self, 
        patterns: List[Dict[str, Any]], 
        batch_size: int = 5
    ) -> List[Dict[str, Any]]:
        """Generate embeddings for patterns in batches."""
        patterns_with_embeddings = []
        
        total_batches = (len(patterns) + batch_size - 1) // batch_size
        logger.info(f"Processing {len(patterns)} patterns in {total_batches} batches...")
        
        for i in range(0, len(patterns), batch_size):
            batch = patterns[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            logger.info(f"\nBatch {batch_num}/{total_batches}")
            
            # Extract texts for embedding
            texts = [p.get("searchable_text", "") for p in batch]
            
            # Log what we're embedding
            for j, (pattern, text) in enumerate(zip(batch, texts)):
                logger.info(f"  Pattern {j+1}: {pattern['creator_id']} - {pattern['pattern_name']} ({len(text)} chars)")
            
            try:
                # Generate embeddings
                result = self.voyage_client.embed(texts, model="voyage-3.5")
                embeddings = result.embeddings
                
                # Add embeddings to patterns
                for pattern, embedding in zip(batch, embeddings):
                    pattern["embedding"] = embedding
                    pattern["embedding_model"] = "voyage-3.5"
                    pattern["embedding_dimensions"] = len(embedding)
                    pattern["indexed_at"] = datetime.now(timezone.utc)
                    patterns_with_embeddings.append(pattern)
                
                logger.info(f"  ✓ Generated {len(embeddings)} embeddings")
                
                # Rate limiting
                if i + batch_size < len(patterns):
                    logger.info(f"  Waiting {self.rate_limit_delay}s for rate limit...")
                    time.sleep(self.rate_limit_delay)
                    
            except Exception as e:
                logger.error(f"  ✗ Error in batch {batch_num}: {e}")
                # Continue with next batch
                
        return patterns_with_embeddings


def save_debug_output(patterns: List[Dict[str, Any]], output_dir: str = "outputs/hook_ending_embeddings"):
    """Save patterns to JSON for debugging."""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save full patterns
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_file = output_path / f"patterns_with_embeddings_{timestamp}.json"
    
    # Convert datetime objects to strings
    for pattern in patterns:
        if "indexed_at" in pattern and hasattr(pattern["indexed_at"], "isoformat"):
            pattern["indexed_at"] = pattern["indexed_at"].isoformat()
        if "metadata" in pattern and "extracted_at" in pattern["metadata"]:
            if hasattr(pattern["metadata"]["extracted_at"], "isoformat"):
                pattern["metadata"]["extracted_at"] = pattern["metadata"]["extracted_at"].isoformat()
    
    with open(full_file, 'w') as f:
        json.dump(patterns, f, indent=2)
    
    logger.info(f"Saved {len(patterns)} patterns to {full_file}")
    
    # Save summary without embeddings
    summary_file = output_path / f"patterns_summary_{timestamp}.json"
    summary_patterns = []
    
    for p in patterns:
        summary = {
            "creator_id": p.get("creator_id"),
            "analysis_type": p.get("analysis_type"),
            "pattern_name": p.get("pattern_name"),
            "searchable_text_length": len(p.get("searchable_text", "")),
            "embedding_dimensions": p.get("embedding_dimensions")
        }
        summary_patterns.append(summary)
    
    with open(summary_file, 'w') as f:
        json.dump(summary_patterns, f, indent=2)
    
    logger.info(f"Saved summary to {summary_file}")


def main():
    """Run the hook and ending embedding pipeline."""
    logger.info("Starting hook and ending embedding pipeline")
    logger.info("=" * 60)
    
    # 1. Extract patterns
    extractor = HookEndingPatternExtractor()
    patterns = extractor.extract_all_patterns()
    
    if not patterns:
        logger.error("No patterns extracted!")
        return
    
    # Show statistics
    hook_count = sum(1 for p in patterns if p["analysis_type"] == "hook")
    ending_count = sum(1 for p in patterns if p["analysis_type"] == "ending")
    creators = set(p["creator_id"] for p in patterns)
    
    logger.info(f"\nExtraction complete:")
    logger.info(f"  - Hook patterns: {hook_count}")
    logger.info(f"  - Ending patterns: {ending_count}")
    logger.info(f"  - Unique creators: {len(creators)}")
    
    # 2. Generate embeddings
    processor = BatchEmbeddingProcessor(rate_limit_delay=20)
    patterns_with_embeddings = processor.generate_embeddings_batch(patterns, batch_size=5)
    
    logger.info(f"\nSuccessfully generated embeddings for {len(patterns_with_embeddings)} patterns")
    
    # 3. Save debug output
    save_debug_output(patterns_with_embeddings)
    
    # 4. Save to MongoDB
    logger.info("\nSaving to MongoDB...")
    
    collection = extractor.db["pattern_embeddings"]  # Production collection
    
    # Optional: Clear existing hook/ending data
    # result = collection.delete_many({"analysis_type": {"$in": ["hook", "ending"]}})
    # logger.info(f"Cleared {result.deleted_count} existing hook/ending patterns")
    
    # Insert new patterns
    if patterns_with_embeddings:
        result = collection.insert_many(patterns_with_embeddings)
        logger.info(f"✓ Inserted {len(result.inserted_ids)} patterns into pattern_embeddings")
    
    # 5. Summary statistics
    logger.info("\n" + "=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    
    # Count by type
    type_counts = {}
    creator_counts = {}
    
    for p in patterns_with_embeddings:
        # Count by type
        p_type = p.get("analysis_type", "unknown")
        type_counts[p_type] = type_counts.get(p_type, 0) + 1
        
        # Count by creator
        creator = p.get("creator_id", "unknown")
        creator_counts[creator] = creator_counts.get(creator, 0) + 1
    
    logger.info("\nPatterns by type:")
    for p_type, count in sorted(type_counts.items()):
        logger.info(f"  {p_type}: {count}")
    
    logger.info(f"\nTotal creators processed: {len(creator_counts)}")
    logger.info("\nTop creators by pattern count:")
    for creator, count in sorted(creator_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        logger.info(f"  {creator}: {count} patterns")
    
    logger.info("\n✅ Hook and ending embedding pipeline complete!")
    logger.info("\nNext steps:")
    logger.info("1. Verify patterns in outputs/hook_ending_embeddings/")
    logger.info("2. Create/update vector search index on 'pattern_embeddings' collection")
    logger.info("3. Test vector search with sample queries")
    logger.info("4. Process themes and body patterns next")


if __name__ == "__main__":
    main()