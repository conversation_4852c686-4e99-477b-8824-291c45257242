#!/usr/bin/env python3
"""
Extract individual bundles from bundle discovery files and store in MongoDB.
Bypasses JSON parsing issues by extracting bundles directly.
"""

import json
import ast
import re
import os
from pathlib import Path
from datetime import datetime, timezone
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.mongo_utils import get_mongo_client

def extract_bundles_from_text(text):
    """Extract bundle objects from text using regex patterns."""
    
    bundles = []
    
    # Look for bundle objects that start with "bundle_name"
    # This pattern finds complete bundle objects
    bundle_pattern = r'"bundle_name":\s*"([^"]+)"[^}]*?(?="bundle_name"|$)'
    
    # More robust: find objects that contain bundle_name
    # Split on bundle_name and reconstruct objects
    parts = text.split('"bundle_name":')
    
    for i, part in enumerate(parts[1:], 1):  # Skip first part before any bundle
        try:
            # Try to find the complete bundle object
            # Look for the opening brace before bundle_name
            preceding_text = parts[i-1] if i > 0 else ""
            
            # Find last opening brace in preceding text
            last_brace = preceding_text.rfind('{')
            if last_brace == -1:
                continue
            
            # Reconstruct potential bundle object
            potential_bundle = preceding_text[last_brace:] + '"bundle_name":' + part
            
            # Try to find the end of this bundle object
            # Simple heuristic: find the next "bundle_name" or end of bundles array
            end_pos = potential_bundle.find('"bundle_name":', 20)  # Skip our own bundle_name
            if end_pos != -1:
                potential_bundle = potential_bundle[:end_pos]
            
            # Try to balance braces to get complete object
            brace_count = 0
            end_idx = 0
            for j, char in enumerate(potential_bundle):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = j + 1
                        break
            
            if end_idx > 0:
                bundle_text = potential_bundle[:end_idx]
                
                # Extract bundle name from the text
                name_match = re.search(r'"bundle_name":\s*"([^"]+)"', bundle_text)
                if name_match:
                    bundle_name = name_match.group(1)
                    bundles.append({
                        'bundle_name': bundle_name,
                        'bundle_text': bundle_text,
                        'extraction_method': 'regex_pattern'
                    })
                    
        except Exception as e:
            continue
    
    return bundles

def extract_bundles_from_file(file_path):
    """Extract bundles from a bundle discovery file."""
    
    print(f"🔍 Extracting bundles from {os.path.basename(file_path)}...")
    
    # Try different extraction methods
    extraction_methods = [
        ("json_nested", extract_from_json_nested),
        ("raw_response", extract_from_raw_response),
        ("text_pattern", extract_bundles_from_text)
    ]
    
    for method_name, method_func in extraction_methods:
        try:
            print(f"   Trying {method_name}...")
            
            if method_name == "text_pattern":
                # For text pattern, read the file directly
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                bundles = method_func(content)
            else:
                bundles = method_func(file_path)
            
            if bundles:
                print(f"✅ Extracted {len(bundles)} bundles using {method_name}")
                return bundles, method_name
                
        except Exception as e:
            print(f"   {method_name} failed: {e}")
    
    print(f"❌ Could not extract bundles from {os.path.basename(file_path)}")
    return [], None

def extract_from_json_nested(file_path):
    """Extract from nested JSON structure."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if 'bundles' in data and isinstance(data['bundles'], str):
        bundles_text = data['bundles']
        # Try to parse as JSON
        try:
            bundle_data = json.loads(bundles_text)
            if 'bundles' in bundle_data and isinstance(bundle_data['bundles'], list):
                return [{'bundle_name': b.get('bundle_name', f'Bundle {i+1}'), 
                        'bundle_text': json.dumps(b, indent=2), 
                        'extraction_method': 'json_nested'} 
                       for i, b in enumerate(bundle_data['bundles'])]
        except:
            pass
    
    # If it's already properly formatted
    if 'bundles' in data and isinstance(data['bundles'], list):
        return [{'bundle_name': b.get('bundle_name', f'Bundle {i+1}'), 
                'bundle_text': json.dumps(b, indent=2), 
                'extraction_method': 'json_nested'} 
               for i, b in enumerate(data['bundles'])]
    
    return []

def extract_from_raw_response(file_path):
    """Extract from raw response file."""
    raw_file = file_path.replace('.json', '_raw_response.txt')
    if not os.path.exists(raw_file):
        return []
    
    with open(raw_file, 'r', encoding='utf-8') as f:
        raw_content = f.read()
    
    # Try to parse as Python dict
    try:
        python_dict = ast.literal_eval(raw_content)
        if 'bundles' in python_dict and isinstance(python_dict['bundles'], str):
            return extract_bundles_from_text(python_dict['bundles'])
    except:
        pass
    
    return []

def store_bundles_in_mongo(creator_name, bundles, extraction_method, source_file):
    """Store extracted bundles in MongoDB."""
    
    if not bundles:
        print(f"❌ No bundles to store for {creator_name}")
        return False
    
    print(f"💾 Storing {len(bundles)} bundles for {creator_name} in MongoDB...")
    
    try:
        client = get_mongo_client()
        db = client.linkedinsight
        collection = db.bundle_patterns
        
        documents = []
        for i, bundle in enumerate(bundles):
            doc = {
                'creator_name': creator_name,
                'bundle_name': bundle['bundle_name'],
                'bundle_text': bundle['bundle_text'],
                'bundle_index': i,
                'extraction_method': extraction_method,
                'source_file': source_file,
                'analysis_type': 'bundle_pattern',
                'created_at': datetime.now(timezone.utc),
                'version': 1
            }
            documents.append(doc)
        
        # Insert documents
        result = collection.insert_many(documents)
        print(f"✅ Stored {len(result.inserted_ids)} bundle documents")
        
        # Show what we stored
        for doc in documents:
            print(f"   - {doc['bundle_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ MongoDB storage failed: {e}")
        return False

def process_all_bundle_discovery_files():
    """Process all bundle discovery files."""
    
    print("🚀 Starting bundle extraction and MongoDB storage...")
    
    base_dir = Path("/Users/<USER>/LinkedInsight/LLM_friendly_Analyses")
    
    # Find all bundle discovery files
    bundle_files = list(base_dir.glob("*/*bundle_discovery*.json"))
    
    if not bundle_files:
        print("❌ No bundle discovery files found")
        return
    
    print(f"📁 Found {len(bundle_files)} bundle discovery files")
    
    processed = 0
    failed = 0
    total_bundles = 0
    
    for bundle_file in bundle_files:
        creator_name = bundle_file.parent.name
        
        bundles, method = extract_bundles_from_file(bundle_file)
        
        if bundles:
            if store_bundles_in_mongo(creator_name, bundles, method, str(bundle_file)):
                processed += 1
                total_bundles += len(bundles)
            else:
                failed += 1
        else:
            failed += 1
    
    print(f"\n🎉 Processing complete!")
    print(f"   ✅ Processed: {processed} creators")
    print(f"   ❌ Failed: {failed} creators")
    print(f"   📦 Total bundles extracted: {total_bundles}")
    print(f"   📊 Average bundles per creator: {total_bundles/processed if processed > 0 else 0:.1f}")

if __name__ == "__main__":
    # Test with retentionadam first
    print("🧪 Testing with retentionadam...")
    test_file = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/retentionadam/retentionadam_bundle_discovery_analysis_20250601144400.json"
    
    bundles, method = extract_bundles_from_file(test_file)
    if bundles:
        print(f"✅ Test successful! Found {len(bundles)} bundles using {method}")
        if store_bundles_in_mongo("retentionadam", bundles, method, test_file):
            print("🚀 Running full processing...")
            process_all_bundle_discovery_files()
    else:
        print("❌ Test failed, not running full processing")