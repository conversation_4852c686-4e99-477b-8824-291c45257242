#!/usr/bin/env python3
"""
Utility script to fetch analysis content from MongoDB and save it to a file.
"""

import os
import sys
import logging
import argparse
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import MongoDB retrieval functions
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.utils.mongo_retrieval import fetch_latest_markdown_content

async def main_async():
    """Async main entry point."""
    parser = argparse.ArgumentParser(
        description="Fetch analysis content from MongoDB and save it to a file",
        epilog="Example: python fetch_analysis.py --creator retentionadam --type body --output-file temp_body_analysis.txt"
    )
    parser.add_argument("--creator", required=True,
                      help="Name of the creator")
    parser.add_argument("--type", required=True,
                      help="Type of analysis (overview, themes, hooks, body, endings, linguistic)")
    parser.add_argument("--output-file", required=True,
                      help="Path to the output file")
    
    args = parser.parse_args()
    
    # Fetch the content
    logger.info(f"Fetching {args.type} analysis for {args.creator}...")
    content = await fetch_latest_markdown_content(args.creator, args.type)
    
    if not content:
        logger.error(f"No {args.type} analysis found for {args.creator}")
        sys.exit(1)
    
    # Save to file
    with open(args.output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"Analysis saved to {args.output_file}")

def main():
    """Main entry point."""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
