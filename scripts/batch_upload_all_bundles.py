#!/usr/bin/env python3
"""
Batch upload all linguistic bundles from bundle discovery files to MongoDB.
"""

import json
import ast
import re
import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.mongo_utils import get_mongo_client

def extract_bundles_simple(text):
    """Extract bundle objects from text using simple pattern matching."""
    
    bundles = []
    
    # Find all bundle_name occurrences
    bundle_starts = []
    for match in re.finditer(r'"bundle_name":\s*"([^"]+)"', text):
        bundle_starts.append({
            'name': match.group(1),
            'start_pos': match.start(),
        })
    
    # For each bundle, extract the surrounding object
    for i, bundle_info in enumerate(bundle_starts):
        try:
            # Find the opening brace before this bundle_name
            search_start = max(0, bundle_info['start_pos'] - 200)
            preceding_text = text[search_start:bundle_info['start_pos']]
            
            last_brace = preceding_text.rfind('{')
            if last_brace == -1:
                continue
            
            object_start = search_start + last_brace
            
            # Find the end of this object
            if i + 1 < len(bundle_starts):
                next_bundle_pos = bundle_starts[i + 1]['start_pos']
                between_text = text[bundle_info['start_pos']:next_bundle_pos]
                last_close_brace = between_text.rfind('}')
                if last_close_brace != -1:
                    object_end = bundle_info['start_pos'] + last_close_brace + 1
                else:
                    object_end = next_bundle_pos
            else:
                # Last bundle
                remaining_text = text[bundle_info['start_pos']:]
                brace_count = 0
                object_end = len(text)
                for j, char in enumerate(remaining_text):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count <= -1:
                            object_end = bundle_info['start_pos'] + j + 1
                            break
            
            bundle_text = text[object_start:object_end]
            
            bundles.append({
                'name': bundle_info['name'],
                'text': bundle_text,
                'length': len(bundle_text)
            })
            
        except Exception as e:
            print(f"   Error extracting bundle {bundle_info['name']}: {e}")
    
    return bundles

def get_bundle_text(file_path):
    """Get bundle text from file - try nested JSON first, then raw response."""
    
    # Try nested JSON first (if file exists)
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if 'bundles' in data and isinstance(data['bundles'], str):
                return data['bundles']
        except:
            pass
    
    # Try raw response (this should work for most cases)
    try:
        raw_file = str(file_path).replace('.json', '_raw_response.txt')
        if os.path.exists(raw_file):
            with open(raw_file, 'r', encoding='utf-8') as f:
                raw_content = f.read()
            python_dict = ast.literal_eval(raw_content)
            if 'bundles' in python_dict and isinstance(python_dict['bundles'], str):
                return python_dict['bundles']
    except Exception as e:
        print(f"   Raw response parsing error: {e}")
    
    return None

def process_creator_bundles(file_path):
    """Process bundles for a single creator."""
    
    creator_name = Path(file_path).parent.name
    print(f"🔍 Processing {creator_name}...")
    
    # Get bundle text
    bundle_text = get_bundle_text(file_path)
    if not bundle_text:
        print(f"   ❌ Could not extract text")
        return []
    
    # Extract bundles
    bundles = extract_bundles_simple(bundle_text)
    if not bundles:
        print(f"   ❌ No bundles found")
        return []
    
    print(f"   ✅ Found {len(bundles)} bundles")
    
    # Create documents
    documents = []
    for bundle in bundles:
        # Extract bundle description for metadata
        bundle_description = ""
        usage_context = ""
        
        desc_match = re.search(r'"bundle_description":\s*"([^"]+)"', bundle['text'])
        if desc_match:
            bundle_description = desc_match.group(1)
        
        context_match = re.search(r'"usage_context":\s*"([^"]+)"', bundle['text'])
        if context_match:
            usage_context = context_match.group(1)
        
        # Create document
        doc = {
            'creator_name': creator_name,
            'bundle_name': bundle['name'],
            'bundle_description': bundle_description,
            'usage_context': usage_context,
            'bundle_text': bundle['text'],
            'text_length': bundle['length'],
            'pattern_type': 'linguistic_bundle',
            'source_file': str(file_path),
            'created_at': datetime.now(timezone.utc),
            'version': 1
        }
        
        documents.append(doc)
        print(f"     - {bundle['name']} ({bundle['length']} chars)")
    
    return documents

def batch_upload_all_bundles():
    """Find all bundle discovery files and upload bundles to MongoDB."""
    
    print("🚀 Starting batch upload of all linguistic bundles...")
    
    base_dir = Path("/Users/<USER>/LinkedInsight/LLM_friendly_Analyses")
    
    # Find all bundle discovery files (JSON or raw response)
    json_files = list(base_dir.glob("*/*bundle_discovery*.json"))
    raw_files = list(base_dir.glob("*/*bundle_discovery*_raw_response.txt"))
    
    # For raw files, create pseudo-JSON paths for processing
    bundle_files = json_files.copy()
    for raw_file in raw_files:
        json_equivalent = str(raw_file).replace('_raw_response.txt', '.json')
        if json_equivalent not in [str(f) for f in json_files]:
            bundle_files.append(Path(json_equivalent))
    
    if not bundle_files:
        print("❌ No bundle discovery files found")
        return
    
    print(f"📁 Found {len(bundle_files)} bundle discovery files")
    
    all_documents = []
    processed_creators = []
    failed_creators = []
    
    for bundle_file in bundle_files:
        try:
            documents = process_creator_bundles(bundle_file)
            if documents:
                all_documents.extend(documents)
                processed_creators.append(Path(bundle_file).parent.name)
            else:
                failed_creators.append(Path(bundle_file).parent.name)
        except Exception as e:
            print(f"   ❌ Error processing {Path(bundle_file).parent.name}: {e}")
            failed_creators.append(Path(bundle_file).parent.name)
    
    if not all_documents:
        print("❌ No documents to upload")
        return
    
    print(f"\n📊 Summary:")
    print(f"   Creators processed: {len(processed_creators)}")
    print(f"   Creators failed: {len(failed_creators)}")
    print(f"   Total bundles: {len(all_documents)}")
    print(f"   Average bundles per creator: {len(all_documents)/len(processed_creators):.1f}")
    
    # Upload to MongoDB
    print(f"\n💾 Uploading {len(all_documents)} documents to MongoDB...")
    
    try:
        client = get_mongo_client()
        db = client.linkedinsight
        collection = db.pattern_embeddings
        
        # Clear existing linguistic bundles
        deleted = collection.delete_many({'pattern_type': 'linguistic_bundle'})
        print(f"🗑️  Deleted {deleted.deleted_count} existing linguistic bundle documents")
        
        # Insert new documents
        result = collection.insert_many(all_documents)
        print(f"✅ Uploaded {len(result.inserted_ids)} bundle documents")
        
        # Show creators processed
        print(f"\n📋 Processed creators:")
        for creator in processed_creators:
            creator_bundles = [d for d in all_documents if d['creator_name'] == creator]
            print(f"   {creator}: {len(creator_bundles)} bundles")
        
        if failed_creators:
            print(f"\n❌ Failed creators:")
            for creator in failed_creators:
                print(f"   {creator}")
        
        return True
        
    except Exception as e:
        print(f"❌ MongoDB upload failed: {e}")
        return False

if __name__ == "__main__":
    success = batch_upload_all_bundles()
    if success:
        print(f"\n🎉 Batch upload completed successfully!")
        print(f"Ready for embedding step.")
    else:
        print(f"\n❌ Batch upload failed")