#!/usr/bin/env python3
"""
Script to concatenate all backend files for the content generation pipeline
(excluding interview-related files)
"""

import os
from datetime import datetime

# Define all the files for the content generation pipeline
PIPELINE_FILES = [
    # Brief Classifier & Creator Matching
    "src/api/routers/creator_matcher.py",
    "src/utils/brief_classifier_utils.py",
    "src/models/brief_classifier_schema.py",
    
    # Agent Service & Generation Pipeline
    "src/api/agent_service.py",
    "src/api/agent_job_manager.py",
    "src/utils/agent_utils/process_step.py",
    "src/utils/pattern_selector.py",
    "src/utils/agent_utils/pattern_filter.py",
    
    # API Endpoints
    "src/api/routers/agent.py",
    "src/api/schemas.py",
    
    # Supporting Infrastructure
    "src/utils/content_agent_tools.py",
    "src/utils/anthropic_client.py",
    "src/utils/api_cache_utils.py",
    "src/utils/mongo_utils_async.py",
    "src/utils/mongo_storage.py",
    "src/utils/scratchpad_logger.py",
    "src/utils/anthropic_text_editor_handler.py",
    
    # Configuration
    "src/config.py",
    
    # Prompts
    "prompts/brief_classifier_system_prompt.txt",
    "prompts/brief_classifier_user_prompt.txt",
    "prompts/agent_prompts/content_agent_system_prompt.txt",
    "prompts/agent_prompts/theme_prompt.txt",
    "prompts/agent_prompts/hook_prompt.txt",
    "prompts/agent_prompts/body_prompt.txt",
    "prompts/agent_prompts/ending_prompt.txt",
    "prompts/pattern_selector_prompts/pattern_selection_prompt.txt",
    "prompts/pattern_selector_prompts/pattern_selector_system_prompt.txt",
]

def concatenate_files():
    """Concatenate all pipeline files into a single text file"""
    output_filename = f"content_generation_pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(output_filename, 'w', encoding='utf-8') as output_file:
        # Write header
        output_file.write("=" * 80 + "\n")
        output_file.write("CONTENT GENERATION PIPELINE - BACKEND FILES\n")
        output_file.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        output_file.write("=" * 80 + "\n\n")
        
        # Process each file
        for filepath in PIPELINE_FILES:
            if os.path.exists(filepath):
                output_file.write("\n" + "=" * 80 + "\n")
                output_file.write(f"FILE: {filepath}\n")
                output_file.write("=" * 80 + "\n\n")
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as input_file:
                        content = input_file.read()
                        output_file.write(content)
                        if not content.endswith('\n'):
                            output_file.write('\n')
                except Exception as e:
                    output_file.write(f"ERROR reading file: {e}\n")
            else:
                output_file.write("\n" + "=" * 80 + "\n")
                output_file.write(f"FILE: {filepath} - NOT FOUND\n")
                output_file.write("=" * 80 + "\n\n")
        
        # Write footer
        output_file.write("\n" + "=" * 80 + "\n")
        output_file.write("END OF CONTENT GENERATION PIPELINE FILES\n")
        output_file.write("=" * 80 + "\n")
    
    print(f"✅ Successfully created: {output_filename}")
    print(f"📄 Total files processed: {len(PIPELINE_FILES)}")
    
    # Count successfully read files
    success_count = sum(1 for f in PIPELINE_FILES if os.path.exists(f))
    print(f"✓ Files found: {success_count}")
    print(f"✗ Files not found: {len(PIPELINE_FILES) - success_count}")
    
    return output_filename

if __name__ == "__main__":
    concatenate_files()