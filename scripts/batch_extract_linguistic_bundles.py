#!/usr/bin/env python3
"""
Batch extract linguistic style bundles from all creators' linguistic analyses.
Uses Claude Sonnet 4 with Citations to preserve granular linguistic details
and create comprehensive style bundles for vector search.
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from pathlib import Path
import glob

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from src.infrastructure.llm.citations_claude import CitationsAnthropic
from src.infrastructure.llm.anthropic import AnthropicClient, AnthropicConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Enhanced bundle extraction prompt with maximum coverage requirements
BUNDLE_EXTRACTION_PROMPT = """You are analyzing a comprehensive LINGUISTIC analysis for a LinkedIn creator. Your task is to INTELLIGENTLY GROUP the original analysis data into 3-4 distinct LINGUISTIC STYLE BUNDLES based on usage context.

CRITICAL OBJECTIVE: DO NOT SUMMARIZE. PRESERVE VERBATIM. Each bundle should contain the EXACT original data structures (sentence patterns, vocabulary lists, formatting rules, etc.) but intelligently grouped by when/how they would be used together.

PRESERVE ALL CONTEXTUAL DATA: Include descriptive fields that explain the "why" behind patterns:
- audience_relationship, emotional_tone_patterns, tone_variation_by_content_type
- sentence_variety_preference, typical_sentence_length_words_description  
- overall_persuasion_style_summary, argument_construction_pattern
- rhetorical_questions_for_reflection_style, direct_address_to_reader_style
- anticipation_building_phrasing_style, validation_or_empathy_cue_usage
- overall_linguistic_intent_summary
- schema_version, generated_at metadata

MANDATORY COVERAGE REQUIREMENTS - The original analysis contains these EXACT categories that MUST ALL be distributed across your bundles:

**SENTENCE PATTERNS (typically 3-4 total - ALL must be included):**
- Look for patterns like "Dialogue attribution", "Declarative insight", "Question-as-transition", "Blunt declaration", "Question-answer", "Clarifying contrast", etc.
- CRITICAL: Include ANY and ALL patterns that are relevant to each bundle's purpose
- CRITICAL: If a pattern works for multiple bundles, include it in ALL relevant bundles

**RHETORICAL DEVICES (typically 3-4 total - ALL must be included):**
- Look for devices like "Dialogue recreation", "Contrasting alternatives", "Rhetorical questions", "Rule of three", "Contrast/juxtaposition", "Enumeration", etc.
- CRITICAL: Include ANY and ALL devices that are relevant to each bundle's purpose
- CRITICAL: If a device works for multiple bundles, include it in ALL relevant bundles

**EMPHASIS PATTERNS (typically 3-4 total - ALL must be included):**
- Look for techniques like "Section headings in all caps", "Repetition of key phrases", "Single-line conclusive statements", "Question-answer pairs", "Three-part stacked statements", etc.

**LINGUISTIC DOS (typically 5-6 total - ALL must be included):**
- Look for rules with priority scores (e.g., "Use specific revenue metrics" priority 9.0)
- CRITICAL: Include ANY and ALL dos that are relevant to each bundle's purpose
- CRITICAL: If a rule works for multiple bundles, include it in ALL relevant bundles

**LINGUISTIC DONTS (typically 4-5 total - ALL must be included):**
- Look for avoidance rules with rationales (e.g., "Don't use complex academic language" priority 9.0)
- CRITICAL: Include ANY and ALL donts that are relevant to each bundle's purpose
- CRITICAL: If a rule works for multiple bundles, include it in ALL relevant bundles

**READER ENGAGEMENT LINGUISTICS (typically 3-4 subcategories - ALL must be included):**
- Look for engagement techniques like rhetorical questions, direct address, anticipation building, validation cues
- CRITICAL: Include ANY and ALL techniques that are relevant to each bundle's purpose

**CONTEXTUAL APPLICATION RULES (typically 2-3 total - ALL must be included):**
- Look for "when" scenarios with specific application guidance
- CRITICAL: Include ANY and ALL rules that are relevant to each bundle's purpose
- CRITICAL: If a rule works for multiple bundles, include it in ALL relevant bundles

INTELLIGENT GROUPING CRITERIA:
- Group patterns that work together in similar content scenarios
- Preserve ALL original field structures and data exactly as written
- Include complete lists, percentages, priority scores, and examples
- Group by contextual usage, not by topic themes

For each bundle, extract and preserve these EXACT ORIGINAL STRUCTURES:

1. **bundle_name**: Descriptive name based on the grouped patterns
2. **content_purpose**: What content scenarios this group serves
3. **when_to_use**: Specific usage contexts for this pattern group

4. **sentence_patterns**: COPY EXACT patterns that belong in this bundle:
   ```json
   [
     {
       "name": "Blunt declaration", 
       "example_template": "[Short, definitive statement about reality].",
       "creator_example": "Startups are HARD.",
       "frequency_percent": 30,
       "typical_usage_context": "Used to establish authority or emphasize a key point, often as a standalone paragraph"
     }
   ]
   ```

5. **paragraph_dynamics**: COPY EXACT rules that apply to this bundle:
   ```json
   {
     "sentences_per_paragraph_rule": "Strongly prefers 1-2 sentences per paragraph, with single-sentence paragraphs used liberally for emphasis",
     "paragraph_length_distribution": [
       {"sentences_count": 1, "frequency_percent": 60},
       {"sentences_count": 2, "frequency_percent": 30}
     ]
   }
   ```

6. **vocabulary_specifics**: COPY EXACT vocabulary rules for this bundle:
   ```json
   {
     "preferred_industry_terms": ["ARR", "PMF", "bootstrapped"],
     "avoided_terms": ["leverage", "synergy", "paradigm"],
     "unique_creator_lexicon": [
       {
         "term": "bootstrapped to $10m ARR",
         "meaning": "Growing a company to significant revenue without venture capital", 
         "typical_usage": "Used as a counter-narrative to the typical VC-funded growth story"
       }
     ]
   }
   ```

7. **rhetorical_devices**: COPY EXACT devices that belong in this bundle:
   ```json
   [
     {
       "device": "Dialogues/Conversations",
       "frequency": "High", 
       "typical_context": "Used to create narrative and show contrasting perspectives",
       "example": "Me: What do I do instead?\\n\\nCEO: Before you hire a SINGLE REP, make sure all of your reps are at 100% capacity."
     }
   ]
   ```

8. **formatting_rules**: COPY EXACT emphasis techniques for this bundle:
   ```json
   {
     "text_formatting_for_emphasis": ["ALL CAPS for key terms or emotional emphasis", "Single-line paragraphs for important points"],
     "use_of_standalone_impact_phrases": {
       "frequency": "Very high",
       "typical_length_words": "3-7", 
       "example": "Keep building."
     }
   }
   ```

9. **transition_techniques**: COPY EXACT transitions that apply:
   ```json
   {
     "between_paragraphs": {
       "common_transition_phrases": ["But here's the thing...", "Why?", "What's the lesson here?"]
     },
     "hook_to_body_transition": {
       "examples": ["Here's why he blew himself up with the Shopify ecosystem:", "Here's what that conversation went like:"]
     }
   }
   ```

10. **linguistic_dos**: COPY EXACT rules with priority scores:
    ```json
    [
      {
        "rule": "Use short, punchy sentences (3-7 words) for key points",
        "priority": 9.5,
        "example": "Startups are hard. Building in public is harder."
      }
    ]
    ```

11. **linguistic_donts**: COPY EXACT avoidance rules:
    ```json
    [
      {
        "rule": "Don't use long, dense paragraphs (more than 3-4 sentences)",
        "priority": 9.5,
        "rationale": "Breaks the quick-reading, high-impact rhythm that characterizes the creator's style"
      }
    ]
    ```

12. **contextual_application_rules**: COPY EXACT application scenarios:
    ```json
    [
      {
        "when": "Sharing a dialogue or conversation",
        "apply": "Strict speaker labels, single-line exchanges, conversational tone with natural speech patterns",
        "avoid": "Long monologues or overly formal language in dialogue",
        "example": "CEO: Of course they do. But don't do it yet..."
      }
    ]
    ```

13. **source_citations**: Reference every preserved element

14. **contextual_descriptors**: COPY EXACT contextual fields that provide strategic insight:
    ```json
    {
      "audience_relationship": "Mentor-to-mentee with peer-level authenticity...",
      "emotional_tone_patterns": {
        "primary_emotions_evoked": ["determination", "ambition", "pragmatism"],
        "emotional_arc": "Typically starts with tension..."
      },
      "sentence_variety_preference": "Prefers deliberate variety with strategic rhythm shifts...",
      "overall_persuasion_style_summary": "Relies on personal experience and specific examples...",
      "argument_construction_pattern": "Typically follows an experience-insight-application structure..."
    }
    ```

15. **metadata**: COPY EXACT metadata fields:
    ```json
    {
      "schema_version": "1.2.0",
      "generated_at": "2023-06-12T14:30:00Z"
    }
    ```

CRITICAL REQUIREMENTS:
- COPY VERBATIM - preserve exact wording, numbers, examples, and structure
- INTELLIGENT GROUPING - group patterns that work together contextually  
- COMPLETE INCLUSION - include ALL relevant items from original analysis that belong in each bundle
- NO CHERRY-PICKING - if 3 sentence patterns apply to a bundle, include all 3, not just 1
- COMPREHENSIVE COVERAGE - each bundle should contain ALL applicable linguistic elements
- NO SUMMARIZATION - if original has 5 examples, include all 5
- MAINTAIN JSON STRUCTURE - preserve original field formats exactly

EXTREMELY IMPORTANT: Each bundle MUST contain ALL relevant examples for each category:
- Include ANY and ALL sentence patterns that fit the bundle's purpose
- Include ANY and ALL rhetorical devices that fit the bundle's purpose
- Include ANY and ALL vocabulary terms that fit the bundle's purpose
- Include ANY and ALL linguistic dos that fit the bundle's purpose
- Include ANY and ALL linguistic donts that fit the bundle's purpose
- Include ANY and ALL contextual application rules that fit the bundle's purpose

OVERLAP IS ENCOURAGED: If a pattern, device, or rule is relevant to multiple bundles, include it in ALL relevant bundles. Better to have comprehensive bundles than to artificially restrict content.

FINAL VERIFICATION CHECKLIST - Before submitting, confirm you have included:
□ ALL sentence patterns distributed across bundles
□ ALL rhetorical devices distributed across bundles  
□ ALL emphasis patterns distributed across bundles
□ ALL linguistic dos distributed across bundles
□ ALL linguistic donts distributed across bundles
□ ALL reader engagement subcategories distributed across bundles
□ ALL contextual application rules distributed across bundles
□ ALL descriptive/contextual fields that explain the patterns
□ ALL metadata (schema_version, generated_at, etc.)
□ ALL qualitative descriptions (audience_relationship, emotional_arc, etc.)

Use the extract_body_bundles tool to return your analysis."""


def load_linguistic_analysis(file_path: str) -> str:
    """Load the linguistic analysis from file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract JSON from markdown code block if present
    if '```json' in content:
        json_start = content.find('```json\n') + 8
        json_end = content.find('\n```', json_start)
        
        if json_start > 7 and json_end > json_start:
            return content[json_start:json_end]
    
    return content


def find_all_linguistic_analyses() -> List[Dict[str, str]]:
    """Find all linguistic analysis files in the LLM_friendly_Analyses directory."""
    analyses = []
    
    # Pattern to match linguistic analysis files
    pattern = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/*/*linguistic_analysis_json_*.txt"
    
    # Use glob to find all matching files
    for file_path in glob.glob(pattern):
        # Extract creator name from path
        creator_name = Path(file_path).parent.name
        analyses.append({
            "creator_name": creator_name,
            "file_path": file_path
        })
    
    # Also find .json files
    pattern_json = "/Users/<USER>/LinkedInsight/LLM_friendly_Analyses/*/*linguistic_analysis_json_*.json"
    for file_path in glob.glob(pattern_json):
        creator_name = Path(file_path).parent.name
        # Skip if we already have this creator from .txt files
        if not any(a["creator_name"] == creator_name for a in analyses):
            analyses.append({
                "creator_name": creator_name,
                "file_path": file_path
            })
    
    logger.info(f"Found {len(analyses)} linguistic analysis files")
    return analyses


async def extract_bundles_with_citations(analysis_text: str, creator_name: str) -> Dict[str, Any]:
    """Use Claude Sonnet 4 with Citations to extract style bundles from the analysis."""
    
    try:
        # Initialize Citations client
        config = AnthropicConfig(
            api_key=os.getenv("ANTHROPIC_API_KEY"),
            beta_header="prompt-caching-2024-07-31"
        )
        
        llm_client = AnthropicClient(config)
        citations_client = CitationsAnthropic(llm_client)
        
        # Use the extract_body_bundles tool with Citations AND thinking enabled
        tool_response = await citations_client.create_transcript_tool_call(
            transcript=analysis_text,
            model="claude-sonnet-4-20250514",
            system_prompt="You are an expert at analyzing content structure and grouping related frameworks into coherent style bundles. Always cite your sources when making bundling decisions. Think through the comprehensive pattern mapping systematically before generating bundles.",
            user_prompt=BUNDLE_EXTRACTION_PROMPT,
            tool_name="extract_body_bundles",
            max_tokens=8000,
            thinking={"enabled": True}
        )
        
        logger.info(f"Successfully extracted bundles for {creator_name}")
        return tool_response
        
    except Exception as e:
        logger.error(f"Error extracting bundles for {creator_name}: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def process_creator_analysis(analysis_info: Dict[str, str]) -> Optional[Dict[str, Any]]:
    """Process a single creator's linguistic analysis."""
    creator_name = analysis_info["creator_name"]
    file_path = analysis_info["file_path"]
    
    logger.info(f"Processing {creator_name} from {file_path}")
    
    try:
        # Load the analysis
        analysis_text = load_linguistic_analysis(file_path)
        
        if not analysis_text:
            logger.warning(f"No analysis text found for {creator_name}")
            return None
        
        logger.info(f"Loaded analysis for {creator_name} ({len(analysis_text)} characters)")
        
        # Extract bundles using Claude with Citations
        tool_response = await extract_bundles_with_citations(analysis_text, creator_name)
        
        if not tool_response:
            logger.warning(f"No bundles extracted for {creator_name}")
            return None
        
        # Extract bundles from tool response
        bundles_raw = tool_response.get('bundles', [])
        
        if not bundles_raw:
            logger.warning(f"No bundles found in tool response for {creator_name}")
            return None
        
        # Parse bundles if they are a JSON string
        if isinstance(bundles_raw, str):
            try:
                bundles = json.loads(bundles_raw)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing bundles JSON for {creator_name}: {e}")
                return None
        else:
            bundles = bundles_raw
        
        if not bundles:
            logger.warning(f"No valid bundles after parsing for {creator_name}")
            return None
        
        # Create result structure
        result = {
            "creator_name": creator_name,
            "analysis_type": "linguistic_analysis",
            "extraction_method": "claude_sonnet_4_citations",
            "bundle_count": len(bundles),
            "bundles": bundles,
            "full_tool_response": tool_response,
            "extraction_timestamp": datetime.utcnow().isoformat(),
            "source_file": file_path
        }
        
        logger.info(f"Successfully processed {creator_name}: {len(bundles)} bundles extracted")
        return result
        
    except Exception as e:
        logger.error(f"Error processing {creator_name}: {e}")
        import traceback
        traceback.print_exc()
        return None


async def save_results(results: List[Dict[str, Any]], output_dir: str):
    """Save the extraction results to files."""
    
    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save individual creator results
    for result in results:
        creator_name = result["creator_name"]
        output_file = output_path / f"{creator_name}_linguistic_style_bundles.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {creator_name} bundles to {output_file}")
    
    # Save summary file
    summary = {
        "extraction_timestamp": datetime.utcnow().isoformat(),
        "total_creators_processed": len(results),
        "total_bundles_extracted": sum(r["bundle_count"] for r in results),
        "creators": [
            {
                "creator_name": r["creator_name"],
                "bundle_count": r["bundle_count"],
                "extraction_success": True
            }
            for r in results
        ]
    }
    
    summary_file = output_path / "linguistic_bundle_extraction_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Saved extraction summary to {summary_file}")


async def main():
    """Main function to process all linguistic analyses."""
    
    logger.info("🧪 Starting Batch Linguistic Bundle Extraction")
    logger.info("=" * 60)
    
    # Find all linguistic analysis files
    analyses = find_all_linguistic_analyses()
    
    if not analyses:
        logger.error("No linguistic analysis files found!")
        return
    
    logger.info(f"Found {len(analyses)} creators to process:")
    for analysis in analyses:
        logger.info(f"  - {analysis['creator_name']}: {analysis['file_path']}")
    
    # Process each creator
    results = []
    failed_creators = []
    
    for i, analysis_info in enumerate(analyses, 1):
        creator_name = analysis_info["creator_name"]
        logger.info(f"\n🤖 Processing creator {i}/{len(analyses)}: {creator_name}")
        
        try:
            result = await process_creator_analysis(analysis_info)
            
            if result:
                results.append(result)
                logger.info(f"✅ Successfully processed {creator_name}")
            else:
                failed_creators.append(creator_name)
                logger.warning(f"⚠️ Failed to process {creator_name}")
            
            # Rate limiting - wait between requests
            if i < len(analyses):
                await asyncio.sleep(2)  # 2 second delay between creators
                
        except Exception as e:
            logger.error(f"❌ Error processing {creator_name}: {e}")
            failed_creators.append(creator_name)
    
    # Save all results
    if results:
        output_dir = "/Users/<USER>/LinkedInsight/linguistic_style_bundles"
        await save_results(results, output_dir)
    
    # Print final summary
    logger.info("\n🎨 BATCH EXTRACTION SUMMARY")
    logger.info("=" * 40)
    logger.info(f"Total creators found: {len(analyses)}")
    logger.info(f"Successfully processed: {len(results)}")
    logger.info(f"Failed: {len(failed_creators)}")
    
    if results:
        total_bundles = sum(r["bundle_count"] for r in results)
        logger.info(f"Total bundles extracted: {total_bundles}")
        logger.info(f"Average bundles per creator: {total_bundles/len(results):.1f}")
        
        logger.info("\n📊 Bundles per creator:")
        for result in results:
            logger.info(f"  {result['creator_name']}: {result['bundle_count']} bundles")
    
    if failed_creators:
        logger.info(f"\n⚠️ Failed creators: {', '.join(failed_creators)}")
    
    logger.info(f"\n✅ Batch extraction completed!")
    logger.info(f"📁 Results saved to: /Users/<USER>/LinkedInsight/linguistic_style_bundles/")


if __name__ == "__main__":
    asyncio.run(main())