# LinkedInsight

AI-powered content generation platform for LinkedIn posts. Transforms user input (transcripts, interviews, or notes) into professional LinkedIn content using Claude API.

## Overview

LinkedInsight is a full-stack web application that provides three content generation pathways:

1. **Transcript Processing** - Extracts ideas from audio/video transcripts and generates posts
2. **Interactive Interview** - AI-driven chat interface that builds content through Q&A
3. **Notes Transformation** - Converts raw notes into structured LinkedIn posts

## Tech Stack

- **Backend**: Python 3.11.8, FastAPI, MongoDB, Anthropic Claude API
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS  
- **Auth**: Clerk (JWT-based authentication)
- **Deployment**: <PERSON><PERSON> (backend), <PERSON><PERSON><PERSON> (frontend)
- **Job Queue**: Hybrid in-memory + file-based async processing
- **Vector Search**: MongoDB Atlas Vector Search with Voyage AI embeddings

## Prerequisites

- Python 3.11.8
- Node.js 16+ and npm
- MongoDB instance (local or cloud)
- Anthropic API key
- Clerk account for authentication

## Quick Start

```bash
# Clone and setup
git clone https://github.com/yourusername/LinkedInsight.git
cd LinkedInsight

# Backend setup
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env      # Configure environment variables (see below)
python run_api.py         # Runs on http://localhost:8001

# Frontend setup (new terminal)
cd linkedinsight-viewer
npm install
npm run dev              # Runs on http://localhost:5173
```

## Environment Configuration

Create a `.env` file in the root directory:

```env
# Required
ANTHROPIC_API_KEY=your_anthropic_api_key
MONGODB_URI=************************************************
CLERK_ISSUER=https://your-instance.clerk.accounts.dev
CLERK_JWKS_URL=https://your-instance.clerk.accounts.dev/.well-known/jwks.json
JWT_SECRET_KEY=your_secret_key_for_sessions

# Required for embeddings/search
VOYAGE_API_KEY=your_voyage_api_key  # For vector embeddings

# Optional
FRONTEND_URL=http://localhost:5173
ANTHROPIC_MODEL=claude-sonnet-4-********
CLAUDE_BETA_HEADER=tools-2024-04-04  # For tool support
LOGFIRE_TOKEN=your_logfire_token    # For monitoring
```

## Architecture Overview

### Async Job System
- **Hybrid Storage**: Jobs stored in memory + file system for reliability
- **Background Processing**: Non-blocking content generation using `asyncio`
- **Progress Tracking**: Real-time updates via `current_generation_step`
- **Auto-cleanup**: Jobs expire after 24 hours

### Authentication Flow
- **Clerk Integration**: JWT tokens verified using JWKS
- **Token Caching**: JWKS keys cached for 1 hour
- **User Isolation**: Data scoped by Clerk user ID (`sub` claim)

### Content Generation Pipeline
1. **Brief Analysis** → Vector search for writing patterns
2. **Hook Generation** → Attention-grabbing openings
3. **Body Development** → Main content with structure
4. **Ending Creation** → Call-to-action and closure
5. **Draft Storage** → Save to draft library

### Vector Search System
- **MongoDB Atlas Vector Search**: Semantic similarity for pattern matching
- **Voyage AI Embeddings**: 1024-dimensional vectors using voyage-3.5 model
- **Pattern Types**: Hooks, bodies, endings, themes, frameworks
- **Performance**: ~50ms queries with 0.7 similarity threshold
- **Caching**: Session-level linguistic pattern optimization

### Streaming Architecture
- **Server-Sent Events (SSE)**: Real-time content streaming
- **Tool Integration**: Anthropic text editor for interactive editing
- **Event Types**: `content_delta`, `tool_call`, `brief_update`, `post_update`

## Project Structure

```
LinkedInsight/
├── linkedinsight-viewer/       # Frontend React application
│   ├── src/
│   │   ├── components/        # UI components by feature
│   │   ├── hooks/            # Custom React hooks
│   │   ├── services/         # API client layer
│   │   └── types/            # TypeScript definitions
│   └── public/               # Static assets
│
├── src/                       # Backend Python application
│   ├── api/
│   │   ├── routers/          # API endpoints
│   │   ├── services/         # Business logic
│   │   ├── agent_job_manager.py  # Async job orchestration
│   │   ├── auth.py           # Clerk authentication
│   │   └── main.py           # FastAPI app setup
│   ├── core/                 # Domain logic
│   └── utils/                # Shared utilities
│       ├── agent_utils/      # Agent-specific utilities
│       │   ├── pattern_filter.py     # Vector pattern filtering
│       │   └── process_step.py       # Vector-enhanced processing
│       ├── vector_pattern_selector.py # Main vector search
│       └── brief_to_patterns.py       # Brief conversion utilities
│
├── prompts/                   # AI prompt templates
├── scripts/                   # Database migrations, utilities
│   └── batch_embed_*.py      # Embedding generation scripts
├── tests/
│   └── embeddings/           # Vector search tests
├── data/db/                   # SQLite for creator posts
├── create_vector_search_index.py # Index creation script
├── embeddings_README.md      # Embeddings documentation
└── run_api.py                # Entry point
```

## Key API Endpoints

### Content Generation
```
POST   /api/agent/generate           # Start generation job
GET    /api/agent/status/{job_id}    # Poll job status
GET    /api/agent/stream/{session_id} # SSE streaming
POST   /api/agent/cancel/{job_id}    # Cancel running job
```

### Interview Flow
```
POST   /api/interview/start          # Initialize session
POST   /api/interview/message        # Send message
GET    /api/interview/session/{id}   # Get session state
```

### Transcript Processing
```
POST   /api/ideas/extract            # Extract ideas async
GET    /api/ideas/status/{job_id}    # Check extraction
PUT    /api/transcripts/{id}/ideas/{idea_id} # Update idea
```

### Content Management
```
GET    /api/v1/draft-library/drafts  # List with pagination
POST   /api/v1/content-strategy      # Save preferences
GET    /api/v1/content-sessions      # User sessions
```

## Database Schema

### MongoDB Collections
- `creators` - Creator profiles and analyses
- `pattern_embeddings` - Vector search for writing patterns (583+ patterns with embeddings)
  - Indexes: Atlas Vector Search on `embedding` field
  - Fields: pattern text, type, embedding vector, hook word count
- `content_generation_sessions` - User generation sessions
- `drafts` - Generated content storage
- `transcripts` - Uploaded transcript data
- `agent_jobs` - Job tracking (v2)

### SQLite Tables
- `posts` - Creator post content
- `analysis_jobs` - Legacy job tracking

## Development Tips

### Testing
```bash
# Run specific test file
pytest tests/test_agent_service.py -v

# Frontend component tests
cd linkedinsight-viewer && npm test

# Test API endpoints
curl -H "Authorization: Bearer $TOKEN" http://localhost:8001/api/auth/me
```

### Common Issues

1. **Job Not Found**: Check both memory and file storage
2. **CORS Errors**: Verify `FRONTEND_URL` in `.env`
3. **Auth Failures**: Ensure Clerk JWKS URL is accessible
4. **Streaming Timeout**: SSE connections may need keep-alive
5. **MongoDB Connection**: Use connection string with `retryWrites=true`
6. **Missing Indexes**: Run `python -m src.utils.ensure_draft_indexes`
7. **Vector Search Errors**: Ensure MongoDB Atlas vector search index exists (`pattern_embeddings_vector_index`)
8. **Embedding Failures**: Check `VOYAGE_API_KEY` is set and valid
9. **Pattern Not Found**: Run embedding scripts in `scripts/batch_embed_*.py` to populate patterns

### Performance Optimization

- **Vector Search**: Pre-embedded patterns for fast retrieval (~50ms queries)
  - MongoDB Atlas Vector Search for semantic similarity
  - Session-level caching of linguistic patterns
  - Pre-computed hook word counts to avoid runtime calculations
- **Prompt Caching**: Reuse system prompts across requests
- **Async Everything**: Non-blocking I/O throughout
- **Context Optimization**: Filtered pattern selection per generation step

## Deployment

### Backend (Heroku)
```bash
heroku create your-app-name
heroku config:set ANTHROPIC_API_KEY=xxx MONGODB_URI=xxx
git push heroku main
```

### Frontend (Vercel)
1. Import GitHub repo
2. Set root directory: `linkedinsight-viewer`
3. Add environment variables
4. Deploy

### Production Considerations
- Use MongoDB Atlas for database
- Enable Heroku Redis for job queue (future)
- Configure Sentry for error tracking
- Set up CloudWatch/Datadog for monitoring

## API Documentation

Interactive API docs available at:
- Local: `http://localhost:8001/docs`
- Production: `https://your-api.herokuapp.com/docs`

## Contributing

1. Create feature branch from `main`
2. Write tests for new functionality
3. Ensure `ruff` and `mypy` pass
4. Update relevant documentation
5. Submit PR with clear description

## License

[MIT License](LICENSE)