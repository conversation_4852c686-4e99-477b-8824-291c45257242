#!/usr/bin/env python3
"""
Test script to verify MongoDB connection.

This script tests the connection to MongoDB using the configuration
from src/config.py and the utility functions from src/utils/mongo_utils.py.
"""

import os
import sys
import logging
from pathlib import Path
import pytest

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to system path
root_dir = Path(__file__).parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

# Import project modules
from src.config import Config
from src.utils.mongo_utils_async import (
    get_async_mongo_client,
    get_async_mongo_db,
    setup_async_mongo_indexes,
)

@pytest.mark.asyncio
async def test_mongodb_connection():
    """Test the connection to MongoDB asynchronously."""
    try:
        # Test getting the MongoDB URI from Config
        logger.info("Testing Config.get_mongodb_uri()...")
        mongodb_uri = Config.get_mongodb_uri()
        logger.info("✅ Successfully retrieved MongoDB URI from Config")
        
        # Test getting a MongoDB client
        logger.info("Testing get_async_mongo_client()...")
        client = await get_async_mongo_client()
        logger.info("✅ Successfully created MongoDB client")
        
        # Test pinging the MongoDB server
        logger.info("Testing MongoDB server connection...")
        await client.admin.command('ping')
        logger.info("✅ Successfully pinged MongoDB server")
        
        # Test getting a MongoDB database
        logger.info("Testing get_async_mongo_db()...")
        db = await get_async_mongo_db()
        logger.info(f"✅ Successfully connected to MongoDB database: {db.name}")
        
        # Test setting up indexes
        logger.info("Testing setup_async_mongo_indexes()...")
        await setup_async_mongo_indexes()
        logger.info("✅ Successfully set up MongoDB indexes")
        
        # List collections in the database
        logger.info("Collections in the database:")
        collections = await db.list_collection_names()
        if collections:
            for collection in collections:
                logger.info(f"  - {collection}")
        else:
            logger.info("  No collections found (this is normal for a new database)")
        
        # List indexes for each collection
        from src.utils.mongo_utils_async import CREATORS_COLLECTION, ANALYSES_COLLECTION
        
        logger.info(f"Indexes for {CREATORS_COLLECTION} collection:")
        try:
            indexes = [idx async for idx in db[CREATORS_COLLECTION].list_indexes()]
            for index in indexes:
                logger.info(f"  - {index['name']}: {index['key']}")
        except Exception as e:
            logger.info(f"  Could not list indexes: {e}")
        
        logger.info(f"Indexes for {ANALYSES_COLLECTION} collection:")
        try:
            indexes = [idx async for idx in db[ANALYSES_COLLECTION].list_indexes()]
            for index in indexes:
                logger.info(f"  - {index['name']}: {index['key']}")
        except Exception as e:
            logger.info(f"  Could not list indexes: {e}")
        
        logger.info("All MongoDB connection tests passed! 🎉")
        return True
    
    except Exception as e:
        logger.error(f"❌ MongoDB connection test failed: {e}")
        return False

if __name__ == "__main__":
    import asyncio, sys as _sys

    success = asyncio.run(test_mongodb_connection())
    _sys.exit(0 if success else 1)
