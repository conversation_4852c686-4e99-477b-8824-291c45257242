"""Integration test for complete 3-step generation with vector patterns."""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.agent_service import AgentService


async def test_vector_pattern_integration():
    """Test that the complete pipeline works with vector pattern selection."""
    
    # Test input
    test_input = "Write about the importance of resilience in entrepreneurship"
    creator_name = "test_creator"  # Not used but still required by API
    
    try:
        # Call generate_content - this will use real vector search
        result = await AgentService.generate_content(
            input_text=test_input,
            creator_name=creator_name,
            post_length="medium"
        )
        
        # Check that we got a result without errors
        assert "error" not in result, f"Generation failed with error: {result.get('error')}"
        assert "generated_post" in result, "No generated_post in result"
        
        # Check that content was generated for each step
        generated_post = result["generated_post"]
        assert len(generated_post) > 100, "Generated post is too short"
        
        # Check that we have the expected structure
        assert "enhanced_scratchpad" in result
        scratchpad = result["enhanced_scratchpad"]
        
        # Verify each step was completed (theme removed for vector pipeline)
        generation_pipeline = scratchpad["generation_pipeline"]
        assert generation_pipeline["hook_creation"]["generated_hook_text"] is not None
        assert generation_pipeline["body_linguistic_construction"]["generated_body_text"] is not None
        assert generation_pipeline["ending_creation"]["generated_ending_text"] is not None
        
        print("✅ Integration test passed: Vector pattern selection working end-to-end")
        print(f"\n📝 Generated post preview (first 200 chars):\n{generated_post[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_vector_pattern_integration())
    sys.exit(0 if success else 1)