"""Test script to verify Anthropic prompt caching is working."""
import asyncio
import logging
import json
from typing import Dict, Any, List

from anthropic import AsyncAnthropic
from anthropic.types import Message

from config import Config

# Configure basic logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

config = Config()
MIN_CACHEABLE_SIZE = 2048  # Minimum cacheable size in tokens

async def create_test_document(size: int = MIN_CACHEABLE_SIZE) -> List[Dict[str, Any]]:
    """Create a test document that exceeds the minimum cacheable size.
    
    Using a single large block for all content to maximize caching efficiency.
    """
    # Sample LinkedIn post content
    post_content = """
    Excited to announce our new product launch today! 🚀
    
    After months of hard work, we're finally ready to share what we've been building.
    
    Key features:
    • Seamless integration with existing workflows
    • AI-powered recommendations
    • Real-time collaboration tools
    • Extended battery life
    
    Early user feedback has been incredible, with a 95% satisfaction rate.
    
    What feature are you most excited about? Drop a comment below!
    
    #ProductLaunch #Innovation #TechNews
    """
    
    # Create a single large block with all posts
    posts = []
    total_posts = 28  # Same total as previous implementation
    
    # Combine all posts into a single large block
    all_posts_content = "ALL POSTS:\n\n"
    for i in range(total_posts):
        all_posts_content += f"Post {i+1}: {post_content}\n\n"
    
    # Add the single large block with cache control
    posts.append({
        "type": "text",
        "text": all_posts_content,
        "cache_control": {"type": "ephemeral"}
    })
    
    logger.info(f"Created test document with 1 cached block containing {total_posts} posts")
    return posts

async def run_first_call(client: AsyncAnthropic, document: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Run the first API call to populate the cache."""
    logger.info("Running first API call to populate cache...")
    
    # System blocks - standard system prompt plus cacheable document
    system_blocks = [
        {
            "type": "text",
            "text": "You are a helpful assistant that analyzes social media content."
        }
    ]
    
    # Add the document blocks (which have cache_control already set)
    system = system_blocks + document
    
    # User message with a simple analysis request
    messages = [
        {
            "role": "user", 
            "content": "Identify the main themes in these posts and summarize them in JSON format."
        }
    ]
    
    # Make the API call
    try:
        response = await client.messages.create(
            model=config.DEFAULT_MODEL,
            max_tokens=1000,
            temperature=0.7,
            system=system,
            messages=messages
        )
        
        # Log token usage
        logger.info(f"First call completed. Total input tokens: {response.usage.input_tokens}")
        if hasattr(response.usage, 'cache_creation_input_tokens') and response.usage.cache_creation_input_tokens:
            logger.info(f"Cache created with {response.usage.cache_creation_input_tokens} tokens")
        
        return response.usage.model_dump()
    
    except Exception as e:
        logger.error(f"Error in first API call: {e}")
        return {}

async def run_second_call(client: AsyncAnthropic, document: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Run the second API call to verify cache usage."""
    logger.info("Running second API call to verify cache usage...")
    
    # Use identical system blocks as the first call
    system_blocks = [
        {
            "type": "text",
            "text": "You are a helpful assistant that analyzes social media content."
        }
    ]
    
    # Add the document blocks (keeping identical structure for cache hit)
    system = system_blocks + document
    
    # Different user message to see if the cache still works
    messages = [
        {
            "role": "user", 
            "content": "What types of engagement strategies are used in these posts?"
        }
    ]
    
    # Make the API call
    try:
        response = await client.messages.create(
            model=config.DEFAULT_MODEL,
            max_tokens=1000,
            temperature=0.7,
            system=system,
            messages=messages
        )
        
        # Log token usage
        logger.info(f"Second call completed. Total input tokens: {response.usage.input_tokens}")
        if hasattr(response.usage, 'cache_read_input_tokens') and response.usage.cache_read_input_tokens:
            logger.info(f"Cache hit! Read {response.usage.cache_read_input_tokens} tokens from cache")
            logger.info(f"Approximately {(response.usage.cache_read_input_tokens / response.usage.input_tokens) * 100:.2f}% of input tokens were from cache")
        else:
            logger.warning("No cache hit detected in the second call")
        
        return response.usage.model_dump()
    
    except Exception as e:
        logger.error(f"Error in second API call: {e}")
        return {}

async def run_test():
    """Run the full caching test."""
    logger.info("Starting caching test...")
    
    # Initialize the Anthropic client
    client = AsyncAnthropic(api_key=config.ANTHROPIC_API_KEY)
    
    # Create a document that exceeds the minimum cacheable size
    document = await create_test_document()
    
    # Run the first call to populate cache
    first_call_usage = await run_first_call(client, document)
    
    # Run the second call to verify cache usage
    second_call_usage = await run_second_call(client, document)
    
    # Compare and analyze the results
    logger.info("===== Caching Test Results =====")
    if first_call_usage and second_call_usage:
        if 'cache_read_input_tokens' in second_call_usage and second_call_usage['cache_read_input_tokens'] > 0:
            logger.info("✅ CACHE HIT CONFIRMED!")
            logger.info(f"First call usage: {json.dumps(first_call_usage, indent=2)}")
            logger.info(f"Second call usage: {json.dumps(second_call_usage, indent=2)}")
            
            # Calculate savings
            tokens_saved = second_call_usage.get('cache_read_input_tokens', 0)
            potential_tokens = tokens_saved + second_call_usage.get('input_tokens', 0)
            percent_saved = (tokens_saved / potential_tokens) * 100 if potential_tokens > 0 else 0
            
            logger.info(f"Tokens saved: {tokens_saved} ({percent_saved:.2f}%)")
        else:
            logger.warning("❌ No cache hit detected. Cache may not be working properly.")
            logger.info(f"First call usage: {json.dumps(first_call_usage, indent=2)}")
            logger.info(f"Second call usage: {json.dumps(second_call_usage, indent=2)}")
    else:
        logger.error("Test failed - missing usage data from one or both calls")
    
    logger.info("===== Test Complete =====")

if __name__ == "__main__":
    asyncio.run(run_test())