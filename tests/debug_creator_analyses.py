#!/usr/bin/env python3
"""
Debug Creator Analyses <PERSON><PERSON><PERSON>
----------------------------
Queries the MongoDB `analyses` collection for a given creator username and
prints basic statistics along with a sample of documents.  If a legacy
filesystem directory exists under `outputs/<creator_username>/`, the script
also lists JSON files there for quick comparison.

Usage:
    python scripts/debug_creator_analyses.py <creator_username>

Environment Variables:
    MONGODB_URI  MongoDB connection string (default: mongodb://localhost:27017)
    DB_NAME      Database name (default: linkedinsight)
"""

from __future__ import annotations

import argparse
import os
import sys
from pathlib import Path
from pprint import pprint

from pymongo import MongoClient


def parse_args() -> argparse.Namespace:  # noqa: D401
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Inspect analyses for a specific creator username in MongoDB and (optionally) on the local filesystem.",
    )
    parser.add_argument(
        "username",
        nargs="?",
        default="taylordbarnes",
        help="Creator username to inspect (default: taylordbarnes)",
    )
    parser.add_argument(
        "-l",
        "--limit",
        type=int,
        default=5,
        help="Number of sample documents / files to display (default: 5)",
    )
    return parser.parse_args()


def connect_mongo() -> MongoClient:  # noqa: D401
    """Create a MongoDB client using env vars or defaults."""
    uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
    return MongoClient(uri)


def main() -> None:  # noqa: D401
    """Run the diagnostic workflow."""
    args = parse_args()

    client = connect_mongo()
    db_name = os.getenv("DB_NAME", "linkedinsight")
    db = client[db_name]
    analyses_col = db["analyses"]

    print("=== MongoDB Inspection ===")
    # Query by creator_name (canonical field)
    query = {"creator_name": args.username}
    count = analyses_col.count_documents(query)
    print(f"Documents for '{args.username}': {count}")

    if count:
        print("Sample documents:")
        for doc in analyses_col.find(query).limit(args.limit):
            print(f"- _id: {doc.get('_id')} | title: {doc.get('title')}")

    print("\n=== Filesystem Inspection ===")
    outputs_dir = Path("outputs") / args.username
    if outputs_dir.exists():
        fs_files = list(outputs_dir.glob("*.json"))
        print(f"JSON files in {outputs_dir}: {len(fs_files)}")
        for path in fs_files[: args.limit]:
            print(f"- {path.name}")
    else:
        print(f"No local directory found at {outputs_dir}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        sys.exit(0) 