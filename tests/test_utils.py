"""Tests for utility functions in src/api/utils.py."""

import os
import json
import tempfile
import unittest
from unittest.mock import patch
from typing import Dict, Any

# Add project root to system path
import sys
from pathlib import Path
root_dir = Path(__file__).parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

from src.api.utils import load_json_result


class TestLoadJsonResult(unittest.TestCase):
    """Test cases for the load_json_result function."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a valid JSON file
        self.valid_json_path = os.path.join(self.temp_dir.name, "valid.json")
        self.valid_json_data = {"key": "value", "number": 42}
        with open(self.valid_json_path, 'w', encoding='utf-8') as f:
            json.dump(self.valid_json_data, f)
        
        # Create an invalid JSON file
        self.invalid_json_path = os.path.join(self.temp_dir.name, "invalid.json")
        with open(self.invalid_json_path, 'w', encoding='utf-8') as f:
            f.write("{invalid json")
        
        # Path to a non-existent file
        self.nonexistent_path = os.path.join(self.temp_dir.name, "nonexistent.json")

    def tearDown(self):
        """Clean up test fixtures."""
        self.temp_dir.cleanup()

    def test_load_valid_json(self):
        """Test loading a valid JSON file."""
        result = load_json_result(self.valid_json_path)
        self.assertEqual(result, self.valid_json_data)

    def test_load_nonexistent_file(self):
        """Test loading a non-existent file."""
        with self.assertLogs(level='ERROR') as cm:
            result = load_json_result(self.nonexistent_path)
            self.assertIn("Result file not found at specified path", cm.output[0])
        self.assertIsNone(result)

    def test_load_invalid_json(self):
        """Test loading an invalid JSON file."""
        with self.assertLogs(level='ERROR') as cm:
            result = load_json_result(self.invalid_json_path)
            self.assertIn("Error decoding JSON", cm.output[0])
        self.assertIsNone(result)

    @patch('builtins.open')
    def test_io_error(self, mock_open):
        """Test handling of IO errors."""
        mock_open.side_effect = IOError("Permission denied")
        
        with self.assertLogs(level='ERROR') as cm:
            result = load_json_result(self.valid_json_path)
            self.assertIn("Error loading result", cm.output[0])
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main()