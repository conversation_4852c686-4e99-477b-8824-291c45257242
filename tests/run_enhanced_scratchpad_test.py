"""
Simple script to run the agent with a sample brief and retentionadam's JSONs.

WARNING: This script is DEPRECATED.
It uses the old file-based analysis loading approach which has been replaced
by the vector-based pipeline. This script is preserved for reference only.
"""

import asyncio
import json
import logging
import datetime
from pathlib import Path
from typing import Dict, Any
from unittest.mock import patch

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import project modules
from src.api.agent_service import AgentService

# Sample creator to use for testing
TEST_CREATOR = "retentionadam"  # Using retentionadam who has JSON analyses

def load_json_analysis(creator_name: str, analysis_type: str) -> Dict[str, Any]:
    """
    Load the most recent JSON analysis file for a creator and analysis type.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (themes, hooks, body, endings, linguistic)

    Returns:
        Dictionary containing the analysis data
    """
    # Define the base directory for JSON analyses
    json_dir = Path("/Users/<USER>/LinkedInsight/outputs/LLM_friendly_JSONs") / creator_name

    # Find the most recent JSON file for this analysis type
    pattern = f"{creator_name}_{analysis_type}_analysis_json_*.json"
    json_files = list(json_dir.glob(pattern))

    if not json_files:
        raise FileNotFoundError(f"No JSON files found for {creator_name} {analysis_type} analysis")

    # Sort by modification time (most recent first)
    json_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    # Load the most recent file
    with open(json_files[0], "r", encoding="utf-8") as f:
        return json.load(f)

async def run_test():
    """
    Run the agent with a sample brief and retentionadam's JSONs.
    """
    # Load the sample brief
    brief_path = Path(__file__).parent / "sample_brief.md"
    with open(brief_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Use the first paragraph as the brief
    brief = content.split("\n\n")[0].strip()
    if not brief:
        # Use the first 500 characters as a fallback
        brief = content[:500].strip()

    logger.info(f"Using brief: {brief[:100]}...")

    # Load JSON analyses for retentionadam
    analyses = {}
    for analysis_type in ["themes", "hook", "body_structure", "ending", "linguistic"]:
        try:
            # Map the analysis types to the expected keys in the AgentService
            key = analysis_type
            if analysis_type == "hook":
                key = "hooks"
            elif analysis_type == "body_structure":
                key = "body"
            elif analysis_type == "ending":
                key = "endings"

            analyses[key] = load_json_analysis(TEST_CREATOR, analysis_type)
            logger.info(f"Loaded {analysis_type} analysis for {TEST_CREATOR}")
        except FileNotFoundError as e:
            logger.warning(f"Could not load {analysis_type} analysis: {e}")

    # DEPRECATED: This script mocks load_creator_analyses with local file loading
    # TODO: Replace with vector-based approach when demo scripts are updated
    print("WARNING: Using deprecated file-based analysis loading")
    with patch.object(AgentService, 'load_creator_analyses', return_value=analyses):
        try:
            # Generate content using the enhanced scratchpad
            result = await AgentService.generate_content(brief, TEST_CREATOR)

            # Print the generated post
            logger.info("\n\n=== GENERATED POST ===\n")
            logger.info(result["generated_post"])

            # Print the explanation
            logger.info("\n\n=== EXPLANATION ===\n")
            logger.info(result["explanation"])
        except Exception as e:
            logger.error(f"Error generating content: {e}")

            # Print the scratchpad for debugging
            logger.info("\n\n=== SCRATCHPAD CONTENT ===\n")
            scratchpad_files = list(Path("/Users/<USER>/LinkedInsight/outputs").glob(f"{TEST_CREATOR}_generation_process_*.txt"))
            if scratchpad_files:
                latest_file = max(scratchpad_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, "r", encoding="utf-8") as f:
                    logger.info(f.read())

            # Raise the exception to stop execution
            raise

    # Save the full result to a file for manual inspection
    output_dir = Path(__file__).parent / "output"
    output_dir.mkdir(exist_ok=True)

    # Create filename with creator name and timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{TEST_CREATOR}_enhanced_scratchpad_{timestamp}.json"
    output_path = output_dir / output_filename
    
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2)

    logger.info(f"Full result saved to {output_path}")

if __name__ == "__main__":
    asyncio.run(run_test())
