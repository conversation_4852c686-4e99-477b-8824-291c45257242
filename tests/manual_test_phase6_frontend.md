# Phase 6: Frontend Funnel Stage Display - Manual Test Guide

## Overview
This guide helps verify that funnel stages are properly displayed in the frontend UI.

## Implementation Summary

### 1. IdeaCard Component Updates
- **File**: `linkedinsight-viewer/src/components/transcript/IdeaCard.tsx`
- Changed "Category" label to "Funnel Stage"
- Added `formatFunnelStage()` helper to display friendly names
- Added `getFunnelTooltip()` helper for hover tooltips
- Updated edit mode with dropdown for funnel stages

### 2. Agent Explanation Enhancement
- **File**: `linkedinsight-viewer/src/components/agent/AgentExplanation.tsx`
- Created new component to parse and enhance explanations
- Adds colored badges for funnel stages (Top/Middle/Bottom)
- Integrated into AgentPanes component

## Manual Testing Steps

### Test 1: Idea Card Display
1. Navigate to the Ideas page after processing a transcript
2. Verify each idea card shows "Funnel Stage:" instead of "Category:"
3. Check that funnel values display as:
   - "Top of Funnel" (not "top_of_funnel")
   - "Middle Funnel" (not "middle_funnel")
   - "Bottom Funnel" (not "bottom_funnel")
4. Hover over funnel stage to see tooltip explanation

### Test 2: Idea Card Editing
1. Click the edit button on an idea card
2. Verify "Funnel Stage:" dropdown appears with options:
   - Not classified
   - Top of Funnel
   - Middle Funnel
   - Bottom Funnel
3. Change the funnel stage and save
4. Verify the new stage displays correctly

### Test 3: Agent Explanation Display
1. Generate content using the Content Agent
2. Click "Info" button to view explanation
3. Look for "Content Strategy Applied:" section
4. Verify funnel stage appears as a colored badge:
   - Top of Funnel: Blue badge
   - Middle Funnel: Purple badge
   - Bottom Funnel: Green badge

### Test 4: Dark Mode Compatibility
1. Toggle dark mode
2. Verify funnel stage badges remain readable
3. Check that tooltips work in dark mode

## Expected Results

### Idea Cards
- Clean display of funnel stages with proper capitalization
- Tooltips explaining each funnel stage on hover
- Dropdown in edit mode with all funnel options

### Agent Explanations
- Funnel stage badges with appropriate colors
- Strategic reasoning visible in explanation text
- Badges adapt to dark/light mode

## Backward Compatibility
- Ideas without funnel stages show "Not classified"
- Old category values (if any) still display correctly
- System handles both old and new category formats

## Visual Examples

### Idea Card View Mode
```
Funnel Stage: Top of Funnel
[hover shows: "Broad appeal content for awareness and thought leadership"]
```

### Agent Explanation
```
# How This Post Was Created

**Content Strategy Applied:** [Middle Funnel badge] content for Product managers...
```

## Implementation Checklist
✓ Updated IdeaCard label from "Category" to "Funnel Stage"
✓ Added formatFunnelStage() helper function
✓ Added getFunnelTooltip() helper function
✓ Updated edit mode with funnel dropdown
✓ Created AgentExplanation component
✓ Integrated badge display for funnel stages
✓ Updated AgentPanes to use new component
✓ Maintained backward compatibility