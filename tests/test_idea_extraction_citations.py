"""Unit tests for idea extraction with comprehensive citations."""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from src.core.ideas_extraction.ideas_extractor import IdeasExtractor
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem, PostOutline
from src.utils.citation_schema import Citation


class TestIdeaExtractionWithCitations:
    """Test suite for idea extraction with citation support."""
    
    @pytest.fixture
    def mock_citations_client(self):
        """Create a mock CitationsAnthropic client."""
        client = Mock()
        client.create_transcript_tool_call = AsyncMock()
        return client
    
    @pytest.fixture
    def sample_transcript(self):
        """Sample transcript for testing."""
        return """
[00:00:15] <PERSON>: I've been thinking about how AI is changing content creation.
[00:00:30] Sarah: Absolutely! The speed of iteration is incredible now.
[00:01:00] John: Yes, and it's not just speed. The quality has improved dramatically.
[00:01:30] Sarah: I use AI for brainstorming all the time. It helps me see angles I wouldn't have considered.
[00:02:00] <PERSON>: That's the key - it's a thinking partner, not a replacement.
[00:02:30] Sarah: Exactly. The human touch is still essential for authentic connection.
[00:03:00] <PERSON>: Speaking of authenticity, I've found that AI helps me be MORE authentic.
[00:03:30] <PERSON>: How so?
[00:04:00] <PERSON>: It handles the routine tasks so I can focus on sharing real insights and experiences.
[00:04:30] Sarah: That's brilliant. It's like having an assistant who handles the logistics.
[00:05:00] <PERSON>: And the data analysis capabilities are game-changing for content strategy.
[00:05:30] <PERSON>: Oh yes, understanding what resonates with your audience is so much easier now.
"""
    
    @pytest.fixture
    def mock_api_response_with_citations(self):
        """Mock API response with comprehensive citations."""
        return {
            'ideas': [
                {
                    'idea': 'AI as thinking partner',
                    'category': 'Technology & Innovation',
                    'engagement': 5,
                    'summary': 'AI transforms content creation not by replacing humans but by serving as a collaborative thinking partner. This partnership enhances both speed and quality while preserving authenticity. The key insight is that AI handles routine tasks, freeing creators to focus on genuine insights and human connection.',
                    'citations': [
                        {
                            'text': "The speed of iteration is incredible now.",
                            'timestamp': "[00:00:30]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "Yes, and it's not just speed. The quality has improved dramatically.",
                            'timestamp': "[00:01:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "I use AI for brainstorming all the time. It helps me see angles I wouldn't have considered.",
                            'timestamp': "[00:01:30]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "That's the key - it's a thinking partner, not a replacement.",
                            'timestamp': "[00:02:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "The human touch is still essential for authentic connection.",
                            'timestamp': "[00:02:30]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "It handles the routine tasks so I can focus on sharing real insights and experiences.",
                            'timestamp': "[00:04:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "It's like having an assistant who handles the logistics.",
                            'timestamp': "[00:04:30]",
                            'speaker': "Sarah"
                        }
                    ],
                    'suggested_outlines': [
                        {
                            'title': 'Partnership Evolution Story',
                            'throughline': 'From skepticism to collaboration',
                            'sections': ['Initial Resistance', 'Discovery Moment', 'Integration Process', 'Multiplied Impact']
                        },
                        {
                            'title': 'Practical Implementation Guide',
                            'throughline': 'How to integrate AI into your workflow',
                            'sections': ['Tool Selection', 'Workflow Design', 'Quality Control', 'Measuring Success']
                        },
                        {
                            'title': 'Myth-Busting Approach',
                            'throughline': 'Debunking AI replacement fears',
                            'sections': ['Common Misconceptions', 'Reality Check', 'Complementary Strengths', 'Future Vision']
                        }
                    ]
                },
                {
                    'idea': 'Authenticity through AI efficiency',
                    'category': 'Personal Branding',
                    'engagement': 4,
                    'summary': 'Counter-intuitively, AI enhances authenticity in content creation by handling routine tasks and data analysis. This efficiency allows creators to invest more time in sharing genuine experiences and insights. The transformation lies in using AI to amplify rather than manufacture the human voice.',
                    'citations': [
                        {
                            'text': "I've found that AI helps me be MORE authentic.",
                            'timestamp': "[00:03:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "It handles the routine tasks so I can focus on sharing real insights and experiences.",
                            'timestamp': "[00:04:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "The human touch is still essential for authentic connection.",
                            'timestamp': "[00:02:30]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "And the data analysis capabilities are game-changing for content strategy.",
                            'timestamp': "[00:05:00]",
                            'speaker': "John"
                        },
                        {
                            'text': "Understanding what resonates with your audience is so much easier now.",
                            'timestamp': "[00:05:30]",
                            'speaker': "Sarah"
                        }
                    ],
                    'suggested_outlines': [
                        {
                            'title': 'Authenticity Paradox',
                            'throughline': 'How automation creates more human content',
                            'sections': ['The Automation Dilemma', 'Time Liberation Effect', 'Deeper Connections']
                        },
                        {
                            'title': 'Behind the Scenes',
                            'throughline': 'My authentic content process with AI',
                            'sections': ['Morning Routine', 'AI Integration Points', 'Human Touch Moments']
                        },
                        {
                            'title': 'Data-Driven Authenticity',
                            'throughline': 'Using insights to be more genuine',
                            'sections': ['Know Your Audience', 'Authentic Response', 'Measuring Connection']
                        }
                    ]
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_extract_ideas_with_comprehensive_citations(self, mock_citations_client, sample_transcript, mock_api_response_with_citations):
        """Test that idea extraction properly captures all relevant citations."""
        # Setup
        mock_citations_client.create_transcript_tool_call.return_value = mock_api_response_with_citations
        extractor = IdeasExtractor(mock_citations_client)
        
        # Execute
        with patch('src.utils.mongo_utils_async.get_async_mongo_db', new_callable=AsyncMock):
            result = await extractor.extract_ideas(sample_transcript, "<EMAIL>")
        
        # Verify
        assert len(result.ideas) == 2
        
        # Check first idea
        first_idea = result.ideas[0]
        assert first_idea.idea == 'AI as thinking partner'
        assert first_idea.category == 'Technology & Innovation'
        assert first_idea.engagement == 5
        assert len(first_idea.citations) == 7  # All relevant citations captured
        assert len(first_idea.suggested_outlines) == 3
        
        # Verify citation structure
        for citation in first_idea.citations:
            assert isinstance(citation, Citation)
            assert citation.text
            assert citation.timestamp
            assert citation.speaker
        
        # Check second idea
        second_idea = result.ideas[1]
        assert second_idea.idea == 'Authenticity through AI efficiency'
        assert len(second_idea.citations) == 5  # Different number of citations per idea
        
        # Verify summary quality
        assert len(first_idea.summary) > 100  # Meaty summary
        assert 'thinking partner' in first_idea.summary.lower()
        assert 'authenticity' in second_idea.summary.lower()
    
    @pytest.mark.asyncio
    async def test_citation_parsing_preserves_all_fields(self, mock_citations_client):
        """Test that citation parsing preserves all required fields."""
        # Setup mock response with edge cases
        mock_response = {
            'ideas': [{
                'idea': 'Test idea',
                'category': 'Test',
                'engagement': 3,
                'summary': 'Test summary.',
                'citations': [
                    {
                        'text': 'Quote with special characters: "AI & ML"',
                        'timestamp': '[Session 2, 15:30]',  # Non-standard timestamp
                        'speaker': 'Dr. Smith'
                    },
                    {
                        'text': 'Quote without speaker',
                        'timestamp': '[00:00:00]',
                        'speaker': ''  # Empty speaker
                    }
                ],
                'suggested_outlines': [
                    {'title': 'Test', 'throughline': 'Test', 'sections': ['A', 'B', 'C']}
                ] * 3
            }]
        }
        
        mock_citations_client.create_transcript_tool_call.return_value = mock_response
        extractor = IdeasExtractor(mock_citations_client)
        
        # Execute
        ideas = await extractor.extract_ideas_from_transcript("Test transcript")
        
        # Verify citation fields preserved correctly
        assert len(ideas[0].citations) == 2
        assert ideas[0].citations[0].text == 'Quote with special characters: "AI & ML"'
        assert ideas[0].citations[0].timestamp == '[Session 2, 15:30]'
        assert ideas[0].citations[0].speaker == 'Dr. Smith'
        assert ideas[0].citations[1].speaker == ''  # Empty string preserved
    
    @pytest.mark.asyncio 
    async def test_handles_many_citations(self, mock_citations_client):
        """Test handling of ideas with many citations (30+)."""
        # Create response with 30 citations
        many_citations = [
            {
                'text': f'Citation number {i} with relevant content',
                'timestamp': f'[00:{i:02d}:00]',
                'speaker': 'Speaker' if i % 2 == 0 else 'Other'
            }
            for i in range(30)
        ]
        
        mock_response = {
            'ideas': [{
                'idea': 'Comprehensive topic',
                'category': 'Deep Dive',
                'engagement': 5,
                'summary': 'A topic discussed extensively throughout the conversation.',
                'citations': many_citations,
                'suggested_outlines': [
                    {'title': f'Outline {i}', 'throughline': 'Test', 'sections': ['A', 'B', 'C']}
                    for i in range(3)
                ]
            }]
        }
        
        mock_citations_client.create_transcript_tool_call.return_value = mock_response
        extractor = IdeasExtractor(mock_citations_client)
        
        # Execute
        ideas = await extractor.extract_ideas_from_transcript("Long transcript")
        
        # Verify all citations captured
        assert len(ideas[0].citations) == 30
        for i, citation in enumerate(ideas[0].citations):
            assert f'Citation number {i}' in citation.text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])