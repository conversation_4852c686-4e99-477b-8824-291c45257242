"""Manual test script for Phase 5: Pattern Selection with Strategy Influence."""
import asyncio
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Add project root to path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from src.utils.brief_to_patterns import match_brief_to_patterns, create_strategy_context_text

# Test data
test_strategies = {
    "top_funnel": {
        "industry": "B2B SaaS",
        "offering": "Customer feedback analytics platform",
        "target_audience": "Product managers and startup founders",
        "differentiators": "AI-powered insights, real-time feedback loops",
        "content_goal": "Build thought leadership and brand awareness"
    },
    "middle_funnel": {
        "industry": "B2B SaaS",
        "offering": "Customer feedback analytics platform", 
        "target_audience": "Product managers evaluating solutions",
        "differentiators": "AI-powered insights, real-time feedback loops",
        "content_goal": "Demonstrate expertise and educate on best practices"
    },
    "bottom_funnel": {
        "industry": "B2B SaaS",
        "offering": "Customer feedback analytics platform",
        "target_audience": "Product managers ready to buy",
        "differentiators": "AI-powered insights, real-time feedback loops", 
        "content_goal": "Showcase client results and drive conversions"
    }
}

test_brief = """
How to transform customer feedback into product gold - practical strategies for product managers.

Drawing from experience helping 100+ startups build better products, this post explores 
concrete techniques for turning user complaints into competitive advantages.
"""

async def test_strategy_context_creation():
    """Test strategy context text generation."""
    console = Console()
    
    console.print(Panel.fit("[bold blue]Testing Strategy Context Creation[/bold blue]"))
    
    for stage, strategy in test_strategies.items():
        context_text = create_strategy_context_text(strategy)
        console.print(f"\n[cyan]{stage.upper()} Strategy Context:[/cyan]")
        console.print(context_text)
    
    console.print("\n[green]✓ Strategy context text generation working[/green]")
    return True

async def test_combined_embedding():
    """Test that brief + strategy creates combined embedding."""
    console = Console()
    
    console.print(Panel.fit("[bold blue]Testing Combined Embedding[/bold blue]"))
    
    # Test with middle funnel strategy
    strategy = test_strategies["middle_funnel"]
    
    console.print("1. Testing pattern search WITHOUT strategy...")
    results_without = await match_brief_to_patterns(
        brief_text=test_brief,
        desired_length="medium"
    )
    
    console.print("2. Testing pattern search WITH strategy...")
    results_with = await match_brief_to_patterns(
        brief_text=test_brief,
        desired_length="medium",
        content_strategy=strategy
    )
    
    # Compare top patterns
    comparison_table = Table(title="Pattern Comparison")
    comparison_table.add_column("Pattern Type", style="cyan")
    comparison_table.add_column("Without Strategy", style="yellow")
    comparison_table.add_column("With Strategy", style="green")
    
    for pattern_type in ["hook", "body", "ending"]:
        without_top = results_without.get(pattern_type, [{}])[0].get("pattern_name", "None")
        with_top = results_with.get(pattern_type, [{}])[0].get("pattern_name", "None")
        comparison_table.add_row(pattern_type, without_top, with_top)
    
    console.print(comparison_table)
    console.print("\n[green]✓ Combined embedding affects pattern selection[/green]")
    return True

async def test_funnel_scoring():
    """Test funnel-aware scoring adjustments."""
    console = Console()
    
    console.print(Panel.fit("[bold blue]Testing Funnel-Aware Scoring[/bold blue]"))
    
    # Test each funnel stage
    for stage, strategy in test_strategies.items():
        console.print(f"\n[cyan]Testing {stage.upper()} funnel scoring:[/cyan]")
        
        results = await match_brief_to_patterns(
            brief_text=test_brief,
            desired_length="medium",
            content_strategy=strategy
        )
        
        # Show patterns with funnel boosts
        for pattern_type in ["hook", "body", "ending"]:
            patterns = results.get(pattern_type, [])
            if patterns and len(patterns) > 0:
                top_pattern = patterns[0]
                boost = top_pattern.get("funnel_boost", 0)
                if boost > 0:
                    console.print(f"  {pattern_type}: '{top_pattern['pattern_name']}' got {boost:.1%} boost")
    
    console.print("\n[green]✓ Funnel scoring applies subtle boosts[/green]")
    return True

async def test_pattern_ranking():
    """Test that funnel scoring affects final ranking."""
    console = Console()
    
    console.print(Panel.fit("[bold blue]Testing Pattern Ranking Changes[/bold blue]"))
    
    # Compare bottom funnel results
    strategy = test_strategies["bottom_funnel"]
    
    results = await match_brief_to_patterns(
        brief_text=test_brief,
        desired_length="medium", 
        content_strategy=strategy
    )
    
    # Show top 3 patterns for each type with scores
    ranking_table = Table(title="Bottom Funnel Pattern Rankings")
    ranking_table.add_column("Type", style="cyan")
    ranking_table.add_column("Rank", style="yellow")
    ranking_table.add_column("Pattern", style="white")
    ranking_table.add_column("Score", style="green")
    ranking_table.add_column("Boost", style="magenta")
    
    for pattern_type in ["hook", "body", "ending"]:
        patterns = results.get(pattern_type, [])
        for i, pattern in enumerate(patterns[:3], 1):
            ranking_table.add_row(
                pattern_type if i == 1 else "",
                str(i),
                pattern.get("pattern_name", "Unknown"),
                f"{pattern.get('score', 0):.3f}",
                f"{pattern.get('funnel_boost', 0):.1%}"
            )
    
    console.print(ranking_table)
    console.print("\n[green]✓ Funnel scoring affects pattern rankings[/green]")
    return True

async def test_backward_compatibility():
    """Test that system works without strategy."""
    console = Console()
    
    console.print(Panel.fit("[bold blue]Testing Backward Compatibility[/bold blue]"))
    
    try:
        results = await match_brief_to_patterns(
            brief_text=test_brief,
            desired_length="medium"
            # No content_strategy provided
        )
        
        console.print("✓ Pattern matching works without strategy")
        console.print(f"  Found {sum(len(p) for p in results.values())} total patterns")
        
        # Verify no funnel boosts applied
        has_boosts = any(
            pattern.get("funnel_boost", 0) > 0
            for patterns in results.values()
            for pattern in patterns
        )
        
        if not has_boosts:
            console.print("✓ No funnel boosts applied when strategy absent")
        
        console.print("\n[green]✓ Backward compatibility maintained[/green]")
        return True
        
    except Exception as e:
        console.print(f"\n[red]✗ Backward compatibility failed: {e}[/red]")
        return False

async def main():
    """Run all manual tests for Phase 5."""
    console = Console()
    
    console.print(Panel.fit(
        "[bold green]Phase 5: Pattern Selection with Strategy Influence[/bold green]\n" +
        "Manual Test Suite",
        title="Strategy-Influenced Pattern Selection"
    ))
    
    tests = [
        ("Strategy Context Creation", test_strategy_context_creation),
        ("Combined Embedding", test_combined_embedding),
        ("Funnel Scoring", test_funnel_scoring),
        ("Pattern Ranking", test_pattern_ranking),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, "PASSED" if result else "FAILED"))
            console.print()
        except Exception as e:
            results.append((test_name, f"ERROR: {str(e)}"))
            console.print(f"\n[red]✗ {test_name} failed: {e}[/red]\n")
    
    # Summary
    console.print(Panel.fit("[bold]Test Summary[/bold]"))
    summary_table = Table()
    summary_table.add_column("Test", style="cyan")
    summary_table.add_column("Result", style="green")
    
    for test_name, result in results:
        style = "green" if result == "PASSED" else "red"
        summary_table.add_row(test_name, f"[{style}]{result}[/{style}]")
    
    console.print(summary_table)
    
    # Implementation checklist
    console.print("\n[bold]Implementation Checklist:[/bold]")
    checklist = [
        "✓ Created strategy context text function",
        "✓ Modified match_brief_to_patterns to accept content_strategy",
        "✓ Combined brief + strategy for embedding",
        "✓ Added apply_funnel_scoring function",
        "✓ Implemented funnel stage inference from strategy",
        "✓ Applied subtle scoring boosts (2-10%)",
        "✓ Updated SimplifiedAgentRunner to pass strategy",
        "✓ Maintained backward compatibility"
    ]
    for item in checklist:
        console.print(f"  {item}")
    
    # Manual testing steps
    console.print("\n[bold]Manual Testing Steps:[/bold]")
    console.print("1. Generate content with top-funnel strategy goal")
    console.print("2. Verify personal/vulnerable patterns rank higher")
    console.print("3. Generate content with bottom-funnel strategy goal") 
    console.print("4. Verify transformation/results patterns rank higher")
    console.print("5. Test without strategy to ensure no regression")
    
    console.print("\n[bold]Next Implementation:[/bold]")
    console.print("Phase 6: Frontend Funnel Stage Display")

if __name__ == "__main__":
    asyncio.run(main())