"""
Tests for the enhanced scratchpad functionality.
"""

import pytest
import json
from unittest.mock import patch, MagicMock

from src.api.agent_service import AgentService
from src.utils.agent_utils import process_step, parse_step_response

@pytest.fixture
def mock_analyses():
    """Mock creator analyses."""
    return {
        "themes": {"theme1": "description1"},
        "hooks": {"hook1": "description1"},
        "body": {"body1": "description1"},
        "endings": {"ending1": "description1"},
        "linguistic": {"pattern1": "description1"}
    }

@pytest.fixture
def mock_process_step():
    """Mock the process_step function."""
    with patch("src.api.agent_service.process_step") as mock:
        # Configure the mock to return a valid result
        mock.return_value = {
            "reasoning": "Test reasoning",
            "selection": "Test selection",
            "output": "Test output",
            "decision_summary": "Test decision summary",
            "thinking": "Test thinking",
            "step_name": "theme"
        }
        yield mock

@pytest.mark.asyncio
async def test_initialize_enhanced_scratchpad():
    """Test that the enhanced scratchpad is correctly initialized."""
    # Arrange
    input_text = "Test input"
    creator_analyses = {
        "themes": {"theme1": "description1"},
        "hooks": {"hook1": "description1"}
    }

    # Act
    scratchpad = AgentService.initialize_enhanced_scratchpad(input_text, creator_analyses)

    # Assert - user_input_brief removed in refactor (stored in cache instead)
    assert "user_input_brief" not in scratchpad  # REFACTORED: Brief stored in API cache, not scratchpad
    # creator_style_guides removed for vector refactor
    assert "generation_pipeline" in scratchpad
    # theme_selection removed for vector pipeline
    assert "hook_creation" in scratchpad["generation_pipeline"]
    assert "body_linguistic_construction" in scratchpad["generation_pipeline"]
    assert "ending_creation" in scratchpad["generation_pipeline"]
    assert scratchpad["current_draft_post"] == ""
    assert "final_explanation_to_user_components" in scratchpad

@pytest.mark.asyncio
async def test_generate_content_with_enhanced_scratchpad(mock_analyses, mock_process_step):
    """Test that the generate_content method correctly uses the enhanced scratchpad."""
    # Arrange
    with patch.object(AgentService, "load_prompt_template", return_value="Test prompt"), \
         patch.object(AgentService, "format_explanation_from_enhanced_scratchpad", return_value="Test explanation"):

        input_text = "Test input"

        # Act
        result = await AgentService.generate_content(input_text)

        # Assert
        assert result["generated_post"] is not None
        assert result["explanation"] is not None
        assert "enhanced_scratchpad" in result

        # Verify that process_step was called with the correct arguments
        assert mock_process_step.call_count == 3  # hook, body, ending (theme removed)

        # Check the first call (hook creation) - theme step removed
        first_call_args = mock_process_step.call_args_list[0][0]
        assert first_call_args[0] == "hook"  # step
        assert first_call_args[1] == input_text  # input_text

        # Check the scratchpad structure in the first call
        scratchpad_arg = first_call_args[3]  # scratchpad is the 4th argument
        assert "generation_pipeline" in scratchpad_arg
        # theme_selection removed for vector pipeline
        assert "hook_creation" in scratchpad_arg["generation_pipeline"]
        assert "body_linguistic_construction" in scratchpad_arg["generation_pipeline"]
        assert "ending_creation" in scratchpad_arg["generation_pipeline"]

@pytest.mark.asyncio
async def test_parse_step_response_with_decision_summary():
    """Test that parse_step_response correctly extracts the decision summary."""
    # Arrange
    response_text = """
    REASONING: This is the reasoning.

    SELECTION: This is the selection.

    DECISION SUMMARY: This is the decision summary.

    OUTPUT: This is the output.
    """

    # Act
    result = parse_step_response(response_text)

    # Assert
    assert result["reasoning"] == "This is the reasoning."
    assert result["selection"] == "This is the selection."
    assert result["decision_summary"] == "This is the decision summary."
    assert result["output"] == "This is the output."

@pytest.mark.asyncio
async def test_format_explanation_from_enhanced_scratchpad():
    """Test that format_explanation_from_enhanced_scratchpad correctly formats the explanation."""
    # Arrange
    generation_pipeline = {
        "theme_selection": {
            "selected_theme_name": "Test theme",
            "detailed_reasoning_for_selection": "Test theme reasoning",
            "decision_summary_for_next_step": "Test theme decision summary"
        },
        "hook_creation": {
            "selected_hook_pattern": "Test hook",
            "generated_hook_text": "Test hook text",
            "detailed_reasoning_for_selection_and_text": "Test hook reasoning",
            "decision_summary_for_next_step": "Test hook decision summary"
        },
        "body_linguistic_construction": {
            "selected_body_structure": "Test body",
            "selected_linguistic_patterns": "Test linguistic patterns",
            "generated_body_text": "Test body text",
            "detailed_reasoning_for_selection_and_text": "Test body reasoning",
            "decision_summary_for_next_step": "Test body decision summary"
        },
        "ending_creation": {
            "selected_ending_pattern": "Test ending",
            "generated_ending_text": "Test ending text",
            "detailed_reasoning_for_selection_and_text": "Test ending reasoning",
            "decision_summary_for_next_step": "Test ending decision summary"
        }
    }

    # Act
    explanation = AgentService.format_explanation_from_enhanced_scratchpad(generation_pipeline)

    # Assert - updated for refactored explanation format
    # Theme removed for vector pipeline
    assert "Test hook decision summary" in explanation
    assert "Test body decision summary" in explanation
    assert "How This Post Was Created" in explanation
    assert "**Hook Creation:**" in explanation
    assert "**Body Structure & Style:**" in explanation
    assert "**Ending:**" in explanation
