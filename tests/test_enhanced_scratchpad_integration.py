"""
Integration test for the enhanced scratchpad implementation.

This test verifies that the entire enhanced scratchpad implementation works correctly
end-to-end by generating content for a sample brief and creator.
"""

import pytest
import os
import logging
import json
import datetime
from pathlib import Path
from typing import Dict, Any
from unittest.mock import patch, AsyncMock

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import project modules
from src.api.agent_service import AgentService

# Sample creator to use for testing
TEST_CREATOR = "retentionadam"  # Using retentionadam who has JSON analyses

def load_json_analysis(creator_name: str, analysis_type: str) -> Dict[str, Any]:
    """
    Load the most recent JSON analysis file for a creator and analysis type.

    Args:
        creator_name: Name of the creator
        analysis_type: Type of analysis (themes, hooks, body, endings, linguistic)

    Returns:
        Dictionary containing the analysis data
    """
    # Define the base directory for JSON analyses
    json_dir = Path("/Users/<USER>/LinkedInsight/outputs/LLM_friendly_JSONs") / creator_name

    # Find the most recent JSON file for this analysis type
    pattern = f"{creator_name}_{analysis_type}_analysis_json_*.json"
    json_files = list(json_dir.glob(pattern))

    if not json_files:
        raise FileNotFoundError(f"No JSON files found for {creator_name} {analysis_type} analysis")

    # Sort by modification time (most recent first)
    json_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    # Load the most recent file
    with open(json_files[0], "r", encoding="utf-8") as f:
        return json.load(f)

@pytest.mark.asyncio
@pytest.mark.skip("DEPRECATED: Integration test uses deprecated file-based analysis loading")
async def test_enhanced_scratchpad_integration():
    """
    Test the enhanced scratchpad implementation end-to-end.

    This test:
    1. Loads a sample brief from tests/sample_brief.md
    2. Loads JSON analyses for retentionadam
    3. Generates content using the enhanced scratchpad
    4. Verifies that the generated content includes all expected components
    5. Verifies that decision summaries are correctly passed between steps
    """
    # Load the sample brief
    brief_path = Path(__file__).parent / "sample_brief.md"
    with open(brief_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Use the first paragraph as the brief
    brief = content.split("\n\n")[0].strip()
    if not brief:
        # Use the first 500 characters as a fallback
        brief = content[:500].strip()

    logger.info(f"Using brief: {brief[:100]}...")

    # Load JSON analyses for retentionadam
    analyses = {}
    for analysis_type in ["themes", "hook", "body_structure", "ending", "linguistic"]:
        try:
            # Map the analysis types to the expected keys in the AgentService
            key = analysis_type
            if analysis_type == "hook":
                key = "hooks"
            elif analysis_type == "body_structure":
                key = "body"
            elif analysis_type == "ending":
                key = "endings"

            analyses[key] = load_json_analysis(TEST_CREATOR, analysis_type)
            logger.info(f"Loaded {analysis_type} analysis for {TEST_CREATOR}")
        except FileNotFoundError as e:
            logger.warning(f"Could not load {analysis_type} analysis: {e}")

    # DEPRECATED: This test mocks load_creator_analyses with local file loading
    # TODO: Update to use vector-based pipeline when integration tests are needed
    with patch.object(AgentService, 'load_creator_analyses', return_value=analyses):
        # Generate content using the enhanced scratchpad
        result = await AgentService.generate_content(brief, TEST_CREATOR)

    # Verify that the result contains all expected components
    assert "generated_post" in result
    assert "explanation" in result
    assert "enhanced_scratchpad" in result

    # Verify that the generated post is not empty
    assert result["generated_post"]
    assert len(result["generated_post"]) > 100  # Should be a substantial post

    # Verify that the explanation includes all steps
    explanation = result["explanation"]
    assert "Theme Selection" in explanation
    assert "Hook Creation" in explanation
    assert "Body Structure & Linguistic Style" in explanation
    assert "Ending Creation" in explanation
    assert "Post Creation Summary" in explanation

    # Verify that the enhanced scratchpad contains all expected components
    scratchpad = result["enhanced_scratchpad"]
    assert "theme_selection" in scratchpad
    assert "hook_creation" in scratchpad
    assert "body_linguistic_construction" in scratchpad
    assert "ending_creation" in scratchpad

    # Verify that decision summaries are present and passed between steps
    assert scratchpad["theme_selection"]["decision_summary_for_next_step"]
    assert scratchpad["hook_creation"]["decision_summary_for_next_step"]
    assert scratchpad["body_linguistic_construction"]["decision_summary_for_next_step"]

    # Optional: Verify that the ending has a decision summary (though it's not used by any next step)
    if "decision_summary_for_next_step" in scratchpad["ending_creation"]:
        assert scratchpad["ending_creation"]["decision_summary_for_next_step"]

    # Verify that the thinking logs are present
    assert "thinking_logs" in result

    # Print the generated post for manual review
    logger.info("\n\n=== GENERATED POST ===\n")
    logger.info(result["generated_post"])
    logger.info("\n\n=== EXPLANATION ===\n")
    logger.info(result["explanation"][:500] + "...")  # Show just the beginning of the explanation

    # Save the full result to a file for manual inspection
    output_dir = Path(__file__).parent / "output"
    output_dir.mkdir(exist_ok=True)

    # Create filename with creator name and timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{TEST_CREATOR}_enhanced_scratchpad_integration_{timestamp}.json"
    output_path = output_dir / output_filename
    
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2)

    logger.info(f"Full result saved to {output_path}")
