"""Minimal test to verify cache hits between sequential API calls.

This script performs two sequential API calls with the same post content
but different user prompts to verify that the caching mechanism works correctly.
"""

import sys
import os
import logging
import json
import asyncio
import time
from typing import Dict, List, Any
from pprint import pformat
import pytest
from unittest.mock import patch, AsyncMock

# Add project root to system path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

# Import project modules
from src.utils.api_cache_utils import create_api_params, extract_cache_metrics, MIN_CACHEABLE_SIZE
from src.utils.anthropic_client import AnthropicClient
from src.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
SYSTEM_PROMPT = "You are a helpful assistant that analyzes social media content."
MIN_POSTS = 10  # Minimum number of posts to ensure caching threshold is met
CREATOR_NAME = "test_creator"  # Arbitrary name for testing

def create_mock_posts(num_posts: int = MIN_POSTS) -> List[Dict[str, Any]]:
    """Create mock posts for testing.
    
    Posts are designed to be long enough to exceed the minimum cacheable size
    when combined.
    """
    posts = []
    for i in range(num_posts):
        post_content = f"""This is test post #{i+1} with enough content to ensure caching works properly.
        
Post content needs to be sufficiently long to exceed the minimum cacheable size
when combined with other posts. This post includes multiple paragraphs and lines
to simulate real content.

Here's some additional text to increase the token count:
- The quick brown fox jumps over the lazy dog.
- We need to ensure there are enough tokens for caching.
- The minimum cacheable size is about 2048 tokens.
- Each post contributes some tokens to the total.

Engagement metrics:
- Likes: {i * 20 + 50}
- Comments: {i * 5 + 10}
- Shares: {i * 2 + 5}

This should help ensure we have enough content to test caching effectively.
"""
        posts.append({
            "title": f"Post ID: {i+1}",
            "content": post_content
        })
    return posts

@pytest.mark.asyncio
async def test_minimal_cache_hit():
    """Run a minimal test to verify cache hits between sequential API calls."""
    logger.info("=== Starting Minimal Cache Hit Test ===")
    
    # 1. Create client
    client = AnthropicClient()
    
    # 2. Create mock posts with enough content for caching
    logger.info(f"Creating {MIN_POSTS} mock posts for testing...")
    posts = create_mock_posts(MIN_POSTS)
    
    # 3. First API call to populate cache
    logger.info("\n--- Making first API call to populate cache ---")
    first_prompt = f"Identify the main themes in the posts by {CREATOR_NAME}."
    
    first_params = create_api_params(
        system_prompt=SYSTEM_PROMPT,
        posts=posts,
        user_prompt=first_prompt,
        max_tokens=1000,  # Keep response short for test
        temperature=0.7,
        thinking_enabled=False  # Disable thinking for test simplicity
    )
    
    start_time = time.time()
    first_response = await client.client.messages.create(**first_params)
    first_duration = time.time() - start_time
    
    logger.info(f"First call completed in {first_duration:.2f} seconds")
    
    # Extract cache metrics from first call
    first_metrics = extract_cache_metrics(first_response)
    logger.info(f"First call cache metrics: {json.dumps(first_metrics, indent=2)}")
    
    # 4. Second API call to verify cache hit
    logger.info("\n--- Making second API call to verify cache hit ---")
    second_prompt = f"Summarize the engagement patterns in the posts by {CREATOR_NAME}."
    
    # Create identical parameters except for user prompt
    second_params = create_api_params(
        system_prompt=SYSTEM_PROMPT,
        posts=posts,
        user_prompt=second_prompt,
        max_tokens=1000,
        temperature=0.7,
        thinking_enabled=False
    )
    
    start_time = time.time()
    second_response = await client.client.messages.create(**second_params)
    second_duration = time.time() - start_time
    
    logger.info(f"Second call completed in {second_duration:.2f} seconds")
    
    # Extract cache metrics from second call
    second_metrics = extract_cache_metrics(second_response)
    logger.info(f"Second call cache metrics: {json.dumps(second_metrics, indent=2)}")
    
    # 5. Verify cache hit and calculate savings
    logger.info("\n=== Cache Test Results ===")
    
    if second_metrics["cache_read"]:
        logger.info("✅ CACHE HIT CONFIRMED!")
        
        tokens_saved = second_metrics["tokens_saved"]
        percent_saved = second_metrics["percent_saved"]
        speedup = first_duration / second_duration if second_duration > 0 else 0
        
        logger.info(f"Tokens saved: {tokens_saved} ({percent_saved:.2f}%)")
        logger.info(f"Time speedup: {speedup:.2f}x ({first_duration:.2f}s vs {second_duration:.2f}s)")
        
        return True
    else:
        logger.error("❌ NO CACHE HIT DETECTED")
        logger.info("Check that the API parameters are identical except for user prompt")
        logger.info("Make sure posts exceed the minimum cacheable size")
        
        return False

if __name__ == "__main__":
    if not os.getenv("ANTHROPIC_API_KEY"):
        logger.error("ANTHROPIC_API_KEY environment variable not set")
        sys.exit(1)
        
    success = asyncio.run(test_minimal_cache_hit())
    sys.exit(0 if success else 1)