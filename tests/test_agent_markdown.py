#!/usr/bin/env python3
"""Test Agent Markdown Access

DEPRECATED: This script directly tests AgentService.load_creator_analyses
which uses the old file-based analysis loading approach. This has been
replaced by the vector-based pipeline.

This script is preserved for reference but should not be used for testing
the current system functionality.

Original Usage:
    python scripts/test_agent_markdown.py <creator_username>
"""
from __future__ import annotations

import argparse
import asyncio
import sys
import textwrap
from typing import List

# Import inside main to avoid slow startup if dependencies missing

# Ensure project root is on sys.path so `src` imports work when executing from scripts/
import os
from pathlib import Path

project_root = Path(__file__).resolve().parents[1]
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

EXPECTED_TYPES: List[str] = [
    "themes",
    "hooks",
    "body",
    "endings",
    "linguistic",
]


def parse_args() -> argparse.Namespace:  # noqa: D401
    parser = argparse.ArgumentParser(description="Test AgentService markdown retrieval")
    parser.add_argument(
        "username",
        nargs="?",
        default="taylordbarnes",
        help="Creator username (default: taylordbarnes)",
    )
    return parser.parse_args()


async def run_test(username: str) -> None:  # noqa: D401
    # Import here to avoid event loop issues at module import time
    from src.api.agent_service import AgentService  # type: ignore

    print("WARNING: This script uses deprecated file-based analysis loading")
    print("The vector-based pipeline has replaced this functionality.")
    print("This test may fail if local analysis files are not available.\n")
    
    try:
        analyses = await AgentService.load_creator_analyses(username)
    except Exception as e:
        print(f"ERROR: {e}")
        print("This is expected if using the new vector-based pipeline.")
        sys.exit(1)
    print(f"Loaded analyses for '{username}': {list(analyses.keys())}\n")

    missing = [t for t in EXPECTED_TYPES if t not in analyses]
    for atype, content in analyses.items():
        preview = textwrap.shorten(str(content)[:200], width=120)
        print(f"-- {atype.upper()} preview: {preview}\n")

    if missing:
        print(f"ERROR: Missing analysis types: {', '.join(missing)}", file=sys.stderr)
        sys.exit(1)
    print("All expected analysis types present. ✅")


def main() -> None:  # noqa: D401
    args = parse_args()
    asyncio.run(run_test(args.username))


if __name__ == "__main__":
    main() 