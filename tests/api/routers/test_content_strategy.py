"""Unit tests for the Content Strategy API router."""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from fastapi import <PERSON>TT<PERSON>Ex<PERSON>, status

# Model to be tested and used in tests
from src.api.schemas import ContentStrategy 
# Functions to be tested
from src.api.routers.content_strategy import create_or_update_strategy, get_strategy
# Errors
from pymongo.errors import PyMongoError


# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio

CLERK_USER_ID = "user_test_123"
VALID_USER_PAYLOAD = {"sub": CLERK_USER_ID, "name": "Test User"}
INVALID_USER_PAYLOAD_NO_SUB = {"name": "No Sub Field"}

BASE_STRATEGY_DATA = {
    "industry": "Tech",
    "offering": "SaaS Platform",
    "target_audience": "Developers",
    "differentiators": "Speed and reliability",
    "content_goal": "Increase signups"
}

# This includes clerk_user_id which is also in the ContentStrategy model
# but the endpoint logic should prioritize the one from the token.
STRATEGY_PAYLOAD_MODEL_INSTANCE = ContentStrategy(
    clerk_user_id="user_payload_should_be_ignored", 
    **BASE_STRATEGY_DATA
)

# This is what would be stored in DB / returned by GET if clerk_user_id from token is used
EXPECTED_STRATEGY_DOC_FROM_DB = {
    "clerk_user_id": CLERK_USER_ID, 
    **BASE_STRATEGY_DATA,
    # MongoDB might add _id, so we ensure our mock find_one can return it
    # but the Pydantic model ContentStrategy won't include it unless defined.
    "_id": "some_mongo_object_id" 
}


@pytest.fixture
def mock_db_collection():
    """Fixture to create a mock MongoDB collection with AsyncMock methods."""
    mock_collection = MagicMock()
    mock_collection.update_one = AsyncMock()
    mock_collection.find_one = AsyncMock()
    return mock_collection

@pytest.fixture
def mock_get_db(mock_db_collection):
    """Fixture to mock get_mongo_db returning a mock DB with the mock collection."""
    mock_db = MagicMock()
    mock_db.__getitem__.return_value = mock_db_collection # db["collection_name"]
    with patch("src.api.routers.content_strategy.get_mongo_db", return_value=mock_db) as mock_get:
        yield mock_get, mock_db_collection


class TestCreateOrUpdateStrategy:
    async def test_create_new_strategy_success(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        
        mock_collection.update_one.return_value = MagicMock(upserted_id="new_id", matched_count=0, modified_count=0) # Simulating an insert
        # find_one after upsert should return the document including the clerk_user_id from token
        mock_collection.find_one.return_value = EXPECTED_STRATEGY_DOC_FROM_DB

        response_strategy = await create_or_update_strategy(
            strategy_payload=STRATEGY_PAYLOAD_MODEL_INSTANCE, 
            current_user=VALID_USER_PAYLOAD
        )

        assert response_strategy.clerk_user_id == CLERK_USER_ID # Verifies token ID was used
        assert response_strategy.industry == BASE_STRATEGY_DATA["industry"]
        mock_collection.update_one.assert_called_once()
        update_call_args = mock_collection.update_one.call_args[0]
        assert update_call_args[0] == {"clerk_user_id": CLERK_USER_ID} # Query
        assert update_call_args[1]["$set"]["clerk_user_id"] == CLERK_USER_ID # Payload has token's ID
        assert update_call_args[1]["$set"]["industry"] == BASE_STRATEGY_DATA["industry"]
        assert mock_collection.update_one.call_args[1]["upsert"] is True
        mock_collection.find_one.assert_called_once_with({"clerk_user_id": CLERK_USER_ID})


    async def test_update_existing_strategy_success(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db

        mock_collection.update_one.return_value = MagicMock(matched_count=1, modified_count=1, upserted_id=None)
        mock_collection.find_one.return_value = EXPECTED_STRATEGY_DOC_FROM_DB

        response_strategy = await create_or_update_strategy(
            strategy_payload=STRATEGY_PAYLOAD_MODEL_INSTANCE, 
            current_user=VALID_USER_PAYLOAD
        )
        
        assert response_strategy.clerk_user_id == CLERK_USER_ID
        mock_collection.update_one.assert_called_once()


    async def test_clerk_id_missing_in_token(self, mock_get_db):
        with pytest.raises(HTTPException) as exc_info:
            await create_or_update_strategy(
                strategy_payload=STRATEGY_PAYLOAD_MODEL_INSTANCE, 
                current_user=INVALID_USER_PAYLOAD_NO_SUB
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "User ID missing" in exc_info.value.detail


    async def test_db_update_exception(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        mock_collection.update_one.side_effect = PyMongoError("DB update failed")

        with pytest.raises(HTTPException) as exc_info:
            await create_or_update_strategy(
                strategy_payload=STRATEGY_PAYLOAD_MODEL_INSTANCE, 
                current_user=VALID_USER_PAYLOAD
            )
        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Database operation failed" in exc_info.value.detail


    async def test_db_find_after_upsert_fails(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        mock_collection.update_one.return_value = MagicMock(upserted_id="new_id") # Assume update_one works
        mock_collection.find_one.return_value = None # ...but subsequent find_one fails

        with pytest.raises(HTTPException) as exc_info:
            await create_or_update_strategy(
                strategy_payload=STRATEGY_PAYLOAD_MODEL_INSTANCE, 
                current_user=VALID_USER_PAYLOAD
            )
        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to save or retrieve content strategy" in exc_info.value.detail


class TestGetStrategy:
    async def test_get_strategy_success(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        mock_collection.find_one.return_value = EXPECTED_STRATEGY_DOC_FROM_DB

        response_strategy = await get_strategy(current_user=VALID_USER_PAYLOAD)

        assert response_strategy.clerk_user_id == CLERK_USER_ID
        assert response_strategy.industry == BASE_STRATEGY_DATA["industry"]
        mock_collection.find_one.assert_called_once_with({"clerk_user_id": CLERK_USER_ID})


    async def test_get_strategy_not_found(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        mock_collection.find_one.return_value = None

        with pytest.raises(HTTPException) as exc_info:
            await get_strategy(current_user=VALID_USER_PAYLOAD)
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Content strategy not found" in exc_info.value.detail

    
    async def test_clerk_id_missing_in_token_get(self, mock_get_db):
        with pytest.raises(HTTPException) as exc_info:
            await get_strategy(current_user=INVALID_USER_PAYLOAD_NO_SUB)
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "User ID missing" in exc_info.value.detail


    async def test_db_find_exception(self, mock_get_db):
        _mock_get_mongo_db, mock_collection = mock_get_db
        mock_collection.find_one.side_effect = PyMongoError("DB find failed")

        with pytest.raises(HTTPException) as exc_info:
            await get_strategy(current_user=VALID_USER_PAYLOAD)
        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Database operation failed" in exc_info.value.detail 