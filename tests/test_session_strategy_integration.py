"""Test session service strategy integration."""

import asyncio
import pytest
from unittest.mock import patch, AsyncMock

from src.api.services.session_service import (
    get_session_context,
    create_session
)
from src.utils.mongo_utils_async import get_user_content_strategy


@pytest.mark.asyncio
async def test_create_session_with_strategy():
    """Test that create_session auto-fetches strategy when user_id is provided."""
    
    # Mock strategy data
    mock_strategy = {
        "industry": "B2B SaaS",
        "offering": "AI-powered analytics",
        "target_audience": "Data leaders at mid-market companies",
        "differentiators": "Real-time insights, no-code interface",
        "content_goal": "Build authority while generating MQLs"
    }
    
    # Mock MongoDB operations
    mock_db = AsyncMock()
    mock_collection = AsyncMock()
    mock_db.__getitem__.return_value = mock_collection
    mock_collection.insert_one.return_value = AsyncMock()
    
    with patch('src.api.services.session_service.get_user_content_strategy') as mock_get_strategy:
        with patch('src.api.services.session_service.get_async_mongo_db') as mock_get_db:
            # Setup mocks
            mock_get_strategy.return_value = mock_strategy
            mock_get_db.return_value = mock_db
            
            # Call create_session directly with user_id in extra_fields
            session_id = await create_session(
                collection_name="test_collection",
                extra_fields={
                    "user_id": "test-user-123",
                    "brief": "Test brief"
                }
            )
            
            # Verify strategy was fetched
            mock_get_strategy.assert_called_once_with("test-user-123")
            
            # Verify the session was created with strategy
            mock_collection.insert_one.assert_called_once()
            created_doc = mock_collection.insert_one.call_args[0][0]
            
            assert "content_strategy" in created_doc
            assert created_doc["content_strategy"] == mock_strategy
            assert created_doc["brief"] == "Test brief"
            assert created_doc["user_id"] == "test-user-123"


@pytest.mark.asyncio
async def test_create_session_with_no_strategy():
    """Test that sessions work fine when user has no strategy."""
    
    # Mock MongoDB operations
    mock_db = AsyncMock()
    mock_collection = AsyncMock()
    mock_db.__getitem__.return_value = mock_collection
    mock_collection.insert_one.return_value = AsyncMock()
    
    with patch('src.api.services.session_service.get_user_content_strategy') as mock_get_strategy:
        with patch('src.api.services.session_service.get_async_mongo_db') as mock_get_db:
            # Setup mocks - no strategy found
            mock_get_strategy.return_value = None
            mock_get_db.return_value = mock_db
            
            # Call create_session directly
            session_id = await create_session(
                collection_name="test_collection",
                extra_fields={
                    "user_id": "test-user-456",
                    "brief": "Test brief"
                }
            )
            
            # Verify strategy was fetched
            mock_get_strategy.assert_called_once_with("test-user-456")
            
            # Verify the session was created with None strategy
            mock_collection.insert_one.assert_called_once()
            created_doc = mock_collection.insert_one.call_args[0][0]
            
            assert "content_strategy" in created_doc
            assert created_doc["content_strategy"] is None
            assert created_doc["brief"] == "Test brief"


@pytest.mark.asyncio
async def test_get_session_context():
    """Test retrieving session context with strategy."""
    
    mock_session = {
        "session_id": "test-session-123",
        "user_id": "test-user-123",
        "content_strategy": {
            "industry": "B2B SaaS",
            "offering": "AI analytics"
        },
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
    
    with patch('src.api.services.session_service.get_session') as mock_get:
        mock_get.return_value = mock_session
        
        context = await get_session_context("test_collection", "test-session-123")
        
        assert context["session_id"] == "test-session-123"
        assert context["user_id"] == "test-user-123"
        assert context["content_strategy"]["industry"] == "B2B SaaS"
        assert context["exists"] is True


@pytest.mark.asyncio
async def test_get_session_context_not_found():
    """Test retrieving context for non-existent session."""
    
    with patch('src.api.services.session_service.get_session') as mock_get:
        mock_get.return_value = None
        
        context = await get_session_context("test_collection", "non-existent")
        
        assert context["session_id"] == "non-existent"
        assert context["content_strategy"] is None
        assert context["exists"] is False


@pytest.mark.asyncio
async def test_strategy_fetching_utility():
    """Test the strategy fetching utility directly."""
    
    mock_strategy = {
        "clerk_user_id": "test-user-123",
        "industry": "Tech",
        "offering": "Cloud solutions"
    }
    
    # Mock the database
    mock_db = AsyncMock()
    mock_collection = AsyncMock()
    mock_db.__getitem__.return_value = mock_collection
    mock_collection.find_one.return_value = mock_strategy.copy()
    
    with patch('src.utils.mongo_utils_async.get_async_mongo_db') as mock_get_db:
        mock_get_db.return_value = mock_db
        
        # Test fetching strategy
        strategy = await get_user_content_strategy("test-user-123")
        
        # Verify MongoDB was called correctly
        mock_collection.find_one.assert_called_once_with({"clerk_user_id": "test-user-123"})
        
        # Verify _id was removed
        assert "_id" not in strategy
        assert strategy["industry"] == "Tech"


@pytest.mark.asyncio
async def test_create_session_without_user_id():
    """Test that sessions without user_id get content_strategy = None."""
    
    # Mock MongoDB operations
    mock_db = AsyncMock()
    mock_collection = AsyncMock()
    mock_db.__getitem__.return_value = mock_collection
    mock_collection.insert_one.return_value = AsyncMock()
    
    with patch('src.api.services.session_service.get_async_mongo_db') as mock_get_db:
        # Setup mocks
        mock_get_db.return_value = mock_db
        
        # Call create_session without user_id
        session_id = await create_session(
            collection_name="test_collection",
            extra_fields={"brief": "Anonymous session"}
        )
        
        # Verify the session was created with None strategy
        mock_collection.insert_one.assert_called_once()
        created_doc = mock_collection.insert_one.call_args[0][0]
        
        assert "content_strategy" in created_doc
        assert created_doc["content_strategy"] is None
        assert created_doc["brief"] == "Anonymous session"
        assert "user_id" not in created_doc


if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_create_session_with_strategy())
    asyncio.run(test_create_session_with_no_strategy())
    asyncio.run(test_get_session_context())
    asyncio.run(test_get_session_context_not_found())
    asyncio.run(test_strategy_fetching_utility())
    print("All tests passed!")