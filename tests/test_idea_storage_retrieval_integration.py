"""Integration tests for idea storage and retrieval with citations."""
import pytest
import pytest_asyncio
import asyncio
from datetime import datetime, UTC
from bson import ObjectId
from src.utils.mongo_utils_async import get_async_mongo_db
from src.core.models.transcript_document import TranscriptDocument
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem, PostOutline
from src.utils.citation_schema import Citation


class TestIdeaStorageRetrievalIntegration:
    """Integration tests for MongoDB storage and retrieval of ideas with citations."""
    
    @pytest_asyncio.fixture
    async def test_db(self):
        """Get test database and clean up after test."""
        db = await get_async_mongo_db()
        # Use a test collection to avoid affecting real data
        test_collection = db["test_transcripts"]
        
        yield test_collection
        
        # Cleanup after test
        await test_collection.delete_many({})
    
    @pytest.fixture
    def sample_ideas_with_citations(self):
        """Create sample ideas with comprehensive citations."""
        return [
            IdeaItem(
                idea="AI collaboration tools",
                category="Technology",
                engagement=5,
                summary="AI tools are revolutionizing how teams collaborate by providing real-time assistance, automating routine tasks, and enhancing communication. The key is finding the right balance between automation and human creativity.",
                citations=[
                    Citation(
                        text="AI has completely changed how our team works together",
                        timestamp="[00:01:30]",
                        speaker="Sarah"
                    ),
                    Citation(
                        text="We save 3 hours per day on routine tasks now",
                        timestamp="[00:02:15]",
                        speaker="John"
                    ),
                    Citation(
                        text="The real magic happens when AI enhances human creativity rather than replacing it",
                        timestamp="[00:03:45]",
                        speaker="Sarah"
                    ),
                    Citation(
                        text="Our communication is more effective with AI-powered summaries",
                        timestamp="[00:05:00]",
                        speaker="Mike"
                    ),
                    Citation(
                        text="I can focus on strategic work instead of admin tasks",
                        timestamp="[00:06:30]",
                        speaker="John"
                    )
                ],
                suggested_outlines=[
                    PostOutline(
                        title="Team Transformation Story",
                        throughline="From chaos to clarity with AI",
                        sections=["The Old Way", "Discovery Moment", "Implementation Journey", "Results & Insights"]
                    ),
                    PostOutline(
                        title="Practical Implementation Guide",
                        throughline="How to integrate AI into your workflow",
                        sections=["Tool Selection", "Team Training", "Process Integration", "Measuring Success"]
                    ),
                    PostOutline(
                        title="Future of Work Perspective",
                        throughline="What AI collaboration means for teams",
                        sections=["Current State", "Emerging Trends", "Preparing for Change"]
                    )
                ]
            ),
            IdeaItem(
                idea="Remote work culture",
                category="Workplace",
                engagement=4,
                summary="Building strong remote work culture requires intentional communication, trust, and the right tools. Success comes from focusing on outcomes rather than hours.",
                citations=[
                    Citation(
                        text="Trust is the foundation of successful remote teams",
                        timestamp="[00:08:00]",
                        speaker="Lisa"
                    ),
                    Citation(
                        text="We measure results, not time in seat",
                        timestamp="[00:09:30]",
                        speaker="Tom"
                    ),
                    Citation(
                        text="Regular virtual coffee chats keep us connected",
                        timestamp="[00:10:45]",
                        speaker="Lisa"
                    )
                ],
                suggested_outlines=[
                    PostOutline(
                        title="Culture Building Playbook",
                        throughline="Creating connection in distributed teams",
                        sections=["Foundation Principles", "Daily Practices", "Long-term Strategies"]
                    ),
                    PostOutline(
                        title="Leadership Insights",
                        throughline="Managing remote teams effectively",
                        sections=["Trust Building", "Communication Rhythms", "Performance Management"]
                    ),
                    PostOutline(
                        title="Personal Experience",
                        throughline="My remote work transformation",
                        sections=["Initial Challenges", "Key Learnings", "Current Approach"]
                    )
                ]
            )
        ]
    
    @pytest.mark.asyncio
    async def test_store_and_retrieve_transcript_with_citations(self, test_db, sample_ideas_with_citations):
        """Test storing a transcript with ideas containing citations and retrieving it."""
        # Create transcript document
        transcript_doc = TranscriptDocument(
            transcript_content="Test transcript content with AI and remote work discussion...",
            user_email="<EMAIL>",
            ideas=sample_ideas_with_citations
        )
        
        # Store in MongoDB
        doc_dict = transcript_doc.model_dump()
        insert_result = await test_db.insert_one(doc_dict)
        transcript_id = insert_result.inserted_id
        
        assert transcript_id is not None
        
        # Retrieve the document
        retrieved_doc = await test_db.find_one({"_id": transcript_id})
        
        assert retrieved_doc is not None
        assert retrieved_doc["user_email"] == "<EMAIL>"
        assert len(retrieved_doc["ideas"]) == 2
        
        # Verify first idea with citations
        first_idea = retrieved_doc["ideas"][0]
        assert first_idea["idea"] == "AI collaboration tools"
        assert len(first_idea["citations"]) == 5
        assert first_idea["citations"][0]["text"] == "AI has completely changed how our team works together"
        assert first_idea["citations"][0]["speaker"] == "Sarah"
        assert len(first_idea["suggested_outlines"]) == 3
        
        # Verify second idea
        second_idea = retrieved_doc["ideas"][1]
        assert second_idea["idea"] == "Remote work culture"
        assert len(second_idea["citations"]) == 3
    
    @pytest.mark.asyncio
    async def test_find_transcript_by_user_email(self, test_db, sample_ideas_with_citations):
        """Test finding transcripts by user email."""
        # Insert multiple transcripts
        docs = [
            {
                "transcript_content": "First transcript",
                "user_email": "<EMAIL>",
                "ideas": [idea.model_dump() for idea in sample_ideas_with_citations[:1]],
                "createdAt": datetime.now(UTC)
            },
            {
                "transcript_content": "Second transcript",
                "user_email": "<EMAIL>",
                "ideas": [idea.model_dump() for idea in sample_ideas_with_citations[1:]],
                "createdAt": datetime.now(UTC)
            },
            {
                "transcript_content": "Third transcript",
                "user_email": "<EMAIL>",
                "ideas": [idea.model_dump() for idea in sample_ideas_with_citations],
                "createdAt": datetime.utcnow()
            }
        ]
        
        await test_db.insert_many(docs)
        
        # Find transcripts for user1
        cursor = test_db.find({"user_email": "<EMAIL>"})
        user1_transcripts = await cursor.to_list(length=None)
        
        assert len(user1_transcripts) == 2
        assert all(t["user_email"] == "<EMAIL>" for t in user1_transcripts)
    
    @pytest.mark.asyncio
    async def test_find_idea_by_id_within_transcript(self, test_db, sample_ideas_with_citations):
        """Test finding a specific idea by ID within transcripts."""
        # Get idea ID from first idea
        idea_id = sample_ideas_with_citations[0].id
        
        # Store transcript
        transcript_doc = {
            "transcript_content": "Test content",
            "user_email": "<EMAIL>",
            "ideas": [idea.model_dump() for idea in sample_ideas_with_citations],
            "createdAt": datetime.utcnow()
        }
        
        await test_db.insert_one(transcript_doc)
        
        # Find transcript containing the specific idea ID
        found_doc = await test_db.find_one({"ideas.id": idea_id})
        
        assert found_doc is not None
        assert any(idea["id"] == idea_id for idea in found_doc["ideas"])
        
        # Verify the found idea has citations
        found_idea = next(idea for idea in found_doc["ideas"] if idea["id"] == idea_id)
        assert len(found_idea["citations"]) == 5
        assert found_idea["citations"][0]["text"] == "AI has completely changed how our team works together"
    
    @pytest.mark.asyncio
    async def test_update_idea_to_generated_status(self, test_db, sample_ideas_with_citations):
        """Test moving an idea from ideas array to generated_ideas array."""
        # Store initial transcript
        idea_to_move = sample_ideas_with_citations[0]
        idea_id = idea_to_move.id
        
        transcript_doc = {
            "transcript_content": "Test content",
            "user_email": "<EMAIL>",
            "ideas": [idea.model_dump() for idea in sample_ideas_with_citations],
            "generated_ideas": [],
            "createdAt": datetime.utcnow()
        }
        
        result = await test_db.insert_one(transcript_doc)
        doc_id = result.inserted_id
        
        # Move idea to generated_ideas
        update_result = await test_db.update_one(
            {"_id": doc_id},
            {
                "$pull": {"ideas": {"id": idea_id}},
                "$addToSet": {"generated_ideas": idea_to_move.model_dump()}
            }
        )
        
        assert update_result.modified_count == 1
        
        # Verify the move
        updated_doc = await test_db.find_one({"_id": doc_id})
        assert len(updated_doc["ideas"]) == 1  # One idea removed
        assert len(updated_doc["generated_ideas"]) == 1  # One idea added
        assert updated_doc["generated_ideas"][0]["id"] == idea_id
        
        # Verify citations were preserved in the move
        moved_idea = updated_doc["generated_ideas"][0]
        assert len(moved_idea["citations"]) == 5
        assert moved_idea["citations"][0]["speaker"] == "Sarah"
    
    @pytest.mark.asyncio
    async def test_aggregation_with_citation_counts(self, test_db, sample_ideas_with_citations):
        """Test aggregation pipeline that counts citations per idea."""
        # Store transcript
        transcript_doc = {
            "transcript_content": "Test content",
            "user_email": "<EMAIL>",
            "ideas": [idea.model_dump() for idea in sample_ideas_with_citations],
            "createdAt": datetime.utcnow()
        }
        
        await test_db.insert_one(transcript_doc)
        
        # Aggregate to get citation counts per idea
        pipeline = [
            {"$match": {"user_email": "<EMAIL>"}},
            {"$unwind": "$ideas"},
            {
                "$project": {
                    "idea_title": "$ideas.idea",
                    "citation_count": {"$size": "$ideas.citations"},
                    "category": "$ideas.category",
                    "engagement": "$ideas.engagement"
                }
            },
            {"$sort": {"citation_count": -1}}
        ]
        
        cursor = test_db.aggregate(pipeline)
        results = await cursor.to_list(length=None)
        
        assert len(results) == 2
        assert results[0]["idea_title"] == "AI collaboration tools"
        assert results[0]["citation_count"] == 5
        assert results[1]["idea_title"] == "Remote work culture"
        assert results[1]["citation_count"] == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])