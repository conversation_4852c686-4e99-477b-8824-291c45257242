"""Test script to verify Logfire instrumentation for Anthropic API calls."""
import asyncio
import logging
import os
import sys
from datetime import datetime

# Configure basic logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import the AnthropicClient
from src.utils.anthropic_client import AnthropicClient

async def test_anthropic_api_with_logfire():
    """Test function to make a simple Anthropic API call with Logfire instrumentation."""
    logger.info("Initializing AnthropicClient...")
    client = AnthropicClient()

    # Generate a timestamp for the test
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Simple system prompt and user prompt for testing
    system_prompt = "You are a helpful assistant."
    user_prompt = f"This is a test of Logfire instrumentation at {timestamp}. Please respond with a short greeting."

    logger.info("Making API call to Anthropic...")
    try:
        # Make a simple API call
        response = await client.call_claude(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            max_tokens=100,
            temperature=1.0,
            thinking_enabled=False
        )

        # Log the response
        logger.info(f"Received response from Anthropic API: {response.content[0].text[:100]}...")
        logger.info("Test completed successfully. Check the Logfire dashboard to verify instrumentation.")
        return True
    except Exception as e:
        logger.error(f"Error making Anthropic API call: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Logfire instrumentation test for Anthropic API...")
    asyncio.run(test_anthropic_api_with_logfire())
