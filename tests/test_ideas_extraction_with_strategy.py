"""Test ideas extraction with content strategy integration."""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from src.core.ideas_extraction.ideas_extractor import IdeasExtractor
from src.core.ideas_extraction.citations_ideas_extraction_models import IdeaItem
from src.utils.citation_schema import Citation


class TestIdeasExtractionWithStrategy:
    """Test suite for ideas extraction with content strategy integration."""
    
    @pytest.fixture
    def mock_citations_client(self):
        """Create a mock CitationsAnthropic client."""
        client = Mock()
        client.create_transcript_tool_call = AsyncMock()
        return client
    
    @pytest.fixture
    def sample_transcript(self):
        """Sample transcript for testing."""
        return """
[00:00:15] Sarah: I've been thinking about personal branding lately.
[00:00:30] <PERSON>: What specifically about it?
[00:01:00] Sarah: How to share my journey authentically while still being strategic.
[00:01:30] <PERSON>: That's the balance everyone struggles with.
[00:02:00] Sarah: I want to talk about my failures and learnings, not just successes.
[00:02:30] <PERSON>: Vulnerability builds trust. People connect with real stories.
[00:03:00] Sarah: Exactly. Like when I failed at my first startup attempt.
[00:03:30] <PERSON>: What happened?
[00:04:00] Sarah: We built features nobody wanted. <PERSON> mistake - no customer validation.
[00:04:30] <PERSON>: That's a valuable lesson to share.
[00:05:00] <PERSON>: Now I always start with customer interviews and MVPs.
[00:05:30] <PERSON>: Your experience could help others avoid the same pitfall.
[00:06:00] <PERSON>: I'm considering writing a detailed case study about it.
[00:06:30] <PERSON>: With specific metrics and learnings?
[00:07:00] <PERSON>: Yes, showing exactly how we transformed our approach.
"""
    
    @pytest.fixture
    def sample_strategy_context(self):
        """Sample content strategy for testing."""
        return {
            "business_profile": {
                "industry": "B2B SaaS",
                "offering": "Customer feedback analytics platform",
                "target_audience": "Product managers and startup founders",
                "differentiators": "AI-powered insights, real-time feedback loops",
                "content_goal": "Build authority in product development and customer-centric design"
            },
            "funnel_guidance": {
                "top": "Share personal stories and broad insights about product development",
                "middle": "Demonstrate expertise in customer validation and feedback analysis",
                "bottom": "Showcase transformation stories and specific methodologies"
            }
        }
    
    @pytest.fixture
    def mock_api_response_with_strategy(self):
        """Mock API response with strategy-aware categorization."""
        return {
            'ideas': [
                {
                    'idea': 'Vulnerability in personal branding',
                    'category': 'TOFU',
                    'engagement': 5,
                    'summary': 'Authentic personal branding requires balancing vulnerability with strategy. Sharing failures and learnings, not just successes, builds deeper trust and connection with audiences.',
                    'strategic_notes': 'This broad personal story about authenticity aligns with top-of-funnel content goals. It builds trust without being product-specific, perfect for attracting product managers who value genuine leadership.',
                    'citations': [
                        {
                            'text': "How to share my journey authentically while still being strategic.",
                            'timestamp': "[00:01:00]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "I want to talk about my failures and learnings, not just successes.",
                            'timestamp': "[00:02:00]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "Vulnerability builds trust. People connect with real stories.",
                            'timestamp': "[00:02:30]",
                            'speaker': "John"
                        }
                    ],
                    'suggested_outlines': [
                        {
                            'title': 'The Power of Vulnerable Leadership',
                            'throughline': 'From hiding failures to embracing them',
                            'sections': ['The Perfect Facade', 'The Breaking Point', 'Authentic Connection', 'Business Impact']
                        }
                    ]
                },
                {
                    'idea': 'Customer validation methodology',
                    'category': 'MOFU',
                    'engagement': 4,
                    'summary': 'Learning from startup failure: The critical importance of customer validation before building. A shift from feature-first to customer-first development approach.',
                    'strategic_notes': 'This demonstrates domain expertise in customer validation - directly relevant to your target audience of product managers. Middle-funnel content that showcases methodology without selling.',
                    'citations': [
                        {
                            'text': "We built features nobody wanted. Classic mistake - no customer validation.",
                            'timestamp': "[00:04:00]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "Now I always start with customer interviews and MVPs.",
                            'timestamp': "[00:05:00]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "Your experience could help others avoid the same pitfall.",
                            'timestamp': "[00:05:30]",
                            'speaker': "John"
                        }
                    ],
                    'suggested_outlines': [
                        {
                            'title': 'From Feature Factory to Customer Champion',
                            'throughline': 'How failure taught me customer-first development',
                            'sections': ['The Costly Assumption', 'Discovery Process', 'New Framework', 'Results']
                        }
                    ]
                },
                {
                    'idea': 'Transformation case study',
                    'category': 'BOFU',
                    'engagement': 4,
                    'summary': 'Detailed case study showing specific metrics and learnings from transforming product development approach after initial failure.',
                    'strategic_notes': 'Bottom-funnel content opportunity: A detailed case study with metrics would showcase your transformation methodology - perfect for demonstrating how your platform helps achieve similar results.',
                    'citations': [
                        {
                            'text': "I'm considering writing a detailed case study about it.",
                            'timestamp': "[00:06:00]",
                            'speaker': "Sarah"
                        },
                        {
                            'text': "With specific metrics and learnings?",
                            'timestamp': "[00:06:30]",
                            'speaker': "John"
                        },
                        {
                            'text': "Yes, showing exactly how we transformed our approach.",
                            'timestamp': "[00:07:00]",
                            'speaker': "Sarah"
                        }
                    ],
                    'suggested_outlines': [
                        {
                            'title': 'The 10x Validation Transformation',
                            'throughline': 'Metrics and methods from our pivot',
                            'sections': ['Before: The Numbers', 'The Shift', 'Implementation', 'After: Results']
                        }
                    ]
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_extract_ideas_with_strategy_context(self, mock_citations_client, sample_transcript, sample_strategy_context, mock_api_response_with_strategy):
        """Test that idea extraction properly uses strategy context for funnel classification."""
        # Setup
        mock_citations_client.create_transcript_tool_call.return_value = mock_api_response_with_strategy
        extractor = IdeasExtractor(mock_citations_client)
        
        # Execute
        with patch('src.utils.mongo_utils_async.get_async_mongo_db', new_callable=AsyncMock):
            result = await extractor.extract_ideas(sample_transcript, "<EMAIL>", strategy_context=sample_strategy_context)
        
        # Verify the API was called with strategy context
        mock_citations_client.create_transcript_tool_call.assert_called_once()
        call_args = mock_citations_client.create_transcript_tool_call.call_args
        assert 'B2B SaaS' in str(call_args)
        assert 'Customer feedback analytics platform' in str(call_args)
        
        # Verify ideas have funnel classification
        assert len(result.ideas) == 3
        
        # Check TOFU idea
        tofu_idea = result.ideas[0]
        assert tofu_idea.category == 'TOFU'
        assert tofu_idea.strategic_notes is not None
        assert 'top-of-funnel' in tofu_idea.strategic_notes.lower()
        assert 'trust' in tofu_idea.strategic_notes.lower()
        
        # Check MOFU idea
        mofu_idea = result.ideas[1]
        assert mofu_idea.category == 'MOFU'
        assert mofu_idea.strategic_notes is not None
        assert 'middle-funnel' in mofu_idea.strategic_notes.lower()
        assert 'expertise' in mofu_idea.strategic_notes.lower()
        
        # Check BOFU idea
        bofu_idea = result.ideas[2]
        assert bofu_idea.category == 'BOFU'
        assert bofu_idea.strategic_notes is not None
        assert 'bottom-funnel' in bofu_idea.strategic_notes.lower()
        assert 'case study' in bofu_idea.strategic_notes.lower()
    
    @pytest.mark.asyncio
    async def test_extract_ideas_without_strategy_fallback(self, mock_citations_client, sample_transcript):
        """Test that idea extraction works without strategy context (backward compatibility)."""
        # Setup mock response without strategy fields
        mock_response = {
            'ideas': [{
                'idea': 'Personal branding insights',
                'category': 'Personal Development',  # Regular category, not funnel
                'engagement': 4,
                'summary': 'Insights about authentic personal branding.',
                'citations': [
                    {
                        'text': "How to share my journey authentically",
                        'timestamp': "[00:01:00]",
                        'speaker': "Sarah"
                    }
                ],
                'suggested_outlines': [
                    {'title': 'Test', 'throughline': 'Test', 'sections': ['A', 'B', 'C']}
                ]
            }]
        }
        
        mock_citations_client.create_transcript_tool_call.return_value = mock_response
        extractor = IdeasExtractor(mock_citations_client)
        
        # Execute without strategy context
        with patch('src.utils.mongo_utils_async.get_async_mongo_db', new_callable=AsyncMock):
            result = await extractor.extract_ideas(sample_transcript, "<EMAIL>", strategy_context=None)
        
        # Verify it works without strategy
        assert len(result.ideas) == 1
        assert result.ideas[0].category == 'Personal Development'  # Not a funnel stage
        assert result.ideas[0].strategic_notes is None  # No strategic notes without strategy
    
    @pytest.mark.asyncio
    async def test_strategy_context_in_prompt(self, mock_citations_client, sample_strategy_context):
        """Test that strategy context is properly formatted in the prompt."""
        extractor = IdeasExtractor(mock_citations_client)
        
        # Mock the tool call to capture the prompt
        captured_prompt = None
        async def capture_prompt(messages, **kwargs):
            nonlocal captured_prompt
            for msg in messages:
                if msg.get('role') == 'user':
                    captured_prompt = msg.get('content', '')
            return {'ideas': []}
        
        mock_citations_client.create_transcript_tool_call.side_effect = capture_prompt
        
        # Execute
        await extractor.extract_ideas_from_transcript("Test transcript", strategy_context=sample_strategy_context)
        
        # Verify strategy context in prompt
        assert captured_prompt is not None
        assert "CONTENT STRATEGY CONTEXT" in captured_prompt
        assert "B2B SaaS" in captured_prompt
        assert "Customer feedback analytics platform" in captured_prompt
        assert "Product managers and startup founders" in captured_prompt
        assert "Build authority in product development" in captured_prompt


if __name__ == "__main__":
    pytest.main([__file__, "-v"])