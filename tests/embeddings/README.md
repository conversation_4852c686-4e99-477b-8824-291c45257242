# Embeddings Test Suite

This folder contains tests for the MongoDB Atlas Vector Search implementation.

## Test Files

### test_pattern_extraction_from_mongo.py
Extracts individual patterns (hooks, endings, themes) from the MongoDB `LLM_friendly_analyses` collection and saves them as JSON files. This is the first step in the embedding pipeline.

**Usage**: `python test_pattern_extraction_from_mongo.py`

### test_voyage_embedding_simple_pipeline.py
Generates embeddings for extracted patterns using Voyage AI's API and saves them to JSON files. Includes rate limiting to respect API limits.

**Usage**: `python test_voyage_embedding_simple_pipeline.py`

### test_vector_working.py
Tests the vector search functionality with the confirmed working configuration. Demonstrates both self-similarity search and semantic query search.

**Usage**: `python test_vector_working.py`

## Key Scripts (in /scripts/)

### insert_embeddings_to_mongo.py
Inserts patterns with embeddings into MongoDB. This is not a test but a necessary script for populating the vector search collection.

**Usage**: `python scripts/insert_embeddings_to_mongo.py`

## Configuration Files (in root)

### mongodb_vector_index_config.json
The JSON configuration for creating the vector search index in MongoDB Atlas.

## Running the Full Pipeline

1. Extract patterns: `python tests/embeddings/test_pattern_extraction_from_mongo.py`
2. Generate embeddings: `python tests/embeddings/test_voyage_embedding_simple_pipeline.py`
3. Insert to MongoDB: `python scripts/insert_embeddings_to_mongo.py`
4. Create index in Atlas UI using `mongodb_vector_index_config.json`
5. Test search: `python tests/embeddings/test_vector_working.py`