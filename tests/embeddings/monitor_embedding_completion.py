#!/usr/bin/env python3
"""
Monitor embedding process completion.
"""

import time
import subprocess
from pathlib import Path

def is_process_running():
    """Check if embedding process is still running."""
    result = subprocess.run(
        ["pgrep", "-f", "batch_embed_hooks_endings.py"],
        capture_output=True,
        text=True
    )
    return bool(result.stdout.strip())

def check_log_completion():
    """Check log file for completion markers."""
    log_file = Path("outputs/embedding_log.txt")
    if not log_file.exists():
        return False, "No log file"
    
    with open(log_file, 'r') as f:
        content = f.read()
        
    if "✅ Hook and ending embedding pipeline complete!" in content:
        return True, "Completed successfully"
    elif "ERROR" in content[-500:]:  # Check last 500 chars for recent errors
        return True, "Completed with errors"
    else:
        # Get last batch info
        lines = content.strip().split('\n')
        for line in reversed(lines):
            if "Batch" in line and "/" in line:
                return False, line.strip()
        return False, "Processing..."

def main():
    print("Monitoring embedding process...")
    print("-" * 50)
    
    while True:
        is_running = is_process_running()
        is_complete, status = check_log_completion()
        
        print(f"\rProcess: {'RUNNING' if is_running else 'STOPPED'} | Status: {status}", end='', flush=True)
        
        if is_complete or not is_running:
            print("\n" + "-" * 50)
            if is_complete and "successfully" in status.lower():
                print("✅ Embedding process completed successfully!")
                print("\nNext steps:")
                print("1. Run: python create_vector_search_index.py")
                print("2. Test vector search functionality")
            elif not is_running and not is_complete:
                print("⚠️  Process stopped unexpectedly. Check outputs/embedding_log.txt")
            break
            
        time.sleep(5)

if __name__ == "__main__":
    main()