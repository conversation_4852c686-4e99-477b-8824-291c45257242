#!/usr/bin/env python3
"""
Debug ending analysis structure to understand field naming
"""

import os
from pymongo import MongoClient
import json

def debug_ending_structure():
    mongo_uri = os.getenv('MONGODB_URI')
    if not mongo_uri:
        raise ValueError("MONGODB_URI environment variable not set")
    
    client = MongoClient(mongo_uri)
    db = client["linkedinsight"]
    collection = db["LLM_friendly_analyses"]
    
    # Find a few ending analyses
    ending_analyses = list(collection.find(
        {"analysis_type": "ending"}, 
        {"creator_name": 1, "llm_analysis_data": 1}
    ).limit(3))
    
    print(f"Found {len(ending_analyses)} ending analyses\n")
    
    for analysis in ending_analyses:
        creator = analysis.get("creator_name", "unknown")
        print(f"\n=== {creator} - Ending Analysis ===")
        
        analysis_data = analysis.get("llm_analysis_data", {})
        
        # Print all keys in llm_analysis_data
        print("\nTop-level keys in llm_analysis_data:")
        for key in sorted(analysis_data.keys()):
            value = analysis_data[key]
            value_type = type(value).__name__
            preview = ""
            
            if isinstance(value, list):
                preview = f" (length: {len(value)})"
                if value and isinstance(value[0], dict):
                    # Show keys of first item
                    first_item_keys = list(value[0].keys())
                    preview += f" - first item keys: {first_item_keys}"
            elif isinstance(value, str):
                preview = f" - '{value[:50]}...'" if len(value) > 50 else f" - '{value}'"
                
            print(f"  '{key}': {value_type}{preview}")
        
        # Look for anything that might be pattern/archetype data
        print("\nPotential pattern fields:")
        for key, value in analysis_data.items():
            if any(word in key.lower() for word in ['archetype', 'pattern', 'ending', 'structure', 'type']):
                print(f"\n  Found: '{key}'")
                if isinstance(value, list) and value:
                    print(f"    Items: {len(value)}")
                    if isinstance(value[0], dict):
                        print(f"    First item: {json.dumps(value[0], indent=6)[:500]}...")

if __name__ == "__main__":
    debug_ending_structure()