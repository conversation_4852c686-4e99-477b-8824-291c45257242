#!/usr/bin/env python3
"""
Test script to extract patterns from MongoDB analyses and save locally.
This is a READ-ONLY test - no modifications to MongoDB.
"""

import json
from datetime import datetime, timezone
from typing import Dict, List, Any
from pathlib import Path
from bson import ObjectId

from src.utils.mongo_utils import get_mongo_db
from src.utils.enhanced_logging import get_logger

logger = get_logger(__name__)

class PatternExtractor:
    """Extract individual patterns from MongoDB analyses."""
    
    def __init__(self, output_dir: str = "outputs/extracted_patterns_test"):
        self.db = get_mongo_db()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def extract_hook_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract individual hook patterns from hook analysis."""
        patterns = []
        
        # Get the actual analysis data
        analysis_data = analysis.get("llm_analysis_data", {})
        creator_name = analysis.get("creator_name")
        
        # Get the hook archetypes
        archetypes = analysis_data.get("2_dominant_hook_archetypes", [])
        
        for idx, archetype in enumerate(archetypes):
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "hook",
                "pattern_index": idx,
                "pattern_name": archetype.get("archetype_name", f"Hook Pattern {idx}"),
                "content": {
                    "description": archetype.get("description_and_key_elements", ""),
                    "example": archetype.get("creator_example_best", ""),
                    "psychological_impact": archetype.get("typical_psychological_impact", ""),
                    "frequency": archetype.get("estimated_frequency_category", "Unknown")
                },
                "metadata": {
                    "extracted_from": "hook_analysis",
                    "extracted_at": datetime.now(timezone.utc).isoformat(),
                    "source_analysis_id": str(analysis.get("_id", ""))
                },
                # This will be used for embedding generation
                "searchable_text": " ".join([
                    archetype.get("archetype_name", ""),
                    archetype.get("description_and_key_elements", ""),
                    archetype.get("creator_example_best", "")
                ])
            }
            patterns.append(pattern)
            
        return patterns
    
    def extract_theme_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract individual theme patterns from themes analysis."""
        patterns = []
        
        # Get the actual analysis data
        analysis_data = analysis.get("llm_analysis_data", {})
        creator_name = analysis.get("creator_name")
        
        # Get the dominant themes
        themes = analysis_data.get("2_dominant_themes", [])
        
        for idx, theme in enumerate(themes):
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "theme",
                "pattern_index": idx,
                "pattern_name": theme.get("theme_name", f"Theme {idx}"),
                "content": {
                    "description": theme.get("theme_description", ""),
                    "manifestation": theme.get("how_it_manifests", ""),
                    "examples": theme.get("representative_quotes", []),
                    "frequency": theme.get("frequency_category", "Unknown")
                },
                "metadata": {
                    "extracted_from": "themes_analysis",
                    "extracted_at": datetime.now(timezone.utc).isoformat(),
                    "source_analysis_id": str(analysis.get("_id", ""))
                },
                "searchable_text": " ".join([
                    theme.get("theme_name", ""),
                    theme.get("theme_description", ""),
                    theme.get("how_it_manifests", "")
                ])
            }
            patterns.append(pattern)
            
        return patterns
    
    def extract_ending_patterns(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract individual ending patterns from ending analysis."""
        patterns = []
        
        # Get the actual analysis data
        analysis_data = analysis.get("llm_analysis_data", {})
        creator_name = analysis.get("creator_name")
        
        # Get the ending types
        ending_types = analysis_data.get("2_dominant_ending_types", [])
        
        for idx, ending in enumerate(ending_types):
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "ending",
                "pattern_index": idx,
                "pattern_name": ending.get("ending_type_name", f"Ending {idx}"),
                "content": {
                    "description": ending.get("description_and_characteristics", ""),
                    "example": ending.get("creator_example", ""),
                    "psychological_effect": ending.get("psychological_effect", ""),
                    "frequency": ending.get("frequency_category", "Unknown")
                },
                "metadata": {
                    "extracted_from": "ending_analysis",
                    "extracted_at": datetime.now(timezone.utc).isoformat(),
                    "source_analysis_id": str(analysis.get("_id", ""))
                },
                "searchable_text": " ".join([
                    ending.get("ending_type_name", ""),
                    ending.get("description_and_characteristics", ""),
                    ending.get("creator_example", "")
                ])
            }
            patterns.append(pattern)
            
        return patterns
    
    def save_patterns_locally(self, creator_id: str, patterns: List[Dict[str, Any]]):
        """Save extracted patterns to local JSON files."""
        creator_dir = self.output_dir / creator_id
        
        # Group patterns by type
        patterns_by_type = {}
        for pattern in patterns:
            pattern_type = pattern["analysis_type"]
            if pattern_type not in patterns_by_type:
                patterns_by_type[pattern_type] = []
            patterns_by_type[pattern_type].append(pattern)
        
        # Save each type to its own file
        for pattern_type, type_patterns in patterns_by_type.items():
            type_dir = creator_dir / f"{pattern_type}_patterns"
            type_dir.mkdir(parents=True, exist_ok=True)
            
            # Save individual pattern files
            for pattern in type_patterns:
                filename = f"pattern_{pattern['pattern_index']}_{pattern['pattern_name'].lower().replace(' ', '_')}.json"
                # Clean filename
                filename = "".join(c for c in filename if c.isalnum() or c in ['_', '-', '.']).lower()
                
                filepath = type_dir / filename
                with open(filepath, 'w') as f:
                    # Convert ObjectId to string for JSON serialization
                    pattern_json = json.loads(json.dumps(pattern, default=str))
                    json.dump(pattern_json, f, indent=2)
                    
                logger.info(f"Saved pattern: {filepath}")
        
        # Also save a summary file
        summary_file = creator_dir / "extraction_summary.json"
        summary = {
            "creator_id": creator_id,
            "extraction_timestamp": datetime.now(timezone.utc).isoformat(),
            "pattern_counts": {k: len(v) for k, v in patterns_by_type.items()},
            "total_patterns": len(patterns)
        }
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
            
    def test_extraction(self, creator_ids: List[str] = ["joshlowman", "retentionadam"]):
        """Test pattern extraction on a few creators."""
        
        for creator_id in creator_ids:
            logger.info(f"\nProcessing creator: {creator_id}")
            all_patterns = []
            
            # Get analyses from MongoDB
            analyses = self.db.LLM_friendly_analyses.find({"creator_name": creator_id})
            
            for analysis in analyses:
                analysis_type = analysis.get("analysis_type", "")
                logger.info(f"  Processing {analysis_type} analysis...")
                
                if analysis_type == "hook":
                    patterns = self.extract_hook_patterns(analysis)
                    all_patterns.extend(patterns)
                    
                elif analysis_type == "themes":
                    patterns = self.extract_theme_patterns(analysis)
                    all_patterns.extend(patterns)
                    
                elif analysis_type == "ending":
                    patterns = self.extract_ending_patterns(analysis)
                    all_patterns.extend(patterns)
                    
                # TODO: Add linguistic patterns extraction
                
            # Save all patterns for this creator
            if all_patterns:
                self.save_patterns_locally(creator_id, all_patterns)
                logger.info(f"Extracted {len(all_patterns)} patterns for {creator_id}")
            else:
                logger.warning(f"No patterns found for {creator_id}")
                
        logger.info(f"\nExtraction complete! Check {self.output_dir}")

if __name__ == "__main__":
    extractor = PatternExtractor()
    extractor.test_extraction()