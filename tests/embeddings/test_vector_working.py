#!/usr/bin/env python3
"""
Test vector search with confirmed working index.
"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv
import voyageai

# Load environment variables
load_dotenv()

# Initialize clients
client = MongoClient(os.getenv("MONGODB_URI"))
db = client["linkedinsight"]  # Note: different database name
collection = db["pattern_embeddings_test"]
voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))

print("Vector Search Test - Confirmed Working Index")
print("=" * 60)
print(f"Database: linkedinsight")
print(f"Collection: pattern_embeddings_test")
print(f"Index: pattern_embeddings_vector_index")
print(f"Status: READY with 8 documents indexed")

# 1. Verify we're looking at the right collection
doc_count = collection.count_documents({})
print(f"\nDocuments in collection: {doc_count}")

if doc_count == 0:
    print("\n⚠️  No documents found! Wrong database?")
    print("The index is on 'linkedinsight' database, not 'storyd_ai'")
    exit(1)

# 2. Get a sample document
sample = collection.find_one()
if sample:
    print(f"\nSample document:")
    print(f"  Pattern: {sample.get('pattern_name')}")
    print(f"  Creator: {sample.get('creator_id')}")
    print(f"  Has embedding: {'embedding' in sample}")

# 3. Test with self-search first
print(f"\n\nTest 1: Self-similarity search")
print("-" * 40)

if sample and 'embedding' in sample:
    pipeline = [
        {
            "$vectorSearch": {
                "index": "pattern_embeddings_vector_index",
                "path": "embedding",
                "queryVector": sample['embedding'],
                "numCandidates": 20,
                "limit": 3
            }
        },
        {
            "$project": {
                "pattern_name": 1,
                "creator_id": 1,
                "score": {"$meta": "vectorSearchScore"}
            }
        }
    ]
    
    try:
        results = list(collection.aggregate(pipeline))
        print(f"Found {len(results)} results")
        
        for i, r in enumerate(results, 1):
            print(f"\n{i}. {r['pattern_name']} ({r['creator_id']})")
            print(f"   Score: {r['score']:.6f}")
            
    except Exception as e:
        print(f"Error: {e}")

# 4. Test with a new query
print(f"\n\nTest 2: Query search")
print("-" * 40)

query = "Start with a bold contrarian statement"
print(f"Query: '{query}'")

# Generate embedding
result = voyage_client.embed([query], model="voyage-3.5")
query_embedding = result.embeddings[0]
print(f"Generated embedding: {len(query_embedding)} dimensions")

pipeline = [
    {
        "$vectorSearch": {
            "index": "pattern_embeddings_vector_index",
            "path": "embedding",
            "queryVector": query_embedding,
            "numCandidates": 50,
            "limit": 5
        }
    },
    {
        "$project": {
            "pattern_name": 1,
            "creator_id": 1,
            "analysis_type": 1,
            "content.description": 1,
            "score": {"$meta": "vectorSearchScore"}
        }
    }
]

try:
    results = list(collection.aggregate(pipeline))
    print(f"\nFound {len(results)} results")
    
    for i, r in enumerate(results, 1):
        print(f"\n{i}. {r['pattern_name']} ({r['creator_id']})")
        print(f"   Type: {r.get('analysis_type')}")
        print(f"   Score: {r['score']:.6f}")
        desc = r.get('content', {}).get('description', '')
        if desc:
            print(f"   Description: {desc[:100]}...")
            
except Exception as e:
    print(f"Error: {e}")