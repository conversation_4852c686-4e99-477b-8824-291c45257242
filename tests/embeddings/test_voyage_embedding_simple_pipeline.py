#!/usr/bin/env python3
"""
Simplified test script to generate embeddings using Voyage AI.
No MongoDB or enhanced logging dependencies.
"""

import os
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any
from pathlib import Path
import voyageai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class SimpleVoyageEmbeddingPipeline:
    """Generate embeddings for patterns using Voyage AI."""
    
    def __init__(self):
        # Initialize Voyage AI client - will use VOYAGE_AI_API_KEY from env
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
        
    def load_extracted_patterns(self, patterns_dir: str = "outputs/extracted_patterns_test") -> List[Dict[str, Any]]:
        """Load previously extracted patterns from local files."""
        patterns = []
        patterns_path = Path(patterns_dir)
        
        # Just load a small sample for testing
        creators_to_test = ["retentionadam", "joshlowman"]
        pattern_types_to_test = ["hook", "ending"]  # Skip themes for now until we fix extraction
        max_patterns_per_type = 2
        
        for creator in creators_to_test:
            creator_path = patterns_path / creator
            if not creator_path.exists():
                continue
                
            for pattern_type in pattern_types_to_test:
                type_dir = creator_path / f"{pattern_type}_patterns"
                if not type_dir.exists():
                    continue
                    
                pattern_files = list(type_dir.glob("*.json"))[:max_patterns_per_type]
                for pattern_file in pattern_files:
                    with open(pattern_file, 'r') as f:
                        pattern = json.load(f)
                        patterns.append(pattern)
                        
        print(f"Loaded {len(patterns)} patterns for testing")
        for p in patterns[:2]:  # Show first 2 patterns
            print(f"  - {p['creator_id']}: {p['pattern_name']} ({p['analysis_type']})")
        return patterns
    
    def generate_embeddings(self, texts: List[str], model: str = "voyage-3.5") -> List[List[float]]:
        """Generate embeddings using Voyage AI."""
        try:
            print(f"Calling Voyage AI with {len(texts)} texts using model {model}")
            # Voyage AI can handle batch embedding
            result = self.voyage_client.embed(texts, model=model)
            print(f"Successfully generated {len(result.embeddings)} embeddings")
            return result.embeddings
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            raise
    
    def prepare_patterns_for_mongodb(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add embeddings to patterns and prepare for MongoDB insertion."""
        # Process in smaller batches to respect rate limits
        batch_size = 2  # Start small with 3 RPM limit
        patterns_with_embeddings = []
        
        print(f"\nGenerating embeddings for {len(patterns)} patterns in batches of {batch_size}...")
        
        for i in range(0, len(patterns), batch_size):
            batch = patterns[i:i+batch_size]
            texts = [p.get("searchable_text", "") for p in batch]
            
            print(f"\nProcessing batch {i//batch_size + 1}/{(len(patterns) + batch_size - 1)//batch_size}")
            print(f"Text lengths: {[len(t) for t in texts]}")
            
            try:
                embeddings = self.generate_embeddings(texts)
                
                # Add embeddings to patterns
                for pattern, embedding in zip(batch, embeddings):
                    pattern["embedding"] = embedding
                    pattern["embedding_model"] = "voyage-3.5"
                    pattern["embedding_dimensions"] = len(embedding)
                    pattern["indexed_at"] = datetime.now(timezone.utc)
                    patterns_with_embeddings.append(pattern)
                
                # Sleep to respect rate limit (3 RPM = 20 seconds between requests)
                if i + batch_size < len(patterns):
                    print("Waiting 20 seconds to respect rate limit...")
                    time.sleep(20)
                    
            except Exception as e:
                print(f"Failed to generate embeddings for batch: {e}")
                raise
            
        return patterns_with_embeddings
    
    def save_patterns_with_embeddings(self, patterns: List[Dict[str, Any]], output_dir: str = "outputs/patterns_with_embeddings"):
        """Save patterns with embeddings to JSON files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save all patterns to a single file
        output_file = output_path / "patterns_with_embeddings.json"
        
        # Convert datetime objects to strings for JSON serialization
        for pattern in patterns:
            if isinstance(pattern.get("indexed_at"), datetime):
                pattern["indexed_at"] = pattern["indexed_at"].isoformat()
        
        with open(output_file, 'w') as f:
            json.dump(patterns, f, indent=2)
            
        print(f"\nSaved {len(patterns)} patterns with embeddings to {output_file}")
        
        # Also save a summary without the full embeddings for easy viewing
        summary_file = output_path / "patterns_summary.json"
        summary_patterns = []
        for p in patterns:
            summary = p.copy()
            if "embedding" in summary:
                summary["embedding"] = f"[{len(p['embedding'])} dimensions]"
            summary_patterns.append(summary)
            
        with open(summary_file, 'w') as f:
            json.dump(summary_patterns, f, indent=2)
            
        print(f"Saved summary to {summary_file}")
    
    def run_test_pipeline(self):
        """Run the complete test pipeline."""
        print("Starting simplified Voyage AI embedding test pipeline...")
        
        # 1. Load extracted patterns
        try:
            patterns = self.load_extracted_patterns()
        except Exception as e:
            print(f"Failed to load patterns: {e}")
            raise
            
        if not patterns:
            print("No patterns found. Run pattern extraction first.")
            return
            
        # 2. Generate embeddings and prepare for MongoDB
        patterns_with_embeddings = self.prepare_patterns_for_mongodb(patterns)
        
        # 3. Save results to files (instead of MongoDB for now)
        self.save_patterns_with_embeddings(patterns_with_embeddings)
        
        print("\n✅ Success! Embeddings generated and saved.")
        print("\nNext steps:")
        print("1. Review the generated embeddings in outputs/patterns_with_embeddings/")
        print("2. Create vector search index in MongoDB Atlas")
        print("3. Insert patterns into MongoDB using the generated files")

if __name__ == "__main__":
    pipeline = SimpleVoyageEmbeddingPipeline()
    pipeline.run_test_pipeline()