#!/usr/bin/env python3
"""
Test using <PERSON>'s native citations to extract body patterns from markdown analysis.

WARNING: This test uses the deprecated AgentService.load_creator_analyses method.
This should be updated to use the vector-based pattern retrieval system.
"""

import asyncio
import json
import re
from typing import List, Dict, Any
from src.api.agent_service import AgentService
from anthropic import Anthropic
import os


async def test_native_citation_pattern_selection():
    """Test extracting body patterns using <PERSON>'s native citation feature."""
    
    print("🔍 Testing Claude Native Citation-Based Body Pattern Selection")
    print("=" * 60)
    
    # Test parameters
    creator_name = "retentionadam"
    test_context = {
        "theme": "Growth Strategies",
        "hook": "Most founders think they need VC money to reach $1M ARR. Here's how we did it in 6 months without a single investor.",
        "user_input": "How we grew our SaaS from 0 to $1M ARR in 6 months without raising any VC money"
    }
    
    try:
        # DEPRECATED: Load creator analyses using old file-based approach
        print(f"\n1. Loading analyses for {creator_name} (using deprecated method)...")
        print("WARNING: This uses deprecated file-based analysis loading")
        analyses = await AgentService.load_creator_analyses(creator_name)
        
        # Get body analysis (markdown)
        body_markdown = analyses.get("body", "")
        if not body_markdown:
            print("❌ No body analysis found")
            return
            
        print(f"✅ Loaded body analysis: {len(body_markdown)} characters")
        
        # Create the message with document and citations enabled
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "document",
                        "cache_control": {"type": "ephemeral"},
                        "source": {
                            "type": "text",
                            "media_type": "text/plain",
                            "data": body_markdown
                        },
                        "citations": {"enabled": True}
                    },
                    {
                        "type": "text",
                        "text": f"""You are a pattern selection assistant. Select the 2 most relevant body structural patterns from the document for generating content.

CONTEXT:
- Theme: {test_context['theme']}
- Hook: {test_context['hook']}
- User Brief: {test_context['user_input']}

TASK:
1. Read through all the structural patterns in the document (look for sections like "### 1. [Pattern Name]")
2. Select the 2 most relevant patterns based on:
   - Theme compatibility (Growth Strategies)
   - Natural flow from the hook
   - Applicability to the user's brief about SaaS growth

3. For each selected pattern, cite the ENTIRE pattern section verbatim, including:
   - The pattern name and number
   - The full description
   - Implementation Pattern details
   - Theme Compatibility ratings
   - Character Count guidelines
   - Any examples or additional details

Quote the complete pattern sections. Start each citation from the pattern heading (e.g., "### 1. Pattern Name") and include everything until the next pattern begins."""
                    }
                ]
            }
        ]
        
        # Initialize Anthropic client with citations enabled
        print("\n2. Calling Claude with native citations enabled...")
        client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        
        response = client.messages.create(
            model="claude-3-5-haiku-latest",
            max_tokens=4000,
            temperature=0.0,
            messages=messages
        )
        
        # Extract the response text
        response_text = response.content[0].text
        print("\n3. Response received:")
        print("-" * 40)
        print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
        
        # Parse citations from the response
        citations = re.findall(r'<cite>(.*?)</cite>', response_text, re.DOTALL)
        
        print(f"\n4. Extracted {len(citations)} pattern citations")
        
        # Display extracted patterns
        for i, citation in enumerate(citations, 1):
            print(f"\n--- Pattern {i} (first 300 chars) ---")
            print(citation.strip()[:300] + "..." if len(citation) > 300 else citation.strip())
            
        # Calculate reduction
        if citations:
            original_size = len(body_markdown)
            selected_size = sum(len(c) for c in citations)
            reduction = (1 - selected_size/original_size) * 100
            
            print(f"\n5. Size comparison:")
            print(f"   Original markdown: {original_size:,} characters")
            print(f"   Selected patterns: {selected_size:,} characters")
            print(f"   Reduction: {reduction:.0f}%")
            
            # Parse pattern names from citations
            pattern_names = []
            for citation in citations:
                match = re.match(r'###\s+\d+\.\s+(.+?)[\n\r]', citation.strip())
                if match:
                    pattern_names.append(match.group(1))
            
            print(f"\n6. Selected patterns:")
            for name in pattern_names:
                print(f"   ✓ {name}")
        
        return citations
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def test_citation_format_validation():
    """Validate that citations preserve the exact markdown format."""
    
    print("\n\n📋 Testing Citation Format Preservation")
    print("=" * 60)
    
    # Create a sample pattern to test exact preservation
    test_markdown = """### 1. Problem-Solution Framework

This structure establishes a clear problem, explores its implications, then presents your solution systematically.

**Implementation Pattern:**
- Opening: State the core problem clearly
- Middle: Break down why this problem matters (2-3 implications)
- Resolution: Present your solution with specific steps or components
- Evidence: Include one concrete example or result

**Theme Compatibility:**
- Business Strategy (5/5)
- Growth Strategies (5/5)
- Sales Excellence (4/5)

**Character Count:** 1200-2000 characters"""

    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "document",
                    "cache_control": {"type": "ephemeral"},
                    "source": {
                        "type": "text",
                        "media_type": "text/plain",
                        "data": test_markdown
                    },
                    "citations": {"enabled": True}
                },
                {
                    "type": "text",
                    "text": "Please cite this exact pattern verbatim:"
                }
            ]
        }
    ]
    
    client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
    
    response = client.messages.create(
        model="claude-3-5-haiku-latest",
        max_tokens=1000,
        temperature=0.0,
        messages=messages
    )
    
    response_text = response.content[0].text
    citations = re.findall(r'<cite>(.*?)</cite>', response_text, re.DOTALL)
    
    if citations:
        cited_text = citations[0].strip()
        print("✅ Citation extracted successfully")
        print(f"Original length: {len(test_markdown)}")
        print(f"Citation length: {len(cited_text)}")
        print(f"Exact match: {cited_text == test_markdown}")
    else:
        print("❌ No citations found in response")


if __name__ == "__main__":
    asyncio.run(test_native_citation_pattern_selection())
    asyncio.run(test_citation_format_validation())