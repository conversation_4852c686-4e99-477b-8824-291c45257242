#!/usr/bin/env python3
"""
Embed ALL hook and ending patterns from all creators in MongoDB.
This focuses on what we know works well.
"""

import os
import time
from pymongo import MongoClient
from typing import Dict, List, Any
import voyageai
from datetime import datetime, timezone
import json

class HookEndingEmbedder:
    def __init__(self):
        # Initialize MongoDB
        mongo_uri = os.getenv('MONGODB_URI')
        if not mongo_uri:
            raise ValueError("MONGODB_URI environment variable not set")
        
        self.client = MongoClient(mongo_uri)
        self.db = self.client["linkedinsight"]
        self.analyses_collection = self.db["LLM_friendly_analyses"]
        self.embeddings_collection = self.db["pattern_embeddings_test"]
        
        # Initialize Voyage AI
        voyage_api_key = os.getenv('VOYAGE_AI_API_KEY')
        if not voyage_api_key:
            raise ValueError("VOYAGE_AI_API_KEY environment variable not set")
        
        self.voyage_client = voyageai.Client(api_key=voyage_api_key)
        
    def get_all_creators(self) -> List[str]:
        """Get all unique creator IDs from the analyses collection"""
        creators = self.analyses_collection.distinct("creator_name")
        return sorted(creators)
    
    def extract_hook_patterns(self, creator_name: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract hook patterns from an analysis"""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        archetypes = analysis_data.get("2_dominant_hook_archetypes", [])
        
        for idx, archetype in enumerate(archetypes):
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "hook",
                "pattern_index": idx,
                "pattern_name": archetype.get("archetype_name"),
                "content": {
                    "description": archetype.get("description", ""),
                    "example": archetype.get("example", ""),
                    "psychological_impact": archetype.get("psychological_impact", ""),
                    "frequency": archetype.get("frequency", "")
                },
                "metadata": {
                    "extracted_from": "hook_analysis",
                    "extracted_at": datetime.now(timezone.utc).isoformat(),
                    "source_analysis_id": str(analysis["_id"])
                },
                "searchable_text": " ".join([
                    archetype.get("archetype_name", ""),
                    archetype.get("description", ""),
                    archetype.get("example", "")
                ])
            }
            patterns.append(pattern)
        
        return patterns
    
    def extract_ending_patterns(self, creator_name: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract ending patterns from an analysis"""
        patterns = []
        analysis_data = analysis.get("llm_analysis_data", {})
        archetypes = analysis_data.get("2_primary_ending_archetypes", [])
        
        for idx, archetype in enumerate(archetypes):
            pattern = {
                "creator_id": creator_name,
                "analysis_type": "ending",
                "pattern_index": idx,
                "pattern_name": archetype.get("archetype_name"),
                "content": {
                    "description": archetype.get("description", ""),
                    "example": archetype.get("example", ""),
                    "emotional_impact": archetype.get("emotional_impact", ""),
                    "frequency": archetype.get("frequency", "")
                },
                "metadata": {
                    "extracted_from": "ending_analysis",
                    "extracted_at": datetime.now(timezone.utc).isoformat(),
                    "source_analysis_id": str(analysis["_id"])
                },
                "searchable_text": " ".join([
                    archetype.get("archetype_name", ""),
                    archetype.get("description", ""),
                    archetype.get("example", "")
                ])
            }
            patterns.append(pattern)
        
        return patterns
    
    def generate_embeddings(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate embeddings for patterns using Voyage AI"""
        if not patterns:
            return []
        
        # Extract texts for embedding
        texts = [p["searchable_text"] for p in patterns]
        
        # Generate embeddings (with rate limiting)
        print(f"Generating embeddings for {len(texts)} patterns...")
        result = self.voyage_client.embed(texts, model="voyage-3.5")
        embeddings = result.embeddings
        
        # Add embeddings to patterns
        for i, pattern in enumerate(patterns):
            pattern["embedding"] = embeddings[i]
            pattern["embedding_model"] = "voyage-3.5"
            pattern["embedding_dimensions"] = 1024
            pattern["indexed_at"] = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        return patterns
    
    def embed_all_hooks_and_endings(self, clear_existing=False):
        """Main function to embed all hooks and endings"""
        if clear_existing:
            print("Clearing existing embeddings...")
            result = self.embeddings_collection.delete_many({})
            print(f"Deleted {result.deleted_count} existing patterns")
        
        creators = self.get_all_creators()
        print(f"\nFound {len(creators)} creators to process")
        
        # Check what's already embedded
        existing = list(self.embeddings_collection.find({}, {"creator_id": 1, "analysis_type": 1}))
        existing_keys = {(p["creator_id"], p["analysis_type"]) for p in existing}
        print(f"Already have {len(existing)} patterns embedded")
        
        total_processed = 0
        total_skipped = 0
        
        for creator in creators:
            print(f"\n--- Processing {creator} ---")
            
            # Check if we already have this creator's patterns
            hook_exists = (creator, "hook") in existing_keys
            ending_exists = (creator, "ending") in existing_keys
            
            if hook_exists and ending_exists:
                print(f"  Skipping {creator} - already embedded")
                total_skipped += 1
                continue
            
            # Get analyses for this creator
            hook_analysis = self.analyses_collection.find_one({
                "creator_name": creator,
                "analysis_type": "hook"
            })
            
            ending_analysis = self.analyses_collection.find_one({
                "creator_name": creator,
                "analysis_type": "ending"
            })
            
            patterns_to_embed = []
            
            # Extract patterns
            if hook_analysis and not hook_exists:
                hook_patterns = self.extract_hook_patterns(creator, hook_analysis)
                patterns_to_embed.extend(hook_patterns)
                print(f"  Found {len(hook_patterns)} hook patterns")
            
            if ending_analysis and not ending_exists:
                ending_patterns = self.extract_ending_patterns(creator, ending_analysis)
                patterns_to_embed.extend(ending_patterns)
                print(f"  Found {len(ending_patterns)} ending patterns")
            
            if patterns_to_embed:
                # Generate embeddings
                embedded_patterns = self.generate_embeddings(patterns_to_embed)
                
                # Store in MongoDB
                if embedded_patterns:
                    result = self.embeddings_collection.insert_many(embedded_patterns)
                    print(f"  Stored {len(result.inserted_ids)} patterns")
                    total_processed += len(result.inserted_ids)
                
                # Rate limiting - Voyage AI allows 300 RPM, but let's be conservative
                time.sleep(20)  # Wait 20 seconds between creators
            else:
                print(f"  No patterns to embed for {creator}")
        
        print(f"\n=== SUMMARY ===")
        print(f"Total patterns processed: {total_processed}")
        print(f"Creators skipped (already embedded): {total_skipped}")
        
        # Final count
        final_count = self.embeddings_collection.count_documents({})
        print(f"Total patterns in database: {final_count}")

if __name__ == "__main__":
    embedder = HookEndingEmbedder()
    # Clear existing voyage-3 embeddings and start fresh with voyage-3.5
    embedder.embed_all_hooks_and_endings(clear_existing=True)