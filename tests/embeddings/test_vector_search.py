#!/usr/bin/env python3
"""
Test vector search functionality with sample queries.
"""

import os
import voyageai
from pymongo import MongoClient
from dotenv import load_dotenv
from pprint import pprint

# Load environment variables
load_dotenv()

def test_vector_search():
    # Initialize clients
    voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
    mongo_client = MongoClient(os.getenv("MONGODB_URI"))
    db = mongo_client["linkedinsight"]
    collection = db["pattern_embeddings"]
    
    # Test queries
    test_queries = [
        "How to write a hook that challenges conventional wisdom",
        "Best way to end a post with a call to action",
        "Creating engaging questions for LinkedIn",
        "Bootstrapping vs VC funding debate"
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"Query: {query}")
        print(f"{'='*60}")
        
        # Generate embedding for query
        result = voyage_client.embed([query], model="voyage-3.5")
        query_embedding = result.embeddings[0]
        
        # Perform vector search
        pipeline = [
            {
                "$vectorSearch": {
                    "index": "pattern_embeddings_vector_index",
                    "path": "embedding",
                    "queryVector": query_embedding,
                    "numCandidates": 50,
                    "limit": 5
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "pattern_name": 1,
                    "creator_id": 1,
                    "analysis_type": 1,
                    "content.description": 1,
                    "score": {"$meta": "vectorSearchScore"}
                }
            }
        ]
        
        results = list(collection.aggregate(pipeline))
        
        print(f"\nTop {len(results)} matches:")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['pattern_name']} ({result['analysis_type']})")
            print(f"   Creator: {result['creator_id']}")
            print(f"   Score: {result['score']:.4f}")
            description = result.get('content', {}).get('description', '')
            if description:
                print(f"   Description: {description[:100]}...")
    
    mongo_client.close()

if __name__ == "__main__":
    test_vector_search()