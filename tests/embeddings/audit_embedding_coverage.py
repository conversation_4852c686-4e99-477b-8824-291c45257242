#!/usr/bin/env python3
"""
Audit current embedding coverage in MongoDB
"""

import os
from pymongo import MongoClient
from collections import defaultdict
from typing import Dict, List, Any
import json

def audit_embeddings():
    """Check what patterns are already embedded in MongoDB"""
    
    # Connect to MongoDB
    mongo_uri = os.getenv('MONGODB_URI')
    if not mongo_uri:
        print("MONGODB_URI environment variable not set")
        return
    
    client = MongoClient(mongo_uri)
    db = client["linkedinsight"]
    collection = db["pattern_embeddings_test"]
    
    # Get all embedded patterns
    all_patterns = list(collection.find({}))
    
    print(f"\n=== EMBEDDING COVERAGE AUDIT ===")
    print(f"Total embedded patterns: {len(all_patterns)}")
    
    # Breakdown by creator
    by_creator = defaultdict(list)
    for pattern in all_patterns:
        creator = pattern.get("creator_id", "unknown")
        by_creator[creator].append(pattern)
    
    print(f"\n--- By Creator ---")
    for creator, patterns in sorted(by_creator.items()):
        print(f"{creator}: {len(patterns)} patterns")
    
    # Breakdown by analysis type
    by_type = defaultdict(list)
    for pattern in all_patterns:
        analysis_type = pattern.get("analysis_type", "unknown")
        by_type[analysis_type].append(pattern)
    
    print(f"\n--- By Analysis Type ---")
    for analysis_type, patterns in sorted(by_type.items()):
        print(f"{analysis_type}: {len(patterns)} patterns")
    
    # Check for missing embeddings
    missing_embeddings = [p for p in all_patterns if "embedding" not in p]
    print(f"\n--- Missing Embeddings ---")
    print(f"Patterns without embeddings: {len(missing_embeddings)}")
    
    # Sample pattern structure
    if all_patterns:
        print(f"\n--- Sample Pattern Structure ---")
        sample = all_patterns[0]
        # Remove embedding for display
        sample_display = {k: v for k, v in sample.items() if k != "embedding"}
        sample_display["embedding"] = f"<vector of {len(sample.get('embedding', []))} dimensions>"
        print(json.dumps(sample_display, indent=2, default=str))
    
    # Detailed breakdown by creator and type
    print(f"\n--- Detailed Breakdown ---")
    for creator in sorted(by_creator.keys()):
        creator_patterns = by_creator[creator]
        type_counts = defaultdict(int)
        for pattern in creator_patterns:
            type_counts[pattern.get("analysis_type", "unknown")] += 1
        
        print(f"\n{creator}:")
        for analysis_type, count in sorted(type_counts.items()):
            print(f"  - {analysis_type}: {count}")
            # Show pattern names
            patterns_of_type = [p for p in creator_patterns if p.get("analysis_type") == analysis_type]
            for p in patterns_of_type[:3]:  # Show first 3
                name = p.get("pattern_name", "unnamed")
                print(f"    • {name}")

if __name__ == "__main__":
    audit_embeddings()