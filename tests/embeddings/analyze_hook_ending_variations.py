#!/usr/bin/env python3
"""
Analyze hook and ending analyses from MongoDB to identify key variations across different creators.
"""

import os
import json
from pymongo import MongoClient
from collections import defaultdict, Counter
from typing import Dict, List, Any, Set
from datetime import datetime

# MongoDB connection
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
DATABASE_NAME = "linkedin_analysis"
COLLECTION_NAME = "creator_analyses"


def connect_to_mongodb():
    """Connect to MongoDB and return the collection."""
    client = MongoClient(MONGO_URI)
    db = client[DATABASE_NAME]
    return db[COLLECTION_NAME]


def get_nested_keys(d: Dict, parent_key: str = '') -> Set[str]:
    """Recursively get all nested keys from a dictionary."""
    keys = set()
    for k, v in d.items():
        new_key = f"{parent_key}.{k}" if parent_key else k
        keys.add(new_key)
        if isinstance(v, dict):
            keys.update(get_nested_keys(v, new_key))
        elif isinstance(v, list) and v and isinstance(v[0], dict):
            # Sample first item if it's a list of dicts
            keys.update(get_nested_keys(v[0], f"{new_key}[0]"))
    return keys


def analyze_field_variations(analyses: List[Dict], field_name: str) -> Dict[str, Any]:
    """Analyze variations in a specific field across different analyses."""
    field_data = []
    all_keys = set()
    key_frequency = Counter()
    value_variations = defaultdict(set)
    
    for analysis in analyses:
        if field_name in analysis:
            field_data.append({
                'creator': analysis['username'],
                'data': analysis[field_name]
            })
            
            # Get all keys from this instance
            if isinstance(analysis[field_name], dict):
                keys = get_nested_keys(analysis[field_name])
                all_keys.update(keys)
                for key in keys:
                    key_frequency[key] += 1
                    
                    # Track value variations for specific keys
                    if '2_dominant' in key:
                        value = get_nested_value(analysis[field_name], key)
                        if value:
                            value_variations[key].add(str(type(value).__name__))
    
    # Categorize keys by consistency
    total_analyses = len(field_data)
    consistent_keys = [k for k, count in key_frequency.items() if count == total_analyses]
    variable_keys = [k for k, count in key_frequency.items() if count < total_analyses]
    
    return {
        'total_analyses': total_analyses,
        'field_data': field_data,
        'all_keys': sorted(list(all_keys)),
        'key_frequency': dict(key_frequency),
        'consistent_keys': sorted(consistent_keys),
        'variable_keys': sorted(variable_keys),
        'value_type_variations': {k: list(v) for k, v in value_variations.items()}
    }


def get_nested_value(d: Dict, key_path: str) -> Any:
    """Get value from nested dictionary using dot notation."""
    keys = key_path.split('.')
    value = d
    for key in keys:
        if '[' in key:
            # Handle array notation
            key_name = key.split('[')[0]
            if key_name in value:
                value = value[key_name]
                if isinstance(value, list) and len(value) > 0:
                    value = value[0]
                else:
                    return None
        elif key in value:
            value = value[key]
        else:
            return None
    return value


def analyze_dominant_structures(analyses: List[Dict], analysis_type: str) -> Dict[str, Any]:
    """Analyze the dominant hook/ending structures across creators."""
    dominant_field = f"2_dominant_{analysis_type}_archetypes" if analysis_type == "hook" else f"2_dominant_{analysis_type}_types"
    structures = []
    
    for analysis in analyses:
        if analysis_type in analysis:
            data = analysis[analysis_type]
            dominant = get_nested_value(data, dominant_field)
            
            if dominant:
                structures.append({
                    'creator': analysis['username'],
                    'dominant_structure': dominant,
                    'structure_type': type(dominant).__name__,
                    'structure_keys': list(dominant.keys()) if isinstance(dominant, dict) else None
                })
    
    # Analyze structure patterns
    structure_types = Counter(s['structure_type'] for s in structures)
    all_structure_keys = set()
    for s in structures:
        if s['structure_keys']:
            all_structure_keys.update(s['structure_keys'])
    
    return {
        'structures': structures,
        'structure_types': dict(structure_types),
        'common_structure_keys': sorted(list(all_structure_keys))
    }


def main():
    """Main analysis function."""
    collection = connect_to_mongodb()
    
    # Get hook analyses from different creators
    print("Fetching hook analyses...")
    hook_analyses = list(collection.find(
        {"hook": {"$exists": True}},
        {"username": 1, "hook": 1}
    ).limit(10))
    
    # Get ending analyses from different creators
    print("Fetching ending analyses...")
    ending_analyses = list(collection.find(
        {"ending": {"$exists": True}},
        {"username": 1, "ending": 1}
    ).limit(10))
    
    # Analyze variations
    print("\nAnalyzing hook variations...")
    hook_variations = analyze_field_variations(hook_analyses, 'hook')
    
    print("\nAnalyzing ending variations...")
    ending_variations = analyze_field_variations(ending_analyses, 'ending')
    
    # Analyze dominant structures
    print("\nAnalyzing dominant hook structures...")
    hook_structures = analyze_dominant_structures(hook_analyses, 'hook')
    
    print("\nAnalyzing dominant ending structures...")
    ending_structures = analyze_dominant_structures(ending_analyses, 'ending')
    
    # Prepare results
    results = {
        'analysis_timestamp': datetime.now().isoformat(),
        'hook_analysis': {
            'total_creators': hook_variations['total_analyses'],
            'creators': [d['creator'] for d in hook_variations['field_data']],
            'consistent_keys': hook_variations['consistent_keys'],
            'variable_keys': hook_variations['variable_keys'],
            'key_frequency': hook_variations['key_frequency'],
            'dominant_structures': hook_structures
        },
        'ending_analysis': {
            'total_creators': ending_variations['total_analyses'],
            'creators': [d['creator'] for d in ending_variations['field_data']],
            'consistent_keys': ending_variations['consistent_keys'],
            'variable_keys': ending_variations['variable_keys'],
            'key_frequency': ending_variations['key_frequency'],
            'dominant_structures': ending_structures
        },
        'key_insights': {
            'hook_unique_keys': len(hook_variations['all_keys']),
            'ending_unique_keys': len(ending_variations['all_keys']),
            'hook_consistent_ratio': len(hook_variations['consistent_keys']) / len(hook_variations['all_keys']) if hook_variations['all_keys'] else 0,
            'ending_consistent_ratio': len(ending_variations['consistent_keys']) / len(ending_variations['all_keys']) if ending_variations['all_keys'] else 0
        }
    }
    
    # Save results
    output_file = 'hook_ending_variations_analysis.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nAnalysis complete! Results saved to {output_file}")
    
    # Print summary
    print("\n=== SUMMARY ===")
    print(f"\nHook Analysis:")
    print(f"- Analyzed {hook_variations['total_analyses']} creators")
    print(f"- Total unique keys: {len(hook_variations['all_keys'])}")
    print(f"- Consistent keys: {len(hook_variations['consistent_keys'])}")
    print(f"- Variable keys: {len(hook_variations['variable_keys'])}")
    
    print(f"\nEnding Analysis:")
    print(f"- Analyzed {ending_variations['total_analyses']} creators")
    print(f"- Total unique keys: {len(ending_variations['all_keys'])}")
    print(f"- Consistent keys: {len(ending_variations['consistent_keys'])}")
    print(f"- Variable keys: {len(ending_variations['variable_keys'])}")
    
    # Print sample variations
    print("\n=== KEY VARIATIONS ===")
    print("\nMost variable hook keys:")
    hook_var_keys = [(k, hook_variations['key_frequency'][k]) for k in hook_variations['variable_keys']]
    hook_var_keys.sort(key=lambda x: x[1])
    for key, count in hook_var_keys[:5]:
        print(f"  - {key}: appears in {count}/{hook_variations['total_analyses']} analyses")
    
    print("\nMost variable ending keys:")
    ending_var_keys = [(k, ending_variations['key_frequency'][k]) for k in ending_variations['variable_keys']]
    ending_var_keys.sort(key=lambda x: x[1])
    for key, count in ending_var_keys[:5]:
        print(f"  - {key}: appears in {count}/{ending_variations['total_analyses']} analyses")
    
    # Print dominant structure insights
    print("\n=== DOMINANT STRUCTURES ===")
    print(f"\nHook dominant structures:")
    for s in hook_structures['structures'][:3]:
        print(f"  - {s['creator']}: {s['structure_type']} with keys: {s['structure_keys']}")
    
    print(f"\nEnding dominant structures:")
    for s in ending_structures['structures'][:3]:
        print(f"  - {s['creator']}: {s['structure_type']} with keys: {s['structure_keys']}")


if __name__ == "__main__":
    main()