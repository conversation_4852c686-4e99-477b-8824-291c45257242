"""Test editor service with MongoDB storage."""

import asyncio
import pytest
from src.api.services.editor_service import (
    create_editor_session,
    get_editor_session,
    update_conversation_history,
    update_post_content,
    get_conversation_history,
    get_post_content
)

@pytest.mark.asyncio
async def test_editor_mongodb_integration():
    """Test full editor workflow with MongoDB."""
    
    # Create a new session
    initial_content = "# My LinkedIn Post\n\nThis is the initial content."
    session_id = await create_editor_session(initial_content)
    print(f"Created session: {session_id}")
    
    # Verify session exists
    session = await get_editor_session(session_id)
    assert session is not None
    assert session["session_id"] == session_id
    assert session["post_content"] == initial_content
    
    # Add user message
    await update_conversation_history(session_id, "Make this post more engaging", is_user=True)
    
    # Add assistant message with tool use
    tool_uses = [{
        "id": "tool_use_1",
        "name": "str_replace_editor",
        "input": {"command": "str_replace", "old_str": "initial content", "new_str": "exciting content"}
    }]
    await update_conversation_history(
        session_id, 
        "I'll make the post more engaging by updating the content.",
        is_user=False,
        tool_uses=tool_uses
    )
    
    # Add tool result
    tool_results = [{
        "type": "tool_result",
        "tool_use_id": "tool_use_1",
        "content": "Successfully replaced text",
        "is_error": False
    }]
    await update_conversation_history(session_id, tool_results, is_user=True)
    
    # Update post content
    new_content = "# My LinkedIn Post\n\nThis is the exciting content."
    await update_post_content(session_id, new_content)
    
    # Verify conversation history
    history = await get_conversation_history(session_id)
    assert len(history) == 3  # user message, assistant message, tool result
    assert history[0]["role"] == "user"
    assert history[0]["content"] == "Make this post more engaging"
    assert history[1]["role"] == "assistant"
    assert history[2]["role"] == "user"
    
    # Verify post content
    current_content = await get_post_content(session_id)
    assert current_content == new_content
    
    print("✅ All tests passed!")

if __name__ == "__main__":
    asyncio.run(test_editor_mongodb_integration())