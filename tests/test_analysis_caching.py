"""Test script to verify caching across multiple analysis modules.

This script tests the caching implementation in multiple analysis modules
by running each module in sequence with the same content to verify cache hits.
"""

import sys
import os
import logging
import json
import asyncio
import time
import sqlite3
from typing import Dict, List, Any, Tuple
from pprint import pformat

# Add project root to system path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

# Import project modules
from src.utils.anthropic_client import AnthropicClient
from src.utils.api_cache_utils import call_with_caching
from src.config import Config

# Import analysis modules
# Note: We now use the local load_prompt function instead of importing from individual analysis modules
# The individual analysis modules have been refactored into the unified analysis engine

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
SYSTEM_PROMPT_FILE = os.path.join(project_root, "prompts", "system_prompt.txt")
# Use body_analysis_prompt.txt instead of the missing ending_analysis_prompt.txt
ENDING_ANALYSIS_PROMPT_FILE = os.path.join(project_root, "prompts", "body_analysis_prompt.txt")
LINGUISTIC_ANALYSIS_PROMPT_FILE = os.path.join(project_root, "prompts", "linguistic_analysis_prompt.txt")
OVERVIEW_PROMPT_FILE = os.path.join(project_root, "prompts", "overview_prompt.txt")
MIN_POSTS = 3  # Reduced number of posts for faster testing
CREATOR_NAME = "test_creator"  # Arbitrary name for testing

def load_prompt(filepath: str) -> str:
    """Loads prompt text from a file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"Prompt file not found: {filepath}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error reading prompt file {filepath}: {e}")
        sys.exit(1)

def create_mock_posts(num_posts: int = MIN_POSTS) -> List[Dict[str, Any]]:
    """Create mock posts for testing in document block format."""
    posts = []
    for i in range(num_posts):
        post_content = f"""This is test post #{i+1} with enough content to ensure caching works properly.

Post content needs to be sufficiently long to exceed the minimum cacheable size
when combined with other posts. This post includes multiple paragraphs and lines
to simulate real content.

Here's some additional text to increase the token count:
- The quick brown fox jumps over the lazy dog.
- We need to ensure there are enough tokens for caching.
- The minimum cacheable size is about 2048 tokens.
- Each post contributes some tokens to the total.

Engagement metrics:
- Likes: {i * 20 + 50}
- Comments: {i * 5 + 10}
- Shares: {i * 2 + 5}

This should help ensure we have enough content to test caching effectively.
"""
        # Use the document block format as used in most analysis scripts
        posts.append({
            "type": "document",
            "source": {
                "type": "text",
                "media_type": "text/plain",
                "data": f"POST ID: {i+1}\n\nFULL POST:\n{post_content}\n\nENGAGEMENT METRICS:\nLikes: {i * 20 + 50}\nComments: {i * 5 + 10}\nShares: {i * 2 + 5}\n\nTo cite this post, use [cite:{i+1}]"
            },
            "title": f"Post ID: {i+1}",
            "citations": {"enabled": True}
        })
    return posts

async def run_test_analyses():
    """Run test analyses with multiple modules to verify caching works across them."""
    logger.info("=== Starting Analysis Caching Test ===")

    # 1. Create client
    client = AnthropicClient()

    # 2. Create mock posts with enough content for caching
    logger.info(f"Creating {MIN_POSTS} mock posts for testing...")
    document_blocks = create_mock_posts(MIN_POSTS)

    # 3. Load prompts
    logger.info("Loading prompts...")
    system_prompt = load_prompt(SYSTEM_PROMPT_FILE)

    # 4. Test ending analysis
    logger.info("\n=== Testing Ending Analysis ===")
    # Use the local load_prompt function since the module has been removed
    ending_prompt_template = load_prompt(ENDING_ANALYSIS_PROMPT_FILE)
    ending_prompt = ending_prompt_template.replace("{creator_name}", CREATOR_NAME)

    start_time = time.time()
    ending_result = await call_with_caching(
        client=client,
        system_prompt=system_prompt,
        posts=document_blocks,
        user_prompt=ending_prompt
    )
    ending_duration = time.time() - start_time

    logger.info(f"Ending analysis completed in {ending_duration:.2f} seconds")
    logger.info(f"Ending analysis cache metrics: {json.dumps(ending_result['cache_metrics'], indent=2)}")

    # First call should create cache
    cache_created = ending_result['cache_metrics'].get('cache_created', False)
    logger.info(f"Cache created: {cache_created}")

    # 5. Test linguistic analysis (should hit cache from ending analysis)
    logger.info("\n=== Testing Linguistic Analysis ===")
    linguistic_prompt_template = load_prompt(LINGUISTIC_ANALYSIS_PROMPT_FILE)
    linguistic_prompt = linguistic_prompt_template.replace("{creator_name}", CREATOR_NAME)

    start_time = time.time()
    linguistic_result = await call_with_caching(
        client=client,
        system_prompt=system_prompt,
        posts=document_blocks,
        user_prompt=linguistic_prompt
    )
    linguistic_duration = time.time() - start_time

    logger.info(f"Linguistic analysis completed in {linguistic_duration:.2f} seconds")
    logger.info(f"Linguistic analysis cache metrics: {json.dumps(linguistic_result['cache_metrics'], indent=2)}")

    # Verify cache hit
    linguistic_cache_hit = linguistic_result['cache_metrics'].get('cache_read', False)
    linguistic_tokens_saved = linguistic_result['cache_metrics'].get('tokens_saved', 0)
    linguistic_percent_saved = linguistic_result['cache_metrics'].get('percent_saved', 0)

    # 6. Test theme identification (should also hit cache)
    logger.info("\n=== Testing Theme Identification ===")
    theme_prompt_template = load_prompt(OVERVIEW_PROMPT_FILE)
    theme_prompt = theme_prompt_template.replace("{creator_name}", CREATOR_NAME)

    start_time = time.time()
    theme_result = await call_with_caching(
        client=client,
        system_prompt=system_prompt,
        posts=document_blocks,
        user_prompt=theme_prompt
    )
    theme_duration = time.time() - start_time

    logger.info(f"Theme identification completed in {theme_duration:.2f} seconds")
    logger.info(f"Theme identification cache metrics: {json.dumps(theme_result['cache_metrics'], indent=2)}")

    # Verify cache hit
    theme_cache_hit = theme_result['cache_metrics'].get('cache_read', False)
    theme_tokens_saved = theme_result['cache_metrics'].get('tokens_saved', 0)
    theme_percent_saved = theme_result['cache_metrics'].get('percent_saved', 0)

    # 7. Summarize results
    logger.info("\n=== Cache Test Results Summary ===")

    total_tokens_saved = linguistic_tokens_saved + theme_tokens_saved
    speedup_linguistic = ending_duration / linguistic_duration if linguistic_duration > 0 else 0
    speedup_theme = ending_duration / theme_duration if theme_duration > 0 else 0

    logger.info(f"First call (ending analysis): Cache created = {cache_created}")
    logger.info(f"Second call (linguistic analysis): Cache hit = {linguistic_cache_hit}, Tokens saved = {linguistic_tokens_saved} ({linguistic_percent_saved:.2f}%), Speedup = {speedup_linguistic:.2f}x")
    logger.info(f"Third call (theme identification): Cache hit = {theme_cache_hit}, Tokens saved = {theme_tokens_saved} ({theme_percent_saved:.2f}%), Speedup = {speedup_theme:.2f}x")
    logger.info(f"Total tokens saved across all calls: {total_tokens_saved}")

    # Determine success based on cache hits
    success = linguistic_cache_hit and theme_cache_hit
    if success:
        logger.info("✅ CACHING WORKING CORRECTLY ACROSS ANALYSIS MODULES!")
    else:
        logger.error("❌ CACHE HITS NOT DETECTED IN ALL MODULES")

    return success

if __name__ == "__main__":
    if not os.getenv("ANTHROPIC_API_KEY"):
        logger.error("ANTHROPIC_API_KEY environment variable not set")
        sys.exit(1)

    success = asyncio.run(run_test_analyses())
    sys.exit(0 if success else 1)