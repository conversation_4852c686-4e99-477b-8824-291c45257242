"""Manual test to verify strategy flows through the pipeline.

This script manually traces the strategy flow through each component.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test data
TEST_USER_ID = "manual_test_user_123"
TEST_BRIEF = "Write about AI innovation in content creation"
TEST_STRATEGY = {
    "clerk_user_id": TEST_USER_ID,
    "industry": "AI/Technology",
    "offering": "AI-powered content tools",
    "target_audience": "Content creators",
    "differentiators": "Authentic voice preservation",
    "content_goal": "Build thought leadership"
}


async def step1_setup_test_data():
    """Step 1: Create test user with strategy in MongoDB."""
    from src.utils.mongo_utils_async import get_async_mongo_db, CONTENT_STRATEGIES_COLLECTION
    
    logger.info("=== STEP 1: Setting up test data ===")
    
    db = await get_async_mongo_db()
    
    # Insert test strategy
    await db[CONTENT_STRATEGIES_COLLECTION].update_one(
        {"clerk_user_id": TEST_USER_ID},
        {"$set": TEST_STRATEGY},
        upsert=True
    )
    
    # Verify
    saved = await db[CONTENT_STRATEGIES_COLLECTION].find_one({"clerk_user_id": TEST_USER_ID})
    if saved:
        logger.info(f"✅ Strategy saved for user {TEST_USER_ID}")
        logger.info(f"   Industry: {saved['industry']}")
    else:
        logger.error("❌ Failed to save strategy")
        return False
    
    return True


async def step2_test_strategy_fetch():
    """Step 2: Test that get_user_content_strategy works."""
    from src.utils.mongo_utils_async import get_user_content_strategy
    
    logger.info("\n=== STEP 2: Testing strategy fetch ===")
    
    strategy = await get_user_content_strategy(TEST_USER_ID)
    
    if strategy:
        logger.info(f"✅ Successfully fetched strategy")
        logger.info(f"   Industry: {strategy.get('industry')}")
        logger.info(f"   Target: {strategy.get('target_audience')}")
    else:
        logger.error("❌ Failed to fetch strategy")
        return False
        
    return True


async def step3_test_session_creation():
    """Step 3: Test that session creation auto-fetches strategy."""
    from src.api.services import session_service
    
    logger.info("\n=== STEP 3: Testing session creation with strategy ===")
    
    session_id = await session_service.create_session(
        collection_name="test_sessions",
        extra_fields={
            "user_id": TEST_USER_ID,
            "input_text": TEST_BRIEF
        }
    )
    
    logger.info(f"✅ Created session: {session_id}")
    
    # Verify session has strategy
    session = await session_service.get_session("test_sessions", session_id)
    
    if session and session.get("content_strategy"):
        logger.info(f"✅ Session contains strategy")
        logger.info(f"   Industry: {session['content_strategy'].get('industry')}")
    else:
        logger.error("❌ Session missing strategy")
        return False
        
    return True


async def step4_test_agent_service():
    """Step 4: Test that AgentService receives and stores strategy."""
    from src.api.agent_service import AgentService, AgentSessionState
    
    logger.info("\n=== STEP 4: Testing AgentService with strategy ===")
    
    # Create session state with strategy
    session_state = AgentSessionState(
        user_brief=TEST_BRIEF,
        session_id="test_session_123",
        content_strategy=TEST_STRATEGY
    )
    
    # Verify it's stored
    if session_state.content_strategy:
        logger.info(f"✅ AgentSessionState stores strategy")
        logger.info(f"   Industry: {session_state.content_strategy['industry']}")
    else:
        logger.error("❌ AgentSessionState missing strategy")
        return False
        
    return True


async def step5_test_strategy_formatting():
    """Step 5: Test strategy formatting for prompts."""
    from src.utils.strategy_utils import format_strategy_for_prompt
    
    logger.info("\n=== STEP 5: Testing strategy formatting ===")
    
    formatted = format_strategy_for_prompt(TEST_STRATEGY)
    
    logger.info("✅ Formatted strategy:")
    logger.info(formatted)
    
    # Verify key components are present
    required_components = ["Industry:", "Offering:", "Target Audience:", "Key Differentiators:", "Content Goal:"]
    missing = [comp for comp in required_components if comp not in formatted]
    
    if missing:
        logger.error(f"❌ Missing components: {missing}")
        return False
    else:
        logger.info("✅ All components present in formatted strategy")
        
    return True


async def step6_trace_through_pipeline():
    """Step 6: Trace strategy through the entire pipeline."""
    logger.info("\n=== STEP 6: Tracing through full pipeline ===")
    
    # This would require mocking the API calls, but we can at least show the flow
    logger.info("Strategy flow path:")
    logger.info("1. User makes API request with clerk_user_id")
    logger.info("2. Session creation auto-fetches strategy from MongoDB")
    logger.info("3. Job manager fetches strategy and passes to AgentService")
    logger.info("4. AgentService stores strategy in AgentSessionState")
    logger.info("5. process_step extracts strategy from session_state")
    logger.info("6. call_with_optimized_caching includes strategy in API call")
    logger.info("7. Strategy appears in system prompt sent to Claude")
    
    return True


async def main():
    """Run all manual tests."""
    logger.info("🚀 Starting Manual Strategy Flow Tests\n")
    
    steps = [
        step1_setup_test_data,
        step2_test_strategy_fetch,
        step3_test_session_creation,
        step4_test_agent_service,
        step5_test_strategy_formatting,
        step6_trace_through_pipeline
    ]
    
    for i, step in enumerate(steps, 1):
        if not await step():
            logger.error(f"\n❌ Failed at step {i}. Stopping tests.")
            return
            
    logger.info("\n✅ All manual tests passed!")
    logger.info("\n📝 Next steps to verify with real API:")
    logger.info("1. Start the API: python run_api.py")
    logger.info("2. Run the integration test: python tests/test_api_strategy_integration.py")
    logger.info("3. Check Logfire dashboard for strategy in spans")


if __name__ == "__main__":
    asyncio.run(main())