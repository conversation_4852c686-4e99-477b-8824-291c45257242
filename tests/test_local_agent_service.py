"""
Test script for local agent service.

This script tests the basic functionality of the local agent service.
"""

import os
import sys
import json
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add project root to path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.api.local_agent_service import LocalAgentService

class TestLocalAgentService(unittest.TestCase):
    """Test cases for local agent service."""
    
    def setUp(self):
        """Set up test environment."""
        # Create test directories
        self.test_dir = os.path.join(project_root, "test_analyses")
        self.json_dir = os.path.join(self.test_dir, "json")
        self.markdown_dir = os.path.join(self.test_dir, "markdown")
        
        # Create creator directories
        self.creator_name = "testcreator"
        self.creator_json_dir = os.path.join(self.json_dir, self.creator_name)
        self.creator_md_dir = os.path.join(self.markdown_dir, self.creator_name)
        
        os.makedirs(self.creator_json_dir, exist_ok=True)
        os.makedirs(self.creator_md_dir, exist_ok=True)
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove test directories
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_load_prompt_template(self):
        """Test load_prompt_template function."""
        # Mock the open function to return a test prompt
        with patch('builtins.open', unittest.mock.mock_open(read_data="Test prompt template")) as mock_file:
            prompt = LocalAgentService.load_prompt_template("test_prompt.txt")
            self.assertEqual(prompt, "Test prompt template")
    
    def test_initialize_enhanced_scratchpad(self):
        """Test initialize_enhanced_scratchpad function."""
        input_text = "Test input"
        creator_analyses = {"themes": {"test": "data"}}
        
        scratchpad = LocalAgentService.initialize_enhanced_scratchpad(input_text, creator_analyses)
        
        # Check that the scratchpad has the expected structure
        self.assertEqual(scratchpad["user_input_brief"], input_text)
        self.assertEqual(scratchpad["creator_style_guides"], creator_analyses)
        self.assertEqual(scratchpad["current_draft_post"], "")
        self.assertIn("generation_pipeline", scratchpad)
        self.assertIn("theme_selection", scratchpad["generation_pipeline"])
        self.assertIn("hook_creation", scratchpad["generation_pipeline"])
        self.assertIn("body_linguistic_construction", scratchpad["generation_pipeline"])
        self.assertIn("ending_creation", scratchpad["generation_pipeline"])
    
    def test_format_explanation_from_enhanced_scratchpad(self):
        """Test format_explanation_from_enhanced_scratchpad function."""
        # Create a test pipeline
        pipeline = {
            "theme_selection": {
                "selected_theme_name": "Test Theme",
                "detailed_reasoning_for_selection": "Test theme reasoning"
            },
            "hook_creation": {
                "selected_hook_pattern": "Test Hook",
                "detailed_reasoning_for_selection_and_text": "Test hook reasoning"
            },
            "body_linguistic_construction": {
                "selected_body_structure": "Test Body",
                "selected_linguistic_patterns": "Test Patterns",
                "detailed_reasoning_for_selection_and_text": "Test body reasoning"
            },
            "ending_creation": {
                "selected_ending_pattern": "Test Ending",
                "detailed_reasoning_for_selection_and_text": "Test ending reasoning"
            }
        }
        
        explanation = LocalAgentService.format_explanation_from_enhanced_scratchpad(pipeline, "testcreator")
        
        # Check that the explanation contains the expected sections
        self.assertIn("# Content Generation Explanation for testcreator", explanation)
        self.assertIn("## Theme Selection: Test Theme", explanation)
        self.assertIn("Test theme reasoning", explanation)
        self.assertIn("## Hook Approach: Test Hook", explanation)
        self.assertIn("Test hook reasoning", explanation)
        self.assertIn("## Body Structure: Test Body", explanation)
        self.assertIn("**Linguistic Patterns Applied:** Test Patterns", explanation)
        self.assertIn("Test body reasoning", explanation)
        self.assertIn("## Ending Approach: Test Ending", explanation)
        self.assertIn("Test ending reasoning", explanation)

if __name__ == "__main__":
    unittest.main()
