"""Tests for the agent service."""
import pytest
import os
import json
from unittest.mock import patch, MagicMock, AsyncMock
from src.api.agent_service import AgentService
from src.utils.agent_utils.process_step import process_step
import asyncio
import sys
from pathlib import Path

# Add project root to system path
project_root = Path(__file__).resolve().parent.parent # Assuming tests is a subdir of project root
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from src.config import Config # To ensure .env is loaded if necessary
except ModuleNotFoundError as e:
    print(f"Error: Could not import necessary modules. Original error: {e}")
    print("Ensure this script is in tests/ and src/api/agent_service.py and src/config.py exist.")
    print(f"Current sys.path: {sys.path}")
    print(f"Calculated project_root: {project_root}")
    sys.exit(1) # Exit if imports fail, pytest will not run properly
except ImportError as e:
    print(f"Error: ImportError. Original error: {e}")
    sys.exit(1)

@pytest.fixture
def mock_agent_caching():
    """Mock the call_with_agent_caching function."""
    with patch('src.utils.api_cache_utils.call_with_agent_caching') as mock_caching:
        # Create a mock response
        mock_result = {
            "text": "REASONING: Test reasoning\nSELECTION: Test selection\nOUTPUT: Test output",
            "cache_metrics": {
                "cache_enabled": True,
                "cache_created": True,
                "creation_tokens": 3000
            }
        }

        # Set up the mock to return the result directly
        mock_caching.return_value = mock_result

        yield mock_caching

@pytest.mark.asyncio
async def test_process_step_with_caching(mock_agent_caching):
    """Test that process_step correctly uses caching for user input."""
    # Arrange
    step = "theme"
    input_text = "Test input"
    creator_name = "test_creator"
    analysis_data = {"test": "data"}
    scratchpad = {"test": "scratchpad"}

    # Act
    result = await AgentService.process_step(
        step, input_text, creator_name, analysis_data, scratchpad
    )

    # Assert
    # Check that the caching function was called with the correct parameters
    mock_agent_caching.assert_called_once()
    call_args = mock_agent_caching.call_args[1]

    # Check that the user input is being cached
    assert call_args["user_input"] == input_text

    # Check that thinking is enabled
    assert call_args["thinking_enabled"] is True
    assert call_args["thinking_budget_tokens"] == 8000

    # Check that the result was correctly parsed
    assert result["reasoning"] == "Test reasoning"
    assert result["selection"] == "Test selection"
    assert result["output"] == "Test output"

@pytest.mark.asyncio
async def test_process_step_formats_prompt_correctly(mock_agent_caching):
    """Test that process_step correctly formats the prompt with the provided data."""
    # Arrange
    step = "theme"
    input_text = "Test input"
    creator_name = "test_creator"
    analysis_data = {"patterns": ["pattern1", "pattern2"]}
    scratchpad = {"theme": "test theme"}

    # Act
    await AgentService.process_step(
        step, input_text, creator_name, analysis_data, scratchpad
    )

    # Assert
    mock_agent_caching.assert_called_once()
    call_args = mock_agent_caching.call_args[1]

    # Check that the user prompt contains the expected data
    user_prompt = call_args["user_prompt"]
    assert input_text in user_prompt
    assert creator_name in user_prompt
    assert json.dumps(analysis_data, indent=2) in user_prompt
    assert json.dumps(scratchpad, indent=2) in user_prompt

@pytest.mark.asyncio
async def test_parse_step_response():
    """Test that parse_step_response correctly extracts data from the response."""
    # Arrange
    response_text = """
    Here's my analysis:

    REASONING: This is a detailed reasoning about the selection.

    SELECTION: Selected option

    OUTPUT: This is the final output text.
    """

    # Act
    result = AgentService.parse_step_response(response_text)

    # Assert
    assert result["reasoning"] == "This is a detailed reasoning about the selection."
    assert result["selection"] == "Selected option"
    assert result["output"] == "This is the final output text."

@pytest.mark.asyncio
async def test_parse_step_response_handles_missing_sections():
    """Test that parse_step_response handles responses with missing sections."""
    # Arrange
    response_text = """
    Here's my analysis:

    REASONING: This is a detailed reasoning about the selection.
    """

    # Act
    result = AgentService.parse_step_response(response_text)

    # Assert
    assert result["reasoning"] == "This is a detailed reasoning about the selection."
    assert result["selection"] == ""
    assert result["output"] == ""

# List of creators to test
TEST_CREATORS = ["retentionadam", "sarahkisskohersh"] # Add more as needed

# Sample input text for testing
SAMPLE_INPUT_TEXT = "Write a short, engaging post about the impact of AI on content creation."

# Define mock responses for each step
# These need to match the structure expected by AgentService.generate_content
MOCK_THEME_RESULT = {
    "selection": "Mock Theme", "output": "Mock Theme Output (not directly used)", 
    "reasoning": "Mock theme reasoning.", "decision_summary": "Theme decision summary.",
    "step_name": "theme", "thinking": "Theme thinking..."
}
MOCK_HOOK_RESULT = {
    "selection": "Mock Hook Pattern", "output": "Mock Hook Text.",
    "reasoning": "Mock hook reasoning.", "decision_summary": "Hook decision summary.",
    "step_name": "hook", "thinking": "Hook thinking..."
}
MOCK_BODY_RESULT = {
    "selection": "Mock Body Structure", "output": "Mock Body Text.",
    "reasoning": "Mock body reasoning.", "decision_summary": "Body decision summary.",
    "step_name": "body", "thinking": "Body thinking..."
}
MOCK_ENDING_RESULT = {
    "selection": "Mock Ending Pattern", "output": "Mock Ending Text.",
    "reasoning": "Mock ending reasoning.", "decision_summary": "Ending decision summary (optional for explanation).",
    "step_name": "ending", "thinking": "Ending thinking..."
}

# This function will be used as a side_effect for the mock
async def mock_process_step_side_effect(step, input_text, creator_name, analysis_data, scratchpad, prompt_template, system_prompt, generation_session_id, post_length=None):
    if step == "theme":
        return MOCK_THEME_RESULT
    elif step == "hook":
        return MOCK_HOOK_RESULT
    elif step == "body":
        return MOCK_BODY_RESULT
    elif step == "ending":
        return MOCK_ENDING_RESULT
    return {} # Default empty dict if step is unknown

@pytest.mark.asyncio
@pytest.mark.parametrize("creator_name", TEST_CREATORS)
@patch('src.api.agent_service.process_step', new_callable=AsyncMock) # Corrected patch target
async def test_generate_content_for_creator_mocked(mock_process_step, creator_name):
    """
    Test that AgentService.generate_content orchestrates steps correctly
    using a mocked process_step, without live API calls.
    """
    print(f"Testing AgentService.generate_content (mocked) for creator: {creator_name}")

    # Configure the mock's side_effect
    mock_process_step.side_effect = mock_process_step_side_effect

    # Ensure .env is loaded for Config if AgentService.load_creator_analyses needs it
    try:
        _ = Config.get_mongodb_uri()
        print(f"MongoDB URI retrieved from Config (ensuring .env loaded for {creator_name}).")
    except ValueError as ve:
        print(f"Warning: Could not get MongoDB URI from Config for {creator_name}: {ve}.")
        # Test will proceed; load_creator_analyses might be affected if DB isn't reachable
        # but this test primarily focuses on generate_content's orchestration after analyses are loaded.

    result = await AgentService.generate_content(
        input_text=SAMPLE_INPUT_TEXT,
        creator_name=creator_name
    )

    assert result is not None, "AgentService.generate_content returned None"
    assert "error" not in result, f"AgentService returned an error for {creator_name}: {result.get('error')}"
    
    # Check that process_step was called 4 times (once for each step)
    assert mock_process_step.call_count == 4, f"Expected process_step to be called 4 times, but was called {mock_process_step.call_count} times."

    # Verify calls with specific step names
    # call_args_list provides a list of all calls made to the mock
    # Each item in the list is a call object, where call[0] is args and call[1] is kwargs
    called_steps = [call_args[0][0] for call_args in mock_process_step.call_args_list] # Get the first positional argument (step name)
    assert "theme" in called_steps, "process_step was not called for 'theme'"
    assert "hook" in called_steps, "process_step was not called for 'hook'"
    assert "body" in called_steps, "process_step was not called for 'body'"
    assert "ending" in called_steps, "process_step was not called for 'ending'"

    # Verify generated_post structure based on mocked outputs
    expected_post_parts = [
        MOCK_HOOK_RESULT["output"],
        MOCK_BODY_RESULT["output"],
        MOCK_ENDING_RESULT["output"]
    ]
    # The signature part will also be there, so we check if our mock parts are present
    for part in expected_post_parts:
        assert part in result["generated_post"], f"Expected part '{part}' not in generated_post for {creator_name}"
    
    assert f"Generated for @{creator_name} style" in result["generated_post"], "Creator signature missing"

    assert "explanation" in result, f"Missing 'explanation' in response for {creator_name}"
    assert isinstance(result["explanation"], str), f"\'explanation\' is not a string for {creator_name}"
    # Check if decision summaries from mock results are in the explanation
    assert MOCK_THEME_RESULT["decision_summary"] in result["explanation"]
    assert MOCK_HOOK_RESULT["decision_summary"] in result["explanation"]
    assert MOCK_BODY_RESULT["decision_summary"] in result["explanation"]
    # MOCK_ENDING_RESULT["decision_summary"] is optional in explanation construction if empty

    assert "enhanced_scratchpad" in result, f"Missing 'enhanced_scratchpad' for {creator_name}"
    assert result["enhanced_scratchpad"]["theme_selection"]["selected_theme_name"] == MOCK_THEME_RESULT["selection"]
    assert result["enhanced_scratchpad"]["hook_creation"]["generated_hook_text"] == MOCK_HOOK_RESULT["output"]
    assert result["enhanced_scratchpad"]["body_linguistic_construction"]["generated_body_text"] == MOCK_BODY_RESULT["output"]
    assert result["enhanced_scratchpad"]["ending_creation"]["generated_ending_text"] == MOCK_ENDING_RESULT["output"]

    assert "thinking_logs" in result, f"Missing 'thinking_logs' for {creator_name}"
    assert result["thinking_logs"]["theme"] == MOCK_THEME_RESULT["thinking"]
    assert result["thinking_logs"]["hook"] == MOCK_HOOK_RESULT["thinking"]
    assert result["thinking_logs"]["body"] == MOCK_BODY_RESULT["thinking"]
    assert result["thinking_logs"]["ending"] == MOCK_ENDING_RESULT["thinking"]

    print(f"Successfully generated content (mocked) for {creator_name}.")

# If you want to keep the old integration test that makes live calls,
# you can rename it, e.g., test_generate_content_for_creator_integration
# and remove the @patch decorator from it. Or, create a separate file for integration tests.
# For now, this change replaces the previous test_generate_content_for_creator.
# The original test can be found in git history if needed.

# To run this specific test: pytest tests/test_agent_service.py -k test_generate_content_for_creator_mocked -s
