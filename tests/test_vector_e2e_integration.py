"""
End-to-end integration test for the vector pipeline.

This test verifies:
1. Complete generation flow works with vector search
2. Scratchpad context flows correctly between steps
3. No theme dependencies remain
4. Pattern selection happens on-demand for each step
"""

import asyncio
import json
import pytest
from typing import Dict, Any

from src.api.agent_service import AgentService


@pytest.mark.asyncio
async def test_vector_pipeline_e2e_basic():
    """Test the basic vector pipeline functionality without heavy mocking."""
    
    # Test input - simple and clear
    user_brief = "Write about effective communication skills for career growth."
    
    # Run the actual pipeline
    result = await AgentService.generate_content(
        input_text=user_brief,
        user_preferences=None,
        conversation_id=None,
        conversation_history=None,
        update_step_callback=None,
        post_length="medium"
    )
    
    # Verify the result doesn't have errors
    assert "error" not in result, f"Pipeline failed with error: {result.get('error')}"
    
    # Verify basic structure
    assert "generated_post" in result
    assert "enhanced_scratchpad" in result
    assert len(result["generated_post"]) > 0
    
    # Verify scratchpad flow
    scratchpad = result["enhanced_scratchpad"]
    
    # Check that no theme was selected (theme dependencies removed)
    assert "theme_selection" not in scratchpad
    
    # Verify hook step completed
    assert "hook_creation" in scratchpad
    hook_data = scratchpad["hook_creation"]
    assert hook_data["generated_hook_text"] is not None
    assert len(hook_data["generated_hook_text"]) > 0
    
    # Verify body step completed  
    assert "body_linguistic_construction" in scratchpad
    body_data = scratchpad["body_linguistic_construction"]
    assert body_data["generated_body_text"] is not None
    assert len(body_data["generated_body_text"]) > 0
    
    # Verify ending step completed
    assert "ending_creation" in scratchpad
    ending_data = scratchpad["ending_creation"]
    assert ending_data["generated_ending_text"] is not None
    assert len(ending_data["generated_ending_text"]) > 0
    
    # Verify vector search was used (at minimum for ending, as that's where metadata is stored)
    # Note: Pattern selection metadata is currently only stored for ending step
    assert "pattern_selection" in ending_data
    assert ending_data["pattern_selection"]["patterns_used"] > 0
    
    # Verify that patterns were selected for all steps by checking generated content exists
    # This proves vector search worked even if metadata isn't stored everywhere yet
    assert hook_data["selected_hook_pattern"] is not None
    assert body_data["selected_body_structure"] is not None  
    assert ending_data["selected_ending_pattern"] is not None
    
    print("✅ End-to-end vector pipeline test passed!")
    print(f"Generated content length: {len(result['generated_post'])} characters")
    print(f"Hook pattern: {hook_data['selected_hook_pattern']}")
    print(f"Body structure: {body_data['selected_body_structure']}")
    print(f"Ending pattern count: {ending_data['pattern_selection']['patterns_used']}")
    
    return result


@pytest.mark.asyncio  
async def test_vector_context_flow():
    """Test that context flows correctly between steps in the vector pipeline."""
    
    user_brief = "Write about the importance of consistent daily habits."
    
    # Run the pipeline
    result = await AgentService.generate_content(
        input_text=user_brief,
        user_preferences=None,
        conversation_id=None,
        conversation_history=None,
        update_step_callback=None,
        post_length="medium"
    )
    
    assert "error" not in result
    scratchpad = result["enhanced_scratchpad"]
    
    # Get generated content from each step
    hook_text = scratchpad["hook_creation"]["generated_hook_text"]
    body_text = scratchpad["body_linguistic_construction"]["generated_body_text"]
    ending_text = scratchpad["ending_creation"]["generated_ending_text"]
    
    # Verify content flows logically
    assert len(hook_text) > 0
    assert len(body_text) > len(hook_text)  # Body should be longer than hook
    assert len(ending_text) > 0
    
    # Verify the final content combines all parts
    final_content = result["generated_post"]
    assert hook_text.strip() in final_content
    assert body_text.strip() in final_content  
    assert ending_text.strip() in final_content
    
    print("✅ Vector context flow test passed!")
    print(f"Hook: {len(hook_text)} chars")
    print(f"Body: {len(body_text)} chars") 
    print(f"Ending: {len(ending_text)} chars")
    print(f"Total: {len(final_content)} chars")


if __name__ == "__main__":
    asyncio.run(test_vector_pipeline_e2e_basic())
    asyncio.run(test_vector_context_flow())