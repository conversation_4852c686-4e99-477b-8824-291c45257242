"""Simple test to verify API cache structure works as expected.

This script tests the caching utility functions to ensure they create
compatible API parameters for caching.
"""

import sys
import os
import logging
import json
import asyncio
from typing import Dict, List, Any
from pprint import pformat
import pytest
from unittest.mock import patch, AsyncMock

# Add project root to system path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

# Import project modules
from src.utils.api_cache_utils import create_api_params
from src.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
SYSTEM_PROMPT = "You are a helpful assistant that analyzes social media content."
CREATOR_NAME = "test_creator"  # Arbitrary name for testing

def create_mock_posts(num_posts: int = 10) -> List[Dict[str, Any]]:
    """Create mock posts for testing."""
    posts = []
    for i in range(num_posts):
        posts.append({
            "title": f"Post ID: {i+1}",
            "content": f"""This is a test post #{i+1}.
            
It has multiple lines to simulate a real LinkedIn post.

The content is long enough to make sure caching will work properly
when combined with other posts.

Engagement metrics:
- Likes: {(i+1) * 10}
- Comments: {(i+1) * 5}
- Shares: {(i+1) * 2}
"""
        })
    return posts

def compare_api_structures(params1: Dict[str, Any], params2: Dict[str, Any]) -> bool:
    """Compare API parameter structures for caching compatibility."""
    # For caching to work, the structure up to and including the cache block must be identical
    
    # Check system prompt
    if params1["system"][0]["text"] != params2["system"][0]["text"]:
        logger.warning("System prompts are different")
        return False
    
    # Check cached block structure (not the content)
    cached1 = params1["system"][1]
    cached2 = params2["system"][1]
    
    if cached1["type"] != cached2["type"]:
        logger.warning("Cache block types are different")
        return False
        
    if "cache_control" not in cached1 or "cache_control" not in cached2:
        logger.warning("Cache control missing from one or both blocks")
        return False
        
    if cached1["cache_control"]["type"] != cached2["cache_control"]["type"]:
        logger.warning("Cache control types are different")
        return False
    
    logger.info("API structures are compatible for caching")
    return True

@pytest.mark.asyncio
async def test_api_structures():
    """Test the structure of the API calls and cache usage."""
    logger.info("Starting API structure compatibility test...")
    
    # Create mock posts
    posts = create_mock_posts(15)  # Create enough posts to exceed MIN_CACHEABLE_SIZE
    
    # Generate API parameters for different analysis types
    first_params = create_api_params(
        system_prompt=SYSTEM_PROMPT,
        posts=posts,
        user_prompt=f"Analyze posts by {CREATOR_NAME} for hook patterns."
    )
    
    second_params = create_api_params(
        system_prompt=SYSTEM_PROMPT,
        posts=posts,
        user_prompt=f"Analyze posts by {CREATOR_NAME} for engagement patterns."
    )
    
    # Compare structures
    logger.info("Comparing API parameters with different user prompts...")
    compatible = compare_api_structures(first_params, second_params)
    
    # Output summary
    logger.info("\n===== API STRUCTURE COMPATIBILITY RESULTS =====")
    logger.info(f"Compatible for caching: {'✅ Yes' if compatible else '❌ No'}")
    
    # Detailed output for debugging
    if os.environ.get("VERBOSE", "0") == "1":
        logger.info("\n===== DETAILED API PARAMETERS =====")
        logger.info(f"First API Params:\n{pformat(first_params)}")
        logger.info(f"Second API Params:\n{pformat(second_params)}")
    
    return compatible

if __name__ == "__main__":
    success = asyncio.run(test_api_structures())
    sys.exit(0 if success else 1)