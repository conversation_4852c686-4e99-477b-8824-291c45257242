"""
Test script for local analysis loader functions.

This script tests the functions in the local_analysis_loader module.
"""

import os
import sys
import json
import unittest
from datetime import datetime
from pathlib import Path

# Add project root to path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.utils.local_analysis_loader import (
    extract_creator_name,
    extract_timestamp,
    find_latest_analysis_file,
    load_json_analysis,
    load_markdown_analysis,
    load_creator_analyses_local
)

@unittest.skip("DEPRECATED: Local analysis loading replaced by vector pipeline")
class TestLocalAnalysisLoader(unittest.TestCase):
    """Test cases for local analysis loader functions.
    
    WARNING: This test class is DEPRECATED.
    The local file-based analysis loading functionality is being replaced
    by the vector-based pipeline. These tests are preserved for reference
    but the underlying functionality is deprecated.
    """
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary test directories
        self.test_dir = os.path.join(project_root, "test_analyses")
        self.json_dir = os.path.join(self.test_dir, "json")
        self.markdown_dir = os.path.join(self.test_dir, "markdown")
        
        # Create creator directories
        self.creator_name = "testcreator"
        self.creator_json_dir = os.path.join(self.json_dir, self.creator_name)
        self.creator_md_dir = os.path.join(self.markdown_dir, self.creator_name)
        
        os.makedirs(self.creator_json_dir, exist_ok=True)
        os.makedirs(self.creator_md_dir, exist_ok=True)
        
        # Create test files
        self.create_test_files()
    
    def create_test_files(self):
        """Create test files for the tests."""
        # JSON test files
        json_data = {"test": "data"}
        
        # Create files with different timestamps
        timestamps = ["20230101000000", "20230201000000", "20230301000000"]
        
        for ts in timestamps:
            # Themes JSON
            filename = f"{self.creator_name}_themes_json_analysis_{ts}.json"
            with open(os.path.join(self.creator_json_dir, filename), 'w') as f:
                json.dump(json_data, f)
            
            # Hooks JSON
            filename = f"{self.creator_name}_hook_json_analysis_{ts}.json"
            with open(os.path.join(self.creator_json_dir, filename), 'w') as f:
                json.dump(json_data, f)
        
        # Markdown test files
        markdown_content = "# Test Markdown\n\nThis is test content."
        
        for ts in timestamps:
            filename = f"{self.creator_name}_body_llm_friendly_analysis_{ts}.md"
            with open(os.path.join(self.creator_md_dir, filename), 'w') as f:
                f.write(markdown_content)
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove test directories
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_extract_creator_name(self):
        """Test extract_creator_name function."""
        filename = "testcreator_themes_json_analysis_20230101000000.json"
        self.assertEqual(extract_creator_name(filename), "testcreator")
        
        filename = "invalid_filename"
        self.assertIsNone(extract_creator_name(filename))
    
    def test_extract_timestamp(self):
        """Test extract_timestamp function."""
        filename = "testcreator_themes_json_analysis_20230101000000.json"
        expected = datetime(2023, 1, 1, 0, 0, 0)
        self.assertEqual(extract_timestamp(filename), expected)
        
        filename = "invalid_filename"
        self.assertIsNone(extract_timestamp(filename))
    
    def test_find_latest_analysis_file(self):
        """Test find_latest_analysis_file function."""
        # Test JSON file
        latest_json = find_latest_analysis_file(
            self.creator_name, "themes_json", self.json_dir, is_json=True
        )
        self.assertIsNotNone(latest_json)
        self.assertTrue("20230301000000" in latest_json)
        
        # Test markdown file
        latest_md = find_latest_analysis_file(
            self.creator_name, "body_llm_friendly", self.markdown_dir, is_json=False
        )
        self.assertIsNotNone(latest_md)
        self.assertTrue("20230301000000" in latest_md)
    
    def test_load_json_analysis(self):
        """Test load_json_analysis function."""
        json_data = load_json_analysis(
            self.creator_name, "themes_json", self.json_dir
        )
        self.assertIsNotNone(json_data)
        self.assertEqual(json_data, {"test": "data"})
    
    def test_load_markdown_analysis(self):
        """Test load_markdown_analysis function."""
        md_content = load_markdown_analysis(
            self.creator_name, "body_llm_friendly", self.markdown_dir
        )
        self.assertIsNotNone(md_content)
        self.assertTrue("# Test Markdown" in md_content)
    
    def test_load_creator_analyses_local(self):
        """Test load_creator_analyses_local function."""
        analyses = load_creator_analyses_local(
            self.creator_name, self.json_dir, self.markdown_dir
        )
        self.assertIsNotNone(analyses)
        self.assertIn("themes", analyses)
        self.assertIn("hooks", analyses)
        self.assertIn("body", analyses)

if __name__ == "__main__":
    unittest.main()
