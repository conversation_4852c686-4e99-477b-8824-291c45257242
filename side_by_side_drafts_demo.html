<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Side-by-Side Drafts Demo</title>
  <!-- Import Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Import IBM Plex Mono font -->
  <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Import Archivo font -->
  <link href="https://fonts.googleapis.com/css2?family=Archivo:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            archivo: ['Archivo', 'sans-serif'],
            'ibm-plex-mono': ['IBM Plex Mono', 'monospace'],
          },
          colors: {
            border: '#e5e5e5',
            input: '#f3f3f3',
            background: '#ededed',
            foreground: '#0a0a0a',
            primary: '#0a0a0a',
            'primary-foreground': '#fafafa',
            secondary: '#f5f5f5',
            'secondary-foreground': '#0a0a0a',
            muted: '#f5f5f5',
            'muted-foreground': '#737373',
            card: '#ffffff',
            'card-foreground': '#0a0a0a',
          },
        },
      },
    }
  </script>
  
  <style>
    body {
      font-family: 'Archivo', sans-serif;
      background-color: #ededed;
      color: #0a0a0a;
    }
    
    .shadow-container {
      position: relative;
      margin-bottom: 7px;
      margin-right: 7px;
    }
    
    .shadow-container-content {
      position: relative;
      background-color: white;
      border-radius: 0.5rem;
      border: 1px solid black;
      overflow: hidden;
      padding: 1.5rem;
      box-shadow: 7px 7px 0px 0px #4D4D4D;
      z-index: 1;
    }
    
    .font-ibm-plex-mono {
      font-family: 'IBM Plex Mono', monospace !important;
    }
    
    .draft-textarea {
      min-height: 300px;
      resize: vertical;
      width: 100%;
      padding: 1rem;
      border: 1px solid #e5e5e5;
      border-radius: 0.375rem;
      background-color: #f9f9f9;
      transition: all 0.2s;
    }
    
    .draft-textarea:focus {
      outline: none;
      border-color: #0a0a0a;
      box-shadow: 0 0 0 2px rgba(10, 10, 10, 0.1);
    }
    
    .character-count {
      font-size: 0.75rem;
      color: #737373;
      text-align: right;
      margin-top: 0.5rem;
    }
    
    /* Dark mode styles */
    .dark body {
      background-color: #0a0a0a;
      color: #fafafa;
    }
    
    .dark .shadow-container-content {
      background-color: #1a1a1a;
      border-color: white;
      box-shadow: 7px 7px 0px 0px #444444;
    }
    
    .dark .draft-textarea {
      background-color: #2a2a2a;
      border-color: #444444;
      color: #fafafa;
    }
    
    .dark .draft-textarea:focus {
      border-color: #fafafa;
      box-shadow: 0 0 0 2px rgba(250, 250, 250, 0.1);
    }
  </style>
</head>
<body class="p-4 md:p-8">
  <div class="max-w-7xl mx-auto">
    <!-- Brief Summary -->
    <div class="shadow-container mb-8">
      <div class="shadow-container-content">
        <h1 class="text-2xl font-bold mb-4">Your Brief</h1>
        <div class="bg-muted/30 p-4 rounded-lg border border-border mb-4">
          <p class="whitespace-pre-line">Create a LinkedIn post about how AI can help marketing agencies become more efficient without replacing human creativity. The post should include specific examples and address common concerns about AI taking jobs.</p>
        </div>
        <div class="flex justify-end">
          <button class="bg-primary text-primary-foreground px-4 py-2 rounded-md font-ibm-plex-mono text-sm hover:bg-primary/90 transition-colors">
            Edit Brief
          </button>
        </div>
      </div>
    </div>
    
    <!-- Side-by-Side Drafts -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Draft #1 -->
      <div class="shadow-container">
        <div class="shadow-container-content">
          <h2 class="text-xl font-bold mb-4 font-ibm-plex-mono">Draft #1</h2>
          <div class="mb-4">
            <label for="draft1" class="block text-sm font-medium mb-2 font-ibm-plex-mono">
              LINKEDIN POST
            </label>
            <textarea 
              id="draft1" 
              class="draft-textarea"
              placeholder="Your LinkedIn post content..."
              oninput="updateCharCount('draft1', 'charCount1')"
            >AI isn't replacing marketers—it's supercharging them.

After implementing AI tools at our agency, we've seen:

• 40% reduction in time spent on repetitive tasks
• 3x more campaign variations tested
• Faster first drafts, better final products

The key? Using AI for what it does best (data analysis, content variations, personalization at scale) while keeping humans in charge of strategy, emotional resonance, and creative direction.

The agencies that thrive won't be "AI-only" or "human-only"—they'll be the ones that master the perfect collaboration between the two.

What's your experience with AI in marketing? Has it freed your team to do more creative work, or created new challenges?

#MarketingAI #CreativeStrategy #FutureOfWork</textarea>
            <div id="charCount1" class="character-count">Characters: 0/3000</div>
          </div>
          <div class="flex justify-end">
            <button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md font-ibm-plex-mono text-sm hover:bg-secondary/90 transition-colors mr-2">
              Copy
            </button>
          </div>
        </div>
      </div>
      
      <!-- Draft #2 -->
      <div class="shadow-container">
        <div class="shadow-container-content">
          <h2 class="text-xl font-bold mb-4 font-ibm-plex-mono">Draft #2</h2>
          <div class="mb-4">
            <label for="draft2" class="block text-sm font-medium mb-2 font-ibm-plex-mono">
              LINKEDIN POST
            </label>
            <textarea 
              id="draft2" 
              class="draft-textarea"
              placeholder="Your LinkedIn post content..."
              oninput="updateCharCount('draft2', 'charCount2')"
            >The "AI vs. human creativity" debate misses the point entirely.

It's not either/or—it's both.

At our agency, we've transformed our workflow by:

1. Using AI to analyze performance data across 1000+ campaigns (impossible manually)
2. Generating 15+ headline variations in seconds (vs. hours of brainstorming)
3. Personalizing content for different segments without doubling our team size

But the strategic insights, emotional storytelling, and breakthrough creative concepts? Still 100% human.

The real question isn't whether AI will replace creative jobs—it's whether creative professionals who embrace AI will replace those who don't.

What AI tools have most enhanced your marketing team's capabilities?

#AIinMarketing #CreativeInnovation #MarketingEfficiency</textarea>
            <div id="charCount2" class="character-count">Characters: 0/3000</div>
          </div>
          <div class="flex justify-end">
            <button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md font-ibm-plex-mono text-sm hover:bg-secondary/90 transition-colors mr-2">
              Copy
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Function to update character count
    function updateCharCount(textareaId, counterId) {
      const textarea = document.getElementById(textareaId);
      const counter = document.getElementById(counterId);
      const count = textarea.value.length;
      counter.textContent = `Characters: ${count}/3000`;
      
      // Change color if approaching limit
      if (count > 2700) {
        counter.style.color = '#e05d41';
      } else {
        counter.style.color = '';
      }
    }
    
    // Initialize character counts
    window.onload = function() {
      updateCharCount('draft1', 'charCount1');
      updateCharCount('draft2', 'charCount2');
    };
  </script>
</body>
</html>