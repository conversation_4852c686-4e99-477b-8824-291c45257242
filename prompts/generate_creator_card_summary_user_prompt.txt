Creator Name: {creator_name}

Theme Analysis Summary (from overview_analysis.json and themes_analysis.md):
{theme_summary}

Linguistic Analysis Summary (key points from linguistic_analysis.md - especially voice/tone):
{linguistic_summary}

Structural Patterns Summary (key points from hook, body, ending analyses):
{structural_summary}

Based on the provided summaries, use the generate_creator_style_card tool to create a Creator Style Card with the following fields:
- "creator_name": string
- "top_themes": ARRAY of 3-5 strings (concise theme names) - NOT a comma-separated string
- "voice_keywords": ARRAY of 3-4 strings (descriptive keywords for voice/tone) - NOT a comma-separated string
- "signature_styles": ARRAY of 2-4 EXTREMELY CONCISE strings (2-3 words max for each style descriptor) - NOT a comma-separated string
- "ideal_for_topics": string (a short sentence describing what kind of content this creator is best for emulating)

IMPORTANT: The "top_themes", "voice_keywords", and "signature_styles" fields MUST be arrays of strings, not comma-separated strings. For example:
✅ Correct: ["Theme 1", "Theme 2", "Theme 3"]
❌ Incorrect: "Theme 1, Theme 2, Theme 3"

You MUST use the generate_creator_style_card tool to format your response. Do not respond with plain text or JSON.
Ensure all outputs are concise and suitable for a small UI card.
