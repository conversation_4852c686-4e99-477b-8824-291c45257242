CURRENT TASK: Ending Generation
You are crafting the conclusion and CTA using the hook and body.
Today's date: {current_date}

USER CONTENT PREFERENCES & STRATEGY:
{user_content_preferences}

CURRENT SCRATCHPAD:
{scratchpad}
_This scratchpad contains:
  - `hook`: The generated hook pattern for the post: "{selected_hook_pattern}"
  - `body`: The generated body structure: "{selected_body_structure}"
  - `final_post` (Current Draft): The LinkedIn post content generated so far (hook and body):
    """
    {current_draft_post}
    """_

PREVIOUS STEP SUMMARY (from body creation):
{previous_step_summary}

AVAILABLE PATTERNS:
{analysis_data}

YOUR OBJECTIVE:
1.  Review the PREVIOUS STEP SUMMARY from body creation to understand how the body was structured and the session linguistic patterns used, which should influence your ending approach.
2.  <PERSON>oughly review the `CURRENT SCRATCHPAD`, especially the theme, hook, body structure, and the current draft post content generated so far.
3.  If CONTENT STRATEGY is provided, consider how your ending can subtly align with the content goal without being pushy.
4.  Apply the SESSION LINGUISTIC PATTERNS consistently for voice and style - these were already selected at session start for consistency across all steps.
5.  Carefully examine the AVAILABLE ENDING PATTERNS.
6.  Select the **single most appropriate ending pattern** from `analysis_data` that best fits the existing post's content and the desired engagement style.
7.  Generate the ending text, **faithfully implementing the chosen pattern** while maintaining linguistic consistency. This includes providing a satisfying conclusion to the post and incorporating an effective CTA.

CRITICAL CONSIDERATIONS FOR YOUR ENDING:
-   **Pattern Abstraction:** Use ending patterns for structural inspiration only. Any specific names or handles from analysis_data must not appear in your output.
-   **Linguistic Consistency:** Apply the session linguistic patterns for vocabulary, tone, and stylistic nuances throughout your ending.
-   **Body-to-Ending Transition:** The ending must flow naturally from the body content as described in the PREVIOUS STEP SUMMARY ("{previous_step_summary}").
-   **Content Cohesion:** The ending must logically conclude the discussion from the current draft post and reinforce the core message.
-   **Pattern Adherence:** The generated ending should be a clear and effective implementation of the selected pattern from `analysis_data`.
-   **LENGTH REQUIREMENT:** You MUST keep the ending between 200-400 characters maximum. This ensures the total post stays under LinkedIn's 3000 character limit.
-   **Call-to-Action (CTA) Style:** 
    - Generate contextually appropriate CTAs based on the user's actual needs and preferences
    - If the user has NOT explicitly requested promotional CTAs (in their brief, notes, or preferences), default to soft, engagement-focused CTAs (questions, discussions, sharing thoughts) rather than salesy ones
    - Extract the TECHNIQUE from patterns (e.g., asking a question, creating urgency), not the literal content
    - Never reference creator names or brands from the example patterns - generate CTAs that speak from this post's author's perspective
    - If stage_funnel is provided: awareness = open questions, consideration = thought-provoking prompts, decision = direct engagement asks
-   **Additive Generation:** Your OUTPUT must consist *only* of new, distinct content that forms the conclusion and CTA. It should not repeat or rephrase the final sentences already present in the current draft post. Assume your output will be directly appended to the existing draft.


OUTPUT FORMAT (Strict Adherence Required):
Your entire response MUST be ONLY the REASONING, SELECTION, DECISION SUMMARY, and OUTPUT sections. No extra text.

REASONING:
[Your detailed thought process. Explain:
    - How the PREVIOUS STEP SUMMARY from body creation influenced your approach to the ending.
    - How you applied the session linguistic patterns for consistency.
    - How the existing post content influenced your choice of ending pattern.
    - Which ending pattern from `{analysis_data}` you chose and *why* it's the most suitable for concluding this specific post and driving engagement.
    - How your generated ending text (including the CTA) specifically implements the chosen pattern while maintaining linguistic consistency.]

SELECTION:
[The exact name of the single ending pattern you selected from the AVAILABLE ENDING PATTERNS list.]

DECISION SUMMARY:
[Provide a concise 1-2 sentence summary of your key decision about the ending pattern chosen and how it completes the post. This should capture the essence of how you've concluded the post and the type of CTA used. While this is the final step in the generation process, this summary helps maintain a complete record of the decision-making throughout the post creation.]

CHARACTER COUNT VERIFICATION:
[Count the characters in your ending text and confirm it is between 200-400 characters. If not, revise immediately.]

OUTPUT:
[The ending text, including the call-to-action, based on the selected pattern. CRITICAL: This section must contain ONLY the ending text - no XML tags, no formatting markers, no meta-commentary, no reasoning. Just the pure ending text ready for LinkedIn.]
