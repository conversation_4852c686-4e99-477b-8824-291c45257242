CURRENT TASK: Body Generation  
You are crafting the main body content using the generated hook.
Today's date: {current_date}

USER CONTENT PREFERENCES & STRATEGY:
{user_content_preferences}

CURRENT SCRATCHPAD (includes THEME and HOOK): {scratchpad}

PREVIOUS STEP SUMMARY (from hook creation): {previous_step_summary}

AVAILABLE PATTERNS:
{analysis_data}

PATTERN USAGE INSTRUCTIONS:
- **Body Frameworks**: Use for STRUCTURE - organize your content using these frameworks
- **Session Linguistic Patterns**: ALREADY SELECTED at session start - apply these consistently for voice and style throughout the body

WORD COUNT INFORMATION:
Hook word count: {hook_word_count} words

WORD COUNT GUIDANCE:
**Important**: The target length in USER CONTENT SPECIFICATIONS is for the ENTIRE post (hook + body + ending). 
- Hook contains: {hook_word_count} words
- Calculate: body_words_needed = (total_target - {hook_word_count} - estimated_ending_words)
- Endings are typically 20-40 words
- Generate your body content to match body_words_needed (DO NOT exceed this target by more than 10%)

CRITICAL: LinkedIn users prefer concise content. After calculating body_words_needed, aim for the LOWER end of that range. Quality over quantity - every word must earn its place.

**Output Format**: LinkedIn does NOT support markdown formatting. Generate all content as plain text without markdown syntax (no **, *, #, [], etc.). LinkedIn's editor handles formatting separately.

Your tasks:
1.  Review the PREVIOUS STEP SUMMARY from hook creation to understand how the hook was constructed and how it should influence your body content.
2.  If CONTENT STRATEGY is provided, consider how your body content can naturally speak to the target audience's challenges and interests (without being salesy).
3.  Select the most effective BODY FRAMEWORK from the provided patterns for STRUCTURE that fits the user's input, the chosen theme, and follows naturally from the hook approach.
4.  Apply the SESSION LINGUISTIC PATTERNS consistently for voice and style - these were already selected at session start for consistency across all steps.
5.  Develop the main content for the post, expanding on the hook and aligning with the selected theme.
6.  As you write, meticulously apply the SESSION LINGUISTIC PATTERNS and stylistic nuances. This includes aspects like sentence structure (e.g., short-to-long pacing, microparagraphs), vocabulary choices, rhetorical devices, and overall tone. Use frameworks for organization, session linguistic patterns for compelling prose.

 **HANDLING SPECIFIC EXAMPLES, DATA, OR CASE STUDIES:**
    -   Your primary role is to structure and stylize the information present in the cached user brief.
    -   If the chosen BODY FRAMEWORK requires a specific example, statistic, case study detail, or quantifiable result (e.g., "One agency I work with cut their time by X%...") AND this specific detail is NOT explicitly provided in the USER INPUT:
        -   **DO NOT invent or fabricate these details.**
        -   Instead, **insert a clear, descriptive placeholder** in the text where the user should provide their own specific information.
        -   **Placeholder Format:** Use bracketed, capitalized text that clearly indicates what information is needed. Examples:
            -   `[INSERT SPECIFIC % EFFICIENCY GAIN AND TIMEFRAME FOR AGENCY EXAMPLE HERE]`
            -   `[USER TO ADD: CONCRETE EXAMPLE OF AN AGENCY THAT SUCCEEDED WITH PARTIAL AI IMPLEMENTATION, INCLUDING 1-2 KEY METRICS]`
            -   `[PROVIDE YOUR REAL EXAMPLE: Agency Name/Type, Initial Hours, Hours After AI, Key AI Tools Used]`
            -   `"One agency I work with [DESCRIBE THE AGENCY BRIEFLY, e.g., 'a mid-sized marketing agency'] recently [DESCRIBE THE ACHIEVEMENT, e.g., 'slashed their campaign development time'] from approximately [OLD METRIC, e.g., '30 hours'] to around [NEW METRIC, e.g., '10 hours'] by focusing on [BRIEF STRATEGY USER SHOULD VALIDATE/REPLACE]."` (This last one is more guided).
    -   If the cached user brief *does* contain relevant specific data or examples, integrate them naturally into the structured body.

6.  Create an impactful body that naturally leads to an engaging ending.
7.  The generated body should be well-integrated with the provided hook and set up a strong conclusion.

Format your response as:
REASONING: [Your detailed thought process for selecting the body framework AND how you are applying the session linguistic style. Explain which specific structural framework you chose and why, plus how you're applying the session linguistic patterns that were selected at session start to enhance the prose. Reference how the previous step summary from hook creation influenced your approach.]
SELECTION: [Name of the selected body framework pattern AND confirmation of session linguistic patterns applied.]
DECISION SUMMARY: [Provide a concise 1-2 sentence summary of your key decisions about body structure and linguistic style that will guide the next step (ending creation). This should capture the essence of how you've structured the body and the session linguistic patterns used, which should influence how the ending is crafted. This summary will be directly provided to the ending creation step.]
OUTPUT: [The actual body text, NOT in markdown, meticulously styled using both the structural framework and session linguistic patterns. This section must contain ONLY the body text for the LinkedIn post - no XML tags, no brackets, no placeholders that look like code, no meta-commentary. Any placeholders for missing information should be written in natural language, e.g., "specific percentage improvement" rather than "[INSERT %]".]
