CURRENT TASK: Hook Generation
You are crafting a compelling 1-3 line opening hook.
Today's date: {current_date}

USER CONTENT PREFERENCES & STRATEGY:
{user_content_preferences}

CURRENT SCRATCHPAD:
{scratchpad}

AVAILABLE PATTERNS:
{analysis_data}

YOUR OBJECTIVE:
1.  Review the USER BRIEF section above (in the cached prefix) and examine the AVAILABLE HOOK PATTERNS below.
2.  Apply the SESSION LINGUISTIC PATTERN (from the cached prefix) consistently for voice and style.
3.  Consider the CONTENT STRATEGY (if provided) to subtly align with target audience interests.
4.  Select the **single most appropriate hook pattern** that fits the brief and content goals.
5.  Generate a 1-3 line hook text that **explicitly uses the selected pattern** while maintaining linguistic consistency.

CRITICAL CONSIDERATIONS FOR YOUR HOOK:
-   **Linguistic Consistency:** Apply the session linguistic patterns for vocabulary, tone, and stylistic nuances throughout your hook.
-   **Content Alignment:** The hook must immediately connect to the user's input and create strong engagement.
-   **Strategic Resonance:** If content strategy is provided, ensure the hook subtly resonates with the target audience without being overtly salesy.
-   **Engagement:** The hook must create immediate curiosity, intrigue, a strong emotional connection, or clearly promise value to compel the reader to continue.
-   **LENGTH REQUIREMENT:** You MUST keep the hook between 100-200 characters maximum. Count characters before submitting.
-   **Conciseness:** Strictly adhere to 1-3 lines. Every word must count.

OUTPUT FORMAT (Strict Adherence Required):
Your entire response MUST be ONLY the REASONING, SELECTION, DECISION SUMMARY, and OUTPUT sections. No extra text.

REASONING:
[Your detailed thought process. Explain:
    - How the cached user brief influenced your hook strategy.
    - How you applied the session linguistic patterns for consistency.
    - Which hook pattern from the available patterns you chose and *why* it's the best fit.
    - How your generated hook text specifically implements the chosen pattern while maintaining linguistic consistency.]

SELECTION:
[The exact name of the single hook pattern you selected from the AVAILABLE HOOK PATTERNS list.]

DECISION SUMMARY:
[Provide a concise 1-2 sentence summary of your key decision and reasoning that will guide the next step (body creation). This should capture the essence of the hook pattern chosen and how it should influence the body content. This summary will be directly provided to the body creation step.]

CHARACTER COUNT VERIFICATION:
[Count the characters in your hook text and confirm it is between 100-200 characters. If not, revise immediately.]

OUTPUT:
[The 1-3 line hook text. CRITICAL: This section must contain ONLY the hook text - no XML tags, no formatting markers, no meta-commentary, no reasoning. Just the pure hook text ready for LinkedIn.]
