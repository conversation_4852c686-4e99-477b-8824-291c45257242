# **Bundle Discovery Analysis Prompt (for Contextual Linguistic Style Bundles)**

## **Task Overview**

You will analyze a collection of LinkedIn posts to discover **natural linguistic style bundles** - clusters of posts that share similar linguistic patterns, contextual usage, and stylistic characteristics. Your task is to identify 3-4 distinct style bundles that emerge organically from the content, rather than forcing predetermined categories.

**Core Objective:** Discover how this creator naturally varies their linguistic style based on content context, audience relationship, and communicative intent. Each bundle should represent a coherent collection of posts that share similar linguistic DNA.

## **Input**

* A collection of ~50 LinkedIn posts by the creator
* The creator's identifier (e.g., "retentionadam")

## **Output Format**

**CRITICAL**: You must use the `extract_linguistic_bundles` tool to structure your response. Do not respond with free-form text or JSON - use the tool to ensure proper data structure.

Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt. The output should be directly usable by content generation systems without requiring further interpretation.

## **Analysis Process**

### **Step 1: Pattern Recognition**

First, read through all posts and identify natural clusters based on:
- **Linguistic similarities**: Similar sentence structures, vocabulary, tone
- **Contextual usage**: Similar content types, audience relationships, communicative goals
- **Stylistic coherence**: Posts that "feel" like they belong together linguistically

Look for patterns like:
- Posts where the creator shares personal experiences vs. gives tactical advice
- Posts with different emotional tones (vulnerable vs. authoritative vs. contrarian)
- Posts with different structural approaches (storytelling vs. instruction vs. philosophical)

### **Step 2: Bundle Identification**

Group posts into 3-4 natural bundles. Each bundle should contain:
- 8-15 posts that share linguistic characteristics
- A coherent usage context (when this style emerges)
- Distinct linguistic patterns that differentiate it from other bundles

**Do NOT force posts into bundles** - if a post doesn't clearly fit a pattern, note it as an outlier.

### **Step 3: Comprehensive Linguistic Analysis**

For each discovered bundle, perform the complete linguistic analysis following the original schema structure:

**1_overall_voice_tone**: Analyze how tone and voice vary within this bundle
**2_sentence_rhythm_and_flow**: Extract sentence patterns, length distributions, and rhythm preferences specific to this bundle
**3_paragraph_dynamics**: Document paragraph length patterns, sequencing, and whitespace usage
**4_vocabulary_and_phrasing**: Identify distinctive vocabulary, jargon level, and metaphor styles
**5_rhetorical_signature**: Capture rhetorical devices and persuasion patterns used in this context
**6_structural_emphasis_techniques**: Note formatting, emphasis, and structural preferences
**7_linguistic_dos**: Rules for what TO do when writing in this bundle's style (with priority scores 1-10)
**8_linguistic_donts**: Rules for what to AVOID in this bundle's style (with priority scores and rationales)
**9_mid_body_reader_engagement_linguistics**: How the creator engages readers within this style
**10_transition_techniques**: Specific transition patterns used in this bundle
**11_contextual_application_rules**: When and how to apply this bundle's techniques
**12_overall_linguistic_intent_summary**: The strategic intent behind this bundle's style

### **Step 4: Rich Example Extraction & Quantification**

For each linguistic pattern, extract multiple real examples from the posts in that bundle and quantify patterns:

**Examples**: 5-10 authentic examples per sentence pattern from real posts
**Quantification**: Provide frequency_percent scores for sentence patterns, paragraph distributions, etc.
**Priority Scoring**: Use 1-10 priority scores for linguistic dos/donts based on importance
**Real Templates**: Use actual phrases and transitions, not theoretical constructs
**Concrete Metrics**: Sentence length distributions, paragraph length patterns with percentages

**Quantification Guidance**: If precise percentages are difficult due to sample size, provide best estimates based on observed patterns. Focus on relative frequencies and importance rankings.

### **Step 5: Contextual Insight**

Capture the deeper context that explains each bundle:
- What audience relationship drives this style?
- What emotional intent underlies these patterns?
- When does the creator naturally shift into this mode?
- How does this bundle serve their overall communication strategy?

## **Output Schema: ContextualBundles v1.0.0**

```json
{
  "schema_version": "1.0.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",
  "discovery_method": "organic_clustering",
  "total_posts_analyzed": 0,
  "discovered_bundle_count": 0,
  "bundles": [
    {
      "bundle_name": "string (discovered organically)",
      "bundle_description": "string (what makes these posts cluster together)",
      "usage_context": "string (when this linguistic style emerges)",
      "post_count_in_bundle": 0,
      "representative_posts": [
        {
          "post_id": "string",
          "excerpt": "string (first 100-150 chars)",
          "why_representative": "string (what makes this exemplify the bundle)",
          "linguistic_markers": ["string", "string"] 
        }
      ],
      "audience_relationship": "string (how creator positions themselves)",
      "emotional_intent": "string (underlying emotional goal)",
      "content_characteristics": [
        "string (type of content that triggers this style)"
      ],
      "1_overall_voice_tone": {
        "primary_descriptors": ["string", "string", "string"],
        "secondary_descriptors": ["string", "string"],
        "audience_relationship": "string",
        "emotional_tone_patterns": {
          "primary_emotions_evoked": ["string", "string", "string"],
          "emotional_arc": "string",
          "tone_variation_by_content_type": [
            {
              "content_type": "string",
              "typical_tone": "string"
            }
          ]
        }
      },
      "2_sentence_rhythm_and_flow": {
        "typical_sentence_length_words_description": "string",
        "sentence_length_distribution": [
          {"length_words": "string", "frequency_percent": 0},
          {"length_words": "string", "frequency_percent": 0}
        ],
        "dominant_sentence_patterns": [
          {
            "name": "string (discovered from posts)",
            "example_template": "string (abstracted pattern)",
            "creator_example": "string (real example from posts)",
            "frequency_percent": 0,
            "typical_usage_context": "string"
          }
        ],
        "sentence_variety_preference": "string"
      },
      "3_paragraph_dynamics": {
        "sentences_per_paragraph_rule": "string",
        "paragraph_length_distribution": [
          {"sentences_count": 0, "frequency_percent": 0},
          {"sentences_count": 0, "frequency_percent": 0}
        ],
        "whitespace_preference": "string",
        "paragraph_sequencing_patterns": [
          {
            "pattern_name": "string",
            "description": "string",
            "example": "string"
          }
        ]
      },
      "4_vocabulary_and_phrasing": {
        "formality_and_specificity_level": "string",
        "jargon_level": "string",
        "unique_creator_lexicon": [
          {
            "term": "string",
            "meaning": "string",
            "typical_usage": "string"
          }
        ],
        "vocabulary_preferences": {
          "preferred_industry_terms": ["string", "string"],
          "avoided_terms": ["string", "string"],
          "specificity_with_numbers": "boolean"
        },
        "metaphor_and_analogy_style": "string"
      },
      "5_rhetorical_signature": {
        "key_rhetorical_devices_used": [
          {
            "device": "string",
            "frequency": "string",
            "typical_context": "string",
            "example": "string"
          }
        ],
        "overall_persuasion_style_summary": "string",
        "argument_construction_pattern": "string"
      },
      "6_structural_emphasis_techniques": {
        "text_formatting_for_emphasis": ["string"],
        "use_of_standalone_impact_phrases": {
          "frequency": "string",
          "typical_length_words": "string",
          "typical_placement": "string",
          "example": "string"
        },
        "emphasis_patterns": [
          {
            "technique": "string",
            "example": "string"
          }
        ]
      },
      "7_linguistic_dos": [
        {
          "rule": "string",
          "priority": 0.0,
          "example": "string"
        }
      ],
      "8_linguistic_donts": [
        {
          "rule": "string",
          "priority": 0.0,
          "rationale": "string"
        }
      ],
      "9_mid_body_reader_engagement_linguistics": {
        "rhetorical_questions_for_reflection_style": {
          "frequency": "string",
          "typical_placement": "string",
          "example": "string"
        },
        "direct_address_to_reader_style": {
          "frequency": "string",
          "typical_phrasing": "string",
          "example": "string"
        },
        "anticipation_building_phrasing_style": "string",
        "validation_or_empathy_cue_usage": "string"
      },
      "10_transition_techniques": {
        "between_paragraphs": {
          "dominant_style": "string",
          "common_transition_phrases": ["string", "string"]
        },
        "between_sections": {
          "dominant_style": "string",
          "examples": ["string", "string"]
        },
        "hook_to_body_transition": {
          "typical_pattern": "string",
          "examples": ["string", "string"]
        },
        "body_to_ending_transition": {
          "typical_pattern": "string",
          "examples": ["string", "string"]
        },
        "pacing_variation": "string"
      },
      "11_contextual_application_rules": [
        {
          "when": "string",
          "apply": "string",
          "avoid": "string",
          "example": "string"
        }
      ],
      "12_overall_linguistic_intent_summary": "string",
      "contextual_triggers": [
        {
          "trigger": "string (what prompts this style)",
          "response": "string (how the style manifests)",
          "examples": ["string", "string"]
        }
      ]
    }
  ],
  "cross_bundle_analysis": {
    "style_variation_drivers": ["string", "string"],
    "consistent_elements": ["string", "string"], 
    "bundle_boundaries": "string (what differentiates bundles)",
    "evolution_patterns": "string (how style changes across bundles)"
  },
  "discovery_insights": {
    "primary_finding": "string (key insight about creator's style variation)",
    "bundle_naturalness": "string (how organic vs forced the clusters felt)",
    "outlier_posts": 0,
    "confidence_level": "string (high/medium/low confidence in bundle boundaries)"
  }
}
```

## **Key Requirements**

1. **Organic Discovery**: Let the bundles emerge naturally from the data, don't force predetermined categories
2. **Rich Examples**: Include many real examples from actual posts, not theoretical templates  
3. **Contextual Depth**: Capture not just what patterns exist, but why they emerge in specific contexts
4. **Actionable Patterns**: Ensure each pattern has enough detail for content generation
5. **Natural Boundaries**: Only create bundles where clear linguistic distinctions exist
6. **Authentic Voice**: Preserve the creator's authentic style markers in each bundle

## **Example Application**

The content generation system will use this analysis to:
1. Select the appropriate bundle based on content context
2. Apply the linguistic patterns specific to that bundle
3. Use the real examples as style templates
4. Follow the contextual rules for that specific style mode
5. Maintain authentic voice while adapting to different content scenarios

Your analysis should enable natural, context-appropriate style variation that reflects how the creator actually writes in different situations.