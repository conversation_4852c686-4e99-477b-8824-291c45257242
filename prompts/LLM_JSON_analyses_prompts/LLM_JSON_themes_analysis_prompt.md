# LLM-Focused Themes Analysis Prompt (for JSON Style Guide Generation)

## Task Overview
You will analyze the content themes and topics of a LinkedIn creator based on their posts. Your primary task is to generate a **structured JSON object** conforming to the `JsonThemesStyleGuide` schema provided below. This guide will be used directly by an AI content generation system to understand the creator's preferred content topics, themes, and engagement patterns to generate highly relevant and on-brand content.

Content themes represent the recurring subject matters, topics, and areas of expertise that define a creator's content strategy. Understanding these themes is crucial for generating authentic content that aligns with the creator's established areas of authority and resonates with their audience's expectations.

## Input
* A collection of LinkedIn posts by the creator (the complete posts including hooks, body, and endings).
* The creator's identifier (e.g., "retentionadam").
* (Optional) An existing human-readable Markdown analysis of the creator's content themes, which you can use as a reference to synthesize information.
* (Optional) Engagement metrics for the posts if available.

## Output Format
Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt (see "Output Schema: JsonThemesStyleGuide v1.0.0"). The output should be directly usable by another LLM for theme selection without requiring further human interpretation.

## Analysis Process

### Step 1: Thorough Content Review
Carefully review all provided posts to identify recurring themes, topics, and subject areas. If a reference Markdown analysis is provided, review it to understand existing interpretations of the creator's content strategy. Your primary source for examples and direct observations should always be the creator's actual posts.

### Step 2: Theme Identification & Quantification
Identify primary and secondary content themes, their characteristics, and engagement patterns. Where possible, estimate frequencies or engagement metrics.
* **Guidance on Quantification:**
    * When providing `frequency_percent`, use a 0.0-1.0 scale representing the approximate proportion of content dedicated to this theme. If precise quantification is difficult due to sample size, provide your best estimate.
    * For `engagement_level_relative`, use a 0.0-1.0 scale where 1.0 represents the creator's highest-engaging theme and other values are scaled relatively.
    * When available, include actual engagement metrics (average likes, comments, shares) for each theme.
    * Clearly indicate if a value is an estimate based on general observation rather than hard statistical counts.

### Step 3: Structured Analysis & Schema Population
Organize your findings meticulously according to the provided JSON schema. Be specific and precise:
* Include authentic, concise examples from the creator's content to illustrate each theme.
* Prioritize themes based on their frequency, consistency, and engagement levels in the creator's content.
* For `1_overall_content_strategy_summary.description`, provide a holistic overview of the creator's content approach, including their areas of expertise, tone, and apparent goals.
* When populating `2_primary_content_themes`, focus on the 3-5 most dominant themes that appear consistently throughout the creator's content. Include detailed descriptions, examples, and engagement metrics when available. For `theme_specific_engagement_tactics`, identify the specific techniques the creator uses to drive engagement when discussing this particular theme (e.g., asking specific types of questions, using certain formatting, sharing particular resources).
* For `3_secondary_content_themes`, identify 2-4 less frequent but still notable themes that supplement the primary content strategy. Include `theme_specific_engagement_tactics` as you did for primary themes.
* For `4_theme_interconnection_patterns`, analyze how different themes relate to and support each other in the creator's overall content strategy.
* For `5_content_theme_dos_and_best_practices` and `6_content_theme_donts_and_pitfalls_to_avoid`, convert your observations into clear guidance for theme selection and development.
* For `7_audience_resonance_by_theme`, analyze how different themes appear to resonate with the audience based on engagement metrics or comment sentiment.
* For `8_theme_presentation_patterns`, examine how the creator typically introduces and develops different themes in their content.
* For `9_theme_specific_vocabulary`, identify key terms, phrases, or jargon associated with each major theme.
* For `10_seasonal_or_topical_themes`, identify any themes that appear tied to specific times, events, or trends.
* For `11_theme_evolution_trends`, analyze how the creator's theme preferences have evolved over time, if discernible from the available data.
* For `12_overall_theme_effectiveness_assessment`, provide a concise assessment of which themes appear most effective and why.
* **Handling Non-Prominent Features:** If a specific element defined in the schema is not notably present or consistently used by the creator:
    * For array fields, provide an empty list `[]`.
    * For optional string or object fields where no pattern is observed, use `null` or a specific descriptive string like "No prominent pattern observed" or "Insufficient data to determine" if the field type allows.
    * Do not invent patterns merely to fill the schema.

### Step 4: Validation (Self-Correction)
Before finalizing your output, review your completed JSON for:
* **Schema Conformance:** Does it exactly match the provided schema structure and data types?
* **Completeness:** Are all relevant schema fields populated according to the guidance?
* **Accuracy:** Do the identified themes and examples genuinely reflect the creator's actual content strategy?
* **Actionability:** Is the guidance specific and clear enough for another LLM to implement effectively?
* **Consistency:** Is there any contradictory guidance within the JSON?
* **Example Diversity:** Do the examples provided represent a range of the creator's content rather than focusing on just one or two posts?

## Output Schema: JsonThemesStyleGuide v1.1.0
Your analysis **must** conform exactly to this JSON schema:
```json
{
  "schema_version": "1.1.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",

  "1_overall_content_strategy_summary": {
    "description": "string",
    "primary_areas_of_expertise": ["string"],
    "apparent_content_goals": ["string"]
  },

  "2_primary_content_themes": [
    {
      "theme_name": "string",
      "description": "string",
      "characteristic_elements": ["string"],
      "creator_example_best": "string",
      "frequency_percent": 0.0,
      "engagement_metrics": {
        "avg_likes": 0.0,
        "avg_comments": 0.0,
        "avg_shares": 0.0
      },
      "engagement_level_relative": 0.0,
      "theme_specific_engagement_tactics": ["string"],
      "why_theme_works": "string"
    }
  ],

  "3_secondary_content_themes": [
    {
      "theme_name": "string",
      "description": "string",
      "characteristic_elements": ["string"],
      "creator_example": "string",
      "frequency_percent": 0.0,
      "engagement_metrics": {
        "avg_likes": 0.0,
        "avg_comments": 0.0,
        "avg_shares": 0.0
      },
      "engagement_level_relative": 0.0,
      "theme_specific_engagement_tactics": ["string"]
    }
  ],

  "4_theme_interconnection_patterns": [
    {
      "primary_theme": "string",
      "secondary_theme": "string",
      "connection_description": "string",
      "example_of_interconnection": "string"
    }
  ],

  "5_content_theme_dos_and_best_practices": [
    {
      "do_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "6_content_theme_donts_and_pitfalls_to_avoid": [
    {
      "dont_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "7_audience_resonance_by_theme": [
    {
      "theme": "string",
      "audience_response_pattern": "string",
      "typical_comment_types": ["string"],
      "engagement_driver_hypothesis": "string"
    }
  ],

  "8_theme_presentation_patterns": [
    {
      "theme": "string",
      "typical_content_structure": "string",
      "common_formats_used": ["string"],
      "characteristic_intro_approach": "string"
    }
  ],

  "9_theme_specific_vocabulary": [
    {
      "theme": "string",
      "key_terms_and_phrases": ["string"],
      "industry_jargon": ["string"],
      "creator_unique_terminology": ["string"]
    }
  ],

  "10_seasonal_or_topical_themes": [
    {
      "theme": "string",
      "seasonal_or_event_trigger": "string",
      "frequency_pattern": "string",
      "typical_approach": "string"
    }
  ],

  "11_theme_evolution_trends": {
    "emerging_themes": ["string"],
    "declining_themes": ["string"],
    "consistent_core_themes": ["string"],
    "evolution_pattern_description": "string"
  },

  "12_overall_theme_effectiveness_assessment": {
    "most_effective_themes": ["string"],
    "effectiveness_drivers_summary": "string",
    "theme_selection_guidance": "string"
  }
}
```