# LLM-Focused Ending Analysis Prompt (for JSON Style Guide Generation)

## Task Overview
You will analyze the ending writing style of a LinkedIn creator based on their posts. Your primary task is to generate a **structured JSON object** conforming to the `JsonEndingStyleGuide` schema provided below. This guide will be used directly by an AI content generation system to emulate the creator's distinctive ending style with high fidelity.

An "ending" is typically the final paragraph or closing segment of a post, designed to conclude the content effectively, often including a call-to-action, summary, or provocative thought that invites engagement. Effective endings are critical on LinkedIn as they often determine whether readers engage through comments, likes, or shares. When analyzing posts, consider the ending to be the final 1-3 lines or natural conclusion of the content.

## Input
* A collection of LinkedIn posts by the creator (focus on the final paragraph or concluding lines of each).
* The creator's identifier (e.g., "retentionadam").
* (Optional) An existing human-readable Markdown analysis of the creator's endings, which you can use as a reference to synthesize information.

## Output Format
Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt (see "Output Schema: JsonEndingStyleGuide v1.0.0"). The output should be directly usable by another LLM for style emulation without requiring further human interpretation.

## Analysis Process

### Step 1: Thorough Review of Endings
Carefully review the closing lines (endings) of all provided posts. If a reference Markdown analysis is provided, review it to understand existing interpretations of patterns, call-to-actions, and frameworks. Your primary source for examples and direct observations should always be the creator's actual post endings.

### Step 2: Pattern Identification & Quantification
Identify recurring ending types, engagement techniques, structural characteristics, and linguistic elements. Where possible, estimate frequencies or commonalities.
* **Guidance on Quantification:**
    * When providing `priority` scores (e.g., in `ending_dos_and_best_practices`), use a 0.0-1.0 scale where 1.0 is highest. If precise quantification is difficult due to sample size or the nature of the reference material, provide your best estimate.
    * For `estimated_frequency_category` (e.g., in `dominant_ending_types`), use descriptive categories like "Very Frequent," "Frequent," "Occasional," or "Rare," based on your observations or the reference analysis.
    * Clearly indicate if a value is an estimate based on general observation rather than hard statistical counts if the schema allows for such annotation (e.g., in a descriptive string field).

### Step 3: Structured Analysis & Schema Population
Organize your findings meticulously according to the provided JSON schema. Be specific and precise:
* Include authentic, concise examples from the creator's endings to illustrate patterns.
* Prioritize rules and patterns based on their impact and consistency in the creator's style.
* For `1_overall_ending_strategy_summary.description`, synthesize the creator's approach to endings, including their typical goals and techniques. For `primary_goals_of_endings`, infer these from the overall analysis of patterns and engagement techniques.
* When populating `2_dominant_ending_types`, identify the most common types of endings used by the creator (e.g., "Question-Based CTA," "Bold Statement," "Value Proposition," "Personal Invitation"). Provide detailed descriptions and examples.
* For `3_key_engagement_techniques_leveraged`, identify the top 3-4 most impactful techniques the creator uses to drive engagement in their endings.
* For `4_ending_structural_characteristics`, analyze line count, word count, and common closing phrases or elements.
* For fields like `5_ending_linguistic_style_highlights` and `6_ending_formatting_for_impact`, provide analysis based on direct observation of the creator's ending styles.
* For `7_ending_dos_and_best_practices` and `8_ending_donts_and_pitfalls_to_avoid`, convert your observations into clear 'do' or 'don't' statements. Infer `priority` based on the emphasis given in the analysis or the observed impact.
* For `9_call_to_action_elements`, analyze the techniques, phrasing, and psychological impact of the creator's CTAs. For `cta_body_connection`, examine how CTAs relate to or follow from the content in the body of the post.
* For `10_audience_connection_in_endings`, identify how the creator builds a relationship with their audience in the closing segments.
* For `11_emotional_resonance_in_endings`, identify the primary emotions the creator aims to evoke in their endings and how they complete the emotional arc of the post.
* For `12_body_to_ending_transition_techniques`, analyze how the creator transitions from the main body content to the ending, including any signal phrases, formatting changes, or tonal shifts.
* For `13_overall_ending_effectiveness_drivers_summary`, provide a concise distillation of why the creator's endings are effective.
* If engagement metrics (likes, comments) are available for posts, note any correlation between specific ending types and higher engagement levels in your analysis.
* **Handling Non-Prominent Features:** If a specific stylistic element defined in the schema is not notably present or consistently used by the creator (based on your direct analysis of posts and any reference material):
    * For array fields, provide an empty list `[]`.
    * For optional string or object fields where no pattern is observed, use `null` or a specific descriptive string like "No prominent pattern observed" or "Not a defining feature" if the field type allows.
    * Do not invent patterns merely to fill the schema.

### Step 4: Validation (Self-Correction)
Before finalizing your output, review your completed JSON for:
* **Schema Conformance:** Does it exactly match the provided schema structure and data types?
* **Completeness:** Are all relevant schema fields populated according to the guidance?
* **Accuracy:** Do the identified patterns and examples genuinely reflect the creator's actual ending style?
* **Actionability:** Is the guidance specific and clear enough for another LLM to implement effectively?
* **Consistency:** Is there any contradictory guidance within the JSON?
* **Example Diversity:** Do the examples provided represent a range of the creator's endings rather than focusing on just one or two patterns?

## Output Schema: JsonEndingStyleGuide v1.1.0
Your analysis **must** conform exactly to this JSON schema:
```json
{
  "schema_version": "1.1.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",

  "1_overall_ending_strategy_summary": {
    "description": "string",
    "primary_goals_of_endings": ["string"]
  },

  "2_dominant_ending_types": [
    {
      "ending_type_name": "string",
      "description_and_key_elements": "string",
      "creator_example_best": "string",
      "typical_engagement_impact": "string",
      "estimated_frequency_category": "string"
    }
  ],

  "3_key_engagement_techniques_leveraged": [
    {
      "technique_name": "string",
      "how_creator_applies_it_in_endings": "string",
      "creator_example": "string"
    }
  ],

  "4_ending_structural_characteristics": {
    "typical_line_count": "string",
    "typical_word_count_range": ["integer", "integer"],
    "common_closing_phrases_or_elements": ["string"]
  },

  "5_ending_linguistic_style_highlights": {
    "use_of_action_verbs_style": "string",
    "question_usage_style_in_endings": "string",
    "directness_level_in_endings_description": "string"
  },

  "6_ending_formatting_for_impact": {
    "use_of_all_caps_or_bold_in_ending_description": "string",
    "emoji_usage_in_ending_description": "string"
  },

  "7_ending_dos_and_best_practices": [
    {
      "do_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "8_ending_donts_and_pitfalls_to_avoid": [
    {
      "dont_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "9_call_to_action_elements": {
      "cta_frequency": "string",
      "common_cta_phrasing": ["string"],
      "typical_cta_intensity": "string",
      "action_request_specificity": "string",
      "cta_body_connection": "string"
  },

  "10_audience_connection_in_endings": {
      "relationship_building_techniques": ["string"],
      "value_proposition_framing": "string",
      "community_building_elements": ["string"]
  },

  "11_emotional_resonance_in_endings": {
      "primary_emotions_evoked": ["string"],
      "emotional_arc_completion_style": "string"
  },

  "12_body_to_ending_transition_techniques": ["string"],

  "13_overall_ending_effectiveness_drivers_summary": "string"
}
```