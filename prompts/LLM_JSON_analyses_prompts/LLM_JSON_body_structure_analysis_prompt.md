# LLM-Focused Body Structure Analysis Prompt (for JSON Style Guide Generation)

## Task Overview
You will analyze the body structures and frameworks used by a LinkedIn creator in their posts. Your primary task is to generate a **structured JSON object** conforming to the `JsonBodyStructureGuide` schema provided below. This guide will be used directly by an AI content generation system to emulate the creator's distinctive body structure patterns with high fidelity.

This analysis focuses exclusively on the **structural organization** of post bodies, not linguistic style (which is captured in a separate analysis). Your analysis should identify, document, and exemplify the frameworks, patterns, and organizational techniques that define how the creator structures the body content of their posts.

For the purpose of this analysis, the "body" is defined as:
- The main content section that follows the hook/opening and precedes the ending/conclusion
- Typically constitutes 80-90% of the post's content
- Contains the substantive arguments, explanations, stories, or advice
- Usually spans from the second or third paragraph through most of the post

## Input
* A collection of LinkedIn posts by the creator (focus on the body sections between the hooks and endings).
* The creator's identifier (e.g., "retentionadam").
* (Optional) An existing human-readable Markdown analysis of the creator's body structures, which you can use as a reference to synthesize information.
* (Optional) Engagement metrics for the posts if available.

## Output Format
Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt (see "Output Schema: JsonBodyStructureGuide v1.0.0"). The output should be directly usable by another LLM for structure selection and application without requiring further human interpretation.

## Analysis Process

### Step 1: Thorough Review of Body Structures
Carefully review the body sections of all provided posts to identify recurring structural patterns, organization techniques, and frameworks. If a reference Markdown analysis is provided, review it to understand existing interpretations of the creator's body structures. Your primary source for examples and direct observations should always be the creator's actual posts.

### Step 2: Framework Identification & Documentation
Identify the distinct structural frameworks the creator uses to organize body content. For each framework:
* **Complete Component Sequence**: Document every step in the framework in the exact order they appear, capturing the full template
* **Component Functions**: For each component, explain its specific purpose and function within the overall framework
* **Authentic Examples**: Provide complete, unmodified examples from the creator's posts showing the entire framework in use
* **Application Guidance**: Explain when and how to use the framework effectively
* **Theme Associations**: Identify which content themes this framework is typically used for
* **Engagement Metrics**: Note metrics associated with posts using this framework, if available

### Step 3: Structured Analysis & Schema Population
Organize your findings meticulously according to the provided JSON schema. Be specific and precise:

* **Overall Strategy (1_overall_body_structure_strategy)**:
  - Provide a comprehensive overview of the creator's approach to structuring body content
  - Identify the primary structural approaches they consistently use
  - Infer the apparent goals these structures are designed to achieve

* **Framework Catalog (2_body_frameworks_catalog)**:
  - Document each framework as a complete, reusable template
  - List all components in precise sequential order
  - For each component, provide a detailed description of its function
  - Include a full, authentic example showing the entire framework in use
  - Provide clear guidance on when and how to use the framework
  - Include engagement metrics where available

* **Organization Techniques (3_content_organization_techniques)**:
  - Document specific techniques used to organize content within frameworks
  - Explain how each technique functions and when it's typically applied
  - Provide authentic examples showing the technique in practice

* **Visual Formatting (4_visual_formatting_patterns)**:
  - Analyze paragraph length preferences with specific character or line counts
  - Document whitespace usage patterns (line breaks between sections, etc.)
  - Identify all emphasis techniques (bold, ALL CAPS, standalone lines, etc.)
  - Note structural signposts that organize content (section headers, dividers, etc.)
  - Explain the overall approach to visual chunking and organization

* **Theme Mapping (5_framework_to_theme_mapping)**:
  - For each framework, list the specific themes it's most effectively used for
  - Note any themes the framework is typically not used for
  - Where metrics are available, indicate relative effectiveness by theme

* **Best Practices (6_body_structure_dos_and_best_practices and 7_body_structure_donts_and_pitfalls_to_avoid)**:
  - Convert your observations into clear, actionable guidance
  - Prioritize recommendations based on their apparent importance
  - Provide rationales explaining why each practice is effective or problematic

* **Effectiveness Assessment (8_overall_body_structure_effectiveness_assessment)**:
  - Identify the frameworks that appear most effective based on engagement
  - Summarize the key factors that drive effectiveness across frameworks
  - Provide guidance on how to select the appropriate framework by context

### Step 4: Validation (Self-Correction)
Before finalizing your output, review your completed JSON for:
* **Schema Conformance:** Does it exactly match the provided schema structure and data types?
* **Completeness:** Are all frameworks documented with their complete component sequences?
* **Accuracy:** Do the identified patterns genuinely reflect the creator's actual approach?
* **Actionability:** Could another LLM directly apply these frameworks without additional interpretation?
* **Example Quality:** Do the examples clearly demonstrate each framework in its entirety?
* **Component Detail:** Is each component's function clearly explained?

### Step 5: JSON Format Validation
Carefully check your JSON for formatting errors that could prevent proper parsing:
* **String Formatting:** All string values must be enclosed in double quotes (`"value"`)
* **String Escaping:** All quotes, newlines, and special characters within strings must be properly escaped (`"This is a \"quote\""`; use `\n` for line breaks)
* **Balanced Braces/Brackets:** Every `{` needs a matching `}` and every `[` needs a matching `]` 
* **Comma Usage:** No trailing commas after the last item in arrays or objects
* **Numeric Values:** Numbers should not be quoted
* **Nested Objects:** Check deep nesting for proper formatting and closing delimiters
* **Special Characters:** Ensure any non-ASCII characters are properly escaped

After generating your final JSON, review it as a whole for valid syntax and structure before submitting. Remember that this JSON will be programmatically parsed, so formatting errors will cause parsing failures.

## Output Schema: JsonBodyStructureGuide v1.0.0
Your analysis **must** conform exactly to this JSON schema:
```json
{
  "schema_version": "1.0.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",

  "1_overall_body_structure_strategy": {
    "description": "string",
    "primary_structural_approaches": ["string"],
    "apparent_organizational_goals": ["string"]
  },

  "2_body_frameworks_catalog": [
    {
      "framework_name": "string",
      "framework_description": "string",
      "structure_components_sequence": ["string"],
      "component_functions": {
        "component_name": "string description of function and purpose"
      },
      "creator_example_full": "string",
      "when_to_use": "string",
      "engagement_metrics": {
        "avg_likes": 0.0,
        "avg_comments": 0.0,
        "avg_shares": 0.0,
        "relative_engagement_score": 0.0
      }
    }
  ],

  "3_content_organization_techniques": [
    {
      "technique_name": "string",
      "description": "string",
      "typical_application": "string",
      "creator_example": "string"
    }
  ],

  "4_visual_formatting_patterns": {
    "paragraph_length_preference": "string",
    "whitespace_usage": "string",
    "emphasis_techniques": ["string"],
    "structural_signposts": ["string"],
    "visual_chunking_approach": "string"
  },

  "5_framework_to_theme_mapping": [
    {
      "framework_name": "string",
      "effective_for_themes": ["string"],
      "typically_avoids_for_themes": ["string"],
      "engagement_by_theme": [
        {
          "theme": "string",
          "relative_effectiveness": 0.0
        }
      ]
    }
  ],

  "6_body_structure_dos_and_best_practices": [
    {
      "do_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "7_body_structure_donts_and_pitfalls_to_avoid": [
    {
      "dont_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "8_overall_body_structure_effectiveness_assessment": {
    "most_effective_frameworks": ["string"],
    "effectiveness_drivers_summary": "string",
    "framework_selection_guidance": "string"
  }
}
```

## Example Application
To illustrate how this analysis will be used: The content generation agent will directly reference your JSON output when structuring content. For example:

1. It will select a framework from "body_frameworks_catalog" based on the content theme being addressed
2. It will follow the exact "structure_components_sequence" for that framework, step by step
3. It will implement each component according to its documented function
4. It will apply the identified "content_organization_techniques" throughout the body
5. It will follow the "visual_formatting_patterns" for presenting the content
6. It will adhere to the documented "dos" and avoid the identified "donts"

Therefore, your analysis must provide complete, detailed framework templates that can be directly applied by an LLM without requiring further interpretation or elaboration.

## Important Notes
- Focus exclusively on structural patterns and frameworks, not linguistic style or content themes
- Document the precise, complete sequence of components for each framework
- Provide detailed explanations of each component's function within the framework
- Include authentic, complete examples showing each framework in its entirety
- Associate frameworks with the specific themes they're most effectively used for
- Include engagement metrics where available to indicate relative effectiveness
- Ensure all guidance is directly actionable by an LLM

Your output should begin with the complete JSON object, followed by a brief explanation of the key insights about the creator's body structures that would be most important for an LLM to capture when emulating their style.