# **LLM-Focused Body Linguistic Analysis Prompt (for JSON Style Guide Generation)**

## **Task Overview**

You will analyze the linguistic style of a LinkedIn creator's post bodies. Your primary task is to generate a **structured JSON object** that serves as an LLM-friendly style guide specifically for the body section of posts. This guide will be used directly by an AI content generation system to emulate the creator's distinctive linguistic style with high fidelity in the main body content.

**Important: Focus exclusively on the BODY section of posts.** The LinkedInsight system has separate specialized components for analyzing and generating hooks (post openings) and endings. Your analysis should NOT include patterns specific to hooks or endings, but should focus on the main body content that comprises the bulk of each post.

### **Definition of "Body"**

For the purpose of this analysis, the "body" is defined as:
- The main content section that follows the hook/opening and precedes the ending/conclusion
- Typically constitutes 80-90% of the post's content
- Contains the substantive arguments, explanations, stories, or advice
- Usually spans from the second or third paragraph through most of the post
- Does NOT include the attention-grabbing opening lines (hook)
- Does NOT include the final call-to-action, summary, or concluding statements (ending)

While your analysis should focus on the body, you should pay special attention to how the body typically connects to the hook and transitions to the ending. These transition points are crucial for maintaining flow in the generated content.

## **Input**

* A collection of LinkedIn posts by the creator.
* The creator's identifier (e.g., "retentionadam").
* (Optional, if applicable) A predefined list of content\_type categories to consider for tone\_variation\_by\_content\_type.

## **Output Format**

Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt (see "Output Schema: JsonLinguisticStyleGuide v1.2.0"). The output should be directly usable by another LLM for style emulation without requiring further human interpretation.

## **Analysis Process**

### **Step 1: Thorough Review**

First, carefully review all provided posts to identify linguistic patterns, stylistic elements, and distinctive features of the creator's writing. Pay attention to recurring phrases, sentence structures, vocabulary, tone, and rhetorical choices.

### **Step 2: Pattern Identification & Quantification**

Identify quantifiable patterns where possible. Your goal is to be as specific as the source material allows:

* Analyze sentence length distributions, paragraph structure preferences, and vocabulary choices within the body sections.
* Identify unique terminology, rhetorical devices, and their apparent frequency or common contexts.
* Note formatting and emphasis techniques.
* Pay special attention to how the body typically connects to the hook and transitions to the ending.
* **Guidance on Quantification:**
  * When providing frequency\_percent or priority scores (e.g., in sentence\_length\_distribution, dominant\_sentence\_patterns, paragraph\_length\_distribution, linguistic\_dos, linguistic\_donts), if precise quantification from the provided texts is difficult due to sample size or variability, provide your best estimate.
  * You may also use descriptive categories for frequency (e.g., "High," "Medium," "Low") or priority (e.g., "Critical," "Important," "Consider") if exact percentages are not well-supported by the data. If using such categories, ensure the schema definition for that field allows for string types or use a dedicated notes field if available. *For schema v1.2.0, stick to numerical estimates for frequency\_percent and priority where specified, but internally use this guidance to derive those estimates reasonably.*
  * Clearly indicate if a value is an estimate based on general observation rather than hard statistical counts if the schema allows for such annotation (e.g., in a descriptive string field related to the metric).

### **Step 3: Structured Analysis & Schema Population**

Organize your findings meticulously according to the provided JSON schema. Be specific and precise:

* Include authentic, concise examples from the creator's content to illustrate patterns.
* Prioritize rules and patterns based on their impact and consistency in the creator's style (e.g., using the priority field in "linguistic\_dos" and "linguistic\_donts").
* Identify contextual rules for when specific techniques should be applied, especially for the 11\_contextual\_application\_rules field. Focus on 1-3 of the most clear and impactful rules if precise conditions for many rules are hard to formalize from the given texts.
* **Handling Non-Prominent Features:** If a specific stylistic element defined in the schema is not notably present or consistently used by the creator, indicate this explicitly in the JSON.
  * For array fields (e.g., secondary\_descriptors, paragraph\_sequencing\_patterns), provide an empty list \[\].
  * For optional string or object fields where no pattern is observed, use null or a specific descriptive string like "No prominent pattern observed" or "Not a defining feature" if the field type allows.
  * Do not invent patterns merely to fill the schema. Accuracy and reflection of the creator's actual style are paramount.
* **content\_type (for tone\_variation\_by\_content\_type):** If a predefined list of content\_type categories is provided in the input, use those. Otherwise, identify 2-4 common content archetypes you observe in the creator's posts (e.g., "Personal Story," "Contrarian Take," "How-To/Listicle," "Question-Based Engagement Post") and describe the typical tone for each.

### **Step 4: Validation (Self-Correction)**

Before finalizing your output, review your completed JSON for:

* **Schema Conformance:** Does it exactly match the provided schema structure and data types?
* **Completeness:** Are all relevant schema fields populated according to the guidance (using null or \[\] for non-prominent features)?
* **Accuracy:** Do the identified patterns, examples, and quantifications genuinely reflect the creator's actual style as evidenced in their posts?
* **Actionability:** Is the guidance specific and clear enough for another LLM to implement effectively?
* **Consistency:** Is there any contradictory guidance within the JSON?

Your analysis must conform exactly to this JSON schema:

```json
{
  "schema_version": "1.2.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",

  "1_overall_voice_tone": {
    "primary_descriptors": ["string", "string", "string"],
    "secondary_descriptors": ["string", "string"],
    "audience_relationship": "string",
    "emotional_tone_patterns": {
      "primary_emotions_evoked": ["string", "string", "string"],
      "emotional_arc": "string",
      "tone_variation_by_content_type": [
        {
          "content_type": "string",
          "typical_tone": "string"
        }
      ]
    }
  },

  "2_sentence_rhythm_and_flow": {
    "typical_sentence_length_words_description": "string",
    "sentence_length_distribution": [
      {"length_words": "string", "frequency_percent": 0},
      {"length_words": "string", "frequency_percent": 0}
    ],
    "dominant_sentence_patterns": [
      {
        "name": "string",
        "example_template": "string",
        "creator_example": "string",
        "frequency_percent": 0,
        "typical_usage_context": "string"
      }
    ],
    "sentence_variety_preference": "string"
  },

  "3_paragraph_dynamics": {
    "sentences_per_paragraph_rule": "string",
    "paragraph_length_distribution": [
      {"sentences_count": 0, "frequency_percent": 0},
      {"sentences_count": 0, "frequency_percent": 0}
    ],
    "whitespace_preference": "string",
    "paragraph_sequencing_patterns": [
      {
        "pattern_name": "string",
        "description": "string",
        "example": "string"
      }
    ]
  },

  "4_vocabulary_and_phrasing": {
    "formality_and_specificity_level": "string",
    "jargon_level": "string",
    "unique_creator_lexicon": [
      {
        "term": "string",
        "meaning": "string",
        "typical_usage": "string"
      }
    ],
    "vocabulary_preferences": {
      "preferred_industry_terms": ["string", "string"],
      "avoided_terms": ["string", "string"],
      "specificity_with_numbers": "boolean"
    },
    "metaphor_and_analogy_style": "string"
  },

  "5_rhetorical_signature": {
    "key_rhetorical_devices_used": [
      {
        "device": "string",
        "frequency": "string",
        "typical_context": "string",
        "example": "string"
      }
    ],
    "overall_persuasion_style_summary": "string",
    "argument_construction_pattern": "string"
  },

  "6_structural_emphasis_techniques": {
    "text_formatting_for_emphasis": ["string"],
    "use_of_standalone_impact_phrases": {
      "frequency": "string",
      "typical_length_words": "string",
      "typical_placement": "string",
      "example": "string"
    },
    "emphasis_patterns": [
      {
        "technique": "string",
        "example": "string"
      }
    ]
  },

  "7_linguistic_dos": [
    {
      "rule": "string",
      "priority": 0.0,
      "example": "string"
    }
  ],

  "8_linguistic_donts": [
    {
      "rule": "string",
      "priority": 0.0,
      "rationale": "string"
    }
  ],

  "9_mid_body_reader_engagement_linguistics": {
    "rhetorical_questions_for_reflection_style": {
      "frequency": "string",
      "typical_placement": "string",
      "example": "string"
    },
    "direct_address_to_reader_style": {
      "frequency": "string",
      "typical_phrasing": "string",
      "example": "string"
    },
    "anticipation_building_phrasing_style": "string",
    "validation_or_empathy_cue_usage": "string"
  },

  "10_transition_techniques": {
    "between_paragraphs": {
      "dominant_style": "string",
      "common_transition_phrases": ["string", "string"]
    },
    "between_sections": {
      "dominant_style": "string",
      "examples": ["string", "string"]
    },
    "hook_to_body_transition": {
      "typical_pattern": "string",
      "examples": ["string", "string"]
    },
    "body_to_ending_transition": {
      "typical_pattern": "string",
      "examples": ["string", "string"]
    },
    "pacing_variation": "string"
  },

  "11_contextual_application_rules": [
    {
      "when": "string",
      "apply": "string",
      "avoid": "string",
      "example": "string"
    }
  ],

  "12_overall_linguistic_intent_summary": "string"
}
```

## Example Application
To illustrate how this analysis will be used: The content generation agent will directly reference specific elements of your JSON output when crafting content. For example:

1. It might check the "sentence_length_distribution" to ensure it's creating the right mix of sentence lengths
2. It will incorporate phrases from "unique_creator_lexicon" at appropriate frequencies
3. It will apply the "dominant_sentence_patterns" in contexts matching "typical_usage_context"
4. It will follow the prioritized "linguistic_dos" and avoid the "linguistic_donts"

Therefore, your analysis must be precise, specific, and directly actionable by an LLM without requiring further human interpretation.

## Important Notes
- Focus exclusively on the BODY section of posts, not hooks or endings
- Analyze linguistic patterns rather than content topics
- Quantify patterns wherever possible (use percentages, frequencies, etc.)
- Include authentic examples from the creator's content
- Be specific about when and how particular techniques should be applied
- Pay special attention to how the body connects to hooks and transitions to endings
- Ensure all guidance is directly actionable by an LLM

Your output should begin with the complete JSON object, followed by a brief explanation of the key insights that would be most important for an LLM to capture when emulating this creator's style.
