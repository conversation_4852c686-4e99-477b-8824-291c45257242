# JSON Format Instructions for LLM Analysis Prompts

## Proper JSON Formatting Requirements
When generating JSON output, adhere to these strict formatting guidelines:

1. **Complete JSON Structure**: Your response must contain a complete, well-formed JSON object that exactly matches the provided schema.

2. **String Value Formatting**:
   - All string values must be properly enclosed in double quotes (`"string value"`)
   - Escape all double quotes within string values (`"This is a \"quoted\" word"`)
   - Use `\n` for line breaks in string values, never actual line breaks
   - Use `\t` for tabs within string values
   - Escape backslashes with another backslash (`\\`)

3. **Special Character Handling**:
   - Escape all control characters properly (e.g., `\n`, `\r`, `\t`, `\b`, `\f`)
   - For unicode characters, use proper unicode escape sequences (e.g., `\u0022` for quotes)

4. **Numeric Values**:
   - Do not use quotes around numeric values (use `123.4`, not `"123.4"`)
   - Use decimal points for floating-point numbers, not commas

5. **Boolean Values**:
   - Use lowercase `true` and `false` without quotes

6. **Array Formatting**:
   - Arrays must begin with `[` and end with `]`
   - Elements must be separated by commas
   - No trailing comma after the last element

7. **Object Formatting**:
   - Objects must begin with `{` and end with `}`
   - Each key-value pair must be separated by a comma
   - No trailing comma after the last key-value pair
   - All keys must be strings with double quotes

## Example of Correct JSON Formatting

```json
{
  "string_key": "This is a string value with \"quoted text\" and a line break\nand continues here",
  "number_key": 123.45,
  "boolean_key": true,
  "array_key": [
    "item1",
    "item2",
    "item3"
  ],
  "nested_object": {
    "nested_key": "nested value",
    "nested_array": [1, 2, 3]
  }
}
```

## JSON Validation Check
Before finalizing your output, validate your JSON structure by checking:
1. All opening braces/brackets have matching closing braces/brackets
2. All string values and keys are properly enclosed in double quotes
3. All special characters within strings are properly escaped
4. No trailing commas exist in arrays or objects
5. All keys in each object are unique
6. The entire structure exactly matches the requested schema

Remember that your JSON output will be programmatically parsed without human intervention, so strict adherence to proper JSON formatting is essential.