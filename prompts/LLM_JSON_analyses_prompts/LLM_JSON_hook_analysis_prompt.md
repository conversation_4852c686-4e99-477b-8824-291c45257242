# LLM-Focused Hook Analysis Prompt (for JSON Style Guide Generation)

## Task Overview
You will analyze the hook writing style of a LinkedIn creator based on their posts. Your primary task is to generate a **structured JSON object** conforming to the `JsonHookStyleGuide` schema provided below. This guide will be used directly by an AI content generation system to emulate the creator's distinctive hook style with high fidelity.

A "hook" is typically the first 2-3 lines of a post, designed to grab attention and compel readers to click "see more" to continue reading the full post. Effective hooks are critical on LinkedIn as they determine whether content gets engagement. When analyzing posts, consider the hook to be the portion visible before the "see more" expansion on LinkedIn (approximately 140-200 characters) or the first natural break in the content, whichever comes first.

## Input
* A collection of LinkedIn posts by the creator (focus on the first ~3 lines of each).
* The creator's identifier (e.g., "retentionadam").
* (Optional) An existing human-readable Markdown analysis of the creator's hooks, which you can use as a reference to synthesize information.

## Output Format
Your analysis **must** be formatted as a single JSON object conforming exactly to the schema provided at the end of this prompt (see "Output Schema: JsonHookStyleGuide v1.1.0"). The output should be directly usable by another LLM for style emulation without requiring further human interpretation.

## Analysis Process

### Step 1: Thorough Review of Hooks
Carefully review the opening lines (hooks) of all provided posts. If a reference Markdown analysis is provided, review it to understand existing interpretations of patterns, triggers, and frameworks. Your primary source for examples and direct observations should always be the creator's actual post hooks.

### Step 2: Pattern Identification & Quantification
Identify recurring hook archetypes, psychological triggers, structural characteristics, and linguistic elements. Where possible, estimate frequencies or commonalities.
* **Guidance on Quantification:**
    * When providing `priority` scores (e.g., in `hook_dos_and_best_practices`), use a 0.0-1.0 scale where 1.0 is highest. If precise quantification is difficult due to sample size or the nature of the reference material, provide your best estimate.
    * For `estimated_frequency_category` (e.g., in `dominant_hook_archetypes`), use descriptive categories like "Very Frequent," "Frequent," "Occasional," or "Rare," based on your observations or the reference analysis.
    * Clearly indicate if a value is an estimate based on general observation rather than hard statistical counts if the schema allows for such annotation (e.g., in a descriptive string field).

### Step 3: Structured Analysis & Schema Population
Organize your findings meticulously according to the provided JSON schema. Be specific and precise:
* Include authentic, concise examples from the creator's hooks to illustrate patterns.
* Prioritize rules and patterns based on their impact and consistency in the creator's style.
* **Synthesizing Information from Reference Analysis (if provided):**
    * For `1_overall_hook_strategy_summary.description`, draw from sections like "Executive Summary" in a reference Markdown analysis. For `primary_goals_of_hooks`, infer these from the overall analysis of patterns and triggers.
    * When populating `2_dominant_hook_archetypes`, derive the `archetype_name` and initial `description_and_key_elements` from sections like "Hook Patterns Analysis" in reference reports. Further enrich `description_and_key_elements` and `typical_psychological_impact` by incorporating insights from "Frameworks" or "Psychological Triggers" sections if they align with the identified archetype.
    * For `3_key_psychological_triggers_leveraged`, primarily draw from sections titled "Psychological Triggers" in reference analyses. Ensure you select the top 3-4 most impactful ones and provide specific examples of their application in hooks. When analyzing psychological triggers, also identify the primary emotions (curiosity, surprise, fear, etc.) that the creator aims to evoke and how these emotions serve their content strategy.
    * For `7_hook_dos_and_best_practices` and `8_hook_donts_and_pitfalls_to_avoid`, primarily synthesize these from sections like "Recommendations" in reference reports. Convert recommendations into clear 'do' or 'don't' statements. Infer `priority` based on the emphasis given in the analysis or the observed impact. The `rationale_from_analysis` should briefly explain why this 'do' or 'don't' is important based on the overall analysis.
    * For `11_overall_hook_effectiveness_drivers_summary`, provide a concise (1-2 sentence) distillation of the primary reasons the creator's hooks are effective, drawing from your entire analysis and any executive summary in reference material.
* **Direct Observation for Other Fields:**
    * For `4_hook_structural_characteristics`, provide `typical_line_count` (e.g., "2-3 lines"), `typical_word_count_range` (e.g., `[15, 40]`), `target_character_count_for_linkedin_preview_approx` (e.g., 140-200, as LinkedIn typically shows approximately 140-200 characters before the "see more" prompt, though this can vary by device and platform version), and list common `common_opening_phrases_or_elements`.
    * Fields like `5_hook_linguistic_style_highlights` and `6_hook_formatting_for_attention` will primarily require direct observation and analysis of the creator's post hooks.
    * For `9_hook_segue_elements`, identify `common_segue_phrasing_in_hook` (phrases *within* the hook itself that build anticipation or signal continuation, e.g., "Here's why:", "The secret is...") and `typical_final_punctuation_of_hook` (e.g., ':', '...', '?', '!').
    * For `10_audience_targeting_in_hooks`, analyze how the creator tailors hooks to their specific audience. Identify `primary_audience_signals` (phrases or references that indicate who the content is for), `industry_specific_terminology_usage` (specialized terms that resonate with their target audience), and `audience_pain_points_addressed` (common challenges or desires the hooks reference).
    * If engagement metrics (likes, comments) are available for posts, note any correlation between specific hook types and higher engagement levels in your analysis of dominant hook archetypes and effectiveness drivers.
* **Handling Non-Prominent Features:** If a specific stylistic element defined in the schema is not notably present or consistently used by the creator (based on your direct analysis of posts and any reference material):
    * For array fields, provide an empty list `[]`.
    * For optional string or object fields where no pattern is observed, use `null` or a specific descriptive string like "No prominent pattern observed" or "Not a defining feature" if the field type allows.
    * Do not invent patterns merely to fill the schema.

### Step 4: Validation (Self-Correction)
Before finalizing your output, review your completed JSON for:
* **Schema Conformance:** Does it exactly match the provided schema structure and data types?
* **Completeness:** Are all relevant schema fields populated according to the guidance?
* **Accuracy:** Do the identified patterns and examples genuinely reflect the creator's actual hook style?
* **Actionability:** Is the guidance specific and clear enough for another LLM to implement effectively?
* **Consistency:** Is there any contradictory guidance within the JSON?
* **Example Diversity:** Do the examples provided represent a range of the creator's hooks rather than focusing on just one or two patterns?

## Output Schema: JsonHookStyleGuide v1.1.0
Your analysis **must** conform exactly to this JSON schema:
```json
{
  "schema_version": "1.1.0",
  "creator_id": "string",
  "generated_at": "datetime_iso8601_string",

  "1_overall_hook_strategy_summary": {
    "description": "string",
    "primary_goals_of_hooks": ["string"]
  },

  "2_dominant_hook_archetypes": [
    {
      "archetype_name": "string",
      "description_and_key_elements": "string",
      "creator_example_best": "string",
      "typical_psychological_impact": "string",
      "estimated_frequency_category": "string"
    }
  ],

  "3_key_psychological_triggers_leveraged": [
    {
      "trigger_name": "string",
      "how_creator_applies_it_in_hooks": "string",
      "creator_example": "string"
    }
  ],

  "4_hook_structural_characteristics": {
    "typical_line_count": "string",
    "typical_word_count_range": ["integer", "integer"],
    "target_character_count_for_linkedin_preview_approx": "integer",
    "common_opening_phrases_or_elements": ["string"]
  },

  "5_hook_linguistic_style_highlights": {
    "use_of_strong_verbs_or_impact_words_style": "string",
    "question_usage_style_in_hooks": "string",
    "specificity_level_in_hooks_description": "string"
  },

  "6_hook_formatting_for_attention": {
    "use_of_all_caps_or_bold_in_hook_description": "string",
    "emoji_usage_in_hook_description": "string"
  },

  "7_hook_dos_and_best_practices": [
    {
      "do_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "8_hook_donts_and_pitfalls_to_avoid": [
    {
      "dont_statement": "string",
      "priority": 0.0,
      "rationale_from_analysis": "string"
    }
  ],

  "9_hook_segue_elements": {
      "common_segue_phrasing_in_hook": ["string"],
      "typical_final_punctuation_of_hook": "string"
  },

  "10_audience_targeting_in_hooks": {
      "primary_audience_signals": ["string"],
      "industry_specific_terminology_usage": "string",
      "audience_pain_points_addressed": ["string"]
  },

  "11_overall_hook_effectiveness_drivers_summary": "string"
}
