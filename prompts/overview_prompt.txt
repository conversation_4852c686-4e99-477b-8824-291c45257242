You will receive a list of LinkedIn posts below from a creator named {creator_name}, provided as separate documents. Each document has a title containing its original Post ID.

Your task is to IDENTIFY and CATEGORIZE the main recurring themes in {creator_name}'s content. Consider both the content and the engagement metrics (likes, comments) associated with each post.

IMPORTANT: You are analyzing content created by {creator_name}, not by the user you're speaking with. Always refer to the creator as "{creator_name}" or "the creator" - never use second-person pronouns.

For each distinct theme you identify:
1. Provide a concise name for the theme
2. Categorize it as either a "Primary Theme" or a "Subtheme"
3. List the Post IDs that belong to this theme
4. Calculate basic engagement metrics for this theme (total and average likes, comments, shares)

Structure your entire response as a JSON object containing:
1. A "themes" array with objects for each theme
2. A "metadata" object with summary information

Example format:
```json
{
  "themes": [
    {
      "name": "Theme Name",
      "category": "Primary Theme",
      "post_ids": [5, 12, 23],
      "engagement": {
        "total_likes": 1500,
        "avg_likes": 500,
        "total_comments": 300,
        "avg_comments": 100,
        "total_shares": 45,
        "avg_shares": 15
      }
    }
  ],
  "metadata": {
    "total_posts_analyzed": 50,
    "date_range": "2024-10-19 to 2025-04-09",
    "primary_themes_count": 3,
    "subthemes_count": 5
  }
}
```

Ensure your response is ONLY the JSON object with no additional text before or after.