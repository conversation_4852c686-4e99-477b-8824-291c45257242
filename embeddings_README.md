# MongoDB Atlas Vector Search Implementation Guide

This guide documents the vector search implementation for LinkedInsight's pattern matching system.

## Overview

We use MongoDB Atlas Vector Search with Voyage AI embeddings to match content briefs with the most relevant writing patterns from our creator analyses.

## Key Components

### 1. Embedding Model
- **Provider**: Voyage AI
- **Model**: `voyage-3.5`
- **Dimensions**: 1024
- **API Key**: Set as `VOYAGE_AI_API_KEY` environment variable (not `VOYAGE_API_KEY`)

### 2. MongoDB Setup
- **Database**: `linkedinsight` (NOT `storyd_ai`)
- **Collections**: 
  - Source data: `LLM_friendly_analyses` (NOT `creator_analyses`)
  - Embeddings: `pattern_embeddings`
- **Index Name**: `pattern_embeddings_vector_index`
- **Index Type**: Vector Search (Atlas Search)

### 3. Document Structure
Each pattern document contains:
```json
{
  "creator_id": "retentionadam",
  "analysis_type": "hook",  // or "ending", "theme"
  "pattern_name": "Counterintuitive Claim/Myth Busting",
  "content": {
    "description": "...",
    "example": "...",
    "psychological_impact": "...",
    "frequency": "..."
  },
  "searchable_text": "concatenated text for semantic search",
  "embedding": [/* 1024 float values */],
  "embedding_model": "voyage-3.5",
  "embedding_dimensions": 1024,
  "indexed_at": "2025-05-28T00:24:02.456Z"
}
```

## Implementation Steps

### 1. Extract Patterns from MongoDB
```python
# See: test_pattern_extraction_from_mongo.py
# Extracts individual patterns from LLM_friendly_analyses collection
```

### 2. Generate Embeddings
```python
# See: test_voyage_embedding_simple_pipeline.py
import voyageai

client = voyageai.Client(api_key=os.getenv("VOYAGE_AI_API_KEY"))
result = client.embed(texts, model="voyage-3.5")
embeddings = result.embeddings
```

**Important**: Voyage AI has rate limits (3 RPM initially). Add delays between batches.

### 3. Create Vector Search Index

In MongoDB Atlas UI:
1. Go to your cluster → Search tab
2. Create Search Index → JSON Editor
3. Use this configuration:

```json
{
  "fields": [
    {
      "type": "vector",
      "path": "embedding",
      "numDimensions": 1024,
      "similarity": "cosine"
    },
    {
      "type": "filter",
      "path": "creator_id"
    },
    {
      "type": "filter",
      "path": "analysis_type"
    },
    {
      "type": "filter",
      "path": "pattern_type"
    },
    {
      "type": "filter",
      "path": "creator_name"
    },
    {
      "type": "filter",
      "path": "pattern_name"
    }
  ]
}
```

### 4. Perform Vector Search
```python
# See: test_vector_working.py
pipeline = [
    {
        "$vectorSearch": {
            "index": "pattern_embeddings_vector_index",
            "path": "embedding",
            "queryVector": query_embedding,
            "numCandidates": 100,  # 10-20x the limit
            "limit": 5
        }
    },
    {
        "$project": {
            "pattern_name": 1,
            "creator_id": 1,
            "score": {"$meta": "vectorSearchScore"}
        }
    }
]
results = list(collection.aggregate(pipeline))
```

## Common Issues & Solutions

### Issue 1: Wrong Database Name
- **Problem**: Using `storyd_ai` instead of `linkedinsight`
- **Solution**: Always use `db = client["linkedinsight"]`

### Issue 2: Index Not Found
- **Problem**: $vectorSearch returns 0 results even though index shows as READY
- **Diagnosis**: Check index is created on correct database/collection
- **Solution**: Verify in Atlas UI that index is on `linkedinsight.pattern_embeddings_test`

### Issue 3: Rate Limiting
- **Problem**: Voyage AI returns rate limit errors
- **Solution**: Add 20-second delays between API calls for 3 RPM limit

### Issue 4: Empty Pattern Descriptions
- **Problem**: Some ending patterns have empty descriptions from extraction
- **Solution**: Update extraction logic to handle ending analysis format correctly

## Testing

1. **Connection Test**: `python check_atlas_connection.py`
2. **Vector Search Test**: `python test_vector_working.py`
3. **Full Pipeline**: `python test_voyage_embedding_simple_pipeline.py`

## Production Status

### Completed (as of 2025-05-28)
1. **Full Hook and Ending Embeddings**: Successfully embedded 183 patterns (88 hooks, 95 endings) from 22 creators
2. **Theme Embeddings**: Successfully embedded 65 theme patterns from 16 creators (6 creators had malformed JSON)
3. **Body Pattern Embeddings**: Successfully embedded 254 body patterns from 22 creators using AI-assisted extraction
4. **Rich Framework Embeddings**: Successfully embedded 81 rich body frameworks from 22 creators using markdown parsing (May 2025)
5. **Production Collection**: Using `pattern_embeddings` collection with **583+ total patterns**
6. **Vector Search Index**: Created `pattern_embeddings_vector_index` on Atlas Search with all patterns indexed
7. **Cross-Creator Search**: Verified semantic search works across all creators with scores 0.7-0.86
8. **Brief-to-Pattern Pipeline**: Implemented and tested `brief_to_patterns.py` - successfully matches briefs to relevant patterns across all pattern types

### Key Discoveries
1. **Consistent Key Names**: Hook, ending, and theme analyses have consistent field names across all creators:
   - Hooks: `archetype_name`, `description_and_key_elements`, `creator_example_best`, `typical_psychological_impact`, `estimated_frequency_category`
   - Endings: `ending_type_name`, `description_and_key_elements`, `creator_example_best`, `typical_engagement_impact`, `estimated_frequency_category`
   - Themes: `theme_name`, `description`, `characteristic_elements`, `creator_example_best`, `frequency_percent`, `engagement_metrics`, `engagement_level_relative`, `theme_specific_engagement_tactics`, `why_theme_works`
2. **MongoDB Document Structure**: Analyses are stored with different field names depending on analysis type:
   - Hook/Ending analyses: Data is in `analysis_data` field (JSON object)
   - Theme analyses: Data is in `llm_analysis_data` field (JSON object)
   - Body analyses: Data is in `llm_analysis_data` field (markdown string)
   - Analysis types in database: `"hook"`, `"ending"`, `"themes"`, `"body"` (NOT `"body_llm_friendly"`)
   - Key data location: `analysis_data["2_dominant_hook_archetypes"]` for hooks, `analysis_data["3_effective_ending_approaches"]` for endings, `llm_analysis_data["2_primary_content_themes"]` for themes
3. **Rate Limiting Solution**: Implemented 20-second delays between batches for Voyage AI's 3 RPM limit
4. **Index Creation**: Use PyMongo's `create_search_index()` with proper mappings structure

## Next Steps

1. ✅ **Update vector_pattern_selector.py** - Changed from test collection to production `pattern_embeddings`
2. ✅ **Process themes** - Completed theme extraction and embedding (65 patterns)
3. ✅ **Process body patterns** - Successfully extracted and embedded 254 body patterns using Claude Sonnet 4
4. ✅ **Create brief-to-pattern pipeline** - Implemented `src/utils/brief_to_patterns.py` for semantic pattern matching
5. **Integrate into main pipeline** - Replace current pattern selection logic with vector search
6. **Fix malformed JSON** - Investigate and fix theme data for 6 creators with JSON parsing errors
7. **Monitor performance** - Track vector search latency and quality in production
8. **Add length-based filtering** - Enhance pattern selection based on desired content length

## Performance Notes

- Vector search is extremely fast (~50ms per query)
- Embedding generation is the bottleneck (rate limited)
- Consider batch processing and caching strategies
- numCandidates should be 10-20x your desired limit for best results

## Files Reference

### Production Scripts
- **Hook/Ending embedding pipeline**: `scripts/batch_embed_hooks_endings.py` - Production script for hook and ending embeddings
- **Theme embedding pipeline**: `scripts/batch_embed_themes.py` - Production script for theme embeddings
- **Body pattern embedding pipeline**: `scripts/batch_embed_body_patterns.py` - AI-assisted extraction and embedding of body patterns
- **Rich framework embedding pipeline**: `scripts/batch_embed_frameworks.py` - Markdown parsing and embedding of rich body frameworks
- **Brief-to-pattern matcher**: `src/utils/brief_to_patterns.py` - Main pipeline for matching briefs to patterns via vector search
- **Vector pattern selector**: `src/utils/vector_pattern_selector.py` - Integration with content generation
- **Index creation**: `create_vector_search_index.py` - Programmatic index creation

### Test/Development Scripts
- Pattern extraction: `test_pattern_extraction_from_mongo.py`
- Embedding generation: `test_voyage_embedding_simple_pipeline.py`
- MongoDB insertion: `insert_embeddings_to_mongo.py`
- Vector search testing: `test_vector_working.py`
- Index configuration: `mongodb_vector_index_config.json`