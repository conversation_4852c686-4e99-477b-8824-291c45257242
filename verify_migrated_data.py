import os
import json
import sys
import argparse
from pathlib import Path
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure

# Add project root to system path
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from src.config import Config
except ModuleNotFoundError as e:
    print(f"Error: Could not import Config from src.config. Original error: {e}")
    sys.exit(1)
except ImportError as e:
    print(f"Error: ImportError when trying to import Config from src.config. Original error: {e}")
    sys.exit(1)

MONGO_COLLECTION_NAME = "LLM_friendly_analyses"

# --- Configuration for Verification ---
ANALYSIS_TYPES_TO_VERIFY = {
    "themes": "json",
    "hook": "json",
    "linguistic": "json",
    "ending": "json",
    "body": "markdown"
}

def load_local_file_content(filepath, content_type):
    """Loads content from the local file, parsing JSON if specified."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            if content_type == "json":
                return json.load(f)
            elif content_type == "markdown":
                return f.read()
            else:
                print(f"Unsupported content type for local file: {content_type}")
                return None
    except FileNotFoundError:
        print(f"Error: Local file not found: {filepath}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from local file {filepath}: {e}")
        return None
    except Exception as e:
        print(f"Error reading local file {filepath}: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Verify migrated LLM-friendly analyses in MongoDB against source files.")
    parser.add_argument("creator_name", help="The name of the creator to verify.")
    args = parser.parse_args()

    creator_to_verify = args.creator_name

    print(f"Starting verification for creator: {creator_to_verify}...")
    
    mongodb_uri = None
    try:
        mongodb_uri = Config.get_mongodb_uri()
        uri_parts = mongodb_uri.split('/')
        db_name_from_uri = ""
        if len(uri_parts) >= 4:
            db_name_candidate = uri_parts[3]
            if '?' in db_name_candidate:
                db_name_from_uri = db_name_candidate.split('?')[0]
            else:
                db_name_from_uri = db_name_candidate
        actual_db_name = db_name_from_uri if db_name_from_uri else "linkedinsight"

        client = MongoClient(mongodb_uri)
        client.admin.command('ping') 
        db = client[actual_db_name]
        collection = db[MONGO_COLLECTION_NAME]
        print(f"Successfully connected to MongoDB: DB '{actual_db_name}', Collection '{MONGO_COLLECTION_NAME}'")
    except ValueError as ve:
        print(f"Configuration error: {ve}. Ensure MONGODB_URI is in .env.")
        return
    except ConnectionFailure:
        print(f"Failed to connect to MongoDB (URI used: {mongodb_uri}). Check connection string and if MongoDB is running.")
        return
    except Exception as e:
        print(f"Unexpected error during MongoDB connection: {e}")
        return

    verification_summary = {}

    for analysis_type, content_format in ANALYSIS_TYPES_TO_VERIFY.items():
        print(f"\nVerifying {analysis_type} analysis ({content_format})...")
        
        mongo_doc = collection.find_one({
            "creator_name": creator_to_verify,
            "analysis_type": analysis_type
        })

        if not mongo_doc:
            print(f"  Error: Document not found in MongoDB for {creator_to_verify} - {analysis_type}.")
            verification_summary[analysis_type] = "MongoDB document not found"
            continue

        mongo_data = mongo_doc.get("llm_analysis_data")
        source_file_path = mongo_doc.get("source_file_path")

        if mongo_data is None:
            print(f"  Error: 'llm_analysis_data' field missing or null in MongoDB document for {analysis_type}.")
            verification_summary[analysis_type] = "llm_analysis_data missing/null in MongoDB"
            continue
            
        if not source_file_path:
            print(f"  Error: 'source_file_path' missing in MongoDB document for {analysis_type}. Cannot compare.")
            verification_summary[analysis_type] = "source_file_path missing in MongoDB"
            continue
        
        print(f"  MongoDB source_file_path: {source_file_path}")
        local_data = load_local_file_content(source_file_path, content_format)

        if local_data is None:
            verification_summary[analysis_type] = "Local file error or not found"
            continue

        if mongo_data == local_data:
            print(f"  SUCCESS: Content for {analysis_type} matches between MongoDB and local file.")
            verification_summary[analysis_type] = "Match"
        else:
            print(f"  FAILURE: Content for {analysis_type} DOES NOT MATCH between MongoDB and local file.")
            verification_summary[analysis_type] = "Mismatch"

    print("\n--- Verification Summary ---")
    for analysis_type, result in verification_summary.items():
        print(f"- {analysis_type.capitalize()}: {result}")
    
    client.close()
    print("\nVerification script finished.")

if __name__ == "__main__":
    main() 